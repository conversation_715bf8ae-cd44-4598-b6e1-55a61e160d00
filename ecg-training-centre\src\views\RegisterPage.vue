<template>
  <f7-page name="register">
    <f7-navbar title="Create Account" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Register Header -->
      <div class="register-header">
        <div class="container">
          <h1>Join ECG Training Centre</h1>
          <p>Create your account to access professional training courses</p>
        </div>
      </div>

      <!-- Registration Form -->
      <div class="register-form-section">
        <div class="container">
          <form @submit.prevent="handleRegister">
            <f7-list>
              <f7-list-input
                label="Full Name"
                type="text"
                placeholder="Enter your full name"
                v-model:value="name"
                required
                :disabled="loading"
              ></f7-list-input>
              
              <f7-list-input
                label="Email"
                type="email"
                placeholder="Enter your email"
                v-model:value="email"
                required
                :disabled="loading"
              ></f7-list-input>
              
              <f7-list-input
                label="Phone Number"
                type="tel"
                placeholder="Enter your phone number"
                v-model:value="phone"
                :disabled="loading"
              ></f7-list-input>
              
              <f7-list-input
                label="Password"
                type="password"
                placeholder="Create a password"
                v-model:value="password"
                required
                :disabled="loading"
              ></f7-list-input>
              
              <f7-list-input
                label="Confirm Password"
                type="password"
                placeholder="Confirm your password"
                v-model:value="passwordConfirm"
                required
                :disabled="loading"
              ></f7-list-input>
            </f7-list>
            
            <!-- Password Requirements -->
            <div class="password-requirements">
              <h4>Password Requirements:</h4>
              <ul>
                <li :class="{ valid: password.length >= 8 }">At least 8 characters</li>
                <li :class="{ valid: hasUppercase }">One uppercase letter</li>
                <li :class="{ valid: hasLowercase }">One lowercase letter</li>
                <li :class="{ valid: hasNumber }">One number</li>
              </ul>
            </div>
            
            <!-- Error Display -->
            <div v-if="error" class="error-message">
              <f7-icon ios="f7:exclamationmark_triangle" md="material:error" size="20" color="red"></f7-icon>
              <span>{{ error }}</span>
            </div>
            
            <!-- Terms and Conditions -->
            <div class="terms-section">
              <f7-list>
                <f7-list-item
                  checkbox
                  title="I agree to the Terms and Conditions"
                  v-model:checked="agreeToTerms"
                ></f7-list-item>
              </f7-list>
            </div>
            
            <!-- Register Button -->
            <div class="form-actions">
              <f7-button 
                large 
                fill 
                color="green" 
                type="submit"
                :loading="loading"
                :disabled="!isFormValid"
              >
                Create Account
              </f7-button>
            </div>
          </form>
        </div>
      </div>

      <!-- Login Link -->
      <div class="login-section">
        <div class="container">
          <f7-card>
            <f7-card-content>
              <div class="login-prompt">
                <p>Already have an account?</p>
                <f7-button fill color="blue" @click="goToLogin">Sign In</f7-button>
              </div>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- Benefits -->
      <div class="benefits-section">
        <div class="container">
          <h3>What You'll Get</h3>
          <div class="benefits-list">
            <div class="benefit-item">
              <f7-icon ios="f7:checkmark_circle" md="material:check_circle" size="24" color="green"></f7-icon>
              <span>Access to all course materials</span>
            </div>
            <div class="benefit-item">
              <f7-icon ios="f7:checkmark_circle" md="material:check_circle" size="24" color="green"></f7-icon>
              <span>Industry-recognized certificates</span>
            </div>
            <div class="benefit-item">
              <f7-icon ios="f7:checkmark_circle" md="material:check_circle" size="24" color="green"></f7-icon>
              <span>Expert instructor support</span>
            </div>
            <div class="benefit-item">
              <f7-icon ios="f7:checkmark_circle" md="material:check_circle" size="24" color="green"></f7-icon>
              <span>Professional networking opportunities</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { f7 } from 'framework7-vue'
import { useAuth } from '@/composables/useAuth'

const { register, loading, error, clearError } = useAuth()

const name = ref('')
const email = ref('')
const phone = ref('')
const password = ref('')
const passwordConfirm = ref('')
const agreeToTerms = ref(false)

// Password validation
const hasUppercase = computed(() => /[A-Z]/.test(password.value))
const hasLowercase = computed(() => /[a-z]/.test(password.value))
const hasNumber = computed(() => /\d/.test(password.value))
const isPasswordValid = computed(() => 
  password.value.length >= 8 && 
  hasUppercase.value && 
  hasLowercase.value && 
  hasNumber.value
)

const isFormValid = computed(() => 
  name.value.trim() !== '' &&
  email.value.trim() !== '' &&
  isPasswordValid.value &&
  password.value === passwordConfirm.value &&
  agreeToTerms.value
)

const handleRegister = async () => {
  clearError()
  
  if (password.value !== passwordConfirm.value) {
    f7.dialog.alert('Passwords do not match', 'Error')
    return
  }
  
  if (!isPasswordValid.value) {
    f7.dialog.alert('Please ensure your password meets all requirements', 'Error')
    return
  }
  
  if (!agreeToTerms.value) {
    f7.dialog.alert('Please agree to the Terms and Conditions', 'Error')
    return
  }
  
  try {
    await register({
      name: name.value,
      email: email.value,
      phone: phone.value,
      password: password.value,
      passwordConfirm: passwordConfirm.value
    })
    
    f7.dialog.alert(
      'Account created successfully! Please check your email for verification.',
      'Registration Successful',
      () => {
        f7.views.main.router.navigate('/login/')
      }
    )
  } catch (err: any) {
    console.error('Registration failed:', err)
    // Error is already set by the useAuth composable
  }
}

const goToLogin = () => {
  f7.views.main.router.navigate('/login/')
}
</script>

<style scoped>
.register-header {
  background: linear-gradient(135deg, #059669 0%, #1e40af 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.register-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.register-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.register-form-section {
  padding: 40px 20px;
}

.password-requirements {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px;
}

.password-requirements h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  padding: 5px 0;
  color: #666;
  display: flex;
  align-items: center;
}

.password-requirements li::before {
  content: '✗';
  color: #dc3545;
  margin-right: 8px;
  font-weight: bold;
}

.password-requirements li.valid::before {
  content: '✓';
  color: #28a745;
}

.password-requirements li.valid {
  color: #28a745;
}

.terms-section {
  margin: 20px 0;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 5px;
  margin: 15px 20px;
  color: #c33;
}

.login-section {
  padding: 20px;
  background: #f8f9fa;
}

.login-prompt {
  text-align: center;
}

.login-prompt p {
  margin-bottom: 1rem;
  color: #666;
}

.benefits-section {
  padding: 40px 20px;
}

.benefits-section h3 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.benefit-item span {
  color: #333;
}

.container {
  max-width: 500px;
  margin: 0 auto;
}
</style>
