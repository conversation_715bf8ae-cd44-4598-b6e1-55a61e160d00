<template>
  <div class="register-page min-h-screen bg-gradient-ecg flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <span class="text-white font-bold text-2xl">ECG</span>
        </div>
        <h1 class="text-4xl font-bold text-white mb-2">Join ECG Training Centre</h1>
        <p class="text-blue-100">Create your account to access professional training courses</p>
      </div>

      <!-- Registration Form -->
      <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
        <form @submit.prevent="handleRegister" class="space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-white mb-2">Full Name *</label>
            <input
              id="name"
              type="text"
              placeholder="Enter your full name"
              v-model="name"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-white mb-2">Email *</label>
            <input
              id="email"
              type="email"
              placeholder="Enter your email"
              v-model="email"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <div>
            <label for="phone" class="block text-sm font-medium text-white mb-2">Phone Number</label>
            <input
              id="phone"
              type="tel"
              placeholder="Enter your phone number"
              v-model="phone"
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-white mb-2">Password *</label>
            <input
              id="password"
              type="password"
              placeholder="Create a password"
              v-model="password"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <div>
            <label for="passwordConfirm" class="block text-sm font-medium text-white mb-2">Confirm Password *</label>
            <input
              id="passwordConfirm"
              type="password"
              placeholder="Confirm your password"
              v-model="passwordConfirm"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <!-- Password Requirements -->
          <div class="bg-white/10 rounded-lg p-4">
            <h4 class="text-white font-medium mb-3">Password Requirements:</h4>
            <ul class="space-y-1">
              <li class="flex items-center text-sm" :class="password.length >= 8 ? 'text-green-300' : 'text-blue-200'">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="password.length >= 8 ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'"></path>
                </svg>
                At least 8 characters
              </li>
              <li class="flex items-center text-sm" :class="hasUppercase ? 'text-green-300' : 'text-blue-200'">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="hasUppercase ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'"></path>
                </svg>
                One uppercase letter
              </li>
              <li class="flex items-center text-sm" :class="hasLowercase ? 'text-green-300' : 'text-blue-200'">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="hasLowercase ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'"></path>
                </svg>
                One lowercase letter
              </li>
              <li class="flex items-center text-sm" :class="hasNumber ? 'text-green-300' : 'text-blue-200'">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="hasNumber ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'"></path>
                </svg>
                One number
              </li>
            </ul>
          </div>

          <!-- Error Display -->
          <div v-if="error" class="bg-red-500/20 border border-red-400/50 rounded-lg p-4 flex items-center space-x-2">
            <svg class="w-5 h-5 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-red-200">{{ error }}</span>
          </div>

          <!-- Terms and Conditions -->
          <div class="flex items-start space-x-3">
            <input
              id="agreeToTerms"
              type="checkbox"
              v-model="agreeToTerms"
              class="mt-1 w-4 h-4 text-ecg-blue bg-white/20 border-white/30 rounded focus:ring-white/50 focus:ring-2"
            />
            <label for="agreeToTerms" class="text-blue-100 text-sm">
              I agree to the <a href="#" class="text-white underline hover:text-blue-200">Terms and Conditions</a> and <a href="#" class="text-white underline hover:text-blue-200">Privacy Policy</a>
            </label>
          </div>

          <!-- Register Button -->
          <button
            type="submit"
            :disabled="!isFormValid || loading"
            class="w-full bg-ecg-green text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            {{ loading ? 'Creating Account...' : 'Create Account' }}
          </button>
        </form>
      </div>

      <!-- Login Link -->
      <div class="text-center mt-8">
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
          <p class="text-blue-100 mb-4">Already have an account?</p>
          <button
            @click="goToLogin"
            class="bg-ecg-blue text-white px-6 py-3 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>

      <!-- Benefits -->
      <div class="mt-12">
        <h3 class="text-xl font-semibold text-white text-center mb-8">What You'll Get</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-blue-100">Access to all course materials</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-blue-100">Industry-recognized certificates</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-blue-100">Expert instructor support</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-blue-100">Professional networking opportunities</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const router = useRouter()

const { register, loading, error, clearError } = useAuth()

const name = ref('')
const email = ref('')
const phone = ref('')
const password = ref('')
const passwordConfirm = ref('')
const agreeToTerms = ref(false)

// Password validation
const hasUppercase = computed(() => /[A-Z]/.test(password.value))
const hasLowercase = computed(() => /[a-z]/.test(password.value))
const hasNumber = computed(() => /\d/.test(password.value))
const isPasswordValid = computed(() => 
  password.value.length >= 8 && 
  hasUppercase.value && 
  hasLowercase.value && 
  hasNumber.value
)

const isFormValid = computed(() => 
  name.value.trim() !== '' &&
  email.value.trim() !== '' &&
  isPasswordValid.value &&
  password.value === passwordConfirm.value &&
  agreeToTerms.value
)

const handleRegister = async () => {
  clearError()
  
  if (password.value !== passwordConfirm.value) {
    f7.dialog.alert('Passwords do not match', 'Error')
    return
  }
  
  if (!isPasswordValid.value) {
    f7.dialog.alert('Please ensure your password meets all requirements', 'Error')
    return
  }
  
  if (!agreeToTerms.value) {
    f7.dialog.alert('Please agree to the Terms and Conditions', 'Error')
    return
  }
  
  try {
    await register({
      name: name.value,
      email: email.value,
      phone: phone.value,
      password: password.value,
      passwordConfirm: passwordConfirm.value
    })
    
    alert('Account created successfully! Please check your email for verification.')
    router.push('/login')
  } catch (err: any) {
    console.error('Registration failed:', err)
    // Error is already set by the useAuth composable
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-header {
  background: linear-gradient(135deg, #059669 0%, #1e40af 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.register-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.register-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.register-form-section {
  padding: 40px 20px;
}

.password-requirements {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px;
}

.password-requirements h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  padding: 5px 0;
  color: #666;
  display: flex;
  align-items: center;
}

.password-requirements li::before {
  content: '✗';
  color: #dc3545;
  margin-right: 8px;
  font-weight: bold;
}

.password-requirements li.valid::before {
  content: '✓';
  color: #28a745;
}

.password-requirements li.valid {
  color: #28a745;
}

.terms-section {
  margin: 20px 0;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 5px;
  margin: 15px 20px;
  color: #c33;
}

.login-section {
  padding: 20px;
  background: #f8f9fa;
}

.login-prompt {
  text-align: center;
}

.login-prompt p {
  margin-bottom: 1rem;
  color: #666;
}

.benefits-section {
  padding: 40px 20px;
}

.benefits-section h3 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.benefit-item span {
  color: #333;
}

/* Tailwind CSS handles all styling */
</style>
