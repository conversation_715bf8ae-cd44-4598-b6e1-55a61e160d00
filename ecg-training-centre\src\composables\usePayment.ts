import { ref, computed } from 'vue'
import { paymentService, CoursePricing } from '@/services/payment'
import type { PaymentData } from '@/services/payment'
import { useAuth } from './useAuth'

export function usePayment() {
  const { user } = useAuth()
  const loading = ref(false)
  const error = ref<string | null>(null)
  const paymentReference = ref<string | null>(null)

  const initiateCoursePayment = async (courseId: string, courseCategory: string) => {
    if (!user.value) {
      throw new Error('User must be logged in to make payment')
    }

    loading.value = true
    error.value = null

    try {
      const reference = paymentService.generateReference()
      paymentReference.value = reference

      const paymentData: PaymentData = {
        email: user.value.email,
        amount: CoursePricing.getCoursePriceInPesewas(courseCategory),
        currency: 'GHS',
        reference: reference,
        metadata: {
          course_id: courseId,
          user_id: user.value.id,
          course_category: courseCategory,
          payment_type: 'course_enrollment'
        },
        channels: paymentService.getGhanaPaymentChannels()
      }

      await paymentService.initializePayment(paymentData)
      
      // Payment successful - verify on backend
      const verification = await paymentService.verifyPayment(reference)
      
      if (verification.status && verification.data?.status === 'success') {
        return {
          success: true,
          reference: reference,
          data: verification.data
        }
      } else {
        throw new Error('Payment verification failed')
      }
    } catch (err: any) {
      error.value = err.message || 'Payment failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const verifyPayment = async (reference: string) => {
    loading.value = true
    error.value = null

    try {
      const verification = await paymentService.verifyPayment(reference)
      return verification
    } catch (err: any) {
      error.value = err.message || 'Payment verification failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const formatAmount = (amount: number, currency = 'GHS') => {
    return paymentService.formatAmount(amount, currency)
  }

  const getCoursePrice = (courseCategory: string) => {
    return CoursePricing.getCoursePrice(courseCategory)
  }

  const getFormattedCoursePrice = (courseCategory: string) => {
    return CoursePricing.formatCoursePrice(courseCategory)
  }

  const getMobileMoneyProviders = () => {
    return paymentService.getMobileMoneyProviders()
  }

  const clearError = () => {
    error.value = null
  }

  const clearPaymentReference = () => {
    paymentReference.value = null
  }

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    paymentReference: computed(() => paymentReference.value),
    
    // Actions
    initiateCoursePayment,
    verifyPayment,
    formatAmount,
    getCoursePrice,
    getFormattedCoursePrice,
    getMobileMoneyProviders,
    clearError,
    clearPaymentReference
  }
}
