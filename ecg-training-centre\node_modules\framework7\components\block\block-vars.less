:root {
  --f7-block-padding-horizontal: 16px;
  --f7-block-padding-vertical: 16px;
  --f7-block-font-size: inherit;
  --f7-block-header-margin: 10px;
  --f7-block-footer-margin: 10px;
  --f7-block-header-font-size: 14px;
  --f7-block-footer-font-size: 14px;
  --f7-block-title-text-transform: none;
  --f7-block-title-white-space: nowrap;
  --f7-block-title-medium-text-transform: none;
  --f7-block-title-large-text-transform: none;
  --f7-block-inset-side-margin: 16px;
  .dark-vars({
    --f7-block-outline-border-color: rgba(255, 255, 255, 0.15);
  });
}
.ios-vars({
  --f7-block-text-color: inherit;
  --f7-block-margin-vertical: 35px;
  --f7-block-outline-border-color: rgba(0,0,0,0.22);
  --f7-block-title-font-size: 16px;
  --f7-block-title-font-weight: 600;
  --f7-block-title-line-height: 20px;
  --f7-block-title-margin-bottom: 10px;
  --f7-block-title-medium-font-size: 22px;
  --f7-block-title-medium-font-weight: bold;
  --f7-block-title-medium-line-height: 1.4;
  --f7-block-title-large-font-size: 30px;
  --f7-block-title-large-font-weight: bold;
  --f7-block-title-large-line-height: 1.3;
  --f7-block-inset-border-radius: 8px;
  .light-vars({
    --f7-block-title-text-color: #000;
    --f7-block-strong-text-color: #000;
    --f7-block-header-text-color: rgba(0, 0, 0, 0.45);
    --f7-block-footer-text-color: rgba(0, 0, 0, 0.45);
    --f7-block-strong-bg-color: #fff;
    --f7-block-title-medium-text-color: #000;
    --f7-block-title-large-text-color: #000;
  });
  .dark-vars({
    --f7-block-title-text-color: #fff;
    --f7-block-header-text-color: rgba(255,255,255,0.55);
    --f7-block-footer-text-color: rgba(255,255,255,0.55);
    --f7-block-strong-text-color: #fff;
    --f7-block-strong-bg-color: #1c1c1d;
    --f7-block-title-medium-text-color: #fff;
    --f7-block-title-large-text-color: #fff;
  });
});
.md-vars({
  --f7-block-margin-vertical: 32px;
  --f7-block-title-font-size: inherit;
  --f7-block-title-font-weight: 500;
  --f7-block-title-line-height: 16px;
  --f7-block-title-margin-bottom: 16px;
  --f7-block-title-medium-font-size: 16px;
  --f7-block-title-medium-font-weight: 500;
  --f7-block-title-medium-line-height: 1.3;
  --f7-block-title-large-font-size: 22px;
  --f7-block-title-large-font-weight: 500;
  --f7-block-title-large-line-height: 1.2;
  --f7-block-inset-border-radius: 16px;
});

.md-color-vars({
  --f7-block-text-color: var(--f7-md-on-surface);
  --f7-block-strong-text-color: var(--f7-md-on-surface);
  --f7-block-outline-border-color: var(--f7-md-outline);
  --f7-block-title-text-color: var(--f7-theme-color);
  --f7-block-title-medium-text-color: var(--f7-theme-color);
  --f7-block-title-large-text-color: var(--f7-theme-color);
  --f7-block-strong-bg-color: var(--f7-md-surface-1);
  --f7-block-header-text-color: var(--f7-md-on-surface-variant);
  --f7-block-footer-text-color: var(--f7-md-on-surface-variant);
});
