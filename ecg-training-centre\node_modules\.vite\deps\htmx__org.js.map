{"version": 3, "sources": ["../../htmx.org/dist/htmx.esm.js"], "sourcesContent": ["var htmx = (function() {\n  'use strict'\n\n  // Public API\n  const htmx = {\n    // Tsc madness here, assigning the functions directly results in an invalid TypeScript output, but reassigning is fine\n    /* Event processing */\n    /** @type {typeof onLoadHelper} */\n    onLoad: null,\n    /** @type {typeof processNode} */\n    process: null,\n    /** @type {typeof addEventListenerImpl} */\n    on: null,\n    /** @type {typeof removeEventListenerImpl} */\n    off: null,\n    /** @type {typeof triggerEvent} */\n    trigger: null,\n    /** @type {typeof ajaxHelper} */\n    ajax: null,\n    /* DOM querying helpers */\n    /** @type {typeof find} */\n    find: null,\n    /** @type {typeof findAll} */\n    findAll: null,\n    /** @type {typeof closest} */\n    closest: null,\n    /**\n     * Returns the input values that would resolve for a given element via the htmx value resolution mechanism\n     *\n     * @see https://htmx.org/api/#values\n     *\n     * @param {Element} elt the element to resolve values on\n     * @param {HttpVerb} type the request type (e.g. **get** or **post**) non-GET's will include the enclosing form of the element. Defaults to **post**\n     * @returns {Object}\n     */\n    values: function(elt, type) {\n      const inputValues = getInputValues(elt, type || 'post')\n      return inputValues.values\n    },\n    /* DOM manipulation helpers */\n    /** @type {typeof removeElement} */\n    remove: null,\n    /** @type {typeof addClassToElement} */\n    addClass: null,\n    /** @type {typeof removeClassFromElement} */\n    removeClass: null,\n    /** @type {typeof toggleClassOnElement} */\n    toggleClass: null,\n    /** @type {typeof takeClassForElement} */\n    takeClass: null,\n    /** @type {typeof swap} */\n    swap: null,\n    /* Extension entrypoints */\n    /** @type {typeof defineExtension} */\n    defineExtension: null,\n    /** @type {typeof removeExtension} */\n    removeExtension: null,\n    /* Debugging */\n    /** @type {typeof logAll} */\n    logAll: null,\n    /** @type {typeof logNone} */\n    logNone: null,\n    /* Debugging */\n    /**\n     * The logger htmx uses to log with\n     *\n     * @see https://htmx.org/api/#logger\n     */\n    logger: null,\n    /**\n     * A property holding the configuration htmx uses at runtime.\n     *\n     * Note that using a [meta tag](https://htmx.org/docs/#config) is the preferred mechanism for setting these properties.\n     *\n     * @see https://htmx.org/api/#config\n     */\n    config: {\n      /**\n       * Whether to use history.\n       * @type boolean\n       * @default true\n       */\n      historyEnabled: true,\n      /**\n       * The number of pages to keep in **sessionStorage** for history support.\n       * @type number\n       * @default 10\n       */\n      historyCacheSize: 10,\n      /**\n       * @type boolean\n       * @default false\n       */\n      refreshOnHistoryMiss: false,\n      /**\n       * The default swap style to use if **[hx-swap](https://htmx.org/attributes/hx-swap)** is omitted.\n       * @type HtmxSwapStyle\n       * @default 'innerHTML'\n       */\n      defaultSwapStyle: 'innerHTML',\n      /**\n       * The default delay between receiving a response from the server and doing the swap.\n       * @type number\n       * @default 0\n       */\n      defaultSwapDelay: 0,\n      /**\n       * The default delay between completing the content swap and settling attributes.\n       * @type number\n       * @default 20\n       */\n      defaultSettleDelay: 20,\n      /**\n       * If true, htmx will inject a small amount of CSS into the page to make indicators invisible unless the **htmx-indicator** class is present.\n       * @type boolean\n       * @default true\n       */\n      includeIndicatorStyles: true,\n      /**\n       * The class to place on indicators when a request is in flight.\n       * @type string\n       * @default 'htmx-indicator'\n       */\n      indicatorClass: 'htmx-indicator',\n      /**\n       * The class to place on triggering elements when a request is in flight.\n       * @type string\n       * @default 'htmx-request'\n       */\n      requestClass: 'htmx-request',\n      /**\n       * The class to temporarily place on elements that htmx has added to the DOM.\n       * @type string\n       * @default 'htmx-added'\n       */\n      addedClass: 'htmx-added',\n      /**\n       * The class to place on target elements when htmx is in the settling phase.\n       * @type string\n       * @default 'htmx-settling'\n       */\n      settlingClass: 'htmx-settling',\n      /**\n       * The class to place on target elements when htmx is in the swapping phase.\n       * @type string\n       * @default 'htmx-swapping'\n       */\n      swappingClass: 'htmx-swapping',\n      /**\n       * Allows the use of eval-like functionality in htmx, to enable **hx-vars**, trigger conditions & script tag evaluation. Can be set to **false** for CSP compatibility.\n       * @type boolean\n       * @default true\n       */\n      allowEval: true,\n      /**\n       * If set to false, disables the interpretation of script tags.\n       * @type boolean\n       * @default true\n       */\n      allowScriptTags: true,\n      /**\n       * If set, the nonce will be added to inline scripts.\n       * @type string\n       * @default ''\n       */\n      inlineScriptNonce: '',\n      /**\n       * If set, the nonce will be added to inline styles.\n       * @type string\n       * @default ''\n       */\n      inlineStyleNonce: '',\n      /**\n       * The attributes to settle during the settling phase.\n       * @type string[]\n       * @default ['class', 'style', 'width', 'height']\n       */\n      attributesToSettle: ['class', 'style', 'width', 'height'],\n      /**\n       * Allow cross-site Access-Control requests using credentials such as cookies, authorization headers or TLS client certificates.\n       * @type boolean\n       * @default false\n       */\n      withCredentials: false,\n      /**\n       * @type number\n       * @default 0\n       */\n      timeout: 0,\n      /**\n       * The default implementation of **getWebSocketReconnectDelay** for reconnecting after unexpected connection loss by the event code **Abnormal Closure**, **Service Restart** or **Try Again Later**.\n       * @type {'full-jitter' | ((retryCount:number) => number)}\n       * @default \"full-jitter\"\n       */\n      wsReconnectDelay: 'full-jitter',\n      /**\n       * The type of binary data being received over the WebSocket connection\n       * @type BinaryType\n       * @default 'blob'\n       */\n      wsBinaryType: 'blob',\n      /**\n       * @type string\n       * @default '[hx-disable], [data-hx-disable]'\n       */\n      disableSelector: '[hx-disable], [data-hx-disable]',\n      /**\n       * @type {'auto' | 'instant' | 'smooth'}\n       * @default 'instant'\n       */\n      scrollBehavior: 'instant',\n      /**\n       * If the focused element should be scrolled into view.\n       * @type boolean\n       * @default false\n       */\n      defaultFocusScroll: false,\n      /**\n       * If set to true htmx will include a cache-busting parameter in GET requests to avoid caching partial responses by the browser\n       * @type boolean\n       * @default false\n       */\n      getCacheBusterParam: false,\n      /**\n       * If set to true, htmx will use the View Transition API when swapping in new content.\n       * @type boolean\n       * @default false\n       */\n      globalViewTransitions: false,\n      /**\n       * htmx will format requests with these methods by encoding their parameters in the URL, not the request body\n       * @type {(HttpVerb)[]}\n       * @default ['get', 'delete']\n       */\n      methodsThatUseUrlParams: ['get', 'delete'],\n      /**\n       * If set to true, disables htmx-based requests to non-origin hosts.\n       * @type boolean\n       * @default false\n       */\n      selfRequestsOnly: true,\n      /**\n       * If set to true htmx will not update the title of the document when a title tag is found in new content\n       * @type boolean\n       * @default false\n       */\n      ignoreTitle: false,\n      /**\n       * Whether the target of a boosted element is scrolled into the viewport.\n       * @type boolean\n       * @default true\n       */\n      scrollIntoViewOnBoost: true,\n      /**\n       * The cache to store evaluated trigger specifications into.\n       * You may define a simple object to use a never-clearing cache, or implement your own system using a [proxy object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Proxy)\n       * @type {Object|null}\n       * @default null\n       */\n      triggerSpecsCache: null,\n      /** @type boolean */\n      disableInheritance: false,\n      /** @type HtmxResponseHandlingConfig[] */\n      responseHandling: [\n        { code: '204', swap: false },\n        { code: '[23]..', swap: true },\n        { code: '[45]..', swap: false, error: true }\n      ],\n      /**\n       * Whether to process OOB swaps on elements that are nested within the main response element.\n       * @type boolean\n       * @default true\n       */\n      allowNestedOobSwaps: true,\n      /**\n       * Whether to treat history cache miss full page reload requests as a \"HX-Request\" by returning this response header\n       * This should always be disabled when using HX-Request header to optionally return partial responses\n       * @type boolean\n       * @default true\n       */\n      historyRestoreAsHxRequest: true\n    },\n    /** @type {typeof parseInterval} */\n    parseInterval: null,\n    /**\n     * proxy of window.location used for page reload functions\n     * @type location\n     */\n    location,\n    /** @type {typeof internalEval} */\n    _: null,\n    version: '2.0.6'\n  }\n  // Tsc madness part 2\n  htmx.onLoad = onLoadHelper\n  htmx.process = processNode\n  htmx.on = addEventListenerImpl\n  htmx.off = removeEventListenerImpl\n  htmx.trigger = triggerEvent\n  htmx.ajax = ajaxHelper\n  htmx.find = find\n  htmx.findAll = findAll\n  htmx.closest = closest\n  htmx.remove = removeElement\n  htmx.addClass = addClassToElement\n  htmx.removeClass = removeClassFromElement\n  htmx.toggleClass = toggleClassOnElement\n  htmx.takeClass = takeClassForElement\n  htmx.swap = swap\n  htmx.defineExtension = defineExtension\n  htmx.removeExtension = removeExtension\n  htmx.logAll = logAll\n  htmx.logNone = logNone\n  htmx.parseInterval = parseInterval\n  htmx._ = internalEval\n\n  const internalAPI = {\n    addTriggerHandler,\n    bodyContains,\n    canAccessLocalStorage,\n    findThisElement,\n    filterValues,\n    swap,\n    hasAttribute,\n    getAttributeValue,\n    getClosestAttributeValue,\n    getClosestMatch,\n    getExpressionVars,\n    getHeaders,\n    getInputValues,\n    getInternalData,\n    getSwapSpecification,\n    getTriggerSpecs,\n    getTarget,\n    makeFragment,\n    mergeObjects,\n    makeSettleInfo,\n    oobSwap,\n    querySelectorExt,\n    settleImmediately,\n    shouldCancel,\n    triggerEvent,\n    triggerErrorEvent,\n    withExtensions\n  }\n\n  const VERBS = ['get', 'post', 'put', 'delete', 'patch']\n  const VERB_SELECTOR = VERBS.map(function(verb) {\n    return '[hx-' + verb + '], [data-hx-' + verb + ']'\n  }).join(', ')\n\n  //= ===================================================================\n  // Utilities\n  //= ===================================================================\n\n  /**\n   * Parses an interval string consistent with the way htmx does. Useful for plugins that have timing-related attributes.\n   *\n   * Caution: Accepts an int followed by either **s** or **ms**. All other values use **parseFloat**\n   *\n   * @see https://htmx.org/api/#parseInterval\n   *\n   * @param {string} str timing string\n   * @returns {number|undefined}\n   */\n  function parseInterval(str) {\n    if (str == undefined) {\n      return undefined\n    }\n\n    let interval = NaN\n    if (str.slice(-2) == 'ms') {\n      interval = parseFloat(str.slice(0, -2))\n    } else if (str.slice(-1) == 's') {\n      interval = parseFloat(str.slice(0, -1)) * 1000\n    } else if (str.slice(-1) == 'm') {\n      interval = parseFloat(str.slice(0, -1)) * 1000 * 60\n    } else {\n      interval = parseFloat(str)\n    }\n    return isNaN(interval) ? undefined : interval\n  }\n\n  /**\n   * @param {Node} elt\n   * @param {string} name\n   * @returns {(string | null)}\n   */\n  function getRawAttribute(elt, name) {\n    return elt instanceof Element && elt.getAttribute(name)\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {string} qualifiedName\n   * @returns {boolean}\n   */\n  // resolve with both hx and data-hx prefixes\n  function hasAttribute(elt, qualifiedName) {\n    return !!elt.hasAttribute && (elt.hasAttribute(qualifiedName) ||\n      elt.hasAttribute('data-' + qualifiedName))\n  }\n\n  /**\n   *\n   * @param {Node} elt\n   * @param {string} qualifiedName\n   * @returns {(string | null)}\n   */\n  function getAttributeValue(elt, qualifiedName) {\n    return getRawAttribute(elt, qualifiedName) || getRawAttribute(elt, 'data-' + qualifiedName)\n  }\n\n  /**\n   * @param {Node} elt\n   * @returns {Node | null}\n   */\n  function parentElt(elt) {\n    const parent = elt.parentElement\n    if (!parent && elt.parentNode instanceof ShadowRoot) return elt.parentNode\n    return parent\n  }\n\n  /**\n   * @returns {Document}\n   */\n  function getDocument() {\n    return document\n  }\n\n  /**\n   * @param {Node} elt\n   * @param {boolean} global\n   * @returns {Node|Document}\n   */\n  function getRootNode(elt, global) {\n    return elt.getRootNode ? elt.getRootNode({ composed: global }) : getDocument()\n  }\n\n  /**\n   * @param {Node} elt\n   * @param {(e:Node) => boolean} condition\n   * @returns {Node | null}\n   */\n  function getClosestMatch(elt, condition) {\n    while (elt && !condition(elt)) {\n      elt = parentElt(elt)\n    }\n\n    return elt || null\n  }\n\n  /**\n   * @param {Element} initialElement\n   * @param {Element} ancestor\n   * @param {string} attributeName\n   * @returns {string|null}\n   */\n  function getAttributeValueWithDisinheritance(initialElement, ancestor, attributeName) {\n    const attributeValue = getAttributeValue(ancestor, attributeName)\n    const disinherit = getAttributeValue(ancestor, 'hx-disinherit')\n    var inherit = getAttributeValue(ancestor, 'hx-inherit')\n    if (initialElement !== ancestor) {\n      if (htmx.config.disableInheritance) {\n        if (inherit && (inherit === '*' || inherit.split(' ').indexOf(attributeName) >= 0)) {\n          return attributeValue\n        } else {\n          return null\n        }\n      }\n      if (disinherit && (disinherit === '*' || disinherit.split(' ').indexOf(attributeName) >= 0)) {\n        return 'unset'\n      }\n    }\n    return attributeValue\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {string} attributeName\n   * @returns {string | null}\n   */\n  function getClosestAttributeValue(elt, attributeName) {\n    let closestAttr = null\n    getClosestMatch(elt, function(e) {\n      return !!(closestAttr = getAttributeValueWithDisinheritance(elt, asElement(e), attributeName))\n    })\n    if (closestAttr !== 'unset') {\n      return closestAttr\n    }\n  }\n\n  /**\n   * @param {Node} elt\n   * @param {string} selector\n   * @returns {boolean}\n   */\n  function matches(elt, selector) {\n    return elt instanceof Element && elt.matches(selector)\n  }\n\n  /**\n   * @param {string} str\n   * @returns {string}\n   */\n  function getStartTag(str) {\n    const tagMatcher = /<([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)/i\n    const match = tagMatcher.exec(str)\n    if (match) {\n      return match[1].toLowerCase()\n    } else {\n      return ''\n    }\n  }\n\n  /**\n   * @param {string} resp\n   * @returns {Document}\n   */\n  function parseHTML(resp) {\n    const parser = new DOMParser()\n    return parser.parseFromString(resp, 'text/html')\n  }\n\n  /**\n   * @param {DocumentFragment} fragment\n   * @param {Node} elt\n   */\n  function takeChildrenFor(fragment, elt) {\n    while (elt.childNodes.length > 0) {\n      fragment.append(elt.childNodes[0])\n    }\n  }\n\n  /**\n   * @param {HTMLScriptElement} script\n   * @returns {HTMLScriptElement}\n   */\n  function duplicateScript(script) {\n    const newScript = getDocument().createElement('script')\n    forEach(script.attributes, function(attr) {\n      newScript.setAttribute(attr.name, attr.value)\n    })\n    newScript.textContent = script.textContent\n    newScript.async = false\n    if (htmx.config.inlineScriptNonce) {\n      newScript.nonce = htmx.config.inlineScriptNonce\n    }\n    return newScript\n  }\n\n  /**\n   * @param {HTMLScriptElement} script\n   * @returns {boolean}\n   */\n  function isJavaScriptScriptNode(script) {\n    return script.matches('script') && (script.type === 'text/javascript' || script.type === 'module' || script.type === '')\n  }\n\n  /**\n   * we have to make new copies of script tags that we are going to insert because\n   * SOME browsers (not saying who, but it involves an element and an animal) don't\n   * execute scripts created in <template> tags when they are inserted into the DOM\n   * and all the others do lmao\n   * @param {DocumentFragment} fragment\n   */\n  function normalizeScriptTags(fragment) {\n    Array.from(fragment.querySelectorAll('script')).forEach(/** @param {HTMLScriptElement} script */ (script) => {\n      if (isJavaScriptScriptNode(script)) {\n        const newScript = duplicateScript(script)\n        const parent = script.parentNode\n        try {\n          parent.insertBefore(newScript, script)\n        } catch (e) {\n          logError(e)\n        } finally {\n          script.remove()\n        }\n      }\n    })\n  }\n\n  /**\n   * @typedef {DocumentFragment & {title?: string}} DocumentFragmentWithTitle\n   * @description  a document fragment representing the response HTML, including\n   * a `title` property for any title information found\n   */\n\n  /**\n   * @param {string} response HTML\n   * @returns {DocumentFragmentWithTitle}\n   */\n  function makeFragment(response) {\n    // strip head tag to determine shape of response we are dealing with\n    const responseWithNoHead = response.replace(/<head(\\s[^>]*)?>[\\s\\S]*?<\\/head>/i, '')\n    const startTag = getStartTag(responseWithNoHead)\n    /** @type DocumentFragmentWithTitle */\n    let fragment\n    if (startTag === 'html') {\n      // if it is a full document, parse it and return the body\n      fragment = /** @type DocumentFragmentWithTitle */ (new DocumentFragment())\n      const doc = parseHTML(response)\n      takeChildrenFor(fragment, doc.body)\n      fragment.title = doc.title\n    } else if (startTag === 'body') {\n      // parse body w/o wrapping in template\n      fragment = /** @type DocumentFragmentWithTitle */ (new DocumentFragment())\n      const doc = parseHTML(responseWithNoHead)\n      takeChildrenFor(fragment, doc.body)\n      fragment.title = doc.title\n    } else {\n      // otherwise we have non-body partial HTML content, so wrap it in a template to maximize parsing flexibility\n      const doc = parseHTML('<body><template class=\"internal-htmx-wrapper\">' + responseWithNoHead + '</template></body>')\n      fragment = /** @type DocumentFragmentWithTitle */ (doc.querySelector('template').content)\n      // extract title into fragment for later processing\n      fragment.title = doc.title\n\n      // for legacy reasons we support a title tag at the root level of non-body responses, so we need to handle it\n      var titleElement = fragment.querySelector('title')\n      if (titleElement && titleElement.parentNode === fragment) {\n        titleElement.remove()\n        fragment.title = titleElement.innerText\n      }\n    }\n    if (fragment) {\n      if (htmx.config.allowScriptTags) {\n        normalizeScriptTags(fragment)\n      } else {\n        // remove all script tags if scripts are disabled\n        fragment.querySelectorAll('script').forEach((script) => script.remove())\n      }\n    }\n    return fragment\n  }\n\n  /**\n   * @param {Function} func\n   */\n  function maybeCall(func) {\n    if (func) {\n      func()\n    }\n  }\n\n  /**\n   * @param {any} o\n   * @param {string} type\n   * @returns\n   */\n  function isType(o, type) {\n    return Object.prototype.toString.call(o) === '[object ' + type + ']'\n  }\n\n  /**\n   * @param {*} o\n   * @returns {o is Function}\n   */\n  function isFunction(o) {\n    return typeof o === 'function'\n  }\n\n  /**\n   * @param {*} o\n   * @returns {o is Object}\n   */\n  function isRawObject(o) {\n    return isType(o, 'Object')\n  }\n\n  /**\n   * @typedef {Object} OnHandler\n   * @property {(keyof HTMLElementEventMap)|string} event\n   * @property {EventListener} listener\n   */\n\n  /**\n   * @typedef {Object} ListenerInfo\n   * @property {string} trigger\n   * @property {EventListener} listener\n   * @property {EventTarget} on\n   */\n\n  /**\n   * @typedef {Object} HtmxNodeInternalData\n   * Element data\n   * @property {number} [initHash]\n   * @property {boolean} [boosted]\n   * @property {OnHandler[]} [onHandlers]\n   * @property {number} [timeout]\n   * @property {ListenerInfo[]} [listenerInfos]\n   * @property {boolean} [cancelled]\n   * @property {boolean} [triggeredOnce]\n   * @property {number} [delayed]\n   * @property {number|null} [throttle]\n   * @property {WeakMap<HtmxTriggerSpecification,WeakMap<EventTarget,string>>} [lastValue]\n   * @property {boolean} [loaded]\n   * @property {string} [path]\n   * @property {string} [verb]\n   * @property {boolean} [polling]\n   * @property {HTMLButtonElement|HTMLInputElement|null} [lastButtonClicked]\n   * @property {number} [requestCount]\n   * @property {XMLHttpRequest} [xhr]\n   * @property {(() => void)[]} [queuedRequests]\n   * @property {boolean} [abortable]\n   * @property {boolean} [firstInitCompleted]\n   *\n   * Event data\n   * @property {HtmxTriggerSpecification} [triggerSpec]\n   * @property {EventTarget[]} [handledFor]\n   */\n\n  /**\n   * getInternalData retrieves \"private\" data stored by htmx within an element\n   * @param {EventTarget|Event} elt\n   * @returns {HtmxNodeInternalData}\n   */\n  function getInternalData(elt) {\n    const dataProp = 'htmx-internal-data'\n    let data = elt[dataProp]\n    if (!data) {\n      data = elt[dataProp] = {}\n    }\n    return data\n  }\n\n  /**\n   * toArray converts an ArrayLike object into a real array.\n   * @template T\n   * @param {ArrayLike<T>} arr\n   * @returns {T[]}\n   */\n  function toArray(arr) {\n    const returnArr = []\n    if (arr) {\n      for (let i = 0; i < arr.length; i++) {\n        returnArr.push(arr[i])\n      }\n    }\n    return returnArr\n  }\n\n  /**\n   * @template T\n   * @param {T[]|NamedNodeMap|HTMLCollection|HTMLFormControlsCollection|ArrayLike<T>} arr\n   * @param {(T) => void} func\n   */\n  function forEach(arr, func) {\n    if (arr) {\n      for (let i = 0; i < arr.length; i++) {\n        func(arr[i])\n      }\n    }\n  }\n\n  /**\n   * @param {Element} el\n   * @returns {boolean}\n   */\n  function isScrolledIntoView(el) {\n    const rect = el.getBoundingClientRect()\n    const elemTop = rect.top\n    const elemBottom = rect.bottom\n    return elemTop < window.innerHeight && elemBottom >= 0\n  }\n\n  /**\n   * Checks whether the element is in the document (includes shadow roots).\n   * This function this is a slight misnomer; it will return true even for elements in the head.\n   *\n   * @param {Node} elt\n   * @returns {boolean}\n   */\n  function bodyContains(elt) {\n    return elt.getRootNode({ composed: true }) === document\n  }\n\n  /**\n   * @param {string} trigger\n   * @returns {string[]}\n   */\n  function splitOnWhitespace(trigger) {\n    return trigger.trim().split(/\\s+/)\n  }\n\n  /**\n   * mergeObjects takes all the keys from\n   * obj2 and duplicates them into obj1\n   * @template T1\n   * @template T2\n   * @param {T1} obj1\n   * @param {T2} obj2\n   * @returns {T1 & T2}\n   */\n  function mergeObjects(obj1, obj2) {\n    for (const key in obj2) {\n      if (obj2.hasOwnProperty(key)) {\n        // @ts-ignore tsc doesn't seem to properly handle types merging\n        obj1[key] = obj2[key]\n      }\n    }\n    // @ts-ignore tsc doesn't seem to properly handle types merging\n    return obj1\n  }\n\n  /**\n   * @param {string} jString\n   * @returns {any|null}\n   */\n  function parseJSON(jString) {\n    try {\n      return JSON.parse(jString)\n    } catch (error) {\n      logError(error)\n      return null\n    }\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  function canAccessLocalStorage() {\n    const test = 'htmx:sessionStorageTest'\n    try {\n      sessionStorage.setItem(test, test)\n      sessionStorage.removeItem(test)\n      return true\n    } catch (e) {\n      return false\n    }\n  }\n\n  /**\n   * @param {string} path\n   * @returns {string}\n   */\n  function normalizePath(path) {\n    // use dummy base URL to allow normalize on path only\n    const url = new URL(path, 'http://x')\n    if (url) {\n      path = url.pathname + url.search\n    }\n    // remove trailing slash, unless index page\n    if (path != '/') {\n      path = path.replace(/\\/+$/, '')\n    }\n    return path\n  }\n\n  //= =========================================================================================\n  // public API\n  //= =========================================================================================\n\n  /**\n   * @param {string} str\n   * @returns {any}\n   */\n  function internalEval(str) {\n    return maybeEval(getDocument().body, function() {\n      return eval(str)\n    })\n  }\n\n  /**\n   * Adds a callback for the **htmx:load** event. This can be used to process new content, for example initializing the content with a javascript library\n   *\n   * @see https://htmx.org/api/#onLoad\n   *\n   * @param {(elt: Node) => void} callback the callback to call on newly loaded content\n   * @returns {EventListener}\n   */\n  function onLoadHelper(callback) {\n    const value = htmx.on('htmx:load', /** @param {CustomEvent} evt */ function(evt) {\n      callback(evt.detail.elt)\n    })\n    return value\n  }\n\n  /**\n   * Log all htmx events, useful for debugging.\n   *\n   * @see https://htmx.org/api/#logAll\n   */\n  function logAll() {\n    htmx.logger = function(elt, event, data) {\n      if (console) {\n        console.log(event, elt, data)\n      }\n    }\n  }\n\n  function logNone() {\n    htmx.logger = null\n  }\n\n  /**\n   * Finds an element matching the selector\n   *\n   * @see https://htmx.org/api/#find\n   *\n   * @param {ParentNode|string} eltOrSelector  the root element to find the matching element in, inclusive | the selector to match\n   * @param {string} [selector] the selector to match\n   * @returns {Element|null}\n   */\n  function find(eltOrSelector, selector) {\n    if (typeof eltOrSelector !== 'string') {\n      return eltOrSelector.querySelector(selector)\n    } else {\n      return find(getDocument(), eltOrSelector)\n    }\n  }\n\n  /**\n   * Finds all elements matching the selector\n   *\n   * @see https://htmx.org/api/#findAll\n   *\n   * @param {ParentNode|string} eltOrSelector the root element to find the matching elements in, inclusive | the selector to match\n   * @param {string} [selector] the selector to match\n   * @returns {NodeListOf<Element>}\n   */\n  function findAll(eltOrSelector, selector) {\n    if (typeof eltOrSelector !== 'string') {\n      return eltOrSelector.querySelectorAll(selector)\n    } else {\n      return findAll(getDocument(), eltOrSelector)\n    }\n  }\n\n  /**\n   * @returns Window\n   */\n  function getWindow() {\n    return window\n  }\n\n  /**\n   * Removes an element from the DOM\n   *\n   * @see https://htmx.org/api/#remove\n   *\n   * @param {Node} elt\n   * @param {number} [delay]\n   */\n  function removeElement(elt, delay) {\n    elt = resolveTarget(elt)\n    if (delay) {\n      getWindow().setTimeout(function() {\n        removeElement(elt)\n        elt = null\n      }, delay)\n    } else {\n      parentElt(elt).removeChild(elt)\n    }\n  }\n\n  /**\n   * @param {any} elt\n   * @return {Element|null}\n   */\n  function asElement(elt) {\n    return elt instanceof Element ? elt : null\n  }\n\n  /**\n   * @param {any} elt\n   * @return {HTMLElement|null}\n   */\n  function asHtmlElement(elt) {\n    return elt instanceof HTMLElement ? elt : null\n  }\n\n  /**\n   * @param {any} value\n   * @return {string|null}\n   */\n  function asString(value) {\n    return typeof value === 'string' ? value : null\n  }\n\n  /**\n   * @param {EventTarget} elt\n   * @return {ParentNode|null}\n   */\n  function asParentNode(elt) {\n    return elt instanceof Element || elt instanceof Document || elt instanceof DocumentFragment ? elt : null\n  }\n\n  /**\n   * This method adds a class to the given element.\n   *\n   * @see https://htmx.org/api/#addClass\n   *\n   * @param {Element|string} elt the element to add the class to\n   * @param {string} clazz the class to add\n   * @param {number} [delay] the delay (in milliseconds) before class is added\n   */\n  function addClassToElement(elt, clazz, delay) {\n    elt = asElement(resolveTarget(elt))\n    if (!elt) {\n      return\n    }\n    if (delay) {\n      getWindow().setTimeout(function() {\n        addClassToElement(elt, clazz)\n        elt = null\n      }, delay)\n    } else {\n      elt.classList && elt.classList.add(clazz)\n    }\n  }\n\n  /**\n   * Removes a class from the given element\n   *\n   * @see https://htmx.org/api/#removeClass\n   *\n   * @param {Node|string} node element to remove the class from\n   * @param {string} clazz the class to remove\n   * @param {number} [delay] the delay (in milliseconds before class is removed)\n   */\n  function removeClassFromElement(node, clazz, delay) {\n    let elt = asElement(resolveTarget(node))\n    if (!elt) {\n      return\n    }\n    if (delay) {\n      getWindow().setTimeout(function() {\n        removeClassFromElement(elt, clazz)\n        elt = null\n      }, delay)\n    } else {\n      if (elt.classList) {\n        elt.classList.remove(clazz)\n        // if there are no classes left, remove the class attribute\n        if (elt.classList.length === 0) {\n          elt.removeAttribute('class')\n        }\n      }\n    }\n  }\n\n  /**\n   * Toggles the given class on an element\n   *\n   * @see https://htmx.org/api/#toggleClass\n   *\n   * @param {Element|string} elt the element to toggle the class on\n   * @param {string} clazz the class to toggle\n   */\n  function toggleClassOnElement(elt, clazz) {\n    elt = resolveTarget(elt)\n    elt.classList.toggle(clazz)\n  }\n\n  /**\n   * Takes the given class from its siblings, so that among its siblings, only the given element will have the class.\n   *\n   * @see https://htmx.org/api/#takeClass\n   *\n   * @param {Node|string} elt the element that will take the class\n   * @param {string} clazz the class to take\n   */\n  function takeClassForElement(elt, clazz) {\n    elt = resolveTarget(elt)\n    forEach(elt.parentElement.children, function(child) {\n      removeClassFromElement(child, clazz)\n    })\n    addClassToElement(asElement(elt), clazz)\n  }\n\n  /**\n   * Finds the closest matching element in the given elements parentage, inclusive of the element\n   *\n   * @see https://htmx.org/api/#closest\n   *\n   * @param {Element|string} elt the element to find the selector from\n   * @param {string} selector the selector to find\n   * @returns {Element|null}\n   */\n  function closest(elt, selector) {\n    elt = asElement(resolveTarget(elt))\n    if (elt) {\n      return elt.closest(selector)\n    }\n    return null\n  }\n\n  /**\n   * @param {string} str\n   * @param {string} prefix\n   * @returns {boolean}\n   */\n  function startsWith(str, prefix) {\n    return str.substring(0, prefix.length) === prefix\n  }\n\n  /**\n   * @param {string} str\n   * @param {string} suffix\n   * @returns {boolean}\n   */\n  function endsWith(str, suffix) {\n    return str.substring(str.length - suffix.length) === suffix\n  }\n\n  /**\n   * @param {string} selector\n   * @returns {string}\n   */\n  function normalizeSelector(selector) {\n    const trimmedSelector = selector.trim()\n    if (startsWith(trimmedSelector, '<') && endsWith(trimmedSelector, '/>')) {\n      return trimmedSelector.substring(1, trimmedSelector.length - 2)\n    } else {\n      return trimmedSelector\n    }\n  }\n\n  /**\n   * @param {Node|Element|Document|string} elt\n   * @param {string} selector\n   * @param {boolean=} global\n   * @returns {(Node|Window)[]}\n   */\n  function querySelectorAllExt(elt, selector, global) {\n    if (selector.indexOf('global ') === 0) {\n      return querySelectorAllExt(elt, selector.slice(7), true)\n    }\n\n    elt = resolveTarget(elt)\n\n    const parts = []\n    {\n      let chevronsCount = 0\n      let offset = 0\n      for (let i = 0; i < selector.length; i++) {\n        const char = selector[i]\n        if (char === ',' && chevronsCount === 0) {\n          parts.push(selector.substring(offset, i))\n          offset = i + 1\n          continue\n        }\n        if (char === '<') {\n          chevronsCount++\n        } else if (char === '/' && i < selector.length - 1 && selector[i + 1] === '>') {\n          chevronsCount--\n        }\n      }\n      if (offset < selector.length) {\n        parts.push(selector.substring(offset))\n      }\n    }\n\n    const result = []\n    const unprocessedParts = []\n    while (parts.length > 0) {\n      const selector = normalizeSelector(parts.shift())\n      let item\n      if (selector.indexOf('closest ') === 0) {\n        item = closest(asElement(elt), normalizeSelector(selector.slice(8)))\n      } else if (selector.indexOf('find ') === 0) {\n        item = find(asParentNode(elt), normalizeSelector(selector.slice(5)))\n      } else if (selector === 'next' || selector === 'nextElementSibling') {\n        item = asElement(elt).nextElementSibling\n      } else if (selector.indexOf('next ') === 0) {\n        item = scanForwardQuery(elt, normalizeSelector(selector.slice(5)), !!global)\n      } else if (selector === 'previous' || selector === 'previousElementSibling') {\n        item = asElement(elt).previousElementSibling\n      } else if (selector.indexOf('previous ') === 0) {\n        item = scanBackwardsQuery(elt, normalizeSelector(selector.slice(9)), !!global)\n      } else if (selector === 'document') {\n        item = document\n      } else if (selector === 'window') {\n        item = window\n      } else if (selector === 'body') {\n        item = document.body\n      } else if (selector === 'root') {\n        item = getRootNode(elt, !!global)\n      } else if (selector === 'host') {\n        item = (/** @type ShadowRoot */(elt.getRootNode())).host\n      } else {\n        unprocessedParts.push(selector)\n      }\n\n      if (item) {\n        result.push(item)\n      }\n    }\n\n    if (unprocessedParts.length > 0) {\n      const standardSelector = unprocessedParts.join(',')\n      const rootNode = asParentNode(getRootNode(elt, !!global))\n      result.push(...toArray(rootNode.querySelectorAll(standardSelector)))\n    }\n\n    return result\n  }\n\n  /**\n   * @param {Node} start\n   * @param {string} match\n   * @param {boolean} global\n   * @returns {Element}\n   */\n  var scanForwardQuery = function(start, match, global) {\n    const results = asParentNode(getRootNode(start, global)).querySelectorAll(match)\n    for (let i = 0; i < results.length; i++) {\n      const elt = results[i]\n      if (elt.compareDocumentPosition(start) === Node.DOCUMENT_POSITION_PRECEDING) {\n        return elt\n      }\n    }\n  }\n\n  /**\n   * @param {Node} start\n   * @param {string} match\n   * @param {boolean} global\n   * @returns {Element}\n   */\n  var scanBackwardsQuery = function(start, match, global) {\n    const results = asParentNode(getRootNode(start, global)).querySelectorAll(match)\n    for (let i = results.length - 1; i >= 0; i--) {\n      const elt = results[i]\n      if (elt.compareDocumentPosition(start) === Node.DOCUMENT_POSITION_FOLLOWING) {\n        return elt\n      }\n    }\n  }\n\n  /**\n   * @param {Node|string} eltOrSelector\n   * @param {string=} selector\n   * @returns {Node|Window}\n   */\n  function querySelectorExt(eltOrSelector, selector) {\n    if (typeof eltOrSelector !== 'string') {\n      return querySelectorAllExt(eltOrSelector, selector)[0]\n    } else {\n      return querySelectorAllExt(getDocument().body, eltOrSelector)[0]\n    }\n  }\n\n  /**\n   * @template {EventTarget} T\n   * @param {T|string} eltOrSelector\n   * @param {T} [context]\n   * @returns {Element|T|null}\n   */\n  function resolveTarget(eltOrSelector, context) {\n    if (typeof eltOrSelector === 'string') {\n      return find(asParentNode(context) || document, eltOrSelector)\n    } else {\n      return eltOrSelector\n    }\n  }\n\n  /**\n   * @typedef {keyof HTMLElementEventMap|string} AnyEventName\n   */\n\n  /**\n   * @typedef {Object} EventArgs\n   * @property {EventTarget} target\n   * @property {AnyEventName} event\n   * @property {EventListener} listener\n   * @property {Object|boolean} options\n   */\n\n  /**\n   * @param {EventTarget|AnyEventName} arg1\n   * @param {AnyEventName|EventListener} arg2\n   * @param {EventListener|Object|boolean} [arg3]\n   * @param {Object|boolean} [arg4]\n   * @returns {EventArgs}\n   */\n  function processEventArgs(arg1, arg2, arg3, arg4) {\n    if (isFunction(arg2)) {\n      return {\n        target: getDocument().body,\n        event: asString(arg1),\n        listener: arg2,\n        options: arg3\n      }\n    } else {\n      return {\n        target: resolveTarget(arg1),\n        event: asString(arg2),\n        listener: arg3,\n        options: arg4\n      }\n    }\n  }\n\n  /**\n   * Adds an event listener to an element\n   *\n   * @see https://htmx.org/api/#on\n   *\n   * @param {EventTarget|string} arg1 the element to add the listener to | the event name to add the listener for\n   * @param {string|EventListener} arg2 the event name to add the listener for | the listener to add\n   * @param {EventListener|Object|boolean} [arg3] the listener to add | options to add\n   * @param {Object|boolean} [arg4] options to add\n   * @returns {EventListener}\n   */\n  function addEventListenerImpl(arg1, arg2, arg3, arg4) {\n    ready(function() {\n      const eventArgs = processEventArgs(arg1, arg2, arg3, arg4)\n      eventArgs.target.addEventListener(eventArgs.event, eventArgs.listener, eventArgs.options)\n    })\n    const b = isFunction(arg2)\n    return b ? arg2 : arg3\n  }\n\n  /**\n   * Removes an event listener from an element\n   *\n   * @see https://htmx.org/api/#off\n   *\n   * @param {EventTarget|string} arg1 the element to remove the listener from | the event name to remove the listener from\n   * @param {string|EventListener} arg2 the event name to remove the listener from | the listener to remove\n   * @param {EventListener} [arg3] the listener to remove\n   * @returns {EventListener}\n   */\n  function removeEventListenerImpl(arg1, arg2, arg3) {\n    ready(function() {\n      const eventArgs = processEventArgs(arg1, arg2, arg3)\n      eventArgs.target.removeEventListener(eventArgs.event, eventArgs.listener)\n    })\n    return isFunction(arg2) ? arg2 : arg3\n  }\n\n  //= ===================================================================\n  // Node processing\n  //= ===================================================================\n\n  const DUMMY_ELT = getDocument().createElement('output') // dummy element for bad selectors\n  /**\n   * @param {Element} elt\n   * @param {string} attrName\n   * @returns {(Node|Window)[]}\n   */\n  function findAttributeTargets(elt, attrName) {\n    const attrTarget = getClosestAttributeValue(elt, attrName)\n    if (attrTarget) {\n      if (attrTarget === 'this') {\n        return [findThisElement(elt, attrName)]\n      } else {\n        const result = querySelectorAllExt(elt, attrTarget)\n        // find `inherit` whole word in value, make sure it's surrounded by commas or is at the start/end of string\n        const shouldInherit = /(^|,)(\\s*)inherit(\\s*)($|,)/.test(attrTarget)\n        if (shouldInherit) {\n          const eltToInheritFrom = asElement(getClosestMatch(elt, function(parent) {\n            return parent !== elt && hasAttribute(asElement(parent), attrName)\n          }))\n          if (eltToInheritFrom) {\n            result.push(...findAttributeTargets(eltToInheritFrom, attrName))\n          }\n        }\n        if (result.length === 0) {\n          logError('The selector \"' + attrTarget + '\" on ' + attrName + ' returned no matches!')\n          return [DUMMY_ELT]\n        } else {\n          return result\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {string} attribute\n   * @returns {Element|null}\n   */\n  function findThisElement(elt, attribute) {\n    return asElement(getClosestMatch(elt, function(elt) {\n      return getAttributeValue(asElement(elt), attribute) != null\n    }))\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {Node|Window|null}\n   */\n  function getTarget(elt) {\n    const targetStr = getClosestAttributeValue(elt, 'hx-target')\n    if (targetStr) {\n      if (targetStr === 'this') {\n        return findThisElement(elt, 'hx-target')\n      } else {\n        return querySelectorExt(elt, targetStr)\n      }\n    } else {\n      const data = getInternalData(elt)\n      if (data.boosted) {\n        return getDocument().body\n      } else {\n        return elt\n      }\n    }\n  }\n\n  /**\n   * @param {string} name\n   * @returns {boolean}\n   */\n  function shouldSettleAttribute(name) {\n    return htmx.config.attributesToSettle.includes(name)\n  }\n\n  /**\n   * @param {Element} mergeTo\n   * @param {Element} mergeFrom\n   */\n  function cloneAttributes(mergeTo, mergeFrom) {\n    forEach(mergeTo.attributes, function(attr) {\n      if (!mergeFrom.hasAttribute(attr.name) && shouldSettleAttribute(attr.name)) {\n        mergeTo.removeAttribute(attr.name)\n      }\n    })\n    forEach(mergeFrom.attributes, function(attr) {\n      if (shouldSettleAttribute(attr.name)) {\n        mergeTo.setAttribute(attr.name, attr.value)\n      }\n    })\n  }\n\n  /**\n   * @param {HtmxSwapStyle} swapStyle\n   * @param {Element} target\n   * @returns {boolean}\n   */\n  function isInlineSwap(swapStyle, target) {\n    const extensions = getExtensions(target)\n    for (let i = 0; i < extensions.length; i++) {\n      const extension = extensions[i]\n      try {\n        if (extension.isInlineSwap(swapStyle)) {\n          return true\n        }\n      } catch (e) {\n        logError(e)\n      }\n    }\n    return swapStyle === 'outerHTML'\n  }\n\n  /**\n   * @param {string} oobValue\n   * @param {Element} oobElement\n   * @param {HtmxSettleInfo} settleInfo\n   * @param {Node|Document} [rootNode]\n   * @returns\n   */\n  function oobSwap(oobValue, oobElement, settleInfo, rootNode) {\n    rootNode = rootNode || getDocument()\n    let selector = '#' + CSS.escape(getRawAttribute(oobElement, 'id'))\n    /** @type HtmxSwapStyle */\n    let swapStyle = 'outerHTML'\n    if (oobValue === 'true') {\n      // do nothing\n    } else if (oobValue.indexOf(':') > 0) {\n      swapStyle = oobValue.substring(0, oobValue.indexOf(':'))\n      selector = oobValue.substring(oobValue.indexOf(':') + 1)\n    } else {\n      swapStyle = oobValue\n    }\n    oobElement.removeAttribute('hx-swap-oob')\n    oobElement.removeAttribute('data-hx-swap-oob')\n\n    const targets = querySelectorAllExt(rootNode, selector, false)\n    if (targets.length) {\n      forEach(\n        targets,\n        function(target) {\n          let fragment\n          const oobElementClone = oobElement.cloneNode(true)\n          fragment = getDocument().createDocumentFragment()\n          fragment.appendChild(oobElementClone)\n          if (!isInlineSwap(swapStyle, target)) {\n            fragment = asParentNode(oobElementClone) // if this is not an inline swap, we use the content of the node, not the node itself\n          }\n\n          const beforeSwapDetails = { shouldSwap: true, target, fragment }\n          if (!triggerEvent(target, 'htmx:oobBeforeSwap', beforeSwapDetails)) return\n\n          target = beforeSwapDetails.target // allow re-targeting\n          if (beforeSwapDetails.shouldSwap) {\n            handlePreservedElements(fragment)\n            swapWithStyle(swapStyle, target, target, fragment, settleInfo)\n            restorePreservedElements()\n          }\n          forEach(settleInfo.elts, function(elt) {\n            triggerEvent(elt, 'htmx:oobAfterSwap', beforeSwapDetails)\n          })\n        }\n      )\n      oobElement.parentNode.removeChild(oobElement)\n    } else {\n      oobElement.parentNode.removeChild(oobElement)\n      triggerErrorEvent(getDocument().body, 'htmx:oobErrorNoTarget', { content: oobElement })\n    }\n    return oobValue\n  }\n\n  function restorePreservedElements() {\n    const pantry = find('#--htmx-preserve-pantry--')\n    if (pantry) {\n      for (const preservedElt of [...pantry.children]) {\n        const existingElement = find('#' + preservedElt.id)\n        // @ts-ignore - use proposed moveBefore feature\n        existingElement.parentNode.moveBefore(preservedElt, existingElement)\n        existingElement.remove()\n      }\n      pantry.remove()\n    }\n  }\n\n  /**\n   * @param {DocumentFragment|ParentNode} fragment\n   */\n  function handlePreservedElements(fragment) {\n    forEach(findAll(fragment, '[hx-preserve], [data-hx-preserve]'), function(preservedElt) {\n      const id = getAttributeValue(preservedElt, 'id')\n      const existingElement = getDocument().getElementById(id)\n      if (existingElement != null) {\n        if (preservedElt.moveBefore) { // if the moveBefore API exists, use it\n          // get or create a storage spot for stuff\n          let pantry = find('#--htmx-preserve-pantry--')\n          if (pantry == null) {\n            getDocument().body.insertAdjacentHTML('afterend', \"<div id='--htmx-preserve-pantry--'></div>\")\n            pantry = find('#--htmx-preserve-pantry--')\n          }\n          // @ts-ignore - use proposed moveBefore feature\n          pantry.moveBefore(existingElement, null)\n        } else {\n          preservedElt.parentNode.replaceChild(existingElement, preservedElt)\n        }\n      }\n    })\n  }\n\n  /**\n   * @param {Node} parentNode\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function handleAttributes(parentNode, fragment, settleInfo) {\n    forEach(fragment.querySelectorAll('[id]'), function(newNode) {\n      const id = getRawAttribute(newNode, 'id')\n      if (id && id.length > 0) {\n        const normalizedId = id.replace(\"'\", \"\\\\'\")\n        const normalizedTag = newNode.tagName.replace(':', '\\\\:')\n        const parentElt = asParentNode(parentNode)\n        const oldNode = parentElt && parentElt.querySelector(normalizedTag + \"[id='\" + normalizedId + \"']\")\n        if (oldNode && oldNode !== parentElt) {\n          const newAttributes = newNode.cloneNode()\n          cloneAttributes(newNode, oldNode)\n          settleInfo.tasks.push(function() {\n            cloneAttributes(newNode, newAttributes)\n          })\n        }\n      }\n    })\n  }\n\n  /**\n   * @param {Node} child\n   * @returns {HtmxSettleTask}\n   */\n  function makeAjaxLoadTask(child) {\n    return function() {\n      removeClassFromElement(child, htmx.config.addedClass)\n      processNode(asElement(child))\n      processFocus(asParentNode(child))\n      triggerEvent(child, 'htmx:load')\n    }\n  }\n\n  /**\n   * @param {ParentNode} child\n   */\n  function processFocus(child) {\n    const autofocus = '[autofocus]'\n    const autoFocusedElt = asHtmlElement(matches(child, autofocus) ? child : child.querySelector(autofocus))\n    if (autoFocusedElt != null) {\n      autoFocusedElt.focus()\n    }\n  }\n\n  /**\n   * @param {Node} parentNode\n   * @param {Node} insertBefore\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function insertNodesBefore(parentNode, insertBefore, fragment, settleInfo) {\n    handleAttributes(parentNode, fragment, settleInfo)\n    while (fragment.childNodes.length > 0) {\n      const child = fragment.firstChild\n      addClassToElement(asElement(child), htmx.config.addedClass)\n      parentNode.insertBefore(child, insertBefore)\n      if (child.nodeType !== Node.TEXT_NODE && child.nodeType !== Node.COMMENT_NODE) {\n        settleInfo.tasks.push(makeAjaxLoadTask(child))\n      }\n    }\n  }\n\n  /**\n   * based on https://gist.github.com/hyamamoto/fd435505d29ebfa3d9716fd2be8d42f0,\n   * derived from Java's string hashcode implementation\n   * @param {string} string\n   * @param {number} hash\n   * @returns {number}\n   */\n  function stringHash(string, hash) {\n    let char = 0\n    while (char < string.length) {\n      hash = (hash << 5) - hash + string.charCodeAt(char++) | 0 // bitwise or ensures we have a 32-bit int\n    }\n    return hash\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {number}\n   */\n  function attributeHash(elt) {\n    let hash = 0\n    for (let i = 0; i < elt.attributes.length; i++) {\n      const attribute = elt.attributes[i]\n      if (attribute.value) { // only include attributes w/ actual values (empty is same as non-existent)\n        hash = stringHash(attribute.name, hash)\n        hash = stringHash(attribute.value, hash)\n      }\n    }\n    return hash\n  }\n\n  /**\n   * @param {EventTarget} elt\n   */\n  function deInitOnHandlers(elt) {\n    const internalData = getInternalData(elt)\n    if (internalData.onHandlers) {\n      for (let i = 0; i < internalData.onHandlers.length; i++) {\n        const handlerInfo = internalData.onHandlers[i]\n        removeEventListenerImpl(elt, handlerInfo.event, handlerInfo.listener)\n      }\n      delete internalData.onHandlers\n    }\n  }\n\n  /**\n   * @param {Node} element\n   */\n  function deInitNode(element) {\n    const internalData = getInternalData(element)\n    if (internalData.timeout) {\n      clearTimeout(internalData.timeout)\n    }\n    if (internalData.listenerInfos) {\n      forEach(internalData.listenerInfos, function(info) {\n        if (info.on) {\n          removeEventListenerImpl(info.on, info.trigger, info.listener)\n        }\n      })\n    }\n    deInitOnHandlers(element)\n    forEach(Object.keys(internalData), function(key) { if (key !== 'firstInitCompleted') delete internalData[key] })\n  }\n\n  /**\n   * @param {Node} element\n   */\n  function cleanUpElement(element) {\n    triggerEvent(element, 'htmx:beforeCleanupElement')\n    deInitNode(element)\n    // @ts-ignore\n    forEach(element.children, function(child) { cleanUpElement(child) })\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapOuterHTML(target, fragment, settleInfo) {\n    if (target.tagName === 'BODY') { // special case the body to innerHTML because DocumentFragments can't contain a body elt unfortunately\n      return swapInnerHTML(target, fragment, settleInfo)\n    }\n    /** @type {Node} */\n    let newElt\n    const eltBeforeNewContent = target.previousSibling\n    const parentNode = parentElt(target)\n    if (!parentNode) { // when parent node disappears, we can't do anything\n      return\n    }\n    insertNodesBefore(parentNode, target, fragment, settleInfo)\n    if (eltBeforeNewContent == null) {\n      newElt = parentNode.firstChild\n    } else {\n      newElt = eltBeforeNewContent.nextSibling\n    }\n    settleInfo.elts = settleInfo.elts.filter(function(e) { return e !== target })\n    // scan through all newly added content and add all elements to the settle info so we trigger\n    // events properly on them\n    while (newElt && newElt !== target) {\n      if (newElt instanceof Element) {\n        settleInfo.elts.push(newElt)\n      }\n      newElt = newElt.nextSibling\n    }\n    cleanUpElement(target)\n    target.remove()\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapAfterBegin(target, fragment, settleInfo) {\n    return insertNodesBefore(target, target.firstChild, fragment, settleInfo)\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapBeforeBegin(target, fragment, settleInfo) {\n    return insertNodesBefore(parentElt(target), target, fragment, settleInfo)\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapBeforeEnd(target, fragment, settleInfo) {\n    return insertNodesBefore(target, null, fragment, settleInfo)\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapAfterEnd(target, fragment, settleInfo) {\n    return insertNodesBefore(parentElt(target), target.nextSibling, fragment, settleInfo)\n  }\n\n  /**\n   * @param {Element} target\n   */\n  function swapDelete(target) {\n    cleanUpElement(target)\n    const parent = parentElt(target)\n    if (parent) {\n      return parent.removeChild(target)\n    }\n  }\n\n  /**\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapInnerHTML(target, fragment, settleInfo) {\n    const firstChild = target.firstChild\n    insertNodesBefore(target, firstChild, fragment, settleInfo)\n    if (firstChild) {\n      while (firstChild.nextSibling) {\n        cleanUpElement(firstChild.nextSibling)\n        target.removeChild(firstChild.nextSibling)\n      }\n      cleanUpElement(firstChild)\n      target.removeChild(firstChild)\n    }\n  }\n\n  /**\n   * @param {HtmxSwapStyle} swapStyle\n   * @param {Element} elt\n   * @param {Element} target\n   * @param {ParentNode} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   */\n  function swapWithStyle(swapStyle, elt, target, fragment, settleInfo) {\n    switch (swapStyle) {\n      case 'none':\n        return\n      case 'outerHTML':\n        swapOuterHTML(target, fragment, settleInfo)\n        return\n      case 'afterbegin':\n        swapAfterBegin(target, fragment, settleInfo)\n        return\n      case 'beforebegin':\n        swapBeforeBegin(target, fragment, settleInfo)\n        return\n      case 'beforeend':\n        swapBeforeEnd(target, fragment, settleInfo)\n        return\n      case 'afterend':\n        swapAfterEnd(target, fragment, settleInfo)\n        return\n      case 'delete':\n        swapDelete(target)\n        return\n      default:\n        var extensions = getExtensions(elt)\n        for (let i = 0; i < extensions.length; i++) {\n          const ext = extensions[i]\n          try {\n            const newElements = ext.handleSwap(swapStyle, target, fragment, settleInfo)\n            if (newElements) {\n              if (Array.isArray(newElements)) {\n                // if handleSwap returns an array (like) of elements, we handle them\n                for (let j = 0; j < newElements.length; j++) {\n                  const child = newElements[j]\n                  if (child.nodeType !== Node.TEXT_NODE && child.nodeType !== Node.COMMENT_NODE) {\n                    settleInfo.tasks.push(makeAjaxLoadTask(child))\n                  }\n                }\n              }\n              return\n            }\n          } catch (e) {\n            logError(e)\n          }\n        }\n        if (swapStyle === 'innerHTML') {\n          swapInnerHTML(target, fragment, settleInfo)\n        } else {\n          swapWithStyle(htmx.config.defaultSwapStyle, elt, target, fragment, settleInfo)\n        }\n    }\n  }\n\n  /**\n   * @param {DocumentFragment} fragment\n   * @param {HtmxSettleInfo} settleInfo\n   * @param {Node|Document} [rootNode]\n   */\n  function findAndSwapOobElements(fragment, settleInfo, rootNode) {\n    var oobElts = findAll(fragment, '[hx-swap-oob], [data-hx-swap-oob]')\n    forEach(oobElts, function(oobElement) {\n      if (htmx.config.allowNestedOobSwaps || oobElement.parentElement === null) {\n        const oobValue = getAttributeValue(oobElement, 'hx-swap-oob')\n        if (oobValue != null) {\n          oobSwap(oobValue, oobElement, settleInfo, rootNode)\n        }\n      } else {\n        oobElement.removeAttribute('hx-swap-oob')\n        oobElement.removeAttribute('data-hx-swap-oob')\n      }\n    })\n    return oobElts.length > 0\n  }\n\n  /**\n   * Implements complete swapping pipeline, including: delay, view transitions, focus and selection preservation,\n   * title updates, scroll, OOB swapping, normal swapping and settling\n   * @param {string|Element} target\n   * @param {string} content\n   * @param {HtmxSwapSpecification} swapSpec\n   * @param {SwapOptions} [swapOptions]\n   */\n  function swap(target, content, swapSpec, swapOptions) {\n    if (!swapOptions) {\n      swapOptions = {}\n    }\n    // optional transition API promise callbacks\n    let settleResolve = null\n    let settleReject = null\n\n    let doSwap = function() {\n      maybeCall(swapOptions.beforeSwapCallback)\n\n      target = resolveTarget(target)\n      const rootNode = swapOptions.contextElement ? getRootNode(swapOptions.contextElement, false) : getDocument()\n\n      // preserve focus and selection\n      const activeElt = document.activeElement\n      let selectionInfo = {}\n      selectionInfo = {\n        elt: activeElt,\n        // @ts-ignore\n        start: activeElt ? activeElt.selectionStart : null,\n        // @ts-ignore\n        end: activeElt ? activeElt.selectionEnd : null\n      }\n      const settleInfo = makeSettleInfo(target)\n\n      // For text content swaps, don't parse the response as HTML, just insert it\n      if (swapSpec.swapStyle === 'textContent') {\n        target.textContent = content\n      // Otherwise, make the fragment and process it\n      } else {\n        let fragment = makeFragment(content)\n\n        settleInfo.title = swapOptions.title || fragment.title\n        if (swapOptions.historyRequest) {\n          // @ts-ignore fragment can be a parentNode Element\n          fragment = fragment.querySelector('[hx-history-elt],[data-hx-history-elt]') || fragment\n        }\n\n        // select-oob swaps\n        if (swapOptions.selectOOB) {\n          const oobSelectValues = swapOptions.selectOOB.split(',')\n          for (let i = 0; i < oobSelectValues.length; i++) {\n            const oobSelectValue = oobSelectValues[i].split(':', 2)\n            let id = oobSelectValue[0].trim()\n            if (id.indexOf('#') === 0) {\n              id = id.substring(1)\n            }\n            const oobValue = oobSelectValue[1] || 'true'\n            const oobElement = fragment.querySelector('#' + id)\n            if (oobElement) {\n              oobSwap(oobValue, oobElement, settleInfo, rootNode)\n            }\n          }\n        }\n        // oob swaps\n        findAndSwapOobElements(fragment, settleInfo, rootNode)\n        forEach(findAll(fragment, 'template'), /** @param {HTMLTemplateElement} template */function(template) {\n          if (template.content && findAndSwapOobElements(template.content, settleInfo, rootNode)) {\n            // Avoid polluting the DOM with empty templates that were only used to encapsulate oob swap\n            template.remove()\n          }\n        })\n\n        // normal swap\n        if (swapOptions.select) {\n          const newFragment = getDocument().createDocumentFragment()\n          forEach(fragment.querySelectorAll(swapOptions.select), function(node) {\n            newFragment.appendChild(node)\n          })\n          fragment = newFragment\n        }\n        handlePreservedElements(fragment)\n        swapWithStyle(swapSpec.swapStyle, swapOptions.contextElement, target, fragment, settleInfo)\n        restorePreservedElements()\n      }\n\n      // apply saved focus and selection information to swapped content\n      if (selectionInfo.elt &&\n        !bodyContains(selectionInfo.elt) &&\n        getRawAttribute(selectionInfo.elt, 'id')) {\n        const newActiveElt = document.getElementById(getRawAttribute(selectionInfo.elt, 'id'))\n        const focusOptions = { preventScroll: swapSpec.focusScroll !== undefined ? !swapSpec.focusScroll : !htmx.config.defaultFocusScroll }\n        if (newActiveElt) {\n          // @ts-ignore\n          if (selectionInfo.start && newActiveElt.setSelectionRange) {\n            try {\n              // @ts-ignore\n              newActiveElt.setSelectionRange(selectionInfo.start, selectionInfo.end)\n            } catch (e) {\n              // the setSelectionRange method is present on fields that don't support it, so just let this fail\n            }\n          }\n          newActiveElt.focus(focusOptions)\n        }\n      }\n\n      target.classList.remove(htmx.config.swappingClass)\n      forEach(settleInfo.elts, function(elt) {\n        if (elt.classList) {\n          elt.classList.add(htmx.config.settlingClass)\n        }\n        triggerEvent(elt, 'htmx:afterSwap', swapOptions.eventInfo)\n      })\n      maybeCall(swapOptions.afterSwapCallback)\n\n      // merge in new title after swap but before settle\n      if (!swapSpec.ignoreTitle) {\n        handleTitle(settleInfo.title)\n      }\n\n      // settle\n      const doSettle = function() {\n        forEach(settleInfo.tasks, function(task) {\n          task.call()\n        })\n        forEach(settleInfo.elts, function(elt) {\n          if (elt.classList) {\n            elt.classList.remove(htmx.config.settlingClass)\n          }\n          triggerEvent(elt, 'htmx:afterSettle', swapOptions.eventInfo)\n        })\n\n        if (swapOptions.anchor) {\n          const anchorTarget = asElement(resolveTarget('#' + swapOptions.anchor))\n          if (anchorTarget) {\n            anchorTarget.scrollIntoView({ block: 'start', behavior: 'auto' })\n          }\n        }\n\n        updateScrollState(settleInfo.elts, swapSpec)\n        maybeCall(swapOptions.afterSettleCallback)\n        maybeCall(settleResolve)\n      }\n\n      if (swapSpec.settleDelay > 0) {\n        getWindow().setTimeout(doSettle, swapSpec.settleDelay)\n      } else {\n        doSettle()\n      }\n    }\n    let shouldTransition = htmx.config.globalViewTransitions\n    if (swapSpec.hasOwnProperty('transition')) {\n      shouldTransition = swapSpec.transition\n    }\n\n    const elt = swapOptions.contextElement || getDocument()\n\n    if (shouldTransition &&\n            triggerEvent(elt, 'htmx:beforeTransition', swapOptions.eventInfo) &&\n            typeof Promise !== 'undefined' &&\n            // @ts-ignore experimental feature atm\n            document.startViewTransition) {\n      const settlePromise = new Promise(function(_resolve, _reject) {\n        settleResolve = _resolve\n        settleReject = _reject\n      })\n      // wrap the original doSwap() in a call to startViewTransition()\n      const innerDoSwap = doSwap\n      doSwap = function() {\n        // @ts-ignore experimental feature atm\n        document.startViewTransition(function() {\n          innerDoSwap()\n          return settlePromise\n        })\n      }\n    }\n\n    try {\n      if (swapSpec?.swapDelay && swapSpec.swapDelay > 0) {\n        getWindow().setTimeout(doSwap, swapSpec.swapDelay)\n      } else {\n        doSwap()\n      }\n    } catch (e) {\n      triggerErrorEvent(elt, 'htmx:swapError', swapOptions.eventInfo)\n      maybeCall(settleReject)\n      throw e\n    }\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @param {string} header\n   * @param {EventTarget} elt\n   */\n  function handleTriggerHeader(xhr, header, elt) {\n    const triggerBody = xhr.getResponseHeader(header)\n    if (triggerBody.indexOf('{') === 0) {\n      const triggers = parseJSON(triggerBody)\n      for (const eventName in triggers) {\n        if (triggers.hasOwnProperty(eventName)) {\n          let detail = triggers[eventName]\n          if (isRawObject(detail)) {\n            // @ts-ignore\n            elt = detail.target !== undefined ? detail.target : elt\n          } else {\n            detail = { value: detail }\n          }\n          triggerEvent(elt, eventName, detail)\n        }\n      }\n    } else {\n      const eventNames = triggerBody.split(',')\n      for (let i = 0; i < eventNames.length; i++) {\n        triggerEvent(elt, eventNames[i].trim(), [])\n      }\n    }\n  }\n\n  const WHITESPACE = /\\s/\n  const WHITESPACE_OR_COMMA = /[\\s,]/\n  const SYMBOL_START = /[_$a-zA-Z]/\n  const SYMBOL_CONT = /[_$a-zA-Z0-9]/\n  const STRINGISH_START = ['\"', \"'\", '/']\n  const NOT_WHITESPACE = /[^\\s]/\n  const COMBINED_SELECTOR_START = /[{(]/\n  const COMBINED_SELECTOR_END = /[})]/\n\n  /**\n   * @param {string} str\n   * @returns {string[]}\n   */\n  function tokenizeString(str) {\n    /** @type string[] */\n    const tokens = []\n    let position = 0\n    while (position < str.length) {\n      if (SYMBOL_START.exec(str.charAt(position))) {\n        var startPosition = position\n        while (SYMBOL_CONT.exec(str.charAt(position + 1))) {\n          position++\n        }\n        tokens.push(str.substring(startPosition, position + 1))\n      } else if (STRINGISH_START.indexOf(str.charAt(position)) !== -1) {\n        const startChar = str.charAt(position)\n        var startPosition = position\n        position++\n        while (position < str.length && str.charAt(position) !== startChar) {\n          if (str.charAt(position) === '\\\\') {\n            position++\n          }\n          position++\n        }\n        tokens.push(str.substring(startPosition, position + 1))\n      } else {\n        const symbol = str.charAt(position)\n        tokens.push(symbol)\n      }\n      position++\n    }\n    return tokens\n  }\n\n  /**\n   * @param {string} token\n   * @param {string|null} last\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  function isPossibleRelativeReference(token, last, paramName) {\n    return SYMBOL_START.exec(token.charAt(0)) &&\n      token !== 'true' &&\n      token !== 'false' &&\n      token !== 'this' &&\n      token !== paramName &&\n      last !== '.'\n  }\n\n  /**\n   * @param {EventTarget|string} elt\n   * @param {string[]} tokens\n   * @param {string} paramName\n   * @returns {ConditionalFunction|null}\n   */\n  function maybeGenerateConditional(elt, tokens, paramName) {\n    if (tokens[0] === '[') {\n      tokens.shift()\n      let bracketCount = 1\n      let conditionalSource = ' return (function(' + paramName + '){ return ('\n      let last = null\n      while (tokens.length > 0) {\n        const token = tokens[0]\n        // @ts-ignore For some reason tsc doesn't understand the shift call, and thinks we're comparing the same value here, i.e. '[' vs ']'\n        if (token === ']') {\n          bracketCount--\n          if (bracketCount === 0) {\n            if (last === null) {\n              conditionalSource = conditionalSource + 'true'\n            }\n            tokens.shift()\n            conditionalSource += ')})'\n            try {\n              const conditionFunction = maybeEval(elt, function() {\n                return Function(conditionalSource)()\n              },\n              function() { return true })\n              conditionFunction.source = conditionalSource\n              return conditionFunction\n            } catch (e) {\n              triggerErrorEvent(getDocument().body, 'htmx:syntax:error', { error: e, source: conditionalSource })\n              return null\n            }\n          }\n        } else if (token === '[') {\n          bracketCount++\n        }\n        if (isPossibleRelativeReference(token, last, paramName)) {\n          conditionalSource += '((' + paramName + '.' + token + ') ? (' + paramName + '.' + token + ') : (window.' + token + '))'\n        } else {\n          conditionalSource = conditionalSource + token\n        }\n        last = tokens.shift()\n      }\n    }\n  }\n\n  /**\n   * @param {string[]} tokens\n   * @param {RegExp} match\n   * @returns {string}\n   */\n  function consumeUntil(tokens, match) {\n    let result = ''\n    while (tokens.length > 0 && !match.test(tokens[0])) {\n      result += tokens.shift()\n    }\n    return result\n  }\n\n  /**\n   * @param {string[]} tokens\n   * @returns {string}\n   */\n  function consumeCSSSelector(tokens) {\n    let result\n    if (tokens.length > 0 && COMBINED_SELECTOR_START.test(tokens[0])) {\n      tokens.shift()\n      result = consumeUntil(tokens, COMBINED_SELECTOR_END).trim()\n      tokens.shift()\n    } else {\n      result = consumeUntil(tokens, WHITESPACE_OR_COMMA)\n    }\n    return result\n  }\n\n  const INPUT_SELECTOR = 'input, textarea, select'\n\n  /**\n   * @param {Element} elt\n   * @param {string} explicitTrigger\n   * @param {Object} cache for trigger specs\n   * @returns {HtmxTriggerSpecification[]}\n   */\n  function parseAndCacheTrigger(elt, explicitTrigger, cache) {\n    /** @type HtmxTriggerSpecification[] */\n    const triggerSpecs = []\n    const tokens = tokenizeString(explicitTrigger)\n    do {\n      consumeUntil(tokens, NOT_WHITESPACE)\n      const initialLength = tokens.length\n      const trigger = consumeUntil(tokens, /[,\\[\\s]/)\n      if (trigger !== '') {\n        if (trigger === 'every') {\n          /** @type HtmxTriggerSpecification */\n          const every = { trigger: 'every' }\n          consumeUntil(tokens, NOT_WHITESPACE)\n          every.pollInterval = parseInterval(consumeUntil(tokens, /[,\\[\\s]/))\n          consumeUntil(tokens, NOT_WHITESPACE)\n          var eventFilter = maybeGenerateConditional(elt, tokens, 'event')\n          if (eventFilter) {\n            every.eventFilter = eventFilter\n          }\n          triggerSpecs.push(every)\n        } else {\n          /** @type HtmxTriggerSpecification */\n          const triggerSpec = { trigger }\n          var eventFilter = maybeGenerateConditional(elt, tokens, 'event')\n          if (eventFilter) {\n            triggerSpec.eventFilter = eventFilter\n          }\n          consumeUntil(tokens, NOT_WHITESPACE)\n          while (tokens.length > 0 && tokens[0] !== ',') {\n            const token = tokens.shift()\n            if (token === 'changed') {\n              triggerSpec.changed = true\n            } else if (token === 'once') {\n              triggerSpec.once = true\n            } else if (token === 'consume') {\n              triggerSpec.consume = true\n            } else if (token === 'delay' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec.delay = parseInterval(consumeUntil(tokens, WHITESPACE_OR_COMMA))\n            } else if (token === 'from' && tokens[0] === ':') {\n              tokens.shift()\n              if (COMBINED_SELECTOR_START.test(tokens[0])) {\n                var from_arg = consumeCSSSelector(tokens)\n              } else {\n                var from_arg = consumeUntil(tokens, WHITESPACE_OR_COMMA)\n                if (from_arg === 'closest' || from_arg === 'find' || from_arg === 'next' || from_arg === 'previous') {\n                  tokens.shift()\n                  const selector = consumeCSSSelector(tokens)\n                  // `next` and `previous` allow a selector-less syntax\n                  if (selector.length > 0) {\n                    from_arg += ' ' + selector\n                  }\n                }\n              }\n              triggerSpec.from = from_arg\n            } else if (token === 'target' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec.target = consumeCSSSelector(tokens)\n            } else if (token === 'throttle' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec.throttle = parseInterval(consumeUntil(tokens, WHITESPACE_OR_COMMA))\n            } else if (token === 'queue' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec.queue = consumeUntil(tokens, WHITESPACE_OR_COMMA)\n            } else if (token === 'root' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec[token] = consumeCSSSelector(tokens)\n            } else if (token === 'threshold' && tokens[0] === ':') {\n              tokens.shift()\n              triggerSpec[token] = consumeUntil(tokens, WHITESPACE_OR_COMMA)\n            } else {\n              triggerErrorEvent(elt, 'htmx:syntax:error', { token: tokens.shift() })\n            }\n            consumeUntil(tokens, NOT_WHITESPACE)\n          }\n          triggerSpecs.push(triggerSpec)\n        }\n      }\n      if (tokens.length === initialLength) {\n        triggerErrorEvent(elt, 'htmx:syntax:error', { token: tokens.shift() })\n      }\n      consumeUntil(tokens, NOT_WHITESPACE)\n    } while (tokens[0] === ',' && tokens.shift())\n    if (cache) {\n      cache[explicitTrigger] = triggerSpecs\n    }\n    return triggerSpecs\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {HtmxTriggerSpecification[]}\n   */\n  function getTriggerSpecs(elt) {\n    const explicitTrigger = getAttributeValue(elt, 'hx-trigger')\n    let triggerSpecs = []\n    if (explicitTrigger) {\n      const cache = htmx.config.triggerSpecsCache\n      triggerSpecs = (cache && cache[explicitTrigger]) || parseAndCacheTrigger(elt, explicitTrigger, cache)\n    }\n\n    if (triggerSpecs.length > 0) {\n      return triggerSpecs\n    } else if (matches(elt, 'form')) {\n      return [{ trigger: 'submit' }]\n    } else if (matches(elt, 'input[type=\"button\"], input[type=\"submit\"]')) {\n      return [{ trigger: 'click' }]\n    } else if (matches(elt, INPUT_SELECTOR)) {\n      return [{ trigger: 'change' }]\n    } else {\n      return [{ trigger: 'click' }]\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   */\n  function cancelPolling(elt) {\n    getInternalData(elt).cancelled = true\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {TriggerHandler} handler\n   * @param {HtmxTriggerSpecification} spec\n   */\n  function processPolling(elt, handler, spec) {\n    const nodeData = getInternalData(elt)\n    nodeData.timeout = getWindow().setTimeout(function() {\n      if (bodyContains(elt) && nodeData.cancelled !== true) {\n        if (!maybeFilterEvent(spec, elt, makeEvent('hx:poll:trigger', {\n          triggerSpec: spec,\n          target: elt\n        }))) {\n          handler(elt)\n        }\n        processPolling(elt, handler, spec)\n      }\n    }, spec.pollInterval)\n  }\n\n  /**\n   * @param {HTMLAnchorElement} elt\n   * @returns {boolean}\n   */\n  function isLocalLink(elt) {\n    return location.hostname === elt.hostname &&\n      getRawAttribute(elt, 'href') &&\n      getRawAttribute(elt, 'href').indexOf('#') !== 0\n  }\n\n  /**\n   * @param {Element} elt\n   */\n  function eltIsDisabled(elt) {\n    return closest(elt, htmx.config.disableSelector)\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxNodeInternalData} nodeData\n   * @param {HtmxTriggerSpecification[]} triggerSpecs\n   */\n  function boostElement(elt, nodeData, triggerSpecs) {\n    if ((elt instanceof HTMLAnchorElement && isLocalLink(elt) && (elt.target === '' || elt.target === '_self')) || (elt.tagName === 'FORM' && String(getRawAttribute(elt, 'method')).toLowerCase() !== 'dialog')) {\n      nodeData.boosted = true\n      let verb, path\n      if (elt.tagName === 'A') {\n        verb = (/** @type HttpVerb */('get'))\n        path = getRawAttribute(elt, 'href')\n      } else {\n        const rawAttribute = getRawAttribute(elt, 'method')\n        verb = (/** @type HttpVerb */(rawAttribute ? rawAttribute.toLowerCase() : 'get'))\n        path = getRawAttribute(elt, 'action')\n        if (path == null || path === '') {\n          // if there is no action attribute on the form set path to current href before the\n          // following logic to properly clear parameters on a GET (not on a POST!)\n          path = location.href\n        }\n        if (verb === 'get' && path.includes('?')) {\n          path = path.replace(/\\?[^#]+/, '')\n        }\n      }\n      triggerSpecs.forEach(function(triggerSpec) {\n        addEventListener(elt, function(node, evt) {\n          const elt = asElement(node)\n          if (eltIsDisabled(elt)) {\n            cleanUpElement(elt)\n            return\n          }\n          issueAjaxRequest(verb, path, elt, evt)\n        }, nodeData, triggerSpec, true)\n      })\n    }\n  }\n\n  /**\n   * @param {Event} evt\n   * @param {Element} elt\n   * @returns {boolean}\n   */\n  function shouldCancel(evt, elt) {\n    if (evt.type === 'submit' || evt.type === 'click') {\n      // use elt from event that was submitted/clicked where possible to determining if default form/link behavior should be canceled\n      elt = asElement(evt.target) || elt\n      if (elt.tagName === 'FORM') {\n        return true\n      }\n      // @ts-ignore Do not cancel on buttons that 1) don't have a related form or 2) have a type attribute of 'reset'/'button'.\n      // The properties will resolve to undefined for elements that don't define 'type' or 'form', which is fine\n      if (elt.form && elt.type === 'submit') {\n        return true\n      }\n      elt = elt.closest('a')\n      // @ts-ignore check for a link wrapping the event elt or if elt is a link. elt will be link so href check is fine\n      if (elt && elt.href &&\n        (elt.getAttribute('href') === '#' || elt.getAttribute('href').indexOf('#') !== 0)) {\n        return true\n      }\n    }\n    return false\n  }\n\n  /**\n   * @param {Node} elt\n   * @param {Event|MouseEvent|KeyboardEvent|TouchEvent} evt\n   * @returns {boolean}\n   */\n  function ignoreBoostedAnchorCtrlClick(elt, evt) {\n    return getInternalData(elt).boosted && elt instanceof HTMLAnchorElement && evt.type === 'click' &&\n      // @ts-ignore this will resolve to undefined for events that don't define those properties, which is fine\n      (evt.ctrlKey || evt.metaKey)\n  }\n\n  /**\n   * @param {HtmxTriggerSpecification} triggerSpec\n   * @param {Node} elt\n   * @param {Event} evt\n   * @returns {boolean}\n   */\n  function maybeFilterEvent(triggerSpec, elt, evt) {\n    const eventFilter = triggerSpec.eventFilter\n    if (eventFilter) {\n      try {\n        return eventFilter.call(elt, evt) !== true\n      } catch (e) {\n        const source = eventFilter.source\n        triggerErrorEvent(getDocument().body, 'htmx:eventFilter:error', { error: e, source })\n        return true\n      }\n    }\n    return false\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {TriggerHandler} handler\n   * @param {HtmxNodeInternalData} nodeData\n   * @param {HtmxTriggerSpecification} triggerSpec\n   * @param {boolean} [explicitCancel]\n   */\n  function addEventListener(elt, handler, nodeData, triggerSpec, explicitCancel) {\n    const elementData = getInternalData(elt)\n    /** @type {(Node|Window)[]} */\n    let eltsToListenOn\n    if (triggerSpec.from) {\n      eltsToListenOn = querySelectorAllExt(elt, triggerSpec.from)\n    } else {\n      eltsToListenOn = [elt]\n    }\n    // store the initial values of the elements, so we can tell if they change\n    if (triggerSpec.changed) {\n      if (!('lastValue' in elementData)) {\n        elementData.lastValue = new WeakMap()\n      }\n      eltsToListenOn.forEach(function(eltToListenOn) {\n        if (!elementData.lastValue.has(triggerSpec)) {\n          elementData.lastValue.set(triggerSpec, new WeakMap())\n        }\n        // @ts-ignore value will be undefined for non-input elements, which is fine\n        elementData.lastValue.get(triggerSpec).set(eltToListenOn, eltToListenOn.value)\n      })\n    }\n    forEach(eltsToListenOn, function(eltToListenOn) {\n      /** @type EventListener */\n      const eventListener = function(evt) {\n        if (!bodyContains(elt)) {\n          eltToListenOn.removeEventListener(triggerSpec.trigger, eventListener)\n          return\n        }\n        if (ignoreBoostedAnchorCtrlClick(elt, evt)) {\n          return\n        }\n        if (explicitCancel || shouldCancel(evt, elt)) {\n          evt.preventDefault()\n        }\n        if (maybeFilterEvent(triggerSpec, elt, evt)) {\n          return\n        }\n        const eventData = getInternalData(evt)\n        eventData.triggerSpec = triggerSpec\n        if (eventData.handledFor == null) {\n          eventData.handledFor = []\n        }\n        if (eventData.handledFor.indexOf(elt) < 0) {\n          eventData.handledFor.push(elt)\n          if (triggerSpec.consume) {\n            evt.stopPropagation()\n          }\n          if (triggerSpec.target && evt.target) {\n            if (!matches(asElement(evt.target), triggerSpec.target)) {\n              return\n            }\n          }\n          if (triggerSpec.once) {\n            if (elementData.triggeredOnce) {\n              return\n            } else {\n              elementData.triggeredOnce = true\n            }\n          }\n          if (triggerSpec.changed) {\n            const node = evt.target\n            // @ts-ignore value will be undefined for non-input elements, which is fine\n            const value = node.value\n            const lastValue = elementData.lastValue.get(triggerSpec)\n            if (lastValue.has(node) && lastValue.get(node) === value) {\n              return\n            }\n            lastValue.set(node, value)\n          }\n          if (elementData.delayed) {\n            clearTimeout(elementData.delayed)\n          }\n          if (elementData.throttle) {\n            return\n          }\n\n          if (triggerSpec.throttle > 0) {\n            if (!elementData.throttle) {\n              triggerEvent(elt, 'htmx:trigger')\n              handler(elt, evt)\n              elementData.throttle = getWindow().setTimeout(function() {\n                elementData.throttle = null\n              }, triggerSpec.throttle)\n            }\n          } else if (triggerSpec.delay > 0) {\n            elementData.delayed = getWindow().setTimeout(function() {\n              triggerEvent(elt, 'htmx:trigger')\n              handler(elt, evt)\n            }, triggerSpec.delay)\n          } else {\n            triggerEvent(elt, 'htmx:trigger')\n            handler(elt, evt)\n          }\n        }\n      }\n      if (nodeData.listenerInfos == null) {\n        nodeData.listenerInfos = []\n      }\n      nodeData.listenerInfos.push({\n        trigger: triggerSpec.trigger,\n        listener: eventListener,\n        on: eltToListenOn\n      })\n      eltToListenOn.addEventListener(triggerSpec.trigger, eventListener)\n    })\n  }\n\n  let windowIsScrolling = false // used by initScrollHandler\n  let scrollHandler = null\n  function initScrollHandler() {\n    if (!scrollHandler) {\n      scrollHandler = function() {\n        windowIsScrolling = true\n      }\n      window.addEventListener('scroll', scrollHandler)\n      window.addEventListener('resize', scrollHandler)\n      setInterval(function() {\n        if (windowIsScrolling) {\n          windowIsScrolling = false\n          forEach(getDocument().querySelectorAll(\"[hx-trigger*='revealed'],[data-hx-trigger*='revealed']\"), function(elt) {\n            maybeReveal(elt)\n          })\n        }\n      }, 200)\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   */\n  function maybeReveal(elt) {\n    if (!hasAttribute(elt, 'data-hx-revealed') && isScrolledIntoView(elt)) {\n      elt.setAttribute('data-hx-revealed', 'true')\n      const nodeData = getInternalData(elt)\n      if (nodeData.initHash) {\n        triggerEvent(elt, 'revealed')\n      } else {\n        // if the node isn't initialized, wait for it before triggering the request\n        elt.addEventListener('htmx:afterProcessNode', function() { triggerEvent(elt, 'revealed') }, { once: true })\n      }\n    }\n  }\n\n  //= ===================================================================\n\n  /**\n   * @param {Element} elt\n   * @param {TriggerHandler} handler\n   * @param {HtmxNodeInternalData} nodeData\n   * @param {number} delay\n   */\n  function loadImmediately(elt, handler, nodeData, delay) {\n    const load = function() {\n      if (!nodeData.loaded) {\n        nodeData.loaded = true\n        triggerEvent(elt, 'htmx:trigger')\n        handler(elt)\n      }\n    }\n    if (delay > 0) {\n      getWindow().setTimeout(load, delay)\n    } else {\n      load()\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxNodeInternalData} nodeData\n   * @param {HtmxTriggerSpecification[]} triggerSpecs\n   * @returns {boolean}\n   */\n  function processVerbs(elt, nodeData, triggerSpecs) {\n    let explicitAction = false\n    forEach(VERBS, function(verb) {\n      if (hasAttribute(elt, 'hx-' + verb)) {\n        const path = getAttributeValue(elt, 'hx-' + verb)\n        explicitAction = true\n        nodeData.path = path\n        nodeData.verb = verb\n        triggerSpecs.forEach(function(triggerSpec) {\n          addTriggerHandler(elt, triggerSpec, nodeData, function(node, evt) {\n            const elt = asElement(node)\n            if (eltIsDisabled(elt)) {\n              cleanUpElement(elt)\n              return\n            }\n            issueAjaxRequest(verb, path, elt, evt)\n          })\n        })\n      }\n    })\n    return explicitAction\n  }\n\n  /**\n   * @callback TriggerHandler\n   * @param {Element} elt\n   * @param {Event} [evt]\n   */\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxTriggerSpecification} triggerSpec\n   * @param {HtmxNodeInternalData} nodeData\n   * @param {TriggerHandler} handler\n   */\n  function addTriggerHandler(elt, triggerSpec, nodeData, handler) {\n    if (triggerSpec.trigger === 'revealed') {\n      initScrollHandler()\n      addEventListener(elt, handler, nodeData, triggerSpec)\n      maybeReveal(asElement(elt))\n    } else if (triggerSpec.trigger === 'intersect') {\n      const observerOptions = {}\n      if (triggerSpec.root) {\n        observerOptions.root = querySelectorExt(elt, triggerSpec.root)\n      }\n      if (triggerSpec.threshold) {\n        observerOptions.threshold = parseFloat(triggerSpec.threshold)\n      }\n      const observer = new IntersectionObserver(function(entries) {\n        for (let i = 0; i < entries.length; i++) {\n          const entry = entries[i]\n          if (entry.isIntersecting) {\n            triggerEvent(elt, 'intersect')\n            break\n          }\n        }\n      }, observerOptions)\n      observer.observe(asElement(elt))\n      addEventListener(asElement(elt), handler, nodeData, triggerSpec)\n    } else if (!nodeData.firstInitCompleted && triggerSpec.trigger === 'load') {\n      if (!maybeFilterEvent(triggerSpec, elt, makeEvent('load', { elt }))) {\n        loadImmediately(asElement(elt), handler, nodeData, triggerSpec.delay)\n      }\n    } else if (triggerSpec.pollInterval > 0) {\n      nodeData.polling = true\n      processPolling(asElement(elt), handler, triggerSpec)\n    } else {\n      addEventListener(elt, handler, nodeData, triggerSpec)\n    }\n  }\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function shouldProcessHxOn(node) {\n    const elt = asElement(node)\n    if (!elt) {\n      return false\n    }\n    const attributes = elt.attributes\n    for (let j = 0; j < attributes.length; j++) {\n      const attrName = attributes[j].name\n      if (startsWith(attrName, 'hx-on:') || startsWith(attrName, 'data-hx-on:') ||\n        startsWith(attrName, 'hx-on-') || startsWith(attrName, 'data-hx-on-')) {\n        return true\n      }\n    }\n    return false\n  }\n\n  /**\n   * @param {Node} elt\n   * @returns {Element[]}\n   */\n  const HX_ON_QUERY = new XPathEvaluator()\n    .createExpression('.//*[@*[ starts-with(name(), \"hx-on:\") or starts-with(name(), \"data-hx-on:\") or' +\n      ' starts-with(name(), \"hx-on-\") or starts-with(name(), \"data-hx-on-\") ]]')\n\n  function processHXOnRoot(elt, elements) {\n    if (shouldProcessHxOn(elt)) {\n      elements.push(asElement(elt))\n    }\n    const iter = HX_ON_QUERY.evaluate(elt)\n    let node = null\n    while (node = iter.iterateNext()) elements.push(asElement(node))\n  }\n\n  function findHxOnWildcardElements(elt) {\n    /** @type {Element[]} */\n    const elements = []\n    if (elt instanceof DocumentFragment) {\n      for (const child of elt.childNodes) {\n        processHXOnRoot(child, elements)\n      }\n    } else {\n      processHXOnRoot(elt, elements)\n    }\n    return elements\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {NodeListOf<Element>|[]}\n   */\n  function findElementsToProcess(elt) {\n    if (elt.querySelectorAll) {\n      const boostedSelector = ', [hx-boost] a, [data-hx-boost] a, a[hx-boost], a[data-hx-boost]'\n\n      const extensionSelectors = []\n      for (const e in extensions) {\n        const extension = extensions[e]\n        if (extension.getSelectors) {\n          var selectors = extension.getSelectors()\n          if (selectors) {\n            extensionSelectors.push(selectors)\n          }\n        }\n      }\n\n      const results = elt.querySelectorAll(VERB_SELECTOR + boostedSelector + \", form, [type='submit'],\" +\n        ' [hx-ext], [data-hx-ext], [hx-trigger], [data-hx-trigger]' + extensionSelectors.flat().map(s => ', ' + s).join(''))\n\n      return results\n    } else {\n      return []\n    }\n  }\n\n  /**\n   * Handle submit buttons/inputs that have the form attribute set\n   * see https://developer.mozilla.org/docs/Web/HTML/Element/button\n   * @param {Event} evt\n   */\n  function maybeSetLastButtonClicked(evt) {\n    const elt = getTargetButton(evt.target)\n    const internalData = getRelatedFormData(evt)\n    if (internalData) {\n      internalData.lastButtonClicked = elt\n    }\n  }\n\n  /**\n   * @param {Event} evt\n   */\n  function maybeUnsetLastButtonClicked(evt) {\n    const internalData = getRelatedFormData(evt)\n    if (internalData) {\n      internalData.lastButtonClicked = null\n    }\n  }\n\n  /**\n   * @param {EventTarget} target\n   * @returns {HTMLButtonElement|HTMLInputElement|null}\n   */\n  function getTargetButton(target) {\n    return /** @type {HTMLButtonElement|HTMLInputElement|null} */ (closest(asElement(target), \"button, input[type='submit']\"))\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {HTMLFormElement|null}\n   */\n  function getRelatedForm(elt) {\n    // @ts-ignore Get the related form if available, else find the closest parent form\n    return elt.form || closest(elt, 'form')\n  }\n\n  /**\n   * @param {Event} evt\n   * @returns {HtmxNodeInternalData|undefined}\n   */\n  function getRelatedFormData(evt) {\n    const elt = getTargetButton(evt.target)\n    if (!elt) {\n      return\n    }\n    const form = getRelatedForm(elt)\n    return getInternalData(form)\n  }\n\n  /**\n   * @param {EventTarget} elt\n   */\n  function initButtonTracking(elt) {\n    // need to handle both click and focus in:\n    //   focusin - in case someone tabs in to a button and hits the space bar\n    //   click - on OSX buttons do not focus on click see https://bugs.webkit.org/show_bug.cgi?id=13724\n    elt.addEventListener('click', maybeSetLastButtonClicked)\n    elt.addEventListener('focusin', maybeSetLastButtonClicked)\n    elt.addEventListener('focusout', maybeUnsetLastButtonClicked)\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {string} eventName\n   * @param {string} code\n   */\n  function addHxOnEventHandler(elt, eventName, code) {\n    const nodeData = getInternalData(elt)\n    if (!Array.isArray(nodeData.onHandlers)) {\n      nodeData.onHandlers = []\n    }\n    let func\n    /** @type EventListener */\n    const listener = function(e) {\n      maybeEval(elt, function() {\n        if (eltIsDisabled(elt)) {\n          return\n        }\n        if (!func) {\n          func = new Function('event', code)\n        }\n        func.call(elt, e)\n      })\n    }\n    elt.addEventListener(eventName, listener)\n    nodeData.onHandlers.push({ event: eventName, listener })\n  }\n\n  /**\n   * @param {Element} elt\n   */\n  function processHxOnWildcard(elt) {\n    // wipe any previous on handlers so that this function takes precedence\n    deInitOnHandlers(elt)\n\n    for (let i = 0; i < elt.attributes.length; i++) {\n      const name = elt.attributes[i].name\n      const value = elt.attributes[i].value\n      if (startsWith(name, 'hx-on') || startsWith(name, 'data-hx-on')) {\n        const afterOnPosition = name.indexOf('-on') + 3\n        const nextChar = name.slice(afterOnPosition, afterOnPosition + 1)\n        if (nextChar === '-' || nextChar === ':') {\n          let eventName = name.slice(afterOnPosition + 1)\n          // if the eventName starts with a colon or dash, prepend \"htmx\" for shorthand support\n          if (startsWith(eventName, ':')) {\n            eventName = 'htmx' + eventName\n          } else if (startsWith(eventName, '-')) {\n            eventName = 'htmx:' + eventName.slice(1)\n          } else if (startsWith(eventName, 'htmx-')) {\n            eventName = 'htmx:' + eventName.slice(5)\n          }\n\n          addHxOnEventHandler(elt, eventName, value)\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {Element|HTMLInputElement} elt\n   */\n  function initNode(elt) {\n    triggerEvent(elt, 'htmx:beforeProcessNode')\n\n    const nodeData = getInternalData(elt)\n    const triggerSpecs = getTriggerSpecs(elt)\n    const hasExplicitHttpAction = processVerbs(elt, nodeData, triggerSpecs)\n\n    if (!hasExplicitHttpAction) {\n      if (getClosestAttributeValue(elt, 'hx-boost') === 'true') {\n        boostElement(elt, nodeData, triggerSpecs)\n      } else if (hasAttribute(elt, 'hx-trigger')) {\n        triggerSpecs.forEach(function(triggerSpec) {\n          // For \"naked\" triggers, don't do anything at all\n          addTriggerHandler(elt, triggerSpec, nodeData, function() {\n          })\n        })\n      }\n    }\n\n    // Handle submit buttons/inputs that have the form attribute set\n    // see https://developer.mozilla.org/docs/Web/HTML/Element/button\n    if (elt.tagName === 'FORM' || (getRawAttribute(elt, 'type') === 'submit' && hasAttribute(elt, 'form'))) {\n      initButtonTracking(elt)\n    }\n\n    nodeData.firstInitCompleted = true\n    triggerEvent(elt, 'htmx:afterProcessNode')\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {boolean}\n   */\n  function maybeDeInitAndHash(elt) {\n    // Ensure only valid Elements and not shadow DOM roots are inited\n    if (!(elt instanceof Element)) {\n      return false\n    }\n\n    const nodeData = getInternalData(elt)\n    const hash = attributeHash(elt)\n    if (nodeData.initHash !== hash) {\n      deInitNode(elt)\n      nodeData.initHash = hash\n      return true\n    }\n    return false\n  }\n\n  /**\n   * Processes new content, enabling htmx behavior. This can be useful if you have content that is added to the DOM outside of the normal htmx request cycle but still want htmx attributes to work.\n   *\n   * @see https://htmx.org/api/#process\n   *\n   * @param {Element|string} elt element to process\n   */\n  function processNode(elt) {\n    elt = resolveTarget(elt)\n    if (eltIsDisabled(elt)) {\n      cleanUpElement(elt)\n      return\n    }\n\n    const elementsToInit = []\n    if (maybeDeInitAndHash(elt)) {\n      elementsToInit.push(elt)\n    }\n    forEach(findElementsToProcess(elt), function(child) {\n      if (eltIsDisabled(child)) {\n        cleanUpElement(child)\n        return\n      }\n      if (maybeDeInitAndHash(child)) {\n        elementsToInit.push(child)\n      }\n    })\n\n    forEach(findHxOnWildcardElements(elt), processHxOnWildcard)\n    forEach(elementsToInit, initNode)\n  }\n\n  //= ===================================================================\n  // Event/Log Support\n  //= ===================================================================\n\n  /**\n   * @param {string} str\n   * @returns {string}\n   */\n  function kebabEventName(str) {\n    return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {any} detail\n   * @returns {CustomEvent}\n   */\n  function makeEvent(eventName, detail) {\n    // TODO: `composed: true` here is a hack to make global event handlers work with events in shadow DOM\n    // This breaks expected encapsulation but needs to be here until decided otherwise by core devs\n    return new CustomEvent(eventName, { bubbles: true, cancelable: true, composed: true, detail })\n  }\n\n  /**\n   * @param {EventTarget|string} elt\n   * @param {string} eventName\n   * @param {any=} detail\n   */\n  function triggerErrorEvent(elt, eventName, detail) {\n    triggerEvent(elt, eventName, mergeObjects({ error: eventName }, detail))\n  }\n\n  /**\n   * @param {string} eventName\n   * @returns {boolean}\n   */\n  function ignoreEventForLogging(eventName) {\n    return eventName === 'htmx:afterProcessNode'\n  }\n\n  /**\n   * `withExtensions` locates all active extensions for a provided element, then\n   * executes the provided function using each of the active extensions. You can filter\n   * the element's extensions by giving it a list of extensions to ignore. It should\n   * be called internally at every extendable execution point in htmx.\n   *\n   * @param {Element} elt\n   * @param {(extension:HtmxExtension) => void} toDo\n   * @param {string[]=} extensionsToIgnore\n   * @returns void\n   */\n  function withExtensions(elt, toDo, extensionsToIgnore) {\n    forEach(getExtensions(elt, [], extensionsToIgnore), function(extension) {\n      try {\n        toDo(extension)\n      } catch (e) {\n        logError(e)\n      }\n    })\n  }\n\n  function logError(msg) {\n    console.error(msg)\n  }\n\n  /**\n   * Triggers a given event on an element\n   *\n   * @see https://htmx.org/api/#trigger\n   *\n   * @param {EventTarget|string} elt the element to trigger the event on\n   * @param {string} eventName the name of the event to trigger\n   * @param {any=} detail details for the event\n   * @returns {boolean}\n   */\n  function triggerEvent(elt, eventName, detail) {\n    elt = resolveTarget(elt)\n    if (detail == null) {\n      detail = {}\n    }\n    detail.elt = elt\n    const event = makeEvent(eventName, detail)\n    if (htmx.logger && !ignoreEventForLogging(eventName)) {\n      htmx.logger(elt, eventName, detail)\n    }\n    if (detail.error) {\n      logError(detail.error)\n      triggerEvent(elt, 'htmx:error', { errorInfo: detail })\n    }\n    let eventResult = elt.dispatchEvent(event)\n    const kebabName = kebabEventName(eventName)\n    if (eventResult && kebabName !== eventName) {\n      const kebabedEvent = makeEvent(kebabName, event.detail)\n      eventResult = eventResult && elt.dispatchEvent(kebabedEvent)\n    }\n    withExtensions(asElement(elt), function(extension) {\n      eventResult = eventResult && (extension.onEvent(eventName, event) !== false && !event.defaultPrevented)\n    })\n    return eventResult\n  }\n\n  //= ===================================================================\n  // History Support\n  //= ===================================================================\n  let currentPathForHistory = location.pathname + location.search\n\n  /**\n   * @param {string} path\n   */\n  function setCurrentPathForHistory(path) {\n    currentPathForHistory = path\n    if (canAccessLocalStorage()) {\n      sessionStorage.setItem('htmx-current-path-for-history', path)\n    }\n  }\n\n  /**\n   * @returns {Element}\n   */\n  function getHistoryElement() {\n    const historyElt = getDocument().querySelector('[hx-history-elt],[data-hx-history-elt]')\n    return historyElt || getDocument().body\n  }\n\n  /**\n   * @param {string} url\n   * @param {Element} rootElt\n   */\n  function saveToHistoryCache(url, rootElt) {\n    if (!canAccessLocalStorage()) {\n      return\n    }\n\n    // get state to save\n    const innerHTML = cleanInnerHtmlForHistory(rootElt)\n    const title = getDocument().title\n    const scroll = window.scrollY\n\n    if (htmx.config.historyCacheSize <= 0) {\n      // make sure that an eventually already existing cache is purged\n      sessionStorage.removeItem('htmx-history-cache')\n      return\n    }\n\n    url = normalizePath(url)\n\n    const historyCache = parseJSON(sessionStorage.getItem('htmx-history-cache')) || []\n    for (let i = 0; i < historyCache.length; i++) {\n      if (historyCache[i].url === url) {\n        historyCache.splice(i, 1)\n        break\n      }\n    }\n\n    /** @type HtmxHistoryItem */\n    const newHistoryItem = { url, content: innerHTML, title, scroll }\n\n    triggerEvent(getDocument().body, 'htmx:historyItemCreated', { item: newHistoryItem, cache: historyCache })\n\n    historyCache.push(newHistoryItem)\n    while (historyCache.length > htmx.config.historyCacheSize) {\n      historyCache.shift()\n    }\n\n    // keep trying to save the cache until it succeeds or is empty\n    while (historyCache.length > 0) {\n      try {\n        sessionStorage.setItem('htmx-history-cache', JSON.stringify(historyCache))\n        break\n      } catch (e) {\n        triggerErrorEvent(getDocument().body, 'htmx:historyCacheError', { cause: e, cache: historyCache })\n        historyCache.shift() // shrink the cache and retry\n      }\n    }\n  }\n\n  /**\n   * @typedef {Object} HtmxHistoryItem\n   * @property {string} url\n   * @property {string} content\n   * @property {string} title\n   * @property {number} scroll\n   */\n\n  /**\n   * @param {string} url\n   * @returns {HtmxHistoryItem|null}\n   */\n  function getCachedHistory(url) {\n    if (!canAccessLocalStorage()) {\n      return null\n    }\n\n    url = normalizePath(url)\n\n    const historyCache = parseJSON(sessionStorage.getItem('htmx-history-cache')) || []\n    for (let i = 0; i < historyCache.length; i++) {\n      if (historyCache[i].url === url) {\n        return historyCache[i]\n      }\n    }\n    return null\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {string}\n   */\n  function cleanInnerHtmlForHistory(elt) {\n    const className = htmx.config.requestClass\n    const clone = /** @type Element */ (elt.cloneNode(true))\n    forEach(findAll(clone, '.' + className), function(child) {\n      removeClassFromElement(child, className)\n    })\n    // remove the disabled attribute for any element disabled due to an htmx request\n    forEach(findAll(clone, '[data-disabled-by-htmx]'), function(child) {\n      child.removeAttribute('disabled')\n    })\n    return clone.innerHTML\n  }\n\n  function saveCurrentPageToHistory() {\n    const elt = getHistoryElement()\n    let path = currentPathForHistory\n    if (canAccessLocalStorage()) {\n      path = sessionStorage.getItem('htmx-current-path-for-history')\n    }\n    path = path || location.pathname + location.search\n\n    // Allow history snapshot feature to be disabled where hx-history=\"false\"\n    // is present *anywhere* in the current document we're about to save,\n    // so we can prevent privileged data entering the cache.\n    // The page will still be reachable as a history entry, but htmx will fetch it\n    // live from the server onpopstate rather than look in the sessionStorage cache\n    const disableHistoryCache = getDocument().querySelector('[hx-history=\"false\" i],[data-hx-history=\"false\" i]')\n    if (!disableHistoryCache) {\n      triggerEvent(getDocument().body, 'htmx:beforeHistorySave', { path, historyElt: elt })\n      saveToHistoryCache(path, elt)\n    }\n\n    if (htmx.config.historyEnabled) history.replaceState({ htmx: true }, getDocument().title, location.href)\n  }\n\n  /**\n   * @param {string} path\n   */\n  function pushUrlIntoHistory(path) {\n  // remove the cache buster parameter, if any\n    if (htmx.config.getCacheBusterParam) {\n      path = path.replace(/org\\.htmx\\.cache-buster=[^&]*&?/, '')\n      if (endsWith(path, '&') || endsWith(path, '?')) {\n        path = path.slice(0, -1)\n      }\n    }\n    if (htmx.config.historyEnabled) {\n      history.pushState({ htmx: true }, '', path)\n    }\n    setCurrentPathForHistory(path)\n  }\n\n  /**\n   * @param {string} path\n   */\n  function replaceUrlInHistory(path) {\n    if (htmx.config.historyEnabled) history.replaceState({ htmx: true }, '', path)\n    setCurrentPathForHistory(path)\n  }\n\n  /**\n   * @param {HtmxSettleTask[]} tasks\n   */\n  function settleImmediately(tasks) {\n    forEach(tasks, function(task) {\n      task.call(undefined)\n    })\n  }\n\n  /**\n   * @param {string} path\n   */\n  function loadHistoryFromServer(path) {\n    const request = new XMLHttpRequest()\n    const swapSpec = { swapStyle: 'innerHTML', swapDelay: 0, settleDelay: 0 }\n    const details = { path, xhr: request, historyElt: getHistoryElement(), swapSpec }\n    request.open('GET', path, true)\n    if (htmx.config.historyRestoreAsHxRequest) {\n      request.setRequestHeader('HX-Request', 'true')\n    }\n    request.setRequestHeader('HX-History-Restore-Request', 'true')\n    request.setRequestHeader('HX-Current-URL', location.href)\n    request.onload = function() {\n      if (this.status >= 200 && this.status < 400) {\n        details.response = this.response\n        triggerEvent(getDocument().body, 'htmx:historyCacheMissLoad', details)\n        swap(details.historyElt, details.response, swapSpec, {\n          contextElement: details.historyElt,\n          historyRequest: true\n        })\n        setCurrentPathForHistory(details.path)\n        triggerEvent(getDocument().body, 'htmx:historyRestore', { path, cacheMiss: true, serverResponse: details.response })\n      } else {\n        triggerErrorEvent(getDocument().body, 'htmx:historyCacheMissLoadError', details)\n      }\n    }\n    if (triggerEvent(getDocument().body, 'htmx:historyCacheMiss', details)) {\n      request.send() // only send request if event not prevented\n    }\n  }\n\n  /**\n   * @param {string} [path]\n   */\n  function restoreHistory(path) {\n    saveCurrentPageToHistory()\n    path = path || location.pathname + location.search\n    const cached = getCachedHistory(path)\n    if (cached) {\n      const swapSpec = { swapStyle: 'innerHTML', swapDelay: 0, settleDelay: 0, scroll: cached.scroll }\n      const details = { path, item: cached, historyElt: getHistoryElement(), swapSpec }\n      if (triggerEvent(getDocument().body, 'htmx:historyCacheHit', details)) {\n        swap(details.historyElt, cached.content, swapSpec, {\n          contextElement: details.historyElt,\n          title: cached.title\n        })\n        setCurrentPathForHistory(details.path)\n        triggerEvent(getDocument().body, 'htmx:historyRestore', details)\n      }\n    } else {\n      if (htmx.config.refreshOnHistoryMiss) {\n        // @ts-ignore: optional parameter in reload() function throws error\n        // noinspection JSUnresolvedReference\n        htmx.location.reload(true)\n      } else {\n        loadHistoryFromServer(path)\n      }\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {Element[]}\n   */\n  function addRequestIndicatorClasses(elt) {\n    let indicators = /** @type Element[] */ (findAttributeTargets(elt, 'hx-indicator'))\n    if (indicators == null) {\n      indicators = [elt]\n    }\n    forEach(indicators, function(ic) {\n      const internalData = getInternalData(ic)\n      internalData.requestCount = (internalData.requestCount || 0) + 1\n      ic.classList.add.call(ic.classList, htmx.config.requestClass)\n    })\n    return indicators\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {Element[]}\n   */\n  function disableElements(elt) {\n    let disabledElts = /** @type Element[] */ (findAttributeTargets(elt, 'hx-disabled-elt'))\n    if (disabledElts == null) {\n      disabledElts = []\n    }\n    forEach(disabledElts, function(disabledElement) {\n      const internalData = getInternalData(disabledElement)\n      internalData.requestCount = (internalData.requestCount || 0) + 1\n      disabledElement.setAttribute('disabled', '')\n      disabledElement.setAttribute('data-disabled-by-htmx', '')\n    })\n    return disabledElts\n  }\n\n  /**\n   * @param {Element[]} indicators\n   * @param {Element[]} disabled\n   */\n  function removeRequestIndicators(indicators, disabled) {\n    forEach(indicators.concat(disabled), function(ele) {\n      const internalData = getInternalData(ele)\n      internalData.requestCount = (internalData.requestCount || 1) - 1\n    })\n    forEach(indicators, function(ic) {\n      const internalData = getInternalData(ic)\n      if (internalData.requestCount === 0) {\n        ic.classList.remove.call(ic.classList, htmx.config.requestClass)\n      }\n    })\n    forEach(disabled, function(disabledElement) {\n      const internalData = getInternalData(disabledElement)\n      if (internalData.requestCount === 0) {\n        disabledElement.removeAttribute('disabled')\n        disabledElement.removeAttribute('data-disabled-by-htmx')\n      }\n    })\n  }\n\n  //= ===================================================================\n  // Input Value Processing\n  //= ===================================================================\n\n  /**\n   * @param {Element[]} processed\n   * @param {Element} elt\n   * @returns {boolean}\n   */\n  function haveSeenNode(processed, elt) {\n    for (let i = 0; i < processed.length; i++) {\n      const node = processed[i]\n      if (node.isSameNode(elt)) {\n        return true\n      }\n    }\n    return false\n  }\n\n  /**\n   * @param {Element} element\n   * @return {boolean}\n   */\n  function shouldInclude(element) {\n    // Cast to trick tsc, undefined values will work fine here\n    const elt = /** @type {HTMLInputElement} */ (element)\n    if (elt.name === '' || elt.name == null || elt.disabled || closest(elt, 'fieldset[disabled]')) {\n      return false\n    }\n    // ignore \"submitter\" types (see jQuery src/serialize.js)\n    if (elt.type === 'button' || elt.type === 'submit' || elt.tagName === 'image' || elt.tagName === 'reset' || elt.tagName === 'file') {\n      return false\n    }\n    if (elt.type === 'checkbox' || elt.type === 'radio') {\n      return elt.checked\n    }\n    return true\n  }\n\n  /**\n   * @param {string} name\n   * @param {string|Array|FormDataEntryValue} value\n   * @param {FormData} formData */\n  function addValueToFormData(name, value, formData) {\n    if (name != null && value != null) {\n      if (Array.isArray(value)) {\n        value.forEach(function(v) { formData.append(name, v) })\n      } else {\n        formData.append(name, value)\n      }\n    }\n  }\n\n  /**\n   * @param {string} name\n   * @param {string|Array} value\n   * @param {FormData} formData */\n  function removeValueFromFormData(name, value, formData) {\n    if (name != null && value != null) {\n      let values = formData.getAll(name)\n      if (Array.isArray(value)) {\n        values = values.filter(v => value.indexOf(v) < 0)\n      } else {\n        values = values.filter(v => v !== value)\n      }\n      formData.delete(name)\n      forEach(values, v => formData.append(name, v))\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @returns {string|Array}\n   */\n  function getValueFromInput(elt) {\n    if (elt instanceof HTMLSelectElement && elt.multiple) {\n      return toArray(elt.querySelectorAll('option:checked')).map(function(e) { return (/** @type HTMLOptionElement */(e)).value })\n    }\n    // include file inputs\n    if (elt instanceof HTMLInputElement && elt.files) {\n      return toArray(elt.files)\n    }\n    // @ts-ignore value will be undefined for non-input elements, which is fine\n    return elt.value\n  }\n\n  /**\n   * @param {Element[]} processed\n   * @param {FormData} formData\n   * @param {HtmxElementValidationError[]} errors\n   * @param {Element|HTMLInputElement|HTMLSelectElement|HTMLFormElement} elt\n   * @param {boolean} validate\n   */\n  function processInputValue(processed, formData, errors, elt, validate) {\n    if (elt == null || haveSeenNode(processed, elt)) {\n      return\n    } else {\n      processed.push(elt)\n    }\n    if (shouldInclude(elt)) {\n      const name = getRawAttribute(elt, 'name')\n      addValueToFormData(name, getValueFromInput(elt), formData)\n      if (validate) {\n        validateElement(elt, errors)\n      }\n    }\n    if (elt instanceof HTMLFormElement) {\n      forEach(elt.elements, function(input) {\n        if (processed.indexOf(input) >= 0) {\n          // The input has already been processed and added to the values, but the FormData that will be\n          //  constructed right after on the form, will include it once again. So remove that input's value\n          //  now to avoid duplicates\n          removeValueFromFormData(input.name, getValueFromInput(input), formData)\n        } else {\n          processed.push(input)\n        }\n        if (validate) {\n          validateElement(input, errors)\n        }\n      })\n      new FormData(elt).forEach(function(value, name) {\n        if (value instanceof File && value.name === '') {\n          return // ignore no-name files\n        }\n        addValueToFormData(name, value, formData)\n      })\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxElementValidationError[]} errors\n   */\n  function validateElement(elt, errors) {\n    const element = /** @type {HTMLElement & ElementInternals} */ (elt)\n    if (element.willValidate) {\n      triggerEvent(element, 'htmx:validation:validate')\n      if (!element.checkValidity()) {\n        errors.push({ elt: element, message: element.validationMessage, validity: element.validity })\n        triggerEvent(element, 'htmx:validation:failed', { message: element.validationMessage, validity: element.validity })\n      }\n    }\n  }\n\n  /**\n   * Override values in the one FormData with those from another.\n   * @param {FormData} receiver the formdata that will be mutated\n   * @param {FormData} donor the formdata that will provide the overriding values\n   * @returns {FormData} the {@linkcode receiver}\n   */\n  function overrideFormData(receiver, donor) {\n    for (const key of donor.keys()) {\n      receiver.delete(key)\n    }\n    donor.forEach(function(value, key) {\n      receiver.append(key, value)\n    })\n    return receiver\n  }\n\n  /**\n * @param {Element|HTMLFormElement} elt\n * @param {HttpVerb} verb\n * @returns {{errors: HtmxElementValidationError[], formData: FormData, values: Object}}\n */\n  function getInputValues(elt, verb) {\n    /** @type Element[] */\n    const processed = []\n    const formData = new FormData()\n    const priorityFormData = new FormData()\n    /** @type HtmxElementValidationError[] */\n    const errors = []\n    const internalData = getInternalData(elt)\n    if (internalData.lastButtonClicked && !bodyContains(internalData.lastButtonClicked)) {\n      internalData.lastButtonClicked = null\n    }\n\n    // only validate when form is directly submitted and novalidate or formnovalidate are not set\n    // or if the element has an explicit hx-validate=\"true\" on it\n    let validate = (elt instanceof HTMLFormElement && elt.noValidate !== true) || getAttributeValue(elt, 'hx-validate') === 'true'\n    if (internalData.lastButtonClicked) {\n      validate = validate && internalData.lastButtonClicked.formNoValidate !== true\n    }\n\n    // for a non-GET include the related form, which may or may not be a parent element of elt\n    if (verb !== 'get') {\n      processInputValue(processed, priorityFormData, errors, getRelatedForm(elt), validate)\n    }\n\n    // include the element itself\n    processInputValue(processed, formData, errors, elt, validate)\n\n    // if a button or submit was clicked last, include its value\n    if (internalData.lastButtonClicked || elt.tagName === 'BUTTON' ||\n    (elt.tagName === 'INPUT' && getRawAttribute(elt, 'type') === 'submit')) {\n      const button = internalData.lastButtonClicked || (/** @type HTMLInputElement|HTMLButtonElement */(elt))\n      const name = getRawAttribute(button, 'name')\n      addValueToFormData(name, button.value, priorityFormData)\n    }\n\n    // include any explicit includes\n    const includes = findAttributeTargets(elt, 'hx-include')\n    forEach(includes, function(node) {\n      processInputValue(processed, formData, errors, asElement(node), validate)\n      // if a non-form is included, include any input values within it\n      if (!matches(node, 'form')) {\n        forEach(asParentNode(node).querySelectorAll(INPUT_SELECTOR), function(descendant) {\n          processInputValue(processed, formData, errors, descendant, validate)\n        })\n      }\n    })\n\n    // values from a <form> take precedence, overriding the regular values\n    overrideFormData(formData, priorityFormData)\n\n    return { errors, formData, values: formDataProxy(formData) }\n  }\n\n  /**\n   * @param {string} returnStr\n   * @param {string} name\n   * @param {any} realValue\n   * @returns {string}\n   */\n  function appendParam(returnStr, name, realValue) {\n    if (returnStr !== '') {\n      returnStr += '&'\n    }\n    if (String(realValue) === '[object Object]') {\n      realValue = JSON.stringify(realValue)\n    }\n    const s = encodeURIComponent(realValue)\n    returnStr += encodeURIComponent(name) + '=' + s\n    return returnStr\n  }\n\n  /**\n   * @param {FormData|Object} values\n   * @returns string\n   */\n  function urlEncode(values) {\n    values = formDataFromObject(values)\n    let returnStr = ''\n    values.forEach(function(value, key) {\n      returnStr = appendParam(returnStr, key, value)\n    })\n    return returnStr\n  }\n\n  //= ===================================================================\n  // Ajax\n  //= ===================================================================\n\n  /**\n * @param {Element} elt\n * @param {Element} target\n * @param {string} prompt\n * @returns {HtmxHeaderSpecification}\n */\n  function getHeaders(elt, target, prompt) {\n    /** @type HtmxHeaderSpecification */\n    const headers = {\n      'HX-Request': 'true',\n      'HX-Trigger': getRawAttribute(elt, 'id'),\n      'HX-Trigger-Name': getRawAttribute(elt, 'name'),\n      'HX-Target': getAttributeValue(target, 'id'),\n      'HX-Current-URL': location.href\n    }\n    getValuesForElement(elt, 'hx-headers', false, headers)\n    if (prompt !== undefined) {\n      headers['HX-Prompt'] = prompt\n    }\n    if (getInternalData(elt).boosted) {\n      headers['HX-Boosted'] = 'true'\n    }\n    return headers\n  }\n\n  /**\n * filterValues takes an object containing form input values\n * and returns a new object that only contains keys that are\n * specified by the closest \"hx-params\" attribute\n * @param {FormData} inputValues\n * @param {Element} elt\n * @returns {FormData}\n */\n  function filterValues(inputValues, elt) {\n    const paramsValue = getClosestAttributeValue(elt, 'hx-params')\n    if (paramsValue) {\n      if (paramsValue === 'none') {\n        return new FormData()\n      } else if (paramsValue === '*') {\n        return inputValues\n      } else if (paramsValue.indexOf('not ') === 0) {\n        forEach(paramsValue.slice(4).split(','), function(name) {\n          name = name.trim()\n          inputValues.delete(name)\n        })\n        return inputValues\n      } else {\n        const newValues = new FormData()\n        forEach(paramsValue.split(','), function(name) {\n          name = name.trim()\n          if (inputValues.has(name)) {\n            inputValues.getAll(name).forEach(function(value) { newValues.append(name, value) })\n          }\n        })\n        return newValues\n      }\n    } else {\n      return inputValues\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @return {boolean}\n   */\n  function isAnchorLink(elt) {\n    return !!getRawAttribute(elt, 'href') && getRawAttribute(elt, 'href').indexOf('#') >= 0\n  }\n\n  /**\n * @param {Element} elt\n * @param {HtmxSwapStyle} [swapInfoOverride]\n * @returns {HtmxSwapSpecification}\n */\n  function getSwapSpecification(elt, swapInfoOverride) {\n    const swapInfo = swapInfoOverride || getClosestAttributeValue(elt, 'hx-swap')\n    /** @type HtmxSwapSpecification */\n    const swapSpec = {\n      swapStyle: getInternalData(elt).boosted ? 'innerHTML' : htmx.config.defaultSwapStyle,\n      swapDelay: htmx.config.defaultSwapDelay,\n      settleDelay: htmx.config.defaultSettleDelay\n    }\n    if (htmx.config.scrollIntoViewOnBoost && getInternalData(elt).boosted && !isAnchorLink(elt)) {\n      swapSpec.show = 'top'\n    }\n    if (swapInfo) {\n      const split = splitOnWhitespace(swapInfo)\n      if (split.length > 0) {\n        for (let i = 0; i < split.length; i++) {\n          const value = split[i]\n          if (value.indexOf('swap:') === 0) {\n            swapSpec.swapDelay = parseInterval(value.slice(5))\n          } else if (value.indexOf('settle:') === 0) {\n            swapSpec.settleDelay = parseInterval(value.slice(7))\n          } else if (value.indexOf('transition:') === 0) {\n            swapSpec.transition = value.slice(11) === 'true'\n          } else if (value.indexOf('ignoreTitle:') === 0) {\n            swapSpec.ignoreTitle = value.slice(12) === 'true'\n          } else if (value.indexOf('scroll:') === 0) {\n            const scrollSpec = value.slice(7)\n            var splitSpec = scrollSpec.split(':')\n            const scrollVal = splitSpec.pop()\n            var selectorVal = splitSpec.length > 0 ? splitSpec.join(':') : null\n            // @ts-ignore\n            swapSpec.scroll = scrollVal\n            swapSpec.scrollTarget = selectorVal\n          } else if (value.indexOf('show:') === 0) {\n            const showSpec = value.slice(5)\n            var splitSpec = showSpec.split(':')\n            const showVal = splitSpec.pop()\n            var selectorVal = splitSpec.length > 0 ? splitSpec.join(':') : null\n            swapSpec.show = showVal\n            swapSpec.showTarget = selectorVal\n          } else if (value.indexOf('focus-scroll:') === 0) {\n            const focusScrollVal = value.slice('focus-scroll:'.length)\n            swapSpec.focusScroll = focusScrollVal == 'true'\n          } else if (i == 0) {\n            swapSpec.swapStyle = value\n          } else {\n            logError('Unknown modifier in hx-swap: ' + value)\n          }\n        }\n      }\n    }\n    return swapSpec\n  }\n\n  /**\n   * @param {Element} elt\n   * @return {boolean}\n   */\n  function usesFormData(elt) {\n    return getClosestAttributeValue(elt, 'hx-encoding') === 'multipart/form-data' ||\n    (matches(elt, 'form') && getRawAttribute(elt, 'enctype') === 'multipart/form-data')\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @param {Element} elt\n   * @param {FormData} filteredParameters\n   * @returns {*|string|null}\n   */\n  function encodeParamsForBody(xhr, elt, filteredParameters) {\n    let encodedParameters = null\n    withExtensions(elt, function(extension) {\n      if (encodedParameters == null) {\n        encodedParameters = extension.encodeParameters(xhr, filteredParameters, elt)\n      }\n    })\n    if (encodedParameters != null) {\n      return encodedParameters\n    } else {\n      if (usesFormData(elt)) {\n        // Force conversion to an actual FormData object in case filteredParameters is a formDataProxy\n        // See https://github.com/bigskysoftware/htmx/issues/2317\n        return overrideFormData(new FormData(), formDataFromObject(filteredParameters))\n      } else {\n        return urlEncode(filteredParameters)\n      }\n    }\n  }\n\n  /**\n *\n * @param {Element} target\n * @returns {HtmxSettleInfo}\n */\n  function makeSettleInfo(target) {\n    return { tasks: [], elts: [target] }\n  }\n\n  /**\n   * @param {Element[]} content\n   * @param {HtmxSwapSpecification} swapSpec\n   */\n  function updateScrollState(content, swapSpec) {\n    const first = content[0]\n    const last = content[content.length - 1]\n    if (swapSpec.scroll) {\n      var target = null\n      if (swapSpec.scrollTarget) {\n        target = asElement(querySelectorExt(first, swapSpec.scrollTarget))\n      }\n      if (swapSpec.scroll === 'top' && (first || target)) {\n        target = target || first\n        target.scrollTop = 0\n      }\n      if (swapSpec.scroll === 'bottom' && (last || target)) {\n        target = target || last\n        target.scrollTop = target.scrollHeight\n      }\n      if (typeof swapSpec.scroll === 'number') {\n        getWindow().setTimeout(function() {\n          window.scrollTo(0, /** @type number */ (swapSpec.scroll))\n        }, 0) // next 'tick', so browser has time to render layout\n      }\n    }\n    if (swapSpec.show) {\n      var target = null\n      if (swapSpec.showTarget) {\n        let targetStr = swapSpec.showTarget\n        if (swapSpec.showTarget === 'window') {\n          targetStr = 'body'\n        }\n        target = asElement(querySelectorExt(first, targetStr))\n      }\n      if (swapSpec.show === 'top' && (first || target)) {\n        target = target || first\n        // @ts-ignore For some reason tsc doesn't recognize \"instant\" as a valid option for now\n        target.scrollIntoView({ block: 'start', behavior: htmx.config.scrollBehavior })\n      }\n      if (swapSpec.show === 'bottom' && (last || target)) {\n        target = target || last\n        // @ts-ignore For some reason tsc doesn't recognize \"instant\" as a valid option for now\n        target.scrollIntoView({ block: 'end', behavior: htmx.config.scrollBehavior })\n      }\n    }\n  }\n\n  /**\n * @param {Element} elt\n * @param {string} attr\n * @param {boolean=} evalAsDefault\n * @param {Object=} values\n * @param {Event=} event\n * @returns {Object}\n */\n  function getValuesForElement(elt, attr, evalAsDefault, values, event) {\n    if (values == null) {\n      values = {}\n    }\n    if (elt == null) {\n      return values\n    }\n    const attributeValue = getAttributeValue(elt, attr)\n    if (attributeValue) {\n      let str = attributeValue.trim()\n      let evaluateValue = evalAsDefault\n      if (str === 'unset') {\n        return null\n      }\n      if (str.indexOf('javascript:') === 0) {\n        str = str.slice(11)\n        evaluateValue = true\n      } else if (str.indexOf('js:') === 0) {\n        str = str.slice(3)\n        evaluateValue = true\n      }\n      if (str.indexOf('{') !== 0) {\n        str = '{' + str + '}'\n      }\n      let varsValues\n      if (evaluateValue) {\n        varsValues = maybeEval(elt, function() {\n          if (event) {\n            return Function('event', 'return (' + str + ')').call(elt, event)\n          } else { // allow window.event to be accessible\n            return Function('return (' + str + ')').call(elt)\n          }\n        }, {})\n      } else {\n        varsValues = parseJSON(str)\n      }\n      for (const key in varsValues) {\n        if (varsValues.hasOwnProperty(key)) {\n          if (values[key] == null) {\n            values[key] = varsValues[key]\n          }\n        }\n      }\n    }\n    return getValuesForElement(asElement(parentElt(elt)), attr, evalAsDefault, values, event)\n  }\n\n  /**\n   * @param {EventTarget|string} elt\n   * @param {() => any} toEval\n   * @param {any=} defaultVal\n   * @returns {any}\n   */\n  function maybeEval(elt, toEval, defaultVal) {\n    if (htmx.config.allowEval) {\n      return toEval()\n    } else {\n      triggerErrorEvent(elt, 'htmx:evalDisallowedError')\n      return defaultVal\n    }\n  }\n\n  /**\n * @param {Element} elt\n * @param {Event=} event\n * @param {*?=} expressionVars\n * @returns\n */\n  function getHXVarsForElement(elt, event, expressionVars) {\n    return getValuesForElement(elt, 'hx-vars', true, expressionVars, event)\n  }\n\n  /**\n * @param {Element} elt\n * @param {Event=} event\n * @param {*?=} expressionVars\n * @returns\n */\n  function getHXValsForElement(elt, event, expressionVars) {\n    return getValuesForElement(elt, 'hx-vals', false, expressionVars, event)\n  }\n\n  /**\n * @param {Element} elt\n * @param {Event=} event\n * @returns {FormData}\n */\n  function getExpressionVars(elt, event) {\n    return mergeObjects(getHXVarsForElement(elt, event), getHXValsForElement(elt, event))\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @param {string} header\n   * @param {string|null} headerValue\n   */\n  function safelySetHeaderValue(xhr, header, headerValue) {\n    if (headerValue !== null) {\n      try {\n        xhr.setRequestHeader(header, headerValue)\n      } catch (e) {\n      // On an exception, try to set the header URI encoded instead\n        xhr.setRequestHeader(header, encodeURIComponent(headerValue))\n        xhr.setRequestHeader(header + '-URI-AutoEncoded', 'true')\n      }\n    }\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @return {string}\n   */\n  function getPathFromResponse(xhr) {\n    if (xhr.responseURL) {\n      try {\n        const url = new URL(xhr.responseURL)\n        return url.pathname + url.search\n      } catch (e) {\n        triggerErrorEvent(getDocument().body, 'htmx:badResponseUrl', { url: xhr.responseURL })\n      }\n    }\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @param {RegExp} regexp\n   * @return {boolean}\n   */\n  function hasHeader(xhr, regexp) {\n    return regexp.test(xhr.getAllResponseHeaders())\n  }\n\n  /**\n   * Issues an htmx-style AJAX request\n   *\n   * @see https://htmx.org/api/#ajax\n   *\n   * @param {HttpVerb} verb\n   * @param {string} path the URL path to make the AJAX\n   * @param {Element|string|HtmxAjaxHelperContext} context the element to target (defaults to the **body**) | a selector for the target | a context object that contains any of the following\n   * @return {Promise<void>} Promise that resolves immediately if no request is sent, or when the request is complete\n   */\n  function ajaxHelper(verb, path, context) {\n    verb = (/** @type HttpVerb */(verb.toLowerCase()))\n    if (context) {\n      if (context instanceof Element || typeof context === 'string') {\n        return issueAjaxRequest(verb, path, null, null, {\n          targetOverride: resolveTarget(context) || DUMMY_ELT,\n          returnPromise: true\n        })\n      } else {\n        let resolvedTarget = resolveTarget(context.target)\n        // If target is supplied but can't resolve OR source is supplied but both target and source can't be resolved\n        // then use DUMMY_ELT to abort the request with htmx:targetError to avoid it replacing body by mistake\n        if ((context.target && !resolvedTarget) || (context.source && !resolvedTarget && !resolveTarget(context.source))) {\n          resolvedTarget = DUMMY_ELT\n        }\n        return issueAjaxRequest(verb, path, resolveTarget(context.source), context.event,\n          {\n            handler: context.handler,\n            headers: context.headers,\n            values: context.values,\n            targetOverride: resolvedTarget,\n            swapOverride: context.swap,\n            select: context.select,\n            returnPromise: true\n          })\n      }\n    } else {\n      return issueAjaxRequest(verb, path, null, null, {\n        returnPromise: true\n      })\n    }\n  }\n\n  /**\n   * @param {Element} elt\n   * @return {Element[]}\n   */\n  function hierarchyForElt(elt) {\n    const arr = []\n    while (elt) {\n      arr.push(elt)\n      elt = elt.parentElement\n    }\n    return arr\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {string} path\n   * @param {HtmxRequestConfig} requestConfig\n   * @return {boolean}\n   */\n  function verifyPath(elt, path, requestConfig) {\n    const url = new URL(path, location.protocol !== 'about:' ? location.href : window.origin)\n    const origin = location.protocol !== 'about:' ? location.origin : window.origin\n    const sameHost = origin === url.origin\n\n    if (htmx.config.selfRequestsOnly) {\n      if (!sameHost) {\n        return false\n      }\n    }\n    return triggerEvent(elt, 'htmx:validateUrl', mergeObjects({ url, sameHost }, requestConfig))\n  }\n\n  /**\n   * @param {Object|FormData} obj\n   * @return {FormData}\n   */\n  function formDataFromObject(obj) {\n    if (obj instanceof FormData) return obj\n    const formData = new FormData()\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        if (obj[key] && typeof obj[key].forEach === 'function') {\n          obj[key].forEach(function(v) { formData.append(key, v) })\n        } else if (typeof obj[key] === 'object' && !(obj[key] instanceof Blob)) {\n          formData.append(key, JSON.stringify(obj[key]))\n        } else {\n          formData.append(key, obj[key])\n        }\n      }\n    }\n    return formData\n  }\n\n  /**\n   * @param {FormData} formData\n   * @param {string} name\n   * @param {Array} array\n   * @returns {Array}\n   */\n  function formDataArrayProxy(formData, name, array) {\n    // mutating the array should mutate the underlying form data\n    return new Proxy(array, {\n      get: function(target, key) {\n        if (typeof key === 'number') return target[key]\n        if (key === 'length') return target.length\n        if (key === 'push') {\n          return function(value) {\n            target.push(value)\n            formData.append(name, value)\n          }\n        }\n        if (typeof target[key] === 'function') {\n          return function() {\n            target[key].apply(target, arguments)\n            formData.delete(name)\n            target.forEach(function(v) { formData.append(name, v) })\n          }\n        }\n\n        if (target[key] && target[key].length === 1) {\n          return target[key][0]\n        } else {\n          return target[key]\n        }\n      },\n      set: function(target, index, value) {\n        target[index] = value\n        formData.delete(name)\n        target.forEach(function(v) { formData.append(name, v) })\n        return true\n      }\n    })\n  }\n\n  /**\n   * @param {FormData} formData\n   * @returns {Object}\n   */\n  function formDataProxy(formData) {\n    return new Proxy(formData, {\n      get: function(target, name) {\n        if (typeof name === 'symbol') {\n          // Forward symbol calls to the FormData itself directly\n          const result = Reflect.get(target, name)\n          // Wrap in function with apply to correctly bind the FormData context, as a direct call would result in an illegal invocation error\n          if (typeof result === 'function') {\n            return function() {\n              return result.apply(formData, arguments)\n            }\n          } else {\n            return result\n          }\n        }\n        if (name === 'toJSON') {\n          // Support JSON.stringify call on proxy\n          return () => Object.fromEntries(formData)\n        }\n        if (name in target) {\n          // Wrap in function with apply to correctly bind the FormData context, as a direct call would result in an illegal invocation error\n          if (typeof target[name] === 'function') {\n            return function() {\n              return formData[name].apply(formData, arguments)\n            }\n          }\n        }\n        const array = formData.getAll(name)\n        // Those 2 undefined & single value returns are for retro-compatibility as we weren't using FormData before\n        if (array.length === 0) {\n          return undefined\n        } else if (array.length === 1) {\n          return array[0]\n        } else {\n          return formDataArrayProxy(target, name, array)\n        }\n      },\n      set: function(target, name, value) {\n        if (typeof name !== 'string') {\n          return false\n        }\n        target.delete(name)\n        if (value && typeof value.forEach === 'function') {\n          value.forEach(function(v) { target.append(name, v) })\n        } else if (typeof value === 'object' && !(value instanceof Blob)) {\n          target.append(name, JSON.stringify(value))\n        } else {\n          target.append(name, value)\n        }\n        return true\n      },\n      deleteProperty: function(target, name) {\n        if (typeof name === 'string') {\n          target.delete(name)\n        }\n        return true\n      },\n      // Support Object.assign call from proxy\n      ownKeys: function(target) {\n        return Reflect.ownKeys(Object.fromEntries(target))\n      },\n      getOwnPropertyDescriptor: function(target, prop) {\n        return Reflect.getOwnPropertyDescriptor(Object.fromEntries(target), prop)\n      }\n    })\n  }\n\n  /**\n   * @param {HttpVerb} verb\n   * @param {string} path\n   * @param {Element} elt\n   * @param {Event} event\n   * @param {HtmxAjaxEtc} [etc]\n   * @param {boolean} [confirmed]\n   * @return {Promise<void>}\n   */\n  function issueAjaxRequest(verb, path, elt, event, etc, confirmed) {\n    let resolve = null\n    let reject = null\n    etc = etc != null ? etc : {}\n    if (etc.returnPromise && typeof Promise !== 'undefined') {\n      var promise = new Promise(function(_resolve, _reject) {\n        resolve = _resolve\n        reject = _reject\n      })\n    }\n    if (elt == null) {\n      elt = getDocument().body\n    }\n    const responseHandler = etc.handler || handleAjaxResponse\n    const select = etc.select || null\n\n    if (!bodyContains(elt)) {\n    // do not issue requests for elements removed from the DOM\n      maybeCall(resolve)\n      return promise\n    }\n    const target = etc.targetOverride || asElement(getTarget(elt))\n    if (target == null || target == DUMMY_ELT) {\n      triggerErrorEvent(elt, 'htmx:targetError', { target: getClosestAttributeValue(elt, 'hx-target') })\n      maybeCall(reject)\n      return promise\n    }\n\n    let eltData = getInternalData(elt)\n    const submitter = eltData.lastButtonClicked\n\n    if (submitter) {\n      const buttonPath = getRawAttribute(submitter, 'formaction')\n      if (buttonPath != null) {\n        path = buttonPath\n      }\n\n      const buttonVerb = getRawAttribute(submitter, 'formmethod')\n      if (buttonVerb != null) {\n        if (VERBS.includes(buttonVerb.toLowerCase())) {\n          verb = (/** @type HttpVerb */(buttonVerb))\n        } else {\n          maybeCall(resolve)\n          return promise\n        }\n      }\n    }\n\n    const confirmQuestion = getClosestAttributeValue(elt, 'hx-confirm')\n    // allow event-based confirmation w/ a callback\n    if (confirmed === undefined) {\n      const issueRequest = function(skipConfirmation) {\n        return issueAjaxRequest(verb, path, elt, event, etc, !!skipConfirmation)\n      }\n      const confirmDetails = { target, elt, path, verb, triggeringEvent: event, etc, issueRequest, question: confirmQuestion }\n      if (triggerEvent(elt, 'htmx:confirm', confirmDetails) === false) {\n        maybeCall(resolve)\n        return promise\n      }\n    }\n\n    let syncElt = elt\n    let syncStrategy = getClosestAttributeValue(elt, 'hx-sync')\n    let queueStrategy = null\n    let abortable = false\n    if (syncStrategy) {\n      const syncStrings = syncStrategy.split(':')\n      const selector = syncStrings[0].trim()\n      if (selector === 'this') {\n        syncElt = findThisElement(elt, 'hx-sync')\n      } else {\n        syncElt = asElement(querySelectorExt(elt, selector))\n      }\n      // default to the drop strategy\n      syncStrategy = (syncStrings[1] || 'drop').trim()\n      eltData = getInternalData(syncElt)\n      if (syncStrategy === 'drop' && eltData.xhr && eltData.abortable !== true) {\n        maybeCall(resolve)\n        return promise\n      } else if (syncStrategy === 'abort') {\n        if (eltData.xhr) {\n          maybeCall(resolve)\n          return promise\n        } else {\n          abortable = true\n        }\n      } else if (syncStrategy === 'replace') {\n        triggerEvent(syncElt, 'htmx:abort') // abort the current request and continue\n      } else if (syncStrategy.indexOf('queue') === 0) {\n        const queueStrArray = syncStrategy.split(' ')\n        queueStrategy = (queueStrArray[1] || 'last').trim()\n      }\n    }\n\n    if (eltData.xhr) {\n      if (eltData.abortable) {\n        triggerEvent(syncElt, 'htmx:abort') // abort the current request and continue\n      } else {\n        if (queueStrategy == null) {\n          if (event) {\n            const eventData = getInternalData(event)\n            if (eventData && eventData.triggerSpec && eventData.triggerSpec.queue) {\n              queueStrategy = eventData.triggerSpec.queue\n            }\n          }\n          if (queueStrategy == null) {\n            queueStrategy = 'last'\n          }\n        }\n        if (eltData.queuedRequests == null) {\n          eltData.queuedRequests = []\n        }\n        if (queueStrategy === 'first' && eltData.queuedRequests.length === 0) {\n          eltData.queuedRequests.push(function() {\n            issueAjaxRequest(verb, path, elt, event, etc)\n          })\n        } else if (queueStrategy === 'all') {\n          eltData.queuedRequests.push(function() {\n            issueAjaxRequest(verb, path, elt, event, etc)\n          })\n        } else if (queueStrategy === 'last') {\n          eltData.queuedRequests = [] // dump existing queue\n          eltData.queuedRequests.push(function() {\n            issueAjaxRequest(verb, path, elt, event, etc)\n          })\n        }\n        maybeCall(resolve)\n        return promise\n      }\n    }\n\n    const xhr = new XMLHttpRequest()\n    eltData.xhr = xhr\n    eltData.abortable = abortable\n    const endRequestLock = function() {\n      eltData.xhr = null\n      eltData.abortable = false\n      if (eltData.queuedRequests != null &&\n      eltData.queuedRequests.length > 0) {\n        const queuedRequest = eltData.queuedRequests.shift()\n        queuedRequest()\n      }\n    }\n    const promptQuestion = getClosestAttributeValue(elt, 'hx-prompt')\n    if (promptQuestion) {\n      var promptResponse = prompt(promptQuestion)\n      // prompt returns null if cancelled and empty string if accepted with no entry\n      if (promptResponse === null ||\n      !triggerEvent(elt, 'htmx:prompt', { prompt: promptResponse, target })) {\n        maybeCall(resolve)\n        endRequestLock()\n        return promise\n      }\n    }\n\n    if (confirmQuestion && !confirmed) {\n      if (!confirm(confirmQuestion)) {\n        maybeCall(resolve)\n        endRequestLock()\n        return promise\n      }\n    }\n\n    let headers = getHeaders(elt, target, promptResponse)\n\n    if (verb !== 'get' && !usesFormData(elt)) {\n      headers['Content-Type'] = 'application/x-www-form-urlencoded'\n    }\n\n    if (etc.headers) {\n      headers = mergeObjects(headers, etc.headers)\n    }\n    const results = getInputValues(elt, verb)\n    let errors = results.errors\n    const rawFormData = results.formData\n    if (etc.values) {\n      overrideFormData(rawFormData, formDataFromObject(etc.values))\n    }\n    const expressionVars = formDataFromObject(getExpressionVars(elt, event))\n    const allFormData = overrideFormData(rawFormData, expressionVars)\n    let filteredFormData = filterValues(allFormData, elt)\n\n    if (htmx.config.getCacheBusterParam && verb === 'get') {\n      filteredFormData.set('org.htmx.cache-buster', getRawAttribute(target, 'id') || 'true')\n    }\n\n    // behavior of anchors w/ empty href is to use the current URL\n    if (path == null || path === '') {\n      path = location.href\n    }\n\n    /**\n     * @type {Object}\n     * @property {boolean} [credentials]\n     * @property {number} [timeout]\n     * @property {boolean} [noHeaders]\n     */\n    const requestAttrValues = getValuesForElement(elt, 'hx-request')\n\n    const eltIsBoosted = getInternalData(elt).boosted\n\n    let useUrlParams = htmx.config.methodsThatUseUrlParams.indexOf(verb) >= 0\n\n    /** @type HtmxRequestConfig */\n    const requestConfig = {\n      boosted: eltIsBoosted,\n      useUrlParams,\n      formData: filteredFormData,\n      parameters: formDataProxy(filteredFormData),\n      unfilteredFormData: allFormData,\n      unfilteredParameters: formDataProxy(allFormData),\n      headers,\n      elt,\n      target,\n      verb,\n      errors,\n      withCredentials: etc.credentials || requestAttrValues.credentials || htmx.config.withCredentials,\n      timeout: etc.timeout || requestAttrValues.timeout || htmx.config.timeout,\n      path,\n      triggeringEvent: event\n    }\n\n    if (!triggerEvent(elt, 'htmx:configRequest', requestConfig)) {\n      maybeCall(resolve)\n      endRequestLock()\n      return promise\n    }\n\n    // copy out in case the object was overwritten\n    path = requestConfig.path\n    verb = requestConfig.verb\n    headers = requestConfig.headers\n    filteredFormData = formDataFromObject(requestConfig.parameters)\n    errors = requestConfig.errors\n    useUrlParams = requestConfig.useUrlParams\n\n    if (errors && errors.length > 0) {\n      triggerEvent(elt, 'htmx:validation:halted', requestConfig)\n      maybeCall(resolve)\n      endRequestLock()\n      return promise\n    }\n\n    const splitPath = path.split('#')\n    const pathNoAnchor = splitPath[0]\n    const anchor = splitPath[1]\n\n    let finalPath = path\n    if (useUrlParams) {\n      finalPath = pathNoAnchor\n      const hasValues = !filteredFormData.keys().next().done\n      if (hasValues) {\n        if (finalPath.indexOf('?') < 0) {\n          finalPath += '?'\n        } else {\n          finalPath += '&'\n        }\n        finalPath += urlEncode(filteredFormData)\n        if (anchor) {\n          finalPath += '#' + anchor\n        }\n      }\n    }\n\n    if (!verifyPath(elt, finalPath, requestConfig)) {\n      triggerErrorEvent(elt, 'htmx:invalidPath', requestConfig)\n      maybeCall(reject)\n      endRequestLock()\n      return promise\n    }\n\n    xhr.open(verb.toUpperCase(), finalPath, true)\n    xhr.overrideMimeType('text/html')\n    xhr.withCredentials = requestConfig.withCredentials\n    xhr.timeout = requestConfig.timeout\n\n    // request headers\n    if (requestAttrValues.noHeaders) {\n    // ignore all headers\n    } else {\n      for (const header in headers) {\n        if (headers.hasOwnProperty(header)) {\n          const headerValue = headers[header]\n          safelySetHeaderValue(xhr, header, headerValue)\n        }\n      }\n    }\n\n    /** @type {HtmxResponseInfo} */\n    const responseInfo = {\n      xhr,\n      target,\n      requestConfig,\n      etc,\n      boosted: eltIsBoosted,\n      select,\n      pathInfo: {\n        requestPath: path,\n        finalRequestPath: finalPath,\n        responsePath: null,\n        anchor\n      }\n    }\n\n    xhr.onload = function() {\n      try {\n        const hierarchy = hierarchyForElt(elt)\n        responseInfo.pathInfo.responsePath = getPathFromResponse(xhr)\n        responseHandler(elt, responseInfo)\n        if (responseInfo.keepIndicators !== true) {\n          removeRequestIndicators(indicators, disableElts)\n        }\n        triggerEvent(elt, 'htmx:afterRequest', responseInfo)\n        triggerEvent(elt, 'htmx:afterOnLoad', responseInfo)\n        // if the body no longer contains the element, trigger the event on the closest parent\n        // remaining in the DOM\n        if (!bodyContains(elt)) {\n          let secondaryTriggerElt = null\n          while (hierarchy.length > 0 && secondaryTriggerElt == null) {\n            const parentEltInHierarchy = hierarchy.shift()\n            if (bodyContains(parentEltInHierarchy)) {\n              secondaryTriggerElt = parentEltInHierarchy\n            }\n          }\n          if (secondaryTriggerElt) {\n            triggerEvent(secondaryTriggerElt, 'htmx:afterRequest', responseInfo)\n            triggerEvent(secondaryTriggerElt, 'htmx:afterOnLoad', responseInfo)\n          }\n        }\n        maybeCall(resolve)\n      } catch (e) {\n        triggerErrorEvent(elt, 'htmx:onLoadError', mergeObjects({ error: e }, responseInfo))\n        throw e\n      } finally {\n        endRequestLock()\n      }\n    }\n    xhr.onerror = function() {\n      removeRequestIndicators(indicators, disableElts)\n      triggerErrorEvent(elt, 'htmx:afterRequest', responseInfo)\n      triggerErrorEvent(elt, 'htmx:sendError', responseInfo)\n      maybeCall(reject)\n      endRequestLock()\n    }\n    xhr.onabort = function() {\n      removeRequestIndicators(indicators, disableElts)\n      triggerErrorEvent(elt, 'htmx:afterRequest', responseInfo)\n      triggerErrorEvent(elt, 'htmx:sendAbort', responseInfo)\n      maybeCall(reject)\n      endRequestLock()\n    }\n    xhr.ontimeout = function() {\n      removeRequestIndicators(indicators, disableElts)\n      triggerErrorEvent(elt, 'htmx:afterRequest', responseInfo)\n      triggerErrorEvent(elt, 'htmx:timeout', responseInfo)\n      maybeCall(reject)\n      endRequestLock()\n    }\n    if (!triggerEvent(elt, 'htmx:beforeRequest', responseInfo)) {\n      maybeCall(resolve)\n      endRequestLock()\n      return promise\n    }\n    var indicators = addRequestIndicatorClasses(elt)\n    var disableElts = disableElements(elt)\n\n    forEach(['loadstart', 'loadend', 'progress', 'abort'], function(eventName) {\n      forEach([xhr, xhr.upload], function(target) {\n        target.addEventListener(eventName, function(event) {\n          triggerEvent(elt, 'htmx:xhr:' + eventName, {\n            lengthComputable: event.lengthComputable,\n            loaded: event.loaded,\n            total: event.total\n          })\n        })\n      })\n    })\n    triggerEvent(elt, 'htmx:beforeSend', responseInfo)\n    const params = useUrlParams ? null : encodeParamsForBody(xhr, elt, filteredFormData)\n    xhr.send(params)\n    return promise\n  }\n\n  /**\n   * @typedef {Object} HtmxHistoryUpdate\n   * @property {string|null} [type]\n   * @property {string|null} [path]\n   */\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxResponseInfo} responseInfo\n   * @return {HtmxHistoryUpdate}\n   */\n  function determineHistoryUpdates(elt, responseInfo) {\n    const xhr = responseInfo.xhr\n\n    //= ==========================================\n    // First consult response headers\n    //= ==========================================\n    let pathFromHeaders = null\n    let typeFromHeaders = null\n    if (hasHeader(xhr, /HX-Push:/i)) {\n      pathFromHeaders = xhr.getResponseHeader('HX-Push')\n      typeFromHeaders = 'push'\n    } else if (hasHeader(xhr, /HX-Push-Url:/i)) {\n      pathFromHeaders = xhr.getResponseHeader('HX-Push-Url')\n      typeFromHeaders = 'push'\n    } else if (hasHeader(xhr, /HX-Replace-Url:/i)) {\n      pathFromHeaders = xhr.getResponseHeader('HX-Replace-Url')\n      typeFromHeaders = 'replace'\n    }\n\n    // if there was a response header, that has priority\n    if (pathFromHeaders) {\n      if (pathFromHeaders === 'false') {\n        return {}\n      } else {\n        return {\n          type: typeFromHeaders,\n          path: pathFromHeaders\n        }\n      }\n    }\n\n    //= ==========================================\n    // Next resolve via DOM values\n    //= ==========================================\n    const requestPath = responseInfo.pathInfo.finalRequestPath\n    const responsePath = responseInfo.pathInfo.responsePath\n\n    const pushUrl = getClosestAttributeValue(elt, 'hx-push-url')\n    const replaceUrl = getClosestAttributeValue(elt, 'hx-replace-url')\n    const elementIsBoosted = getInternalData(elt).boosted\n\n    let saveType = null\n    let path = null\n\n    if (pushUrl) {\n      saveType = 'push'\n      path = pushUrl\n    } else if (replaceUrl) {\n      saveType = 'replace'\n      path = replaceUrl\n    } else if (elementIsBoosted) {\n      saveType = 'push'\n      path = responsePath || requestPath // if there is no response path, go with the original request path\n    }\n\n    if (path) {\n    // false indicates no push, return empty object\n      if (path === 'false') {\n        return {}\n      }\n\n      // true indicates we want to follow wherever the server ended up sending us\n      if (path === 'true') {\n        path = responsePath || requestPath // if there is no response path, go with the original request path\n      }\n\n      // restore any anchor associated with the request\n      if (responseInfo.pathInfo.anchor && path.indexOf('#') === -1) {\n        path = path + '#' + responseInfo.pathInfo.anchor\n      }\n\n      return {\n        type: saveType,\n        path\n      }\n    } else {\n      return {}\n    }\n  }\n\n  /**\n   * @param {HtmxResponseHandlingConfig} responseHandlingConfig\n   * @param {number} status\n   * @return {boolean}\n   */\n  function codeMatches(responseHandlingConfig, status) {\n    var regExp = new RegExp(responseHandlingConfig.code)\n    return regExp.test(status.toString(10))\n  }\n\n  /**\n   * @param {XMLHttpRequest} xhr\n   * @return {HtmxResponseHandlingConfig}\n   */\n  function resolveResponseHandling(xhr) {\n    for (var i = 0; i < htmx.config.responseHandling.length; i++) {\n      /** @type HtmxResponseHandlingConfig */\n      var responseHandlingElement = htmx.config.responseHandling[i]\n      if (codeMatches(responseHandlingElement, xhr.status)) {\n        return responseHandlingElement\n      }\n    }\n    // no matches, return no swap\n    return {\n      swap: false\n    }\n  }\n\n  /**\n   * @param {string} title\n   */\n  function handleTitle(title) {\n    if (title) {\n      const titleElt = find('title')\n      if (titleElt) {\n        titleElt.textContent = title\n      } else {\n        window.document.title = title\n      }\n    }\n  }\n\n  /**\n   * Resove the Retarget selector and throw if not found\n   * @param {Element} elt\n   * @param {String} target\n   * @returns {Element}\n   */\n  function resolveRetarget(elt, target) {\n    if (target === 'this') {\n      return elt\n    }\n    const resolvedTarget = asElement(querySelectorExt(elt, target))\n    if (resolvedTarget == null) {\n      triggerErrorEvent(elt, 'htmx:targetError', { target })\n      throw new Error(`Invalid re-target ${target}`)\n    }\n    return resolvedTarget\n  }\n\n  /**\n   * @param {Element} elt\n   * @param {HtmxResponseInfo} responseInfo\n   */\n  function handleAjaxResponse(elt, responseInfo) {\n    const xhr = responseInfo.xhr\n    let target = responseInfo.target\n    const etc = responseInfo.etc\n    const responseInfoSelect = responseInfo.select\n\n    if (!triggerEvent(elt, 'htmx:beforeOnLoad', responseInfo)) return\n\n    if (hasHeader(xhr, /HX-Trigger:/i)) {\n      handleTriggerHeader(xhr, 'HX-Trigger', elt)\n    }\n\n    if (hasHeader(xhr, /HX-Location:/i)) {\n      saveCurrentPageToHistory()\n      let redirectPath = xhr.getResponseHeader('HX-Location')\n      /** @type {HtmxAjaxHelperContext&{path:string}} */\n      var redirectSwapSpec\n      if (redirectPath.indexOf('{') === 0) {\n        redirectSwapSpec = parseJSON(redirectPath)\n        // what's the best way to throw an error if the user didn't include this\n        redirectPath = redirectSwapSpec.path\n        delete redirectSwapSpec.path\n      }\n      ajaxHelper('get', redirectPath, redirectSwapSpec).then(function() {\n        pushUrlIntoHistory(redirectPath)\n      })\n      return\n    }\n\n    const shouldRefresh = hasHeader(xhr, /HX-Refresh:/i) && xhr.getResponseHeader('HX-Refresh') === 'true'\n\n    if (hasHeader(xhr, /HX-Redirect:/i)) {\n      responseInfo.keepIndicators = true\n      htmx.location.href = xhr.getResponseHeader('HX-Redirect')\n      shouldRefresh && htmx.location.reload()\n      return\n    }\n\n    if (shouldRefresh) {\n      responseInfo.keepIndicators = true\n      htmx.location.reload()\n      return\n    }\n\n    const historyUpdate = determineHistoryUpdates(elt, responseInfo)\n\n    const responseHandling = resolveResponseHandling(xhr)\n    const shouldSwap = responseHandling.swap\n    let isError = !!responseHandling.error\n    let ignoreTitle = htmx.config.ignoreTitle || responseHandling.ignoreTitle\n    let selectOverride = responseHandling.select\n    if (responseHandling.target) {\n      responseInfo.target = resolveRetarget(elt, responseHandling.target)\n    }\n    var swapOverride = etc.swapOverride\n    if (swapOverride == null && responseHandling.swapOverride) {\n      swapOverride = responseHandling.swapOverride\n    }\n\n    // response headers override response handling config\n    if (hasHeader(xhr, /HX-Retarget:/i)) {\n      responseInfo.target = resolveRetarget(elt, xhr.getResponseHeader('HX-Retarget'))\n    }\n\n    if (hasHeader(xhr, /HX-Reswap:/i)) {\n      swapOverride = xhr.getResponseHeader('HX-Reswap')\n    }\n\n    var serverResponse = xhr.response\n    /** @type HtmxBeforeSwapDetails */\n    var beforeSwapDetails = mergeObjects({\n      shouldSwap,\n      serverResponse,\n      isError,\n      ignoreTitle,\n      selectOverride,\n      swapOverride\n    }, responseInfo)\n\n    if (responseHandling.event && !triggerEvent(target, responseHandling.event, beforeSwapDetails)) return\n\n    if (!triggerEvent(target, 'htmx:beforeSwap', beforeSwapDetails)) return\n\n    target = beforeSwapDetails.target // allow re-targeting\n    serverResponse = beforeSwapDetails.serverResponse // allow updating content\n    isError = beforeSwapDetails.isError // allow updating error\n    ignoreTitle = beforeSwapDetails.ignoreTitle // allow updating ignoring title\n    selectOverride = beforeSwapDetails.selectOverride // allow updating select override\n    swapOverride = beforeSwapDetails.swapOverride // allow updating swap override\n\n    responseInfo.target = target // Make updated target available to response events\n    responseInfo.failed = isError // Make failed property available to response events\n    responseInfo.successful = !isError // Make successful property available to response events\n\n    if (beforeSwapDetails.shouldSwap) {\n      if (xhr.status === 286) {\n        cancelPolling(elt)\n      }\n\n      withExtensions(elt, function(extension) {\n        serverResponse = extension.transformResponse(serverResponse, xhr, elt)\n      })\n\n      // Save current page if there will be a history update\n      if (historyUpdate.type) {\n        saveCurrentPageToHistory()\n      }\n\n      var swapSpec = getSwapSpecification(elt, swapOverride)\n\n      if (!swapSpec.hasOwnProperty('ignoreTitle')) {\n        swapSpec.ignoreTitle = ignoreTitle\n      }\n\n      target.classList.add(htmx.config.swappingClass)\n\n      if (responseInfoSelect) {\n        selectOverride = responseInfoSelect\n      }\n\n      if (hasHeader(xhr, /HX-Reselect:/i)) {\n        selectOverride = xhr.getResponseHeader('HX-Reselect')\n      }\n\n      const selectOOB = getClosestAttributeValue(elt, 'hx-select-oob')\n      const select = getClosestAttributeValue(elt, 'hx-select')\n\n      swap(target, serverResponse, swapSpec, {\n        select: selectOverride === 'unset' ? null : selectOverride || select,\n        selectOOB,\n        eventInfo: responseInfo,\n        anchor: responseInfo.pathInfo.anchor,\n        contextElement: elt,\n        afterSwapCallback: function() {\n          if (hasHeader(xhr, /HX-Trigger-After-Swap:/i)) {\n            let finalElt = elt\n            if (!bodyContains(elt)) {\n              finalElt = getDocument().body\n            }\n            handleTriggerHeader(xhr, 'HX-Trigger-After-Swap', finalElt)\n          }\n        },\n        afterSettleCallback: function() {\n          if (hasHeader(xhr, /HX-Trigger-After-Settle:/i)) {\n            let finalElt = elt\n            if (!bodyContains(elt)) {\n              finalElt = getDocument().body\n            }\n            handleTriggerHeader(xhr, 'HX-Trigger-After-Settle', finalElt)\n          }\n        },\n        beforeSwapCallback: function() {\n          // if we need to save history, do so, before swapping so that relative resources have the correct base URL\n          if (historyUpdate.type) {\n            triggerEvent(getDocument().body, 'htmx:beforeHistoryUpdate', mergeObjects({ history: historyUpdate }, responseInfo))\n            if (historyUpdate.type === 'push') {\n              pushUrlIntoHistory(historyUpdate.path)\n              triggerEvent(getDocument().body, 'htmx:pushedIntoHistory', { path: historyUpdate.path })\n            } else {\n              replaceUrlInHistory(historyUpdate.path)\n              triggerEvent(getDocument().body, 'htmx:replacedInHistory', { path: historyUpdate.path })\n            }\n          }\n        }\n      })\n    }\n    if (isError) {\n      triggerErrorEvent(elt, 'htmx:responseError', mergeObjects({ error: 'Response Status Error Code ' + xhr.status + ' from ' + responseInfo.pathInfo.requestPath }, responseInfo))\n    }\n  }\n\n  //= ===================================================================\n  // Extensions API\n  //= ===================================================================\n\n  /** @type {Object<string, HtmxExtension>} */\n  const extensions = {}\n\n  /**\n   * extensionBase defines the default functions for all extensions.\n   * @returns {HtmxExtension}\n   */\n  function extensionBase() {\n    return {\n      init: function(api) { return null },\n      getSelectors: function() { return null },\n      onEvent: function(name, evt) { return true },\n      transformResponse: function(text, xhr, elt) { return text },\n      isInlineSwap: function(swapStyle) { return false },\n      handleSwap: function(swapStyle, target, fragment, settleInfo) { return false },\n      encodeParameters: function(xhr, parameters, elt) { return null }\n    }\n  }\n\n  /**\n   * defineExtension initializes the extension and adds it to the htmx registry\n   *\n   * @see https://htmx.org/api/#defineExtension\n   *\n   * @param {string} name the extension name\n   * @param {Partial<HtmxExtension>} extension the extension definition\n   */\n  function defineExtension(name, extension) {\n    if (extension.init) {\n      extension.init(internalAPI)\n    }\n    extensions[name] = mergeObjects(extensionBase(), extension)\n  }\n\n  /**\n   * removeExtension removes an extension from the htmx registry\n   *\n   * @see https://htmx.org/api/#removeExtension\n   *\n   * @param {string} name\n   */\n  function removeExtension(name) {\n    delete extensions[name]\n  }\n\n  /**\n   * getExtensions searches up the DOM tree to return all extensions that can be applied to a given element\n   *\n   * @param {Element} elt\n   * @param {HtmxExtension[]=} extensionsToReturn\n   * @param {string[]=} extensionsToIgnore\n   * @returns {HtmxExtension[]}\n   */\n  function getExtensions(elt, extensionsToReturn, extensionsToIgnore) {\n    if (extensionsToReturn == undefined) {\n      extensionsToReturn = []\n    }\n    if (elt == undefined) {\n      return extensionsToReturn\n    }\n    if (extensionsToIgnore == undefined) {\n      extensionsToIgnore = []\n    }\n    const extensionsForElement = getAttributeValue(elt, 'hx-ext')\n    if (extensionsForElement) {\n      forEach(extensionsForElement.split(','), function(extensionName) {\n        extensionName = extensionName.replace(/ /g, '')\n        if (extensionName.slice(0, 7) == 'ignore:') {\n          extensionsToIgnore.push(extensionName.slice(7))\n          return\n        }\n        if (extensionsToIgnore.indexOf(extensionName) < 0) {\n          const extension = extensions[extensionName]\n          if (extension && extensionsToReturn.indexOf(extension) < 0) {\n            extensionsToReturn.push(extension)\n          }\n        }\n      })\n    }\n    return getExtensions(asElement(parentElt(elt)), extensionsToReturn, extensionsToIgnore)\n  }\n\n  //= ===================================================================\n  // Initialization\n  //= ===================================================================\n  var isReady = false\n  getDocument().addEventListener('DOMContentLoaded', function() {\n    isReady = true\n  })\n\n  /**\n   * Execute a function now if DOMContentLoaded has fired, otherwise listen for it.\n   *\n   * This function uses isReady because there is no reliable way to ask the browser whether\n   * the DOMContentLoaded event has already been fired; there's a gap between DOMContentLoaded\n   * firing and readystate=complete.\n   */\n  function ready(fn) {\n    // Checking readyState here is a failsafe in case the htmx script tag entered the DOM by\n    // some means other than the initial page load.\n    if (isReady || getDocument().readyState === 'complete') {\n      fn()\n    } else {\n      getDocument().addEventListener('DOMContentLoaded', fn)\n    }\n  }\n\n  function insertIndicatorStyles() {\n    if (htmx.config.includeIndicatorStyles !== false) {\n      const nonceAttribute = htmx.config.inlineStyleNonce ? ` nonce=\"${htmx.config.inlineStyleNonce}\"` : ''\n      getDocument().head.insertAdjacentHTML('beforeend',\n        '<style' + nonceAttribute + '>\\\n      .' + htmx.config.indicatorClass + '{opacity:0}\\\n      .' + htmx.config.requestClass + ' .' + htmx.config.indicatorClass + '{opacity:1; transition: opacity 200ms ease-in;}\\\n      .' + htmx.config.requestClass + '.' + htmx.config.indicatorClass + '{opacity:1; transition: opacity 200ms ease-in;}\\\n      </style>')\n    }\n  }\n\n  function getMetaConfig() {\n    /** @type HTMLMetaElement */\n    const element = getDocument().querySelector('meta[name=\"htmx-config\"]')\n    if (element) {\n      return parseJSON(element.content)\n    } else {\n      return null\n    }\n  }\n\n  function mergeMetaConfig() {\n    const metaConfig = getMetaConfig()\n    if (metaConfig) {\n      htmx.config = mergeObjects(htmx.config, metaConfig)\n    }\n  }\n\n  // initialize the document\n  ready(function() {\n    mergeMetaConfig()\n    insertIndicatorStyles()\n    let body = getDocument().body\n    processNode(body)\n    const restoredElts = getDocument().querySelectorAll(\n      \"[hx-trigger='restored'],[data-hx-trigger='restored']\"\n    )\n    body.addEventListener('htmx:abort', function(evt) {\n      const target = evt.target\n      const internalData = getInternalData(target)\n      if (internalData && internalData.xhr) {\n        internalData.xhr.abort()\n      }\n    })\n    /** @type {(ev: PopStateEvent) => any} */\n    const originalPopstate = window.onpopstate ? window.onpopstate.bind(window) : null\n    /** @type {(ev: PopStateEvent) => any} */\n    window.onpopstate = function(event) {\n      if (event.state && event.state.htmx) {\n        restoreHistory()\n        forEach(restoredElts, function(elt) {\n          triggerEvent(elt, 'htmx:restored', {\n            document: getDocument(),\n            triggerEvent\n          })\n        })\n      } else {\n        if (originalPopstate) {\n          originalPopstate(event)\n        }\n      }\n    }\n    getWindow().setTimeout(function() {\n      triggerEvent(body, 'htmx:load', {}) // give ready handlers a chance to load up before firing this event\n      body = null // kill reference for gc\n    }, 0)\n  })\n\n  return htmx\n})()\n\n/** @typedef {'get'|'head'|'post'|'put'|'delete'|'connect'|'options'|'trace'|'patch'} HttpVerb */\n\n/**\n * @typedef {Object} SwapOptions\n * @property {string} [select]\n * @property {string} [selectOOB]\n * @property {*} [eventInfo]\n * @property {string} [anchor]\n * @property {Element} [contextElement]\n * @property {swapCallback} [afterSwapCallback]\n * @property {swapCallback} [afterSettleCallback]\n * @property {swapCallback} [beforeSwapCallback]\n * @property {string} [title]\n * @property {boolean} [historyRequest]\n */\n\n/**\n * @callback swapCallback\n */\n\n/**\n * @typedef {'innerHTML' | 'outerHTML' | 'beforebegin' | 'afterbegin' | 'beforeend' | 'afterend' | 'delete' | 'none' | string} HtmxSwapStyle\n */\n\n/**\n * @typedef HtmxSwapSpecification\n * @property {HtmxSwapStyle} swapStyle\n * @property {number} swapDelay\n * @property {number} settleDelay\n * @property {boolean} [transition]\n * @property {boolean} [ignoreTitle]\n * @property {string} [head]\n * @property {'top' | 'bottom' | number } [scroll]\n * @property {string} [scrollTarget]\n * @property {string} [show]\n * @property {string} [showTarget]\n * @property {boolean} [focusScroll]\n */\n\n/**\n * @typedef {((this:Node, evt:Event) => boolean) & {source: string}} ConditionalFunction\n */\n\n/**\n * @typedef {Object} HtmxTriggerSpecification\n * @property {string} trigger\n * @property {number} [pollInterval]\n * @property {ConditionalFunction} [eventFilter]\n * @property {boolean} [changed]\n * @property {boolean} [once]\n * @property {boolean} [consume]\n * @property {number} [delay]\n * @property {string} [from]\n * @property {string} [target]\n * @property {number} [throttle]\n * @property {string} [queue]\n * @property {string} [root]\n * @property {string} [threshold]\n */\n\n/**\n * @typedef {{elt: Element, message: string, validity: ValidityState}} HtmxElementValidationError\n */\n\n/**\n * @typedef {Record<string, string>} HtmxHeaderSpecification\n * @property {'true'} HX-Request\n * @property {string|null} HX-Trigger\n * @property {string|null} HX-Trigger-Name\n * @property {string|null} HX-Target\n * @property {string} HX-Current-URL\n * @property {string} [HX-Prompt]\n * @property {'true'} [HX-Boosted]\n * @property {string} [Content-Type]\n * @property {'true'} [HX-History-Restore-Request]\n */\n\n/**\n * @typedef HtmxAjaxHelperContext\n * @property {Element|string} [source]\n * @property {Event} [event]\n * @property {HtmxAjaxHandler} [handler]\n * @property {Element|string} [target]\n * @property {HtmxSwapStyle} [swap]\n * @property {Object|FormData} [values]\n * @property {Record<string,string>} [headers]\n * @property {string} [select]\n */\n\n/**\n * @typedef {Object} HtmxRequestConfig\n * @property {boolean} boosted\n * @property {boolean} useUrlParams\n * @property {FormData} formData\n * @property {Object} parameters formData proxy\n * @property {FormData} unfilteredFormData\n * @property {Object} unfilteredParameters unfilteredFormData proxy\n * @property {HtmxHeaderSpecification} headers\n * @property {Element} elt\n * @property {Element} target\n * @property {HttpVerb} verb\n * @property {HtmxElementValidationError[]} errors\n * @property {boolean} withCredentials\n * @property {number} timeout\n * @property {string} path\n * @property {Event} triggeringEvent\n */\n\n/**\n * @typedef {Object} HtmxResponseInfo\n * @property {XMLHttpRequest} xhr\n * @property {Element} target\n * @property {HtmxRequestConfig} requestConfig\n * @property {HtmxAjaxEtc} etc\n * @property {boolean} boosted\n * @property {string} select\n * @property {{requestPath: string, finalRequestPath: string, responsePath: string|null, anchor: string}} pathInfo\n * @property {boolean} [failed]\n * @property {boolean} [successful]\n * @property {boolean} [keepIndicators]\n */\n\n/**\n * @typedef {Object} HtmxAjaxEtc\n * @property {boolean} [returnPromise]\n * @property {HtmxAjaxHandler} [handler]\n * @property {string} [select]\n * @property {Element} [targetOverride]\n * @property {HtmxSwapStyle} [swapOverride]\n * @property {Record<string,string>} [headers]\n * @property {Object|FormData} [values]\n * @property {boolean} [credentials]\n * @property {number} [timeout]\n */\n\n/**\n * @typedef {Object} HtmxResponseHandlingConfig\n * @property {string} [code]\n * @property {boolean} swap\n * @property {boolean} [error]\n * @property {boolean} [ignoreTitle]\n * @property {string} [select]\n * @property {string} [target]\n * @property {string} [swapOverride]\n * @property {string} [event]\n */\n\n/**\n * @typedef {HtmxResponseInfo & {shouldSwap: boolean, serverResponse: any, isError: boolean, ignoreTitle: boolean, selectOverride:string, swapOverride:string}} HtmxBeforeSwapDetails\n */\n\n/**\n * @callback HtmxAjaxHandler\n * @param {Element} elt\n * @param {HtmxResponseInfo} responseInfo\n */\n\n/**\n * @typedef {(() => void)} HtmxSettleTask\n */\n\n/**\n * @typedef {Object} HtmxSettleInfo\n * @property {HtmxSettleTask[]} tasks\n * @property {Element[]} elts\n * @property {string} [title]\n */\n\n/**\n * @see https://github.com/bigskysoftware/htmx-extensions/blob/main/README.md\n * @typedef {Object} HtmxExtension\n * @property {(api: any) => void} init\n * @property {(name: string, event: CustomEvent) => boolean} onEvent\n * @property {(text: string, xhr: XMLHttpRequest, elt: Element) => string} transformResponse\n * @property {(swapStyle: HtmxSwapStyle) => boolean} isInlineSwap\n * @property {(swapStyle: HtmxSwapStyle, target: Node, fragment: Node, settleInfo: HtmxSettleInfo) => boolean|Node[]} handleSwap\n * @property {(xhr: XMLHttpRequest, parameters: FormData, elt: Node) => *|string|null} encodeParameters\n * @property {() => string[]|null} getSelectors\n */\nexport default htmx\n"], "mappings": ";;;AAAA,IAAIA,QAAQ,WAAW;AACrB;AAGA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA,IAIX,QAAQ;AAAA;AAAA,IAER,SAAS;AAAA;AAAA,IAET,IAAI;AAAA;AAAA,IAEJ,KAAK;AAAA;AAAA,IAEL,SAAS;AAAA;AAAA,IAET,MAAM;AAAA;AAAA;AAAA,IAGN,MAAM;AAAA;AAAA,IAEN,SAAS;AAAA;AAAA,IAET,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUT,QAAQ,SAAS,KAAK,MAAM;AAC1B,YAAM,cAAc,eAAe,KAAK,QAAQ,MAAM;AACtD,aAAO,YAAY;AAAA,IACrB;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA;AAAA,IAER,UAAU;AAAA;AAAA,IAEV,aAAa;AAAA;AAAA,IAEb,aAAa;AAAA;AAAA,IAEb,WAAW;AAAA;AAAA,IAEX,MAAM;AAAA;AAAA;AAAA,IAGN,iBAAiB;AAAA;AAAA,IAEjB,iBAAiB;AAAA;AAAA;AAAA,IAGjB,QAAQ;AAAA;AAAA,IAER,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOT,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQR,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMN,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMhB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlB,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMtB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMpB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMhB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMd,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMZ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMf,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMf,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMX,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMjB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMnB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,oBAAoB,CAAC,SAAS,SAAS,SAAS,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxD,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMT,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAKd,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMhB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMpB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMrB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvB,yBAAyB,CAAC,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMzC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMb,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOvB,mBAAmB;AAAA;AAAA,MAEnB,oBAAoB;AAAA;AAAA,MAEpB,kBAAkB;AAAA,QAChB,EAAE,MAAM,OAAO,MAAM,MAAM;AAAA,QAC3B,EAAE,MAAM,UAAU,MAAM,KAAK;AAAA,QAC7B,EAAE,MAAM,UAAU,MAAM,OAAO,OAAO,KAAK;AAAA,MAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOrB,2BAA2B;AAAA,IAC7B;AAAA;AAAA,IAEA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,IAKf;AAAA;AAAA,IAEA,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAEA,OAAK,SAAS;AACd,OAAK,UAAU;AACf,OAAK,KAAK;AACV,OAAK,MAAM;AACX,OAAK,UAAU;AACf,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,WAAW;AAChB,OAAK,cAAc;AACnB,OAAK,cAAc;AACnB,OAAK,YAAY;AACjB,OAAK,OAAO;AACZ,OAAK,kBAAkB;AACvB,OAAK,kBAAkB;AACvB,OAAK,SAAS;AACd,OAAK,UAAU;AACf,OAAK,gBAAgB;AACrB,OAAK,IAAI;AAET,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,QAAQ,CAAC,OAAO,QAAQ,OAAO,UAAU,OAAO;AACtD,QAAM,gBAAgB,MAAM,IAAI,SAAS,MAAM;AAC7C,WAAO,SAAS,OAAO,iBAAiB,OAAO;AAAA,EACjD,CAAC,EAAE,KAAK,IAAI;AAgBZ,WAAS,cAAcC,MAAK;AAC1B,QAAIA,QAAO,QAAW;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AACf,QAAIA,KAAI,MAAM,EAAE,KAAK,MAAM;AACzB,iBAAW,WAAWA,KAAI,MAAM,GAAG,EAAE,CAAC;AAAA,IACxC,WAAWA,KAAI,MAAM,EAAE,KAAK,KAAK;AAC/B,iBAAW,WAAWA,KAAI,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,IAC5C,WAAWA,KAAI,MAAM,EAAE,KAAK,KAAK;AAC/B,iBAAW,WAAWA,KAAI,MAAM,GAAG,EAAE,CAAC,IAAI,MAAO;AAAA,IACnD,OAAO;AACL,iBAAW,WAAWA,IAAG;AAAA,IAC3B;AACA,WAAO,MAAM,QAAQ,IAAI,SAAY;AAAA,EACvC;AAOA,WAAS,gBAAgB,KAAK,MAAM;AAClC,WAAO,eAAe,WAAW,IAAI,aAAa,IAAI;AAAA,EACxD;AAQA,WAAS,aAAa,KAAK,eAAe;AACxC,WAAO,CAAC,CAAC,IAAI,iBAAiB,IAAI,aAAa,aAAa,KAC1D,IAAI,aAAa,UAAU,aAAa;AAAA,EAC5C;AAQA,WAAS,kBAAkB,KAAK,eAAe;AAC7C,WAAO,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,KAAK,UAAU,aAAa;AAAA,EAC5F;AAMA,WAAS,UAAU,KAAK;AACtB,UAAM,SAAS,IAAI;AACnB,QAAI,CAAC,UAAU,IAAI,sBAAsB,WAAY,QAAO,IAAI;AAChE,WAAO;AAAA,EACT;AAKA,WAAS,cAAc;AACrB,WAAO;AAAA,EACT;AAOA,WAAS,YAAY,KAAK,QAAQ;AAChC,WAAO,IAAI,cAAc,IAAI,YAAY,EAAE,UAAU,OAAO,CAAC,IAAI,YAAY;AAAA,EAC/E;AAOA,WAAS,gBAAgB,KAAK,WAAW;AACvC,WAAO,OAAO,CAAC,UAAU,GAAG,GAAG;AAC7B,YAAM,UAAU,GAAG;AAAA,IACrB;AAEA,WAAO,OAAO;AAAA,EAChB;AAQA,WAAS,oCAAoC,gBAAgB,UAAU,eAAe;AACpF,UAAM,iBAAiB,kBAAkB,UAAU,aAAa;AAChE,UAAM,aAAa,kBAAkB,UAAU,eAAe;AAC9D,QAAI,UAAU,kBAAkB,UAAU,YAAY;AACtD,QAAI,mBAAmB,UAAU;AAC/B,UAAI,KAAK,OAAO,oBAAoB;AAClC,YAAI,YAAY,YAAY,OAAO,QAAQ,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI;AAClF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,eAAe,eAAe,OAAO,WAAW,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI;AAC3F,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAOA,WAAS,yBAAyB,KAAK,eAAe;AACpD,QAAI,cAAc;AAClB,oBAAgB,KAAK,SAAS,GAAG;AAC/B,aAAO,CAAC,EAAE,cAAc,oCAAoC,KAAK,UAAU,CAAC,GAAG,aAAa;AAAA,IAC9F,CAAC;AACD,QAAI,gBAAgB,SAAS;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AAOA,WAAS,QAAQ,KAAK,UAAU;AAC9B,WAAO,eAAe,WAAW,IAAI,QAAQ,QAAQ;AAAA,EACvD;AAMA,WAAS,YAAYA,MAAK;AACxB,UAAM,aAAa;AACnB,UAAM,QAAQ,WAAW,KAAKA,IAAG;AACjC,QAAI,OAAO;AACT,aAAO,MAAM,CAAC,EAAE,YAAY;AAAA,IAC9B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAMA,WAAS,UAAU,MAAM;AACvB,UAAM,SAAS,IAAI,UAAU;AAC7B,WAAO,OAAO,gBAAgB,MAAM,WAAW;AAAA,EACjD;AAMA,WAAS,gBAAgB,UAAU,KAAK;AACtC,WAAO,IAAI,WAAW,SAAS,GAAG;AAChC,eAAS,OAAO,IAAI,WAAW,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AAMA,WAAS,gBAAgB,QAAQ;AAC/B,UAAM,YAAY,YAAY,EAAE,cAAc,QAAQ;AACtD,YAAQ,OAAO,YAAY,SAAS,MAAM;AACxC,gBAAU,aAAa,KAAK,MAAM,KAAK,KAAK;AAAA,IAC9C,CAAC;AACD,cAAU,cAAc,OAAO;AAC/B,cAAU,QAAQ;AAClB,QAAI,KAAK,OAAO,mBAAmB;AACjC,gBAAU,QAAQ,KAAK,OAAO;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAMA,WAAS,uBAAuB,QAAQ;AACtC,WAAO,OAAO,QAAQ,QAAQ,MAAM,OAAO,SAAS,qBAAqB,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,EACvH;AASA,WAAS,oBAAoB,UAAU;AACrC,UAAM,KAAK,SAAS,iBAAiB,QAAQ,CAAC,EAAE;AAAA;AAAA,MAAiD,CAAC,WAAW;AAC3G,YAAI,uBAAuB,MAAM,GAAG;AAClC,gBAAM,YAAY,gBAAgB,MAAM;AACxC,gBAAM,SAAS,OAAO;AACtB,cAAI;AACF,mBAAO,aAAa,WAAW,MAAM;AAAA,UACvC,SAAS,GAAG;AACV,qBAAS,CAAC;AAAA,UACZ,UAAE;AACA,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IAAC;AAAA,EACH;AAYA,WAAS,aAAa,UAAU;AAE9B,UAAM,qBAAqB,SAAS,QAAQ,qCAAqC,EAAE;AACnF,UAAM,WAAW,YAAY,kBAAkB;AAE/C,QAAI;AACJ,QAAI,aAAa,QAAQ;AAEvB;AAAA,MAAmD,IAAI,iBAAiB;AACxE,YAAM,MAAM,UAAU,QAAQ;AAC9B,sBAAgB,UAAU,IAAI,IAAI;AAClC,eAAS,QAAQ,IAAI;AAAA,IACvB,WAAW,aAAa,QAAQ;AAE9B;AAAA,MAAmD,IAAI,iBAAiB;AACxE,YAAM,MAAM,UAAU,kBAAkB;AACxC,sBAAgB,UAAU,IAAI,IAAI;AAClC,eAAS,QAAQ,IAAI;AAAA,IACvB,OAAO;AAEL,YAAM,MAAM,UAAU,mDAAmD,qBAAqB,oBAAoB;AAClH;AAAA,MAAmD,IAAI,cAAc,UAAU,EAAE;AAEjF,eAAS,QAAQ,IAAI;AAGrB,UAAI,eAAe,SAAS,cAAc,OAAO;AACjD,UAAI,gBAAgB,aAAa,eAAe,UAAU;AACxD,qBAAa,OAAO;AACpB,iBAAS,QAAQ,aAAa;AAAA,MAChC;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,KAAK,OAAO,iBAAiB;AAC/B,4BAAoB,QAAQ;AAAA,MAC9B,OAAO;AAEL,iBAAS,iBAAiB,QAAQ,EAAE,QAAQ,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,MACzE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAKA,WAAS,UAAU,MAAM;AACvB,QAAI,MAAM;AACR,WAAK;AAAA,IACP;AAAA,EACF;AAOA,WAAS,OAAO,GAAG,MAAM;AACvB,WAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM,aAAa,OAAO;AAAA,EACnE;AAMA,WAAS,WAAW,GAAG;AACrB,WAAO,OAAO,MAAM;AAAA,EACtB;AAMA,WAAS,YAAY,GAAG;AACtB,WAAO,OAAO,GAAG,QAAQ;AAAA,EAC3B;AAiDA,WAAS,gBAAgB,KAAK;AAC5B,UAAM,WAAW;AACjB,QAAI,OAAO,IAAI,QAAQ;AACvB,QAAI,CAAC,MAAM;AACT,aAAO,IAAI,QAAQ,IAAI,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAQA,WAAS,QAAQ,KAAK;AACpB,UAAM,YAAY,CAAC;AACnB,QAAI,KAAK;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,kBAAU,KAAK,IAAI,CAAC,CAAC;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAOA,WAAS,QAAQ,KAAK,MAAM;AAC1B,QAAI,KAAK;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,aAAK,IAAI,CAAC,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAMA,WAAS,mBAAmB,IAAI;AAC9B,UAAM,OAAO,GAAG,sBAAsB;AACtC,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK;AACxB,WAAO,UAAU,OAAO,eAAe,cAAc;AAAA,EACvD;AASA,WAAS,aAAa,KAAK;AACzB,WAAO,IAAI,YAAY,EAAE,UAAU,KAAK,CAAC,MAAM;AAAA,EACjD;AAMA,WAAS,kBAAkB,SAAS;AAClC,WAAO,QAAQ,KAAK,EAAE,MAAM,KAAK;AAAA,EACnC;AAWA,WAAS,aAAa,MAAM,MAAM;AAChC,eAAW,OAAO,MAAM;AACtB,UAAI,KAAK,eAAe,GAAG,GAAG;AAE5B,aAAK,GAAG,IAAI,KAAK,GAAG;AAAA,MACtB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAMA,WAAS,UAAU,SAAS;AAC1B,QAAI;AACF,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B,SAAS,OAAO;AACd,eAAS,KAAK;AACd,aAAO;AAAA,IACT;AAAA,EACF;AAKA,WAAS,wBAAwB;AAC/B,UAAM,OAAO;AACb,QAAI;AACF,qBAAe,QAAQ,MAAM,IAAI;AACjC,qBAAe,WAAW,IAAI;AAC9B,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAMA,WAAS,cAAc,MAAM;AAE3B,UAAM,MAAM,IAAI,IAAI,MAAM,UAAU;AACpC,QAAI,KAAK;AACP,aAAO,IAAI,WAAW,IAAI;AAAA,IAC5B;AAEA,QAAI,QAAQ,KAAK;AACf,aAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAUA,WAAS,aAAa,KAAK;AACzB,WAAO,UAAU,YAAY,EAAE,MAAM,WAAW;AAC9C,aAAO,KAAK,GAAG;AAAA,IACjB,CAAC;AAAA,EACH;AAUA,WAAS,aAAa,UAAU;AAC9B,UAAM,QAAQ,KAAK;AAAA,MAAG;AAAA;AAAA,MAA6C,SAAS,KAAK;AAC/E,iBAAS,IAAI,OAAO,GAAG;AAAA,MACzB;AAAA,IAAC;AACD,WAAO;AAAA,EACT;AAOA,WAAS,SAAS;AAChB,SAAK,SAAS,SAAS,KAAK,OAAO,MAAM;AACvC,UAAI,SAAS;AACX,gBAAQ,IAAI,OAAO,KAAK,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAEA,WAAS,UAAU;AACjB,SAAK,SAAS;AAAA,EAChB;AAWA,WAAS,KAAK,eAAe,UAAU;AACrC,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO,cAAc,cAAc,QAAQ;AAAA,IAC7C,OAAO;AACL,aAAO,KAAK,YAAY,GAAG,aAAa;AAAA,IAC1C;AAAA,EACF;AAWA,WAAS,QAAQ,eAAe,UAAU;AACxC,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO,cAAc,iBAAiB,QAAQ;AAAA,IAChD,OAAO;AACL,aAAO,QAAQ,YAAY,GAAG,aAAa;AAAA,IAC7C;AAAA,EACF;AAKA,WAAS,YAAY;AACnB,WAAO;AAAA,EACT;AAUA,WAAS,cAAc,KAAK,OAAO;AACjC,UAAM,cAAc,GAAG;AACvB,QAAI,OAAO;AACT,gBAAU,EAAE,WAAW,WAAW;AAChC,sBAAc,GAAG;AACjB,cAAM;AAAA,MACR,GAAG,KAAK;AAAA,IACV,OAAO;AACL,gBAAU,GAAG,EAAE,YAAY,GAAG;AAAA,IAChC;AAAA,EACF;AAMA,WAAS,UAAU,KAAK;AACtB,WAAO,eAAe,UAAU,MAAM;AAAA,EACxC;AAMA,WAAS,cAAc,KAAK;AAC1B,WAAO,eAAe,cAAc,MAAM;AAAA,EAC5C;AAMA,WAAS,SAAS,OAAO;AACvB,WAAO,OAAO,UAAU,WAAW,QAAQ;AAAA,EAC7C;AAMA,WAAS,aAAa,KAAK;AACzB,WAAO,eAAe,WAAW,eAAe,YAAY,eAAe,mBAAmB,MAAM;AAAA,EACtG;AAWA,WAAS,kBAAkB,KAAK,OAAO,OAAO;AAC5C,UAAM,UAAU,cAAc,GAAG,CAAC;AAClC,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,OAAO;AACT,gBAAU,EAAE,WAAW,WAAW;AAChC,0BAAkB,KAAK,KAAK;AAC5B,cAAM;AAAA,MACR,GAAG,KAAK;AAAA,IACV,OAAO;AACL,UAAI,aAAa,IAAI,UAAU,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF;AAWA,WAAS,uBAAuB,MAAM,OAAO,OAAO;AAClD,QAAI,MAAM,UAAU,cAAc,IAAI,CAAC;AACvC,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,OAAO;AACT,gBAAU,EAAE,WAAW,WAAW;AAChC,+BAAuB,KAAK,KAAK;AACjC,cAAM;AAAA,MACR,GAAG,KAAK;AAAA,IACV,OAAO;AACL,UAAI,IAAI,WAAW;AACjB,YAAI,UAAU,OAAO,KAAK;AAE1B,YAAI,IAAI,UAAU,WAAW,GAAG;AAC9B,cAAI,gBAAgB,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAUA,WAAS,qBAAqB,KAAK,OAAO;AACxC,UAAM,cAAc,GAAG;AACvB,QAAI,UAAU,OAAO,KAAK;AAAA,EAC5B;AAUA,WAAS,oBAAoB,KAAK,OAAO;AACvC,UAAM,cAAc,GAAG;AACvB,YAAQ,IAAI,cAAc,UAAU,SAAS,OAAO;AAClD,6BAAuB,OAAO,KAAK;AAAA,IACrC,CAAC;AACD,sBAAkB,UAAU,GAAG,GAAG,KAAK;AAAA,EACzC;AAWA,WAAS,QAAQ,KAAK,UAAU;AAC9B,UAAM,UAAU,cAAc,GAAG,CAAC;AAClC,QAAI,KAAK;AACP,aAAO,IAAI,QAAQ,QAAQ;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAOA,WAAS,WAAWA,MAAK,QAAQ;AAC/B,WAAOA,KAAI,UAAU,GAAG,OAAO,MAAM,MAAM;AAAA,EAC7C;AAOA,WAAS,SAASA,MAAK,QAAQ;AAC7B,WAAOA,KAAI,UAAUA,KAAI,SAAS,OAAO,MAAM,MAAM;AAAA,EACvD;AAMA,WAAS,kBAAkB,UAAU;AACnC,UAAM,kBAAkB,SAAS,KAAK;AACtC,QAAI,WAAW,iBAAiB,GAAG,KAAK,SAAS,iBAAiB,IAAI,GAAG;AACvE,aAAO,gBAAgB,UAAU,GAAG,gBAAgB,SAAS,CAAC;AAAA,IAChE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAQA,WAAS,oBAAoB,KAAK,UAAU,QAAQ;AAClD,QAAI,SAAS,QAAQ,SAAS,MAAM,GAAG;AACrC,aAAO,oBAAoB,KAAK,SAAS,MAAM,CAAC,GAAG,IAAI;AAAA,IACzD;AAEA,UAAM,cAAc,GAAG;AAEvB,UAAM,QAAQ,CAAC;AACf;AACE,UAAI,gBAAgB;AACpB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,cAAM,OAAO,SAAS,CAAC;AACvB,YAAI,SAAS,OAAO,kBAAkB,GAAG;AACvC,gBAAM,KAAK,SAAS,UAAU,QAAQ,CAAC,CAAC;AACxC,mBAAS,IAAI;AACb;AAAA,QACF;AACA,YAAI,SAAS,KAAK;AAChB;AAAA,QACF,WAAW,SAAS,OAAO,IAAI,SAAS,SAAS,KAAK,SAAS,IAAI,CAAC,MAAM,KAAK;AAC7E;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS,SAAS,QAAQ;AAC5B,cAAM,KAAK,SAAS,UAAU,MAAM,CAAC;AAAA,MACvC;AAAA,IACF;AAEA,UAAM,SAAS,CAAC;AAChB,UAAM,mBAAmB,CAAC;AAC1B,WAAO,MAAM,SAAS,GAAG;AACvB,YAAMC,YAAW,kBAAkB,MAAM,MAAM,CAAC;AAChD,UAAI;AACJ,UAAIA,UAAS,QAAQ,UAAU,MAAM,GAAG;AACtC,eAAO,QAAQ,UAAU,GAAG,GAAG,kBAAkBA,UAAS,MAAM,CAAC,CAAC,CAAC;AAAA,MACrE,WAAWA,UAAS,QAAQ,OAAO,MAAM,GAAG;AAC1C,eAAO,KAAK,aAAa,GAAG,GAAG,kBAAkBA,UAAS,MAAM,CAAC,CAAC,CAAC;AAAA,MACrE,WAAWA,cAAa,UAAUA,cAAa,sBAAsB;AACnE,eAAO,UAAU,GAAG,EAAE;AAAA,MACxB,WAAWA,UAAS,QAAQ,OAAO,MAAM,GAAG;AAC1C,eAAO,iBAAiB,KAAK,kBAAkBA,UAAS,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM;AAAA,MAC7E,WAAWA,cAAa,cAAcA,cAAa,0BAA0B;AAC3E,eAAO,UAAU,GAAG,EAAE;AAAA,MACxB,WAAWA,UAAS,QAAQ,WAAW,MAAM,GAAG;AAC9C,eAAO,mBAAmB,KAAK,kBAAkBA,UAAS,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM;AAAA,MAC/E,WAAWA,cAAa,YAAY;AAClC,eAAO;AAAA,MACT,WAAWA,cAAa,UAAU;AAChC,eAAO;AAAA,MACT,WAAWA,cAAa,QAAQ;AAC9B,eAAO,SAAS;AAAA,MAClB,WAAWA,cAAa,QAAQ;AAC9B,eAAO,YAAY,KAAK,CAAC,CAAC,MAAM;AAAA,MAClC,WAAWA,cAAa,QAAQ;AAC9B;AAAA,QAAgC,IAAI,YAAY,EAAI;AAAA,MACtD,OAAO;AACL,yBAAiB,KAAKA,SAAQ;AAAA,MAChC;AAEA,UAAI,MAAM;AACR,eAAO,KAAK,IAAI;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,YAAM,mBAAmB,iBAAiB,KAAK,GAAG;AAClD,YAAM,WAAW,aAAa,YAAY,KAAK,CAAC,CAAC,MAAM,CAAC;AACxD,aAAO,KAAK,GAAG,QAAQ,SAAS,iBAAiB,gBAAgB,CAAC,CAAC;AAAA,IACrE;AAEA,WAAO;AAAA,EACT;AAQA,MAAI,mBAAmB,SAAS,OAAO,OAAO,QAAQ;AACpD,UAAM,UAAU,aAAa,YAAY,OAAO,MAAM,CAAC,EAAE,iBAAiB,KAAK;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,MAAM,QAAQ,CAAC;AACrB,UAAI,IAAI,wBAAwB,KAAK,MAAM,KAAK,6BAA6B;AAC3E,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAQA,MAAI,qBAAqB,SAAS,OAAO,OAAO,QAAQ;AACtD,UAAM,UAAU,aAAa,YAAY,OAAO,MAAM,CAAC,EAAE,iBAAiB,KAAK;AAC/E,aAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,YAAM,MAAM,QAAQ,CAAC;AACrB,UAAI,IAAI,wBAAwB,KAAK,MAAM,KAAK,6BAA6B;AAC3E,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAOA,WAAS,iBAAiB,eAAe,UAAU;AACjD,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO,oBAAoB,eAAe,QAAQ,EAAE,CAAC;AAAA,IACvD,OAAO;AACL,aAAO,oBAAoB,YAAY,EAAE,MAAM,aAAa,EAAE,CAAC;AAAA,IACjE;AAAA,EACF;AAQA,WAAS,cAAc,eAAe,SAAS;AAC7C,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO,KAAK,aAAa,OAAO,KAAK,UAAU,aAAa;AAAA,IAC9D,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAqBA,WAAS,iBAAiB,MAAM,MAAM,MAAM,MAAM;AAChD,QAAI,WAAW,IAAI,GAAG;AACpB,aAAO;AAAA,QACL,QAAQ,YAAY,EAAE;AAAA,QACtB,OAAO,SAAS,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,QAAQ,cAAc,IAAI;AAAA,QAC1B,OAAO,SAAS,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAaA,WAAS,qBAAqB,MAAM,MAAM,MAAM,MAAM;AACpD,UAAM,WAAW;AACf,YAAM,YAAY,iBAAiB,MAAM,MAAM,MAAM,IAAI;AACzD,gBAAU,OAAO,iBAAiB,UAAU,OAAO,UAAU,UAAU,UAAU,OAAO;AAAA,IAC1F,CAAC;AACD,UAAM,IAAI,WAAW,IAAI;AACzB,WAAO,IAAI,OAAO;AAAA,EACpB;AAYA,WAAS,wBAAwB,MAAM,MAAM,MAAM;AACjD,UAAM,WAAW;AACf,YAAM,YAAY,iBAAiB,MAAM,MAAM,IAAI;AACnD,gBAAU,OAAO,oBAAoB,UAAU,OAAO,UAAU,QAAQ;AAAA,IAC1E,CAAC;AACD,WAAO,WAAW,IAAI,IAAI,OAAO;AAAA,EACnC;AAMA,QAAM,YAAY,YAAY,EAAE,cAAc,QAAQ;AAMtD,WAAS,qBAAqB,KAAK,UAAU;AAC3C,UAAM,aAAa,yBAAyB,KAAK,QAAQ;AACzD,QAAI,YAAY;AACd,UAAI,eAAe,QAAQ;AACzB,eAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC;AAAA,MACxC,OAAO;AACL,cAAM,SAAS,oBAAoB,KAAK,UAAU;AAElD,cAAM,gBAAgB,8BAA8B,KAAK,UAAU;AACnE,YAAI,eAAe;AACjB,gBAAM,mBAAmB,UAAU,gBAAgB,KAAK,SAAS,QAAQ;AACvE,mBAAO,WAAW,OAAO,aAAa,UAAU,MAAM,GAAG,QAAQ;AAAA,UACnE,CAAC,CAAC;AACF,cAAI,kBAAkB;AACpB,mBAAO,KAAK,GAAG,qBAAqB,kBAAkB,QAAQ,CAAC;AAAA,UACjE;AAAA,QACF;AACA,YAAI,OAAO,WAAW,GAAG;AACvB,mBAAS,mBAAmB,aAAa,UAAU,WAAW,uBAAuB;AACrF,iBAAO,CAAC,SAAS;AAAA,QACnB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAOA,WAAS,gBAAgB,KAAK,WAAW;AACvC,WAAO,UAAU,gBAAgB,KAAK,SAASC,MAAK;AAClD,aAAO,kBAAkB,UAAUA,IAAG,GAAG,SAAS,KAAK;AAAA,IACzD,CAAC,CAAC;AAAA,EACJ;AAMA,WAAS,UAAU,KAAK;AACtB,UAAM,YAAY,yBAAyB,KAAK,WAAW;AAC3D,QAAI,WAAW;AACb,UAAI,cAAc,QAAQ;AACxB,eAAO,gBAAgB,KAAK,WAAW;AAAA,MACzC,OAAO;AACL,eAAO,iBAAiB,KAAK,SAAS;AAAA,MACxC;AAAA,IACF,OAAO;AACL,YAAM,OAAO,gBAAgB,GAAG;AAChC,UAAI,KAAK,SAAS;AAChB,eAAO,YAAY,EAAE;AAAA,MACvB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAMA,WAAS,sBAAsB,MAAM;AACnC,WAAO,KAAK,OAAO,mBAAmB,SAAS,IAAI;AAAA,EACrD;AAMA,WAAS,gBAAgB,SAAS,WAAW;AAC3C,YAAQ,QAAQ,YAAY,SAAS,MAAM;AACzC,UAAI,CAAC,UAAU,aAAa,KAAK,IAAI,KAAK,sBAAsB,KAAK,IAAI,GAAG;AAC1E,gBAAQ,gBAAgB,KAAK,IAAI;AAAA,MACnC;AAAA,IACF,CAAC;AACD,YAAQ,UAAU,YAAY,SAAS,MAAM;AAC3C,UAAI,sBAAsB,KAAK,IAAI,GAAG;AACpC,gBAAQ,aAAa,KAAK,MAAM,KAAK,KAAK;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAOA,WAAS,aAAa,WAAW,QAAQ;AACvC,UAAMC,cAAa,cAAc,MAAM;AACvC,aAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,YAAM,YAAYA,YAAW,CAAC;AAC9B,UAAI;AACF,YAAI,UAAU,aAAa,SAAS,GAAG;AACrC,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,GAAG;AACV,iBAAS,CAAC;AAAA,MACZ;AAAA,IACF;AACA,WAAO,cAAc;AAAA,EACvB;AASA,WAAS,QAAQ,UAAU,YAAY,YAAY,UAAU;AAC3D,eAAW,YAAY,YAAY;AACnC,QAAI,WAAW,MAAM,IAAI,OAAO,gBAAgB,YAAY,IAAI,CAAC;AAEjE,QAAI,YAAY;AAChB,QAAI,aAAa,QAAQ;AAAA,IAEzB,WAAW,SAAS,QAAQ,GAAG,IAAI,GAAG;AACpC,kBAAY,SAAS,UAAU,GAAG,SAAS,QAAQ,GAAG,CAAC;AACvD,iBAAW,SAAS,UAAU,SAAS,QAAQ,GAAG,IAAI,CAAC;AAAA,IACzD,OAAO;AACL,kBAAY;AAAA,IACd;AACA,eAAW,gBAAgB,aAAa;AACxC,eAAW,gBAAgB,kBAAkB;AAE7C,UAAM,UAAU,oBAAoB,UAAU,UAAU,KAAK;AAC7D,QAAI,QAAQ,QAAQ;AAClB;AAAA,QACE;AAAA,QACA,SAAS,QAAQ;AACf,cAAI;AACJ,gBAAM,kBAAkB,WAAW,UAAU,IAAI;AACjD,qBAAW,YAAY,EAAE,uBAAuB;AAChD,mBAAS,YAAY,eAAe;AACpC,cAAI,CAAC,aAAa,WAAW,MAAM,GAAG;AACpC,uBAAW,aAAa,eAAe;AAAA,UACzC;AAEA,gBAAM,oBAAoB,EAAE,YAAY,MAAM,QAAQ,SAAS;AAC/D,cAAI,CAAC,aAAa,QAAQ,sBAAsB,iBAAiB,EAAG;AAEpE,mBAAS,kBAAkB;AAC3B,cAAI,kBAAkB,YAAY;AAChC,oCAAwB,QAAQ;AAChC,0BAAc,WAAW,QAAQ,QAAQ,UAAU,UAAU;AAC7D,qCAAyB;AAAA,UAC3B;AACA,kBAAQ,WAAW,MAAM,SAAS,KAAK;AACrC,yBAAa,KAAK,qBAAqB,iBAAiB;AAAA,UAC1D,CAAC;AAAA,QACH;AAAA,MACF;AACA,iBAAW,WAAW,YAAY,UAAU;AAAA,IAC9C,OAAO;AACL,iBAAW,WAAW,YAAY,UAAU;AAC5C,wBAAkB,YAAY,EAAE,MAAM,yBAAyB,EAAE,SAAS,WAAW,CAAC;AAAA,IACxF;AACA,WAAO;AAAA,EACT;AAEA,WAAS,2BAA2B;AAClC,UAAM,SAAS,KAAK,2BAA2B;AAC/C,QAAI,QAAQ;AACV,iBAAW,gBAAgB,CAAC,GAAG,OAAO,QAAQ,GAAG;AAC/C,cAAM,kBAAkB,KAAK,MAAM,aAAa,EAAE;AAElD,wBAAgB,WAAW,WAAW,cAAc,eAAe;AACnE,wBAAgB,OAAO;AAAA,MACzB;AACA,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAKA,WAAS,wBAAwB,UAAU;AACzC,YAAQ,QAAQ,UAAU,mCAAmC,GAAG,SAAS,cAAc;AACrF,YAAM,KAAK,kBAAkB,cAAc,IAAI;AAC/C,YAAM,kBAAkB,YAAY,EAAE,eAAe,EAAE;AACvD,UAAI,mBAAmB,MAAM;AAC3B,YAAI,aAAa,YAAY;AAE3B,cAAI,SAAS,KAAK,2BAA2B;AAC7C,cAAI,UAAU,MAAM;AAClB,wBAAY,EAAE,KAAK,mBAAmB,YAAY,2CAA2C;AAC7F,qBAAS,KAAK,2BAA2B;AAAA,UAC3C;AAEA,iBAAO,WAAW,iBAAiB,IAAI;AAAA,QACzC,OAAO;AACL,uBAAa,WAAW,aAAa,iBAAiB,YAAY;AAAA,QACpE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAOA,WAAS,iBAAiB,YAAY,UAAU,YAAY;AAC1D,YAAQ,SAAS,iBAAiB,MAAM,GAAG,SAAS,SAAS;AAC3D,YAAM,KAAK,gBAAgB,SAAS,IAAI;AACxC,UAAI,MAAM,GAAG,SAAS,GAAG;AACvB,cAAM,eAAe,GAAG,QAAQ,KAAK,KAAK;AAC1C,cAAM,gBAAgB,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACxD,cAAMC,aAAY,aAAa,UAAU;AACzC,cAAM,UAAUA,cAAaA,WAAU,cAAc,gBAAgB,UAAU,eAAe,IAAI;AAClG,YAAI,WAAW,YAAYA,YAAW;AACpC,gBAAM,gBAAgB,QAAQ,UAAU;AACxC,0BAAgB,SAAS,OAAO;AAChC,qBAAW,MAAM,KAAK,WAAW;AAC/B,4BAAgB,SAAS,aAAa;AAAA,UACxC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAMA,WAAS,iBAAiB,OAAO;AAC/B,WAAO,WAAW;AAChB,6BAAuB,OAAO,KAAK,OAAO,UAAU;AACpD,kBAAY,UAAU,KAAK,CAAC;AAC5B,mBAAa,aAAa,KAAK,CAAC;AAChC,mBAAa,OAAO,WAAW;AAAA,IACjC;AAAA,EACF;AAKA,WAAS,aAAa,OAAO;AAC3B,UAAM,YAAY;AAClB,UAAM,iBAAiB,cAAc,QAAQ,OAAO,SAAS,IAAI,QAAQ,MAAM,cAAc,SAAS,CAAC;AACvG,QAAI,kBAAkB,MAAM;AAC1B,qBAAe,MAAM;AAAA,IACvB;AAAA,EACF;AAQA,WAAS,kBAAkB,YAAY,cAAc,UAAU,YAAY;AACzE,qBAAiB,YAAY,UAAU,UAAU;AACjD,WAAO,SAAS,WAAW,SAAS,GAAG;AACrC,YAAM,QAAQ,SAAS;AACvB,wBAAkB,UAAU,KAAK,GAAG,KAAK,OAAO,UAAU;AAC1D,iBAAW,aAAa,OAAO,YAAY;AAC3C,UAAI,MAAM,aAAa,KAAK,aAAa,MAAM,aAAa,KAAK,cAAc;AAC7E,mBAAW,MAAM,KAAK,iBAAiB,KAAK,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AASA,WAAS,WAAW,QAAQ,MAAM;AAChC,QAAI,OAAO;AACX,WAAO,OAAO,OAAO,QAAQ;AAC3B,cAAQ,QAAQ,KAAK,OAAO,OAAO,WAAW,MAAM,IAAI;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAMA,WAAS,cAAc,KAAK;AAC1B,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,YAAM,YAAY,IAAI,WAAW,CAAC;AAClC,UAAI,UAAU,OAAO;AACnB,eAAO,WAAW,UAAU,MAAM,IAAI;AACtC,eAAO,WAAW,UAAU,OAAO,IAAI;AAAA,MACzC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAKA,WAAS,iBAAiB,KAAK;AAC7B,UAAM,eAAe,gBAAgB,GAAG;AACxC,QAAI,aAAa,YAAY;AAC3B,eAAS,IAAI,GAAG,IAAI,aAAa,WAAW,QAAQ,KAAK;AACvD,cAAM,cAAc,aAAa,WAAW,CAAC;AAC7C,gCAAwB,KAAK,YAAY,OAAO,YAAY,QAAQ;AAAA,MACtE;AACA,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAKA,WAAS,WAAW,SAAS;AAC3B,UAAM,eAAe,gBAAgB,OAAO;AAC5C,QAAI,aAAa,SAAS;AACxB,mBAAa,aAAa,OAAO;AAAA,IACnC;AACA,QAAI,aAAa,eAAe;AAC9B,cAAQ,aAAa,eAAe,SAAS,MAAM;AACjD,YAAI,KAAK,IAAI;AACX,kCAAwB,KAAK,IAAI,KAAK,SAAS,KAAK,QAAQ;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AACA,qBAAiB,OAAO;AACxB,YAAQ,OAAO,KAAK,YAAY,GAAG,SAAS,KAAK;AAAE,UAAI,QAAQ,qBAAsB,QAAO,aAAa,GAAG;AAAA,IAAE,CAAC;AAAA,EACjH;AAKA,WAAS,eAAe,SAAS;AAC/B,iBAAa,SAAS,2BAA2B;AACjD,eAAW,OAAO;AAElB,YAAQ,QAAQ,UAAU,SAAS,OAAO;AAAE,qBAAe,KAAK;AAAA,IAAE,CAAC;AAAA,EACrE;AAOA,WAAS,cAAc,QAAQ,UAAU,YAAY;AACnD,QAAI,OAAO,YAAY,QAAQ;AAC7B,aAAO,cAAc,QAAQ,UAAU,UAAU;AAAA,IACnD;AAEA,QAAI;AACJ,UAAM,sBAAsB,OAAO;AACnC,UAAM,aAAa,UAAU,MAAM;AACnC,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,sBAAkB,YAAY,QAAQ,UAAU,UAAU;AAC1D,QAAI,uBAAuB,MAAM;AAC/B,eAAS,WAAW;AAAA,IACtB,OAAO;AACL,eAAS,oBAAoB;AAAA,IAC/B;AACA,eAAW,OAAO,WAAW,KAAK,OAAO,SAAS,GAAG;AAAE,aAAO,MAAM;AAAA,IAAO,CAAC;AAG5E,WAAO,UAAU,WAAW,QAAQ;AAClC,UAAI,kBAAkB,SAAS;AAC7B,mBAAW,KAAK,KAAK,MAAM;AAAA,MAC7B;AACA,eAAS,OAAO;AAAA,IAClB;AACA,mBAAe,MAAM;AACrB,WAAO,OAAO;AAAA,EAChB;AAOA,WAAS,eAAe,QAAQ,UAAU,YAAY;AACpD,WAAO,kBAAkB,QAAQ,OAAO,YAAY,UAAU,UAAU;AAAA,EAC1E;AAOA,WAAS,gBAAgB,QAAQ,UAAU,YAAY;AACrD,WAAO,kBAAkB,UAAU,MAAM,GAAG,QAAQ,UAAU,UAAU;AAAA,EAC1E;AAOA,WAAS,cAAc,QAAQ,UAAU,YAAY;AACnD,WAAO,kBAAkB,QAAQ,MAAM,UAAU,UAAU;AAAA,EAC7D;AAOA,WAAS,aAAa,QAAQ,UAAU,YAAY;AAClD,WAAO,kBAAkB,UAAU,MAAM,GAAG,OAAO,aAAa,UAAU,UAAU;AAAA,EACtF;AAKA,WAAS,WAAW,QAAQ;AAC1B,mBAAe,MAAM;AACrB,UAAM,SAAS,UAAU,MAAM;AAC/B,QAAI,QAAQ;AACV,aAAO,OAAO,YAAY,MAAM;AAAA,IAClC;AAAA,EACF;AAOA,WAAS,cAAc,QAAQ,UAAU,YAAY;AACnD,UAAM,aAAa,OAAO;AAC1B,sBAAkB,QAAQ,YAAY,UAAU,UAAU;AAC1D,QAAI,YAAY;AACd,aAAO,WAAW,aAAa;AAC7B,uBAAe,WAAW,WAAW;AACrC,eAAO,YAAY,WAAW,WAAW;AAAA,MAC3C;AACA,qBAAe,UAAU;AACzB,aAAO,YAAY,UAAU;AAAA,IAC/B;AAAA,EACF;AASA,WAAS,cAAc,WAAW,KAAK,QAAQ,UAAU,YAAY;AACnE,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH;AAAA,MACF,KAAK;AACH,sBAAc,QAAQ,UAAU,UAAU;AAC1C;AAAA,MACF,KAAK;AACH,uBAAe,QAAQ,UAAU,UAAU;AAC3C;AAAA,MACF,KAAK;AACH,wBAAgB,QAAQ,UAAU,UAAU;AAC5C;AAAA,MACF,KAAK;AACH,sBAAc,QAAQ,UAAU,UAAU;AAC1C;AAAA,MACF,KAAK;AACH,qBAAa,QAAQ,UAAU,UAAU;AACzC;AAAA,MACF,KAAK;AACH,mBAAW,MAAM;AACjB;AAAA,MACF;AACE,YAAID,cAAa,cAAc,GAAG;AAClC,iBAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,gBAAM,MAAMA,YAAW,CAAC;AACxB,cAAI;AACF,kBAAM,cAAc,IAAI,WAAW,WAAW,QAAQ,UAAU,UAAU;AAC1E,gBAAI,aAAa;AACf,kBAAI,MAAM,QAAQ,WAAW,GAAG;AAE9B,yBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,wBAAM,QAAQ,YAAY,CAAC;AAC3B,sBAAI,MAAM,aAAa,KAAK,aAAa,MAAM,aAAa,KAAK,cAAc;AAC7E,+BAAW,MAAM,KAAK,iBAAiB,KAAK,CAAC;AAAA,kBAC/C;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AAAA,UACF,SAAS,GAAG;AACV,qBAAS,CAAC;AAAA,UACZ;AAAA,QACF;AACA,YAAI,cAAc,aAAa;AAC7B,wBAAc,QAAQ,UAAU,UAAU;AAAA,QAC5C,OAAO;AACL,wBAAc,KAAK,OAAO,kBAAkB,KAAK,QAAQ,UAAU,UAAU;AAAA,QAC/E;AAAA,IACJ;AAAA,EACF;AAOA,WAAS,uBAAuB,UAAU,YAAY,UAAU;AAC9D,QAAI,UAAU,QAAQ,UAAU,mCAAmC;AACnE,YAAQ,SAAS,SAAS,YAAY;AACpC,UAAI,KAAK,OAAO,uBAAuB,WAAW,kBAAkB,MAAM;AACxE,cAAM,WAAW,kBAAkB,YAAY,aAAa;AAC5D,YAAI,YAAY,MAAM;AACpB,kBAAQ,UAAU,YAAY,YAAY,QAAQ;AAAA,QACpD;AAAA,MACF,OAAO;AACL,mBAAW,gBAAgB,aAAa;AACxC,mBAAW,gBAAgB,kBAAkB;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,WAAO,QAAQ,SAAS;AAAA,EAC1B;AAUA,WAAS,KAAK,QAAQ,SAAS,UAAU,aAAa;AACpD,QAAI,CAAC,aAAa;AAChB,oBAAc,CAAC;AAAA,IACjB;AAEA,QAAI,gBAAgB;AACpB,QAAI,eAAe;AAEnB,QAAI,SAAS,WAAW;AACtB,gBAAU,YAAY,kBAAkB;AAExC,eAAS,cAAc,MAAM;AAC7B,YAAM,WAAW,YAAY,iBAAiB,YAAY,YAAY,gBAAgB,KAAK,IAAI,YAAY;AAG3G,YAAM,YAAY,SAAS;AAC3B,UAAI,gBAAgB,CAAC;AACrB,sBAAgB;AAAA,QACd,KAAK;AAAA;AAAA,QAEL,OAAO,YAAY,UAAU,iBAAiB;AAAA;AAAA,QAE9C,KAAK,YAAY,UAAU,eAAe;AAAA,MAC5C;AACA,YAAM,aAAa,eAAe,MAAM;AAGxC,UAAI,SAAS,cAAc,eAAe;AACxC,eAAO,cAAc;AAAA,MAEvB,OAAO;AACL,YAAI,WAAW,aAAa,OAAO;AAEnC,mBAAW,QAAQ,YAAY,SAAS,SAAS;AACjD,YAAI,YAAY,gBAAgB;AAE9B,qBAAW,SAAS,cAAc,wCAAwC,KAAK;AAAA,QACjF;AAGA,YAAI,YAAY,WAAW;AACzB,gBAAM,kBAAkB,YAAY,UAAU,MAAM,GAAG;AACvD,mBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,kBAAM,iBAAiB,gBAAgB,CAAC,EAAE,MAAM,KAAK,CAAC;AACtD,gBAAI,KAAK,eAAe,CAAC,EAAE,KAAK;AAChC,gBAAI,GAAG,QAAQ,GAAG,MAAM,GAAG;AACzB,mBAAK,GAAG,UAAU,CAAC;AAAA,YACrB;AACA,kBAAM,WAAW,eAAe,CAAC,KAAK;AACtC,kBAAM,aAAa,SAAS,cAAc,MAAM,EAAE;AAClD,gBAAI,YAAY;AACd,sBAAQ,UAAU,YAAY,YAAY,QAAQ;AAAA,YACpD;AAAA,UACF;AAAA,QACF;AAEA,+BAAuB,UAAU,YAAY,QAAQ;AACrD;AAAA,UAAQ,QAAQ,UAAU,UAAU;AAAA;AAAA,UAA+C,SAAS,UAAU;AACpG,gBAAI,SAAS,WAAW,uBAAuB,SAAS,SAAS,YAAY,QAAQ,GAAG;AAEtF,uBAAS,OAAO;AAAA,YAClB;AAAA,UACF;AAAA,QAAC;AAGD,YAAI,YAAY,QAAQ;AACtB,gBAAM,cAAc,YAAY,EAAE,uBAAuB;AACzD,kBAAQ,SAAS,iBAAiB,YAAY,MAAM,GAAG,SAAS,MAAM;AACpE,wBAAY,YAAY,IAAI;AAAA,UAC9B,CAAC;AACD,qBAAW;AAAA,QACb;AACA,gCAAwB,QAAQ;AAChC,sBAAc,SAAS,WAAW,YAAY,gBAAgB,QAAQ,UAAU,UAAU;AAC1F,iCAAyB;AAAA,MAC3B;AAGA,UAAI,cAAc,OAChB,CAAC,aAAa,cAAc,GAAG,KAC/B,gBAAgB,cAAc,KAAK,IAAI,GAAG;AAC1C,cAAM,eAAe,SAAS,eAAe,gBAAgB,cAAc,KAAK,IAAI,CAAC;AACrF,cAAM,eAAe,EAAE,eAAe,SAAS,gBAAgB,SAAY,CAAC,SAAS,cAAc,CAAC,KAAK,OAAO,mBAAmB;AACnI,YAAI,cAAc;AAEhB,cAAI,cAAc,SAAS,aAAa,mBAAmB;AACzD,gBAAI;AAEF,2BAAa,kBAAkB,cAAc,OAAO,cAAc,GAAG;AAAA,YACvE,SAAS,GAAG;AAAA,YAEZ;AAAA,UACF;AACA,uBAAa,MAAM,YAAY;AAAA,QACjC;AAAA,MACF;AAEA,aAAO,UAAU,OAAO,KAAK,OAAO,aAAa;AACjD,cAAQ,WAAW,MAAM,SAASD,MAAK;AACrC,YAAIA,KAAI,WAAW;AACjB,UAAAA,KAAI,UAAU,IAAI,KAAK,OAAO,aAAa;AAAA,QAC7C;AACA,qBAAaA,MAAK,kBAAkB,YAAY,SAAS;AAAA,MAC3D,CAAC;AACD,gBAAU,YAAY,iBAAiB;AAGvC,UAAI,CAAC,SAAS,aAAa;AACzB,oBAAY,WAAW,KAAK;AAAA,MAC9B;AAGA,YAAM,WAAW,WAAW;AAC1B,gBAAQ,WAAW,OAAO,SAAS,MAAM;AACvC,eAAK,KAAK;AAAA,QACZ,CAAC;AACD,gBAAQ,WAAW,MAAM,SAASA,MAAK;AACrC,cAAIA,KAAI,WAAW;AACjB,YAAAA,KAAI,UAAU,OAAO,KAAK,OAAO,aAAa;AAAA,UAChD;AACA,uBAAaA,MAAK,oBAAoB,YAAY,SAAS;AAAA,QAC7D,CAAC;AAED,YAAI,YAAY,QAAQ;AACtB,gBAAM,eAAe,UAAU,cAAc,MAAM,YAAY,MAAM,CAAC;AACtE,cAAI,cAAc;AAChB,yBAAa,eAAe,EAAE,OAAO,SAAS,UAAU,OAAO,CAAC;AAAA,UAClE;AAAA,QACF;AAEA,0BAAkB,WAAW,MAAM,QAAQ;AAC3C,kBAAU,YAAY,mBAAmB;AACzC,kBAAU,aAAa;AAAA,MACzB;AAEA,UAAI,SAAS,cAAc,GAAG;AAC5B,kBAAU,EAAE,WAAW,UAAU,SAAS,WAAW;AAAA,MACvD,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,mBAAmB,KAAK,OAAO;AACnC,QAAI,SAAS,eAAe,YAAY,GAAG;AACzC,yBAAmB,SAAS;AAAA,IAC9B;AAEA,UAAM,MAAM,YAAY,kBAAkB,YAAY;AAEtD,QAAI,oBACI,aAAa,KAAK,yBAAyB,YAAY,SAAS,KAChE,OAAO,YAAY;AAAA,IAEnB,SAAS,qBAAqB;AACpC,YAAM,gBAAgB,IAAI,QAAQ,SAAS,UAAU,SAAS;AAC5D,wBAAgB;AAChB,uBAAe;AAAA,MACjB,CAAC;AAED,YAAM,cAAc;AACpB,eAAS,WAAW;AAElB,iBAAS,oBAAoB,WAAW;AACtC,sBAAY;AACZ,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI;AACF,UAAI,UAAU,aAAa,SAAS,YAAY,GAAG;AACjD,kBAAU,EAAE,WAAW,QAAQ,SAAS,SAAS;AAAA,MACnD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,SAAS,GAAG;AACV,wBAAkB,KAAK,kBAAkB,YAAY,SAAS;AAC9D,gBAAU,YAAY;AACtB,YAAM;AAAA,IACR;AAAA,EACF;AAOA,WAAS,oBAAoB,KAAK,QAAQ,KAAK;AAC7C,UAAM,cAAc,IAAI,kBAAkB,MAAM;AAChD,QAAI,YAAY,QAAQ,GAAG,MAAM,GAAG;AAClC,YAAM,WAAW,UAAU,WAAW;AACtC,iBAAW,aAAa,UAAU;AAChC,YAAI,SAAS,eAAe,SAAS,GAAG;AACtC,cAAI,SAAS,SAAS,SAAS;AAC/B,cAAI,YAAY,MAAM,GAAG;AAEvB,kBAAM,OAAO,WAAW,SAAY,OAAO,SAAS;AAAA,UACtD,OAAO;AACL,qBAAS,EAAE,OAAO,OAAO;AAAA,UAC3B;AACA,uBAAa,KAAK,WAAW,MAAM;AAAA,QACrC;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,aAAa,YAAY,MAAM,GAAG;AACxC,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,qBAAa,KAAK,WAAW,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa;AACnB,QAAM,sBAAsB;AAC5B,QAAM,eAAe;AACrB,QAAM,cAAc;AACpB,QAAM,kBAAkB,CAAC,KAAK,KAAK,GAAG;AACtC,QAAM,iBAAiB;AACvB,QAAM,0BAA0B;AAChC,QAAM,wBAAwB;AAM9B,WAAS,eAAeF,MAAK;AAE3B,UAAM,SAAS,CAAC;AAChB,QAAI,WAAW;AACf,WAAO,WAAWA,KAAI,QAAQ;AAC5B,UAAI,aAAa,KAAKA,KAAI,OAAO,QAAQ,CAAC,GAAG;AAC3C,YAAI,gBAAgB;AACpB,eAAO,YAAY,KAAKA,KAAI,OAAO,WAAW,CAAC,CAAC,GAAG;AACjD;AAAA,QACF;AACA,eAAO,KAAKA,KAAI,UAAU,eAAe,WAAW,CAAC,CAAC;AAAA,MACxD,WAAW,gBAAgB,QAAQA,KAAI,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC/D,cAAM,YAAYA,KAAI,OAAO,QAAQ;AACrC,YAAI,gBAAgB;AACpB;AACA,eAAO,WAAWA,KAAI,UAAUA,KAAI,OAAO,QAAQ,MAAM,WAAW;AAClE,cAAIA,KAAI,OAAO,QAAQ,MAAM,MAAM;AACjC;AAAA,UACF;AACA;AAAA,QACF;AACA,eAAO,KAAKA,KAAI,UAAU,eAAe,WAAW,CAAC,CAAC;AAAA,MACxD,OAAO;AACL,cAAM,SAASA,KAAI,OAAO,QAAQ;AAClC,eAAO,KAAK,MAAM;AAAA,MACpB;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAQA,WAAS,4BAA4B,OAAO,MAAM,WAAW;AAC3D,WAAO,aAAa,KAAK,MAAM,OAAO,CAAC,CAAC,KACtC,UAAU,UACV,UAAU,WACV,UAAU,UACV,UAAU,aACV,SAAS;AAAA,EACb;AAQA,WAAS,yBAAyB,KAAK,QAAQ,WAAW;AACxD,QAAI,OAAO,CAAC,MAAM,KAAK;AACrB,aAAO,MAAM;AACb,UAAI,eAAe;AACnB,UAAI,oBAAoB,uBAAuB,YAAY;AAC3D,UAAI,OAAO;AACX,aAAO,OAAO,SAAS,GAAG;AACxB,cAAM,QAAQ,OAAO,CAAC;AAEtB,YAAI,UAAU,KAAK;AACjB;AACA,cAAI,iBAAiB,GAAG;AACtB,gBAAI,SAAS,MAAM;AACjB,kCAAoB,oBAAoB;AAAA,YAC1C;AACA,mBAAO,MAAM;AACb,iCAAqB;AACrB,gBAAI;AACF,oBAAM,oBAAoB;AAAA,gBAAU;AAAA,gBAAK,WAAW;AAClD,yBAAO,SAAS,iBAAiB,EAAE;AAAA,gBACrC;AAAA,gBACA,WAAW;AAAE,yBAAO;AAAA,gBAAK;AAAA,cAAC;AAC1B,gCAAkB,SAAS;AAC3B,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,gCAAkB,YAAY,EAAE,MAAM,qBAAqB,EAAE,OAAO,GAAG,QAAQ,kBAAkB,CAAC;AAClG,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,WAAW,UAAU,KAAK;AACxB;AAAA,QACF;AACA,YAAI,4BAA4B,OAAO,MAAM,SAAS,GAAG;AACvD,+BAAqB,OAAO,YAAY,MAAM,QAAQ,UAAU,YAAY,MAAM,QAAQ,iBAAiB,QAAQ;AAAA,QACrH,OAAO;AACL,8BAAoB,oBAAoB;AAAA,QAC1C;AACA,eAAO,OAAO,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAOA,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,SAAS;AACb,WAAO,OAAO,SAAS,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,GAAG;AAClD,gBAAU,OAAO,MAAM;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAMA,WAAS,mBAAmB,QAAQ;AAClC,QAAI;AACJ,QAAI,OAAO,SAAS,KAAK,wBAAwB,KAAK,OAAO,CAAC,CAAC,GAAG;AAChE,aAAO,MAAM;AACb,eAAS,aAAa,QAAQ,qBAAqB,EAAE,KAAK;AAC1D,aAAO,MAAM;AAAA,IACf,OAAO;AACL,eAAS,aAAa,QAAQ,mBAAmB;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB;AAQvB,WAAS,qBAAqB,KAAK,iBAAiB,OAAO;AAEzD,UAAM,eAAe,CAAC;AACtB,UAAM,SAAS,eAAe,eAAe;AAC7C,OAAG;AACD,mBAAa,QAAQ,cAAc;AACnC,YAAM,gBAAgB,OAAO;AAC7B,YAAM,UAAU,aAAa,QAAQ,SAAS;AAC9C,UAAI,YAAY,IAAI;AAClB,YAAI,YAAY,SAAS;AAEvB,gBAAM,QAAQ,EAAE,SAAS,QAAQ;AACjC,uBAAa,QAAQ,cAAc;AACnC,gBAAM,eAAe,cAAc,aAAa,QAAQ,SAAS,CAAC;AAClE,uBAAa,QAAQ,cAAc;AACnC,cAAI,cAAc,yBAAyB,KAAK,QAAQ,OAAO;AAC/D,cAAI,aAAa;AACf,kBAAM,cAAc;AAAA,UACtB;AACA,uBAAa,KAAK,KAAK;AAAA,QACzB,OAAO;AAEL,gBAAM,cAAc,EAAE,QAAQ;AAC9B,cAAI,cAAc,yBAAyB,KAAK,QAAQ,OAAO;AAC/D,cAAI,aAAa;AACf,wBAAY,cAAc;AAAA,UAC5B;AACA,uBAAa,QAAQ,cAAc;AACnC,iBAAO,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,KAAK;AAC7C,kBAAM,QAAQ,OAAO,MAAM;AAC3B,gBAAI,UAAU,WAAW;AACvB,0BAAY,UAAU;AAAA,YACxB,WAAW,UAAU,QAAQ;AAC3B,0BAAY,OAAO;AAAA,YACrB,WAAW,UAAU,WAAW;AAC9B,0BAAY,UAAU;AAAA,YACxB,WAAW,UAAU,WAAW,OAAO,CAAC,MAAM,KAAK;AACjD,qBAAO,MAAM;AACb,0BAAY,QAAQ,cAAc,aAAa,QAAQ,mBAAmB,CAAC;AAAA,YAC7E,WAAW,UAAU,UAAU,OAAO,CAAC,MAAM,KAAK;AAChD,qBAAO,MAAM;AACb,kBAAI,wBAAwB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC3C,oBAAI,WAAW,mBAAmB,MAAM;AAAA,cAC1C,OAAO;AACL,oBAAI,WAAW,aAAa,QAAQ,mBAAmB;AACvD,oBAAI,aAAa,aAAa,aAAa,UAAU,aAAa,UAAU,aAAa,YAAY;AACnG,yBAAO,MAAM;AACb,wBAAM,WAAW,mBAAmB,MAAM;AAE1C,sBAAI,SAAS,SAAS,GAAG;AACvB,gCAAY,MAAM;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF;AACA,0BAAY,OAAO;AAAA,YACrB,WAAW,UAAU,YAAY,OAAO,CAAC,MAAM,KAAK;AAClD,qBAAO,MAAM;AACb,0BAAY,SAAS,mBAAmB,MAAM;AAAA,YAChD,WAAW,UAAU,cAAc,OAAO,CAAC,MAAM,KAAK;AACpD,qBAAO,MAAM;AACb,0BAAY,WAAW,cAAc,aAAa,QAAQ,mBAAmB,CAAC;AAAA,YAChF,WAAW,UAAU,WAAW,OAAO,CAAC,MAAM,KAAK;AACjD,qBAAO,MAAM;AACb,0BAAY,QAAQ,aAAa,QAAQ,mBAAmB;AAAA,YAC9D,WAAW,UAAU,UAAU,OAAO,CAAC,MAAM,KAAK;AAChD,qBAAO,MAAM;AACb,0BAAY,KAAK,IAAI,mBAAmB,MAAM;AAAA,YAChD,WAAW,UAAU,eAAe,OAAO,CAAC,MAAM,KAAK;AACrD,qBAAO,MAAM;AACb,0BAAY,KAAK,IAAI,aAAa,QAAQ,mBAAmB;AAAA,YAC/D,OAAO;AACL,gCAAkB,KAAK,qBAAqB,EAAE,OAAO,OAAO,MAAM,EAAE,CAAC;AAAA,YACvE;AACA,yBAAa,QAAQ,cAAc;AAAA,UACrC;AACA,uBAAa,KAAK,WAAW;AAAA,QAC/B;AAAA,MACF;AACA,UAAI,OAAO,WAAW,eAAe;AACnC,0BAAkB,KAAK,qBAAqB,EAAE,OAAO,OAAO,MAAM,EAAE,CAAC;AAAA,MACvE;AACA,mBAAa,QAAQ,cAAc;AAAA,IACrC,SAAS,OAAO,CAAC,MAAM,OAAO,OAAO,MAAM;AAC3C,QAAI,OAAO;AACT,YAAM,eAAe,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAMA,WAAS,gBAAgB,KAAK;AAC5B,UAAM,kBAAkB,kBAAkB,KAAK,YAAY;AAC3D,QAAI,eAAe,CAAC;AACpB,QAAI,iBAAiB;AACnB,YAAM,QAAQ,KAAK,OAAO;AAC1B,qBAAgB,SAAS,MAAM,eAAe,KAAM,qBAAqB,KAAK,iBAAiB,KAAK;AAAA,IACtG;AAEA,QAAI,aAAa,SAAS,GAAG;AAC3B,aAAO;AAAA,IACT,WAAW,QAAQ,KAAK,MAAM,GAAG;AAC/B,aAAO,CAAC,EAAE,SAAS,SAAS,CAAC;AAAA,IAC/B,WAAW,QAAQ,KAAK,4CAA4C,GAAG;AACrE,aAAO,CAAC,EAAE,SAAS,QAAQ,CAAC;AAAA,IAC9B,WAAW,QAAQ,KAAK,cAAc,GAAG;AACvC,aAAO,CAAC,EAAE,SAAS,SAAS,CAAC;AAAA,IAC/B,OAAO;AACL,aAAO,CAAC,EAAE,SAAS,QAAQ,CAAC;AAAA,IAC9B;AAAA,EACF;AAKA,WAAS,cAAc,KAAK;AAC1B,oBAAgB,GAAG,EAAE,YAAY;AAAA,EACnC;AAOA,WAAS,eAAe,KAAK,SAAS,MAAM;AAC1C,UAAM,WAAW,gBAAgB,GAAG;AACpC,aAAS,UAAU,UAAU,EAAE,WAAW,WAAW;AACnD,UAAI,aAAa,GAAG,KAAK,SAAS,cAAc,MAAM;AACpD,YAAI,CAAC,iBAAiB,MAAM,KAAK,UAAU,mBAAmB;AAAA,UAC5D,aAAa;AAAA,UACb,QAAQ;AAAA,QACV,CAAC,CAAC,GAAG;AACH,kBAAQ,GAAG;AAAA,QACb;AACA,uBAAe,KAAK,SAAS,IAAI;AAAA,MACnC;AAAA,IACF,GAAG,KAAK,YAAY;AAAA,EACtB;AAMA,WAAS,YAAY,KAAK;AACxB,WAAO,SAAS,aAAa,IAAI,YAC/B,gBAAgB,KAAK,MAAM,KAC3B,gBAAgB,KAAK,MAAM,EAAE,QAAQ,GAAG,MAAM;AAAA,EAClD;AAKA,WAAS,cAAc,KAAK;AAC1B,WAAO,QAAQ,KAAK,KAAK,OAAO,eAAe;AAAA,EACjD;AAOA,WAAS,aAAa,KAAK,UAAU,cAAc;AACjD,QAAK,eAAe,qBAAqB,YAAY,GAAG,MAAM,IAAI,WAAW,MAAM,IAAI,WAAW,YAAc,IAAI,YAAY,UAAU,OAAO,gBAAgB,KAAK,QAAQ,CAAC,EAAE,YAAY,MAAM,UAAW;AAC5M,eAAS,UAAU;AACnB,UAAI,MAAM;AACV,UAAI,IAAI,YAAY,KAAK;AACvB;AAAA,QAA8B;AAC9B,eAAO,gBAAgB,KAAK,MAAM;AAAA,MACpC,OAAO;AACL,cAAM,eAAe,gBAAgB,KAAK,QAAQ;AAClD;AAAA,QAA8B,eAAe,aAAa,YAAY,IAAI;AAC1E,eAAO,gBAAgB,KAAK,QAAQ;AACpC,YAAI,QAAQ,QAAQ,SAAS,IAAI;AAG/B,iBAAO,SAAS;AAAA,QAClB;AACA,YAAI,SAAS,SAAS,KAAK,SAAS,GAAG,GAAG;AACxC,iBAAO,KAAK,QAAQ,WAAW,EAAE;AAAA,QACnC;AAAA,MACF;AACA,mBAAa,QAAQ,SAAS,aAAa;AACzC,yBAAiB,KAAK,SAAS,MAAM,KAAK;AACxC,gBAAME,OAAM,UAAU,IAAI;AAC1B,cAAI,cAAcA,IAAG,GAAG;AACtB,2BAAeA,IAAG;AAClB;AAAA,UACF;AACA,2BAAiB,MAAM,MAAMA,MAAK,GAAG;AAAA,QACvC,GAAG,UAAU,aAAa,IAAI;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAOA,WAAS,aAAa,KAAK,KAAK;AAC9B,QAAI,IAAI,SAAS,YAAY,IAAI,SAAS,SAAS;AAEjD,YAAM,UAAU,IAAI,MAAM,KAAK;AAC/B,UAAI,IAAI,YAAY,QAAQ;AAC1B,eAAO;AAAA,MACT;AAGA,UAAI,IAAI,QAAQ,IAAI,SAAS,UAAU;AACrC,eAAO;AAAA,MACT;AACA,YAAM,IAAI,QAAQ,GAAG;AAErB,UAAI,OAAO,IAAI,SACZ,IAAI,aAAa,MAAM,MAAM,OAAO,IAAI,aAAa,MAAM,EAAE,QAAQ,GAAG,MAAM,IAAI;AACnF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAOA,WAAS,6BAA6B,KAAK,KAAK;AAC9C,WAAO,gBAAgB,GAAG,EAAE,WAAW,eAAe,qBAAqB,IAAI,SAAS;AAAA,KAErF,IAAI,WAAW,IAAI;AAAA,EACxB;AAQA,WAAS,iBAAiB,aAAa,KAAK,KAAK;AAC/C,UAAM,cAAc,YAAY;AAChC,QAAI,aAAa;AACf,UAAI;AACF,eAAO,YAAY,KAAK,KAAK,GAAG,MAAM;AAAA,MACxC,SAAS,GAAG;AACV,cAAM,SAAS,YAAY;AAC3B,0BAAkB,YAAY,EAAE,MAAM,0BAA0B,EAAE,OAAO,GAAG,OAAO,CAAC;AACpF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AASA,WAAS,iBAAiB,KAAK,SAAS,UAAU,aAAa,gBAAgB;AAC7E,UAAM,cAAc,gBAAgB,GAAG;AAEvC,QAAI;AACJ,QAAI,YAAY,MAAM;AACpB,uBAAiB,oBAAoB,KAAK,YAAY,IAAI;AAAA,IAC5D,OAAO;AACL,uBAAiB,CAAC,GAAG;AAAA,IACvB;AAEA,QAAI,YAAY,SAAS;AACvB,UAAI,EAAE,eAAe,cAAc;AACjC,oBAAY,YAAY,oBAAI,QAAQ;AAAA,MACtC;AACA,qBAAe,QAAQ,SAAS,eAAe;AAC7C,YAAI,CAAC,YAAY,UAAU,IAAI,WAAW,GAAG;AAC3C,sBAAY,UAAU,IAAI,aAAa,oBAAI,QAAQ,CAAC;AAAA,QACtD;AAEA,oBAAY,UAAU,IAAI,WAAW,EAAE,IAAI,eAAe,cAAc,KAAK;AAAA,MAC/E,CAAC;AAAA,IACH;AACA,YAAQ,gBAAgB,SAAS,eAAe;AAE9C,YAAM,gBAAgB,SAAS,KAAK;AAClC,YAAI,CAAC,aAAa,GAAG,GAAG;AACtB,wBAAc,oBAAoB,YAAY,SAAS,aAAa;AACpE;AAAA,QACF;AACA,YAAI,6BAA6B,KAAK,GAAG,GAAG;AAC1C;AAAA,QACF;AACA,YAAI,kBAAkB,aAAa,KAAK,GAAG,GAAG;AAC5C,cAAI,eAAe;AAAA,QACrB;AACA,YAAI,iBAAiB,aAAa,KAAK,GAAG,GAAG;AAC3C;AAAA,QACF;AACA,cAAM,YAAY,gBAAgB,GAAG;AACrC,kBAAU,cAAc;AACxB,YAAI,UAAU,cAAc,MAAM;AAChC,oBAAU,aAAa,CAAC;AAAA,QAC1B;AACA,YAAI,UAAU,WAAW,QAAQ,GAAG,IAAI,GAAG;AACzC,oBAAU,WAAW,KAAK,GAAG;AAC7B,cAAI,YAAY,SAAS;AACvB,gBAAI,gBAAgB;AAAA,UACtB;AACA,cAAI,YAAY,UAAU,IAAI,QAAQ;AACpC,gBAAI,CAAC,QAAQ,UAAU,IAAI,MAAM,GAAG,YAAY,MAAM,GAAG;AACvD;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,MAAM;AACpB,gBAAI,YAAY,eAAe;AAC7B;AAAA,YACF,OAAO;AACL,0BAAY,gBAAgB;AAAA,YAC9B;AAAA,UACF;AACA,cAAI,YAAY,SAAS;AACvB,kBAAM,OAAO,IAAI;AAEjB,kBAAM,QAAQ,KAAK;AACnB,kBAAM,YAAY,YAAY,UAAU,IAAI,WAAW;AACvD,gBAAI,UAAU,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,MAAM,OAAO;AACxD;AAAA,YACF;AACA,sBAAU,IAAI,MAAM,KAAK;AAAA,UAC3B;AACA,cAAI,YAAY,SAAS;AACvB,yBAAa,YAAY,OAAO;AAAA,UAClC;AACA,cAAI,YAAY,UAAU;AACxB;AAAA,UACF;AAEA,cAAI,YAAY,WAAW,GAAG;AAC5B,gBAAI,CAAC,YAAY,UAAU;AACzB,2BAAa,KAAK,cAAc;AAChC,sBAAQ,KAAK,GAAG;AAChB,0BAAY,WAAW,UAAU,EAAE,WAAW,WAAW;AACvD,4BAAY,WAAW;AAAA,cACzB,GAAG,YAAY,QAAQ;AAAA,YACzB;AAAA,UACF,WAAW,YAAY,QAAQ,GAAG;AAChC,wBAAY,UAAU,UAAU,EAAE,WAAW,WAAW;AACtD,2BAAa,KAAK,cAAc;AAChC,sBAAQ,KAAK,GAAG;AAAA,YAClB,GAAG,YAAY,KAAK;AAAA,UACtB,OAAO;AACL,yBAAa,KAAK,cAAc;AAChC,oBAAQ,KAAK,GAAG;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS,iBAAiB,MAAM;AAClC,iBAAS,gBAAgB,CAAC;AAAA,MAC5B;AACA,eAAS,cAAc,KAAK;AAAA,QAC1B,SAAS,YAAY;AAAA,QACrB,UAAU;AAAA,QACV,IAAI;AAAA,MACN,CAAC;AACD,oBAAc,iBAAiB,YAAY,SAAS,aAAa;AAAA,IACnE,CAAC;AAAA,EACH;AAEA,MAAI,oBAAoB;AACxB,MAAI,gBAAgB;AACpB,WAAS,oBAAoB;AAC3B,QAAI,CAAC,eAAe;AAClB,sBAAgB,WAAW;AACzB,4BAAoB;AAAA,MACtB;AACA,aAAO,iBAAiB,UAAU,aAAa;AAC/C,aAAO,iBAAiB,UAAU,aAAa;AAC/C,kBAAY,WAAW;AACrB,YAAI,mBAAmB;AACrB,8BAAoB;AACpB,kBAAQ,YAAY,EAAE,iBAAiB,wDAAwD,GAAG,SAAS,KAAK;AAC9G,wBAAY,GAAG;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAKA,WAAS,YAAY,KAAK;AACxB,QAAI,CAAC,aAAa,KAAK,kBAAkB,KAAK,mBAAmB,GAAG,GAAG;AACrE,UAAI,aAAa,oBAAoB,MAAM;AAC3C,YAAM,WAAW,gBAAgB,GAAG;AACpC,UAAI,SAAS,UAAU;AACrB,qBAAa,KAAK,UAAU;AAAA,MAC9B,OAAO;AAEL,YAAI,iBAAiB,yBAAyB,WAAW;AAAE,uBAAa,KAAK,UAAU;AAAA,QAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,MAC5G;AAAA,IACF;AAAA,EACF;AAUA,WAAS,gBAAgB,KAAK,SAAS,UAAU,OAAO;AACtD,UAAM,OAAO,WAAW;AACtB,UAAI,CAAC,SAAS,QAAQ;AACpB,iBAAS,SAAS;AAClB,qBAAa,KAAK,cAAc;AAChC,gBAAQ,GAAG;AAAA,MACb;AAAA,IACF;AACA,QAAI,QAAQ,GAAG;AACb,gBAAU,EAAE,WAAW,MAAM,KAAK;AAAA,IACpC,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF;AAQA,WAAS,aAAa,KAAK,UAAU,cAAc;AACjD,QAAI,iBAAiB;AACrB,YAAQ,OAAO,SAAS,MAAM;AAC5B,UAAI,aAAa,KAAK,QAAQ,IAAI,GAAG;AACnC,cAAM,OAAO,kBAAkB,KAAK,QAAQ,IAAI;AAChD,yBAAiB;AACjB,iBAAS,OAAO;AAChB,iBAAS,OAAO;AAChB,qBAAa,QAAQ,SAAS,aAAa;AACzC,4BAAkB,KAAK,aAAa,UAAU,SAAS,MAAM,KAAK;AAChE,kBAAMA,OAAM,UAAU,IAAI;AAC1B,gBAAI,cAAcA,IAAG,GAAG;AACtB,6BAAeA,IAAG;AAClB;AAAA,YACF;AACA,6BAAiB,MAAM,MAAMA,MAAK,GAAG;AAAA,UACvC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAcA,WAAS,kBAAkB,KAAK,aAAa,UAAU,SAAS;AAC9D,QAAI,YAAY,YAAY,YAAY;AACtC,wBAAkB;AAClB,uBAAiB,KAAK,SAAS,UAAU,WAAW;AACpD,kBAAY,UAAU,GAAG,CAAC;AAAA,IAC5B,WAAW,YAAY,YAAY,aAAa;AAC9C,YAAM,kBAAkB,CAAC;AACzB,UAAI,YAAY,MAAM;AACpB,wBAAgB,OAAO,iBAAiB,KAAK,YAAY,IAAI;AAAA,MAC/D;AACA,UAAI,YAAY,WAAW;AACzB,wBAAgB,YAAY,WAAW,YAAY,SAAS;AAAA,MAC9D;AACA,YAAM,WAAW,IAAI,qBAAqB,SAAS,SAAS;AAC1D,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,QAAQ,QAAQ,CAAC;AACvB,cAAI,MAAM,gBAAgB;AACxB,yBAAa,KAAK,WAAW;AAC7B;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAG,eAAe;AAClB,eAAS,QAAQ,UAAU,GAAG,CAAC;AAC/B,uBAAiB,UAAU,GAAG,GAAG,SAAS,UAAU,WAAW;AAAA,IACjE,WAAW,CAAC,SAAS,sBAAsB,YAAY,YAAY,QAAQ;AACzE,UAAI,CAAC,iBAAiB,aAAa,KAAK,UAAU,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG;AACnE,wBAAgB,UAAU,GAAG,GAAG,SAAS,UAAU,YAAY,KAAK;AAAA,MACtE;AAAA,IACF,WAAW,YAAY,eAAe,GAAG;AACvC,eAAS,UAAU;AACnB,qBAAe,UAAU,GAAG,GAAG,SAAS,WAAW;AAAA,IACrD,OAAO;AACL,uBAAiB,KAAK,SAAS,UAAU,WAAW;AAAA,IACtD;AAAA,EACF;AAMA,WAAS,kBAAkB,MAAM;AAC/B,UAAM,MAAM,UAAU,IAAI;AAC1B,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,aAAa,IAAI;AACvB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,WAAW,WAAW,CAAC,EAAE;AAC/B,UAAI,WAAW,UAAU,QAAQ,KAAK,WAAW,UAAU,aAAa,KACtE,WAAW,UAAU,QAAQ,KAAK,WAAW,UAAU,aAAa,GAAG;AACvE,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,QAAM,cAAc,IAAI,eAAe,EACpC,iBAAiB,wJACyD;AAE7E,WAAS,gBAAgB,KAAK,UAAU;AACtC,QAAI,kBAAkB,GAAG,GAAG;AAC1B,eAAS,KAAK,UAAU,GAAG,CAAC;AAAA,IAC9B;AACA,UAAM,OAAO,YAAY,SAAS,GAAG;AACrC,QAAI,OAAO;AACX,WAAO,OAAO,KAAK,YAAY,EAAG,UAAS,KAAK,UAAU,IAAI,CAAC;AAAA,EACjE;AAEA,WAAS,yBAAyB,KAAK;AAErC,UAAM,WAAW,CAAC;AAClB,QAAI,eAAe,kBAAkB;AACnC,iBAAW,SAAS,IAAI,YAAY;AAClC,wBAAgB,OAAO,QAAQ;AAAA,MACjC;AAAA,IACF,OAAO;AACL,sBAAgB,KAAK,QAAQ;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAMA,WAAS,sBAAsB,KAAK;AAClC,QAAI,IAAI,kBAAkB;AACxB,YAAM,kBAAkB;AAExB,YAAM,qBAAqB,CAAC;AAC5B,iBAAW,KAAK,YAAY;AAC1B,cAAM,YAAY,WAAW,CAAC;AAC9B,YAAI,UAAU,cAAc;AAC1B,cAAI,YAAY,UAAU,aAAa;AACvC,cAAI,WAAW;AACb,+BAAmB,KAAK,SAAS;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU,IAAI,iBAAiB,gBAAgB,kBAAkB,sFACP,mBAAmB,KAAK,EAAE,IAAI,OAAK,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAErH,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAOA,WAAS,0BAA0B,KAAK;AACtC,UAAM,MAAM,gBAAgB,IAAI,MAAM;AACtC,UAAM,eAAe,mBAAmB,GAAG;AAC3C,QAAI,cAAc;AAChB,mBAAa,oBAAoB;AAAA,IACnC;AAAA,EACF;AAKA,WAAS,4BAA4B,KAAK;AACxC,UAAM,eAAe,mBAAmB,GAAG;AAC3C,QAAI,cAAc;AAChB,mBAAa,oBAAoB;AAAA,IACnC;AAAA,EACF;AAMA,WAAS,gBAAgB,QAAQ;AAC/B;AAAA;AAAA,MAA+D,QAAQ,UAAU,MAAM,GAAG,8BAA8B;AAAA;AAAA,EAC1H;AAMA,WAAS,eAAe,KAAK;AAE3B,WAAO,IAAI,QAAQ,QAAQ,KAAK,MAAM;AAAA,EACxC;AAMA,WAAS,mBAAmB,KAAK;AAC/B,UAAM,MAAM,gBAAgB,IAAI,MAAM;AACtC,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,OAAO,eAAe,GAAG;AAC/B,WAAO,gBAAgB,IAAI;AAAA,EAC7B;AAKA,WAAS,mBAAmB,KAAK;AAI/B,QAAI,iBAAiB,SAAS,yBAAyB;AACvD,QAAI,iBAAiB,WAAW,yBAAyB;AACzD,QAAI,iBAAiB,YAAY,2BAA2B;AAAA,EAC9D;AAOA,WAAS,oBAAoB,KAAK,WAAW,MAAM;AACjD,UAAM,WAAW,gBAAgB,GAAG;AACpC,QAAI,CAAC,MAAM,QAAQ,SAAS,UAAU,GAAG;AACvC,eAAS,aAAa,CAAC;AAAA,IACzB;AACA,QAAI;AAEJ,UAAM,WAAW,SAAS,GAAG;AAC3B,gBAAU,KAAK,WAAW;AACxB,YAAI,cAAc,GAAG,GAAG;AACtB;AAAA,QACF;AACA,YAAI,CAAC,MAAM;AACT,iBAAO,IAAI,SAAS,SAAS,IAAI;AAAA,QACnC;AACA,aAAK,KAAK,KAAK,CAAC;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI,iBAAiB,WAAW,QAAQ;AACxC,aAAS,WAAW,KAAK,EAAE,OAAO,WAAW,SAAS,CAAC;AAAA,EACzD;AAKA,WAAS,oBAAoB,KAAK;AAEhC,qBAAiB,GAAG;AAEpB,aAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,YAAM,OAAO,IAAI,WAAW,CAAC,EAAE;AAC/B,YAAM,QAAQ,IAAI,WAAW,CAAC,EAAE;AAChC,UAAI,WAAW,MAAM,OAAO,KAAK,WAAW,MAAM,YAAY,GAAG;AAC/D,cAAM,kBAAkB,KAAK,QAAQ,KAAK,IAAI;AAC9C,cAAM,WAAW,KAAK,MAAM,iBAAiB,kBAAkB,CAAC;AAChE,YAAI,aAAa,OAAO,aAAa,KAAK;AACxC,cAAI,YAAY,KAAK,MAAM,kBAAkB,CAAC;AAE9C,cAAI,WAAW,WAAW,GAAG,GAAG;AAC9B,wBAAY,SAAS;AAAA,UACvB,WAAW,WAAW,WAAW,GAAG,GAAG;AACrC,wBAAY,UAAU,UAAU,MAAM,CAAC;AAAA,UACzC,WAAW,WAAW,WAAW,OAAO,GAAG;AACzC,wBAAY,UAAU,UAAU,MAAM,CAAC;AAAA,UACzC;AAEA,8BAAoB,KAAK,WAAW,KAAK;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAKA,WAAS,SAAS,KAAK;AACrB,iBAAa,KAAK,wBAAwB;AAE1C,UAAM,WAAW,gBAAgB,GAAG;AACpC,UAAM,eAAe,gBAAgB,GAAG;AACxC,UAAM,wBAAwB,aAAa,KAAK,UAAU,YAAY;AAEtE,QAAI,CAAC,uBAAuB;AAC1B,UAAI,yBAAyB,KAAK,UAAU,MAAM,QAAQ;AACxD,qBAAa,KAAK,UAAU,YAAY;AAAA,MAC1C,WAAW,aAAa,KAAK,YAAY,GAAG;AAC1C,qBAAa,QAAQ,SAAS,aAAa;AAEzC,4BAAkB,KAAK,aAAa,UAAU,WAAW;AAAA,UACzD,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAIA,QAAI,IAAI,YAAY,UAAW,gBAAgB,KAAK,MAAM,MAAM,YAAY,aAAa,KAAK,MAAM,GAAI;AACtG,yBAAmB,GAAG;AAAA,IACxB;AAEA,aAAS,qBAAqB;AAC9B,iBAAa,KAAK,uBAAuB;AAAA,EAC3C;AAMA,WAAS,mBAAmB,KAAK;AAE/B,QAAI,EAAE,eAAe,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,gBAAgB,GAAG;AACpC,UAAM,OAAO,cAAc,GAAG;AAC9B,QAAI,SAAS,aAAa,MAAM;AAC9B,iBAAW,GAAG;AACd,eAAS,WAAW;AACpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AASA,WAAS,YAAY,KAAK;AACxB,UAAM,cAAc,GAAG;AACvB,QAAI,cAAc,GAAG,GAAG;AACtB,qBAAe,GAAG;AAClB;AAAA,IACF;AAEA,UAAM,iBAAiB,CAAC;AACxB,QAAI,mBAAmB,GAAG,GAAG;AAC3B,qBAAe,KAAK,GAAG;AAAA,IACzB;AACA,YAAQ,sBAAsB,GAAG,GAAG,SAAS,OAAO;AAClD,UAAI,cAAc,KAAK,GAAG;AACxB,uBAAe,KAAK;AACpB;AAAA,MACF;AACA,UAAI,mBAAmB,KAAK,GAAG;AAC7B,uBAAe,KAAK,KAAK;AAAA,MAC3B;AAAA,IACF,CAAC;AAED,YAAQ,yBAAyB,GAAG,GAAG,mBAAmB;AAC1D,YAAQ,gBAAgB,QAAQ;AAAA,EAClC;AAUA,WAAS,eAAeF,MAAK;AAC3B,WAAOA,KAAI,QAAQ,sBAAsB,OAAO,EAAE,YAAY;AAAA,EAChE;AAOA,WAAS,UAAU,WAAW,QAAQ;AAGpC,WAAO,IAAI,YAAY,WAAW,EAAE,SAAS,MAAM,YAAY,MAAM,UAAU,MAAM,OAAO,CAAC;AAAA,EAC/F;AAOA,WAAS,kBAAkB,KAAK,WAAW,QAAQ;AACjD,iBAAa,KAAK,WAAW,aAAa,EAAE,OAAO,UAAU,GAAG,MAAM,CAAC;AAAA,EACzE;AAMA,WAAS,sBAAsB,WAAW;AACxC,WAAO,cAAc;AAAA,EACvB;AAaA,WAAS,eAAe,KAAK,MAAM,oBAAoB;AACrD,YAAQ,cAAc,KAAK,CAAC,GAAG,kBAAkB,GAAG,SAAS,WAAW;AACtE,UAAI;AACF,aAAK,SAAS;AAAA,MAChB,SAAS,GAAG;AACV,iBAAS,CAAC;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,SAAS,KAAK;AACrB,YAAQ,MAAM,GAAG;AAAA,EACnB;AAYA,WAAS,aAAa,KAAK,WAAW,QAAQ;AAC5C,UAAM,cAAc,GAAG;AACvB,QAAI,UAAU,MAAM;AAClB,eAAS,CAAC;AAAA,IACZ;AACA,WAAO,MAAM;AACb,UAAM,QAAQ,UAAU,WAAW,MAAM;AACzC,QAAI,KAAK,UAAU,CAAC,sBAAsB,SAAS,GAAG;AACpD,WAAK,OAAO,KAAK,WAAW,MAAM;AAAA,IACpC;AACA,QAAI,OAAO,OAAO;AAChB,eAAS,OAAO,KAAK;AACrB,mBAAa,KAAK,cAAc,EAAE,WAAW,OAAO,CAAC;AAAA,IACvD;AACA,QAAI,cAAc,IAAI,cAAc,KAAK;AACzC,UAAM,YAAY,eAAe,SAAS;AAC1C,QAAI,eAAe,cAAc,WAAW;AAC1C,YAAM,eAAe,UAAU,WAAW,MAAM,MAAM;AACtD,oBAAc,eAAe,IAAI,cAAc,YAAY;AAAA,IAC7D;AACA,mBAAe,UAAU,GAAG,GAAG,SAAS,WAAW;AACjD,oBAAc,gBAAgB,UAAU,QAAQ,WAAW,KAAK,MAAM,SAAS,CAAC,MAAM;AAAA,IACxF,CAAC;AACD,WAAO;AAAA,EACT;AAKA,MAAI,wBAAwB,SAAS,WAAW,SAAS;AAKzD,WAAS,yBAAyB,MAAM;AACtC,4BAAwB;AACxB,QAAI,sBAAsB,GAAG;AAC3B,qBAAe,QAAQ,iCAAiC,IAAI;AAAA,IAC9D;AAAA,EACF;AAKA,WAAS,oBAAoB;AAC3B,UAAM,aAAa,YAAY,EAAE,cAAc,wCAAwC;AACvF,WAAO,cAAc,YAAY,EAAE;AAAA,EACrC;AAMA,WAAS,mBAAmB,KAAK,SAAS;AACxC,QAAI,CAAC,sBAAsB,GAAG;AAC5B;AAAA,IACF;AAGA,UAAM,YAAY,yBAAyB,OAAO;AAClD,UAAM,QAAQ,YAAY,EAAE;AAC5B,UAAM,SAAS,OAAO;AAEtB,QAAI,KAAK,OAAO,oBAAoB,GAAG;AAErC,qBAAe,WAAW,oBAAoB;AAC9C;AAAA,IACF;AAEA,UAAM,cAAc,GAAG;AAEvB,UAAM,eAAe,UAAU,eAAe,QAAQ,oBAAoB,CAAC,KAAK,CAAC;AACjF,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAI,aAAa,CAAC,EAAE,QAAQ,KAAK;AAC/B,qBAAa,OAAO,GAAG,CAAC;AACxB;AAAA,MACF;AAAA,IACF;AAGA,UAAM,iBAAiB,EAAE,KAAK,SAAS,WAAW,OAAO,OAAO;AAEhE,iBAAa,YAAY,EAAE,MAAM,2BAA2B,EAAE,MAAM,gBAAgB,OAAO,aAAa,CAAC;AAEzG,iBAAa,KAAK,cAAc;AAChC,WAAO,aAAa,SAAS,KAAK,OAAO,kBAAkB;AACzD,mBAAa,MAAM;AAAA,IACrB;AAGA,WAAO,aAAa,SAAS,GAAG;AAC9B,UAAI;AACF,uBAAe,QAAQ,sBAAsB,KAAK,UAAU,YAAY,CAAC;AACzE;AAAA,MACF,SAAS,GAAG;AACV,0BAAkB,YAAY,EAAE,MAAM,0BAA0B,EAAE,OAAO,GAAG,OAAO,aAAa,CAAC;AACjG,qBAAa,MAAM;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAcA,WAAS,iBAAiB,KAAK;AAC7B,QAAI,CAAC,sBAAsB,GAAG;AAC5B,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,GAAG;AAEvB,UAAM,eAAe,UAAU,eAAe,QAAQ,oBAAoB,CAAC,KAAK,CAAC;AACjF,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAI,aAAa,CAAC,EAAE,QAAQ,KAAK;AAC/B,eAAO,aAAa,CAAC;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,WAAS,yBAAyB,KAAK;AACrC,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM;AAAA;AAAA,MAA8B,IAAI,UAAU,IAAI;AAAA;AACtD,YAAQ,QAAQ,OAAO,MAAM,SAAS,GAAG,SAAS,OAAO;AACvD,6BAAuB,OAAO,SAAS;AAAA,IACzC,CAAC;AAED,YAAQ,QAAQ,OAAO,yBAAyB,GAAG,SAAS,OAAO;AACjE,YAAM,gBAAgB,UAAU;AAAA,IAClC,CAAC;AACD,WAAO,MAAM;AAAA,EACf;AAEA,WAAS,2BAA2B;AAClC,UAAM,MAAM,kBAAkB;AAC9B,QAAI,OAAO;AACX,QAAI,sBAAsB,GAAG;AAC3B,aAAO,eAAe,QAAQ,+BAA+B;AAAA,IAC/D;AACA,WAAO,QAAQ,SAAS,WAAW,SAAS;AAO5C,UAAM,sBAAsB,YAAY,EAAE,cAAc,oDAAoD;AAC5G,QAAI,CAAC,qBAAqB;AACxB,mBAAa,YAAY,EAAE,MAAM,0BAA0B,EAAE,MAAM,YAAY,IAAI,CAAC;AACpF,yBAAmB,MAAM,GAAG;AAAA,IAC9B;AAEA,QAAI,KAAK,OAAO,eAAgB,SAAQ,aAAa,EAAE,MAAM,KAAK,GAAG,YAAY,EAAE,OAAO,SAAS,IAAI;AAAA,EACzG;AAKA,WAAS,mBAAmB,MAAM;AAEhC,QAAI,KAAK,OAAO,qBAAqB;AACnC,aAAO,KAAK,QAAQ,mCAAmC,EAAE;AACzD,UAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,GAAG;AAC9C,eAAO,KAAK,MAAM,GAAG,EAAE;AAAA,MACzB;AAAA,IACF;AACA,QAAI,KAAK,OAAO,gBAAgB;AAC9B,cAAQ,UAAU,EAAE,MAAM,KAAK,GAAG,IAAI,IAAI;AAAA,IAC5C;AACA,6BAAyB,IAAI;AAAA,EAC/B;AAKA,WAAS,oBAAoB,MAAM;AACjC,QAAI,KAAK,OAAO,eAAgB,SAAQ,aAAa,EAAE,MAAM,KAAK,GAAG,IAAI,IAAI;AAC7E,6BAAyB,IAAI;AAAA,EAC/B;AAKA,WAAS,kBAAkB,OAAO;AAChC,YAAQ,OAAO,SAAS,MAAM;AAC5B,WAAK,KAAK,MAAS;AAAA,IACrB,CAAC;AAAA,EACH;AAKA,WAAS,sBAAsB,MAAM;AACnC,UAAM,UAAU,IAAI,eAAe;AACnC,UAAM,WAAW,EAAE,WAAW,aAAa,WAAW,GAAG,aAAa,EAAE;AACxE,UAAM,UAAU,EAAE,MAAM,KAAK,SAAS,YAAY,kBAAkB,GAAG,SAAS;AAChF,YAAQ,KAAK,OAAO,MAAM,IAAI;AAC9B,QAAI,KAAK,OAAO,2BAA2B;AACzC,cAAQ,iBAAiB,cAAc,MAAM;AAAA,IAC/C;AACA,YAAQ,iBAAiB,8BAA8B,MAAM;AAC7D,YAAQ,iBAAiB,kBAAkB,SAAS,IAAI;AACxD,YAAQ,SAAS,WAAW;AAC1B,UAAI,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK;AAC3C,gBAAQ,WAAW,KAAK;AACxB,qBAAa,YAAY,EAAE,MAAM,6BAA6B,OAAO;AACrE,aAAK,QAAQ,YAAY,QAAQ,UAAU,UAAU;AAAA,UACnD,gBAAgB,QAAQ;AAAA,UACxB,gBAAgB;AAAA,QAClB,CAAC;AACD,iCAAyB,QAAQ,IAAI;AACrC,qBAAa,YAAY,EAAE,MAAM,uBAAuB,EAAE,MAAM,WAAW,MAAM,gBAAgB,QAAQ,SAAS,CAAC;AAAA,MACrH,OAAO;AACL,0BAAkB,YAAY,EAAE,MAAM,kCAAkC,OAAO;AAAA,MACjF;AAAA,IACF;AACA,QAAI,aAAa,YAAY,EAAE,MAAM,yBAAyB,OAAO,GAAG;AACtE,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAKA,WAAS,eAAe,MAAM;AAC5B,6BAAyB;AACzB,WAAO,QAAQ,SAAS,WAAW,SAAS;AAC5C,UAAM,SAAS,iBAAiB,IAAI;AACpC,QAAI,QAAQ;AACV,YAAM,WAAW,EAAE,WAAW,aAAa,WAAW,GAAG,aAAa,GAAG,QAAQ,OAAO,OAAO;AAC/F,YAAM,UAAU,EAAE,MAAM,MAAM,QAAQ,YAAY,kBAAkB,GAAG,SAAS;AAChF,UAAI,aAAa,YAAY,EAAE,MAAM,wBAAwB,OAAO,GAAG;AACrE,aAAK,QAAQ,YAAY,OAAO,SAAS,UAAU;AAAA,UACjD,gBAAgB,QAAQ;AAAA,UACxB,OAAO,OAAO;AAAA,QAChB,CAAC;AACD,iCAAyB,QAAQ,IAAI;AACrC,qBAAa,YAAY,EAAE,MAAM,uBAAuB,OAAO;AAAA,MACjE;AAAA,IACF,OAAO;AACL,UAAI,KAAK,OAAO,sBAAsB;AAGpC,aAAK,SAAS,OAAO,IAAI;AAAA,MAC3B,OAAO;AACL,8BAAsB,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAMA,WAAS,2BAA2B,KAAK;AACvC,QAAI;AAAA;AAAA,MAAqC,qBAAqB,KAAK,cAAc;AAAA;AACjF,QAAI,cAAc,MAAM;AACtB,mBAAa,CAAC,GAAG;AAAA,IACnB;AACA,YAAQ,YAAY,SAAS,IAAI;AAC/B,YAAM,eAAe,gBAAgB,EAAE;AACvC,mBAAa,gBAAgB,aAAa,gBAAgB,KAAK;AAC/D,SAAG,UAAU,IAAI,KAAK,GAAG,WAAW,KAAK,OAAO,YAAY;AAAA,IAC9D,CAAC;AACD,WAAO;AAAA,EACT;AAMA,WAAS,gBAAgB,KAAK;AAC5B,QAAI;AAAA;AAAA,MAAuC,qBAAqB,KAAK,iBAAiB;AAAA;AACtF,QAAI,gBAAgB,MAAM;AACxB,qBAAe,CAAC;AAAA,IAClB;AACA,YAAQ,cAAc,SAAS,iBAAiB;AAC9C,YAAM,eAAe,gBAAgB,eAAe;AACpD,mBAAa,gBAAgB,aAAa,gBAAgB,KAAK;AAC/D,sBAAgB,aAAa,YAAY,EAAE;AAC3C,sBAAgB,aAAa,yBAAyB,EAAE;AAAA,IAC1D,CAAC;AACD,WAAO;AAAA,EACT;AAMA,WAAS,wBAAwB,YAAY,UAAU;AACrD,YAAQ,WAAW,OAAO,QAAQ,GAAG,SAAS,KAAK;AACjD,YAAM,eAAe,gBAAgB,GAAG;AACxC,mBAAa,gBAAgB,aAAa,gBAAgB,KAAK;AAAA,IACjE,CAAC;AACD,YAAQ,YAAY,SAAS,IAAI;AAC/B,YAAM,eAAe,gBAAgB,EAAE;AACvC,UAAI,aAAa,iBAAiB,GAAG;AACnC,WAAG,UAAU,OAAO,KAAK,GAAG,WAAW,KAAK,OAAO,YAAY;AAAA,MACjE;AAAA,IACF,CAAC;AACD,YAAQ,UAAU,SAAS,iBAAiB;AAC1C,YAAM,eAAe,gBAAgB,eAAe;AACpD,UAAI,aAAa,iBAAiB,GAAG;AACnC,wBAAgB,gBAAgB,UAAU;AAC1C,wBAAgB,gBAAgB,uBAAuB;AAAA,MACzD;AAAA,IACF,CAAC;AAAA,EACH;AAWA,WAAS,aAAa,WAAW,KAAK;AACpC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAM,OAAO,UAAU,CAAC;AACxB,UAAI,KAAK,WAAW,GAAG,GAAG;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,WAAS,cAAc,SAAS;AAE9B,UAAM;AAAA;AAAA,MAAuC;AAAA;AAC7C,QAAI,IAAI,SAAS,MAAM,IAAI,QAAQ,QAAQ,IAAI,YAAY,QAAQ,KAAK,oBAAoB,GAAG;AAC7F,aAAO;AAAA,IACT;AAEA,QAAI,IAAI,SAAS,YAAY,IAAI,SAAS,YAAY,IAAI,YAAY,WAAW,IAAI,YAAY,WAAW,IAAI,YAAY,QAAQ;AAClI,aAAO;AAAA,IACT;AACA,QAAI,IAAI,SAAS,cAAc,IAAI,SAAS,SAAS;AACnD,aAAO,IAAI;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAMA,WAAS,mBAAmB,MAAM,OAAO,UAAU;AACjD,QAAI,QAAQ,QAAQ,SAAS,MAAM;AACjC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,QAAQ,SAAS,GAAG;AAAE,mBAAS,OAAO,MAAM,CAAC;AAAA,QAAE,CAAC;AAAA,MACxD,OAAO;AACL,iBAAS,OAAO,MAAM,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAMA,WAAS,wBAAwB,MAAM,OAAO,UAAU;AACtD,QAAI,QAAQ,QAAQ,SAAS,MAAM;AACjC,UAAI,SAAS,SAAS,OAAO,IAAI;AACjC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAS,OAAO,OAAO,OAAK,MAAM,QAAQ,CAAC,IAAI,CAAC;AAAA,MAClD,OAAO;AACL,iBAAS,OAAO,OAAO,OAAK,MAAM,KAAK;AAAA,MACzC;AACA,eAAS,OAAO,IAAI;AACpB,cAAQ,QAAQ,OAAK,SAAS,OAAO,MAAM,CAAC,CAAC;AAAA,IAC/C;AAAA,EACF;AAMA,WAAS,kBAAkB,KAAK;AAC9B,QAAI,eAAe,qBAAqB,IAAI,UAAU;AACpD,aAAO,QAAQ,IAAI,iBAAiB,gBAAgB,CAAC,EAAE,IAAI,SAAS,GAAG;AAAE;AAAA;AAAA,UAAuC,EAAI;AAAA;AAAA,MAAM,CAAC;AAAA,IAC7H;AAEA,QAAI,eAAe,oBAAoB,IAAI,OAAO;AAChD,aAAO,QAAQ,IAAI,KAAK;AAAA,IAC1B;AAEA,WAAO,IAAI;AAAA,EACb;AASA,WAAS,kBAAkB,WAAW,UAAU,QAAQ,KAAK,UAAU;AACrE,QAAI,OAAO,QAAQ,aAAa,WAAW,GAAG,GAAG;AAC/C;AAAA,IACF,OAAO;AACL,gBAAU,KAAK,GAAG;AAAA,IACpB;AACA,QAAI,cAAc,GAAG,GAAG;AACtB,YAAM,OAAO,gBAAgB,KAAK,MAAM;AACxC,yBAAmB,MAAM,kBAAkB,GAAG,GAAG,QAAQ;AACzD,UAAI,UAAU;AACZ,wBAAgB,KAAK,MAAM;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,eAAe,iBAAiB;AAClC,cAAQ,IAAI,UAAU,SAAS,OAAO;AACpC,YAAI,UAAU,QAAQ,KAAK,KAAK,GAAG;AAIjC,kCAAwB,MAAM,MAAM,kBAAkB,KAAK,GAAG,QAAQ;AAAA,QACxE,OAAO;AACL,oBAAU,KAAK,KAAK;AAAA,QACtB;AACA,YAAI,UAAU;AACZ,0BAAgB,OAAO,MAAM;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,UAAI,SAAS,GAAG,EAAE,QAAQ,SAAS,OAAO,MAAM;AAC9C,YAAI,iBAAiB,QAAQ,MAAM,SAAS,IAAI;AAC9C;AAAA,QACF;AACA,2BAAmB,MAAM,OAAO,QAAQ;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF;AAMA,WAAS,gBAAgB,KAAK,QAAQ;AACpC,UAAM;AAAA;AAAA,MAAyD;AAAA;AAC/D,QAAI,QAAQ,cAAc;AACxB,mBAAa,SAAS,0BAA0B;AAChD,UAAI,CAAC,QAAQ,cAAc,GAAG;AAC5B,eAAO,KAAK,EAAE,KAAK,SAAS,SAAS,QAAQ,mBAAmB,UAAU,QAAQ,SAAS,CAAC;AAC5F,qBAAa,SAAS,0BAA0B,EAAE,SAAS,QAAQ,mBAAmB,UAAU,QAAQ,SAAS,CAAC;AAAA,MACpH;AAAA,IACF;AAAA,EACF;AAQA,WAAS,iBAAiB,UAAU,OAAO;AACzC,eAAW,OAAO,MAAM,KAAK,GAAG;AAC9B,eAAS,OAAO,GAAG;AAAA,IACrB;AACA,UAAM,QAAQ,SAAS,OAAO,KAAK;AACjC,eAAS,OAAO,KAAK,KAAK;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAOA,WAAS,eAAe,KAAK,MAAM;AAEjC,UAAM,YAAY,CAAC;AACnB,UAAM,WAAW,IAAI,SAAS;AAC9B,UAAM,mBAAmB,IAAI,SAAS;AAEtC,UAAM,SAAS,CAAC;AAChB,UAAM,eAAe,gBAAgB,GAAG;AACxC,QAAI,aAAa,qBAAqB,CAAC,aAAa,aAAa,iBAAiB,GAAG;AACnF,mBAAa,oBAAoB;AAAA,IACnC;AAIA,QAAI,WAAY,eAAe,mBAAmB,IAAI,eAAe,QAAS,kBAAkB,KAAK,aAAa,MAAM;AACxH,QAAI,aAAa,mBAAmB;AAClC,iBAAW,YAAY,aAAa,kBAAkB,mBAAmB;AAAA,IAC3E;AAGA,QAAI,SAAS,OAAO;AAClB,wBAAkB,WAAW,kBAAkB,QAAQ,eAAe,GAAG,GAAG,QAAQ;AAAA,IACtF;AAGA,sBAAkB,WAAW,UAAU,QAAQ,KAAK,QAAQ;AAG5D,QAAI,aAAa,qBAAqB,IAAI,YAAY,YACrD,IAAI,YAAY,WAAW,gBAAgB,KAAK,MAAM,MAAM,UAAW;AACtE,YAAM,SAAS,aAAa;AAAA,MAAsE;AAClG,YAAM,OAAO,gBAAgB,QAAQ,MAAM;AAC3C,yBAAmB,MAAM,OAAO,OAAO,gBAAgB;AAAA,IACzD;AAGA,UAAM,WAAW,qBAAqB,KAAK,YAAY;AACvD,YAAQ,UAAU,SAAS,MAAM;AAC/B,wBAAkB,WAAW,UAAU,QAAQ,UAAU,IAAI,GAAG,QAAQ;AAExE,UAAI,CAAC,QAAQ,MAAM,MAAM,GAAG;AAC1B,gBAAQ,aAAa,IAAI,EAAE,iBAAiB,cAAc,GAAG,SAAS,YAAY;AAChF,4BAAkB,WAAW,UAAU,QAAQ,YAAY,QAAQ;AAAA,QACrE,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAGD,qBAAiB,UAAU,gBAAgB;AAE3C,WAAO,EAAE,QAAQ,UAAU,QAAQ,cAAc,QAAQ,EAAE;AAAA,EAC7D;AAQA,WAAS,YAAY,WAAW,MAAM,WAAW;AAC/C,QAAI,cAAc,IAAI;AACpB,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,SAAS,MAAM,mBAAmB;AAC3C,kBAAY,KAAK,UAAU,SAAS;AAAA,IACtC;AACA,UAAM,IAAI,mBAAmB,SAAS;AACtC,iBAAa,mBAAmB,IAAI,IAAI,MAAM;AAC9C,WAAO;AAAA,EACT;AAMA,WAAS,UAAU,QAAQ;AACzB,aAAS,mBAAmB,MAAM;AAClC,QAAI,YAAY;AAChB,WAAO,QAAQ,SAAS,OAAO,KAAK;AAClC,kBAAY,YAAY,WAAW,KAAK,KAAK;AAAA,IAC/C,CAAC;AACD,WAAO;AAAA,EACT;AAYA,WAAS,WAAW,KAAK,QAAQK,SAAQ;AAEvC,UAAM,UAAU;AAAA,MACd,cAAc;AAAA,MACd,cAAc,gBAAgB,KAAK,IAAI;AAAA,MACvC,mBAAmB,gBAAgB,KAAK,MAAM;AAAA,MAC9C,aAAa,kBAAkB,QAAQ,IAAI;AAAA,MAC3C,kBAAkB,SAAS;AAAA,IAC7B;AACA,wBAAoB,KAAK,cAAc,OAAO,OAAO;AACrD,QAAIA,YAAW,QAAW;AACxB,cAAQ,WAAW,IAAIA;AAAA,IACzB;AACA,QAAI,gBAAgB,GAAG,EAAE,SAAS;AAChC,cAAQ,YAAY,IAAI;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAUA,WAAS,aAAa,aAAa,KAAK;AACtC,UAAM,cAAc,yBAAyB,KAAK,WAAW;AAC7D,QAAI,aAAa;AACf,UAAI,gBAAgB,QAAQ;AAC1B,eAAO,IAAI,SAAS;AAAA,MACtB,WAAW,gBAAgB,KAAK;AAC9B,eAAO;AAAA,MACT,WAAW,YAAY,QAAQ,MAAM,MAAM,GAAG;AAC5C,gBAAQ,YAAY,MAAM,CAAC,EAAE,MAAM,GAAG,GAAG,SAAS,MAAM;AACtD,iBAAO,KAAK,KAAK;AACjB,sBAAY,OAAO,IAAI;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACT,OAAO;AACL,cAAM,YAAY,IAAI,SAAS;AAC/B,gBAAQ,YAAY,MAAM,GAAG,GAAG,SAAS,MAAM;AAC7C,iBAAO,KAAK,KAAK;AACjB,cAAI,YAAY,IAAI,IAAI,GAAG;AACzB,wBAAY,OAAO,IAAI,EAAE,QAAQ,SAAS,OAAO;AAAE,wBAAU,OAAO,MAAM,KAAK;AAAA,YAAE,CAAC;AAAA,UACpF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAMA,WAAS,aAAa,KAAK;AACzB,WAAO,CAAC,CAAC,gBAAgB,KAAK,MAAM,KAAK,gBAAgB,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK;AAAA,EACxF;AAOA,WAAS,qBAAqB,KAAK,kBAAkB;AACnD,UAAM,WAAW,oBAAoB,yBAAyB,KAAK,SAAS;AAE5E,UAAM,WAAW;AAAA,MACf,WAAW,gBAAgB,GAAG,EAAE,UAAU,cAAc,KAAK,OAAO;AAAA,MACpE,WAAW,KAAK,OAAO;AAAA,MACvB,aAAa,KAAK,OAAO;AAAA,IAC3B;AACA,QAAI,KAAK,OAAO,yBAAyB,gBAAgB,GAAG,EAAE,WAAW,CAAC,aAAa,GAAG,GAAG;AAC3F,eAAS,OAAO;AAAA,IAClB;AACA,QAAI,UAAU;AACZ,YAAM,QAAQ,kBAAkB,QAAQ;AACxC,UAAI,MAAM,SAAS,GAAG;AACpB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,QAAQ,MAAM,CAAC;AACrB,cAAI,MAAM,QAAQ,OAAO,MAAM,GAAG;AAChC,qBAAS,YAAY,cAAc,MAAM,MAAM,CAAC,CAAC;AAAA,UACnD,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AACzC,qBAAS,cAAc,cAAc,MAAM,MAAM,CAAC,CAAC;AAAA,UACrD,WAAW,MAAM,QAAQ,aAAa,MAAM,GAAG;AAC7C,qBAAS,aAAa,MAAM,MAAM,EAAE,MAAM;AAAA,UAC5C,WAAW,MAAM,QAAQ,cAAc,MAAM,GAAG;AAC9C,qBAAS,cAAc,MAAM,MAAM,EAAE,MAAM;AAAA,UAC7C,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AACzC,kBAAM,aAAa,MAAM,MAAM,CAAC;AAChC,gBAAI,YAAY,WAAW,MAAM,GAAG;AACpC,kBAAM,YAAY,UAAU,IAAI;AAChC,gBAAI,cAAc,UAAU,SAAS,IAAI,UAAU,KAAK,GAAG,IAAI;AAE/D,qBAAS,SAAS;AAClB,qBAAS,eAAe;AAAA,UAC1B,WAAW,MAAM,QAAQ,OAAO,MAAM,GAAG;AACvC,kBAAM,WAAW,MAAM,MAAM,CAAC;AAC9B,gBAAI,YAAY,SAAS,MAAM,GAAG;AAClC,kBAAM,UAAU,UAAU,IAAI;AAC9B,gBAAI,cAAc,UAAU,SAAS,IAAI,UAAU,KAAK,GAAG,IAAI;AAC/D,qBAAS,OAAO;AAChB,qBAAS,aAAa;AAAA,UACxB,WAAW,MAAM,QAAQ,eAAe,MAAM,GAAG;AAC/C,kBAAM,iBAAiB,MAAM,MAAM,gBAAgB,MAAM;AACzD,qBAAS,cAAc,kBAAkB;AAAA,UAC3C,WAAW,KAAK,GAAG;AACjB,qBAAS,YAAY;AAAA,UACvB,OAAO;AACL,qBAAS,kCAAkC,KAAK;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,WAAS,aAAa,KAAK;AACzB,WAAO,yBAAyB,KAAK,aAAa,MAAM,yBACvD,QAAQ,KAAK,MAAM,KAAK,gBAAgB,KAAK,SAAS,MAAM;AAAA,EAC/D;AAQA,WAAS,oBAAoB,KAAK,KAAK,oBAAoB;AACzD,QAAI,oBAAoB;AACxB,mBAAe,KAAK,SAAS,WAAW;AACtC,UAAI,qBAAqB,MAAM;AAC7B,4BAAoB,UAAU,iBAAiB,KAAK,oBAAoB,GAAG;AAAA,MAC7E;AAAA,IACF,CAAC;AACD,QAAI,qBAAqB,MAAM;AAC7B,aAAO;AAAA,IACT,OAAO;AACL,UAAI,aAAa,GAAG,GAAG;AAGrB,eAAO,iBAAiB,IAAI,SAAS,GAAG,mBAAmB,kBAAkB,CAAC;AAAA,MAChF,OAAO;AACL,eAAO,UAAU,kBAAkB;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAOA,WAAS,eAAe,QAAQ;AAC9B,WAAO,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;AAAA,EACrC;AAMA,WAAS,kBAAkB,SAAS,UAAU;AAC5C,UAAM,QAAQ,QAAQ,CAAC;AACvB,UAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AACvC,QAAI,SAAS,QAAQ;AACnB,UAAI,SAAS;AACb,UAAI,SAAS,cAAc;AACzB,iBAAS,UAAU,iBAAiB,OAAO,SAAS,YAAY,CAAC;AAAA,MACnE;AACA,UAAI,SAAS,WAAW,UAAU,SAAS,SAAS;AAClD,iBAAS,UAAU;AACnB,eAAO,YAAY;AAAA,MACrB;AACA,UAAI,SAAS,WAAW,aAAa,QAAQ,SAAS;AACpD,iBAAS,UAAU;AACnB,eAAO,YAAY,OAAO;AAAA,MAC5B;AACA,UAAI,OAAO,SAAS,WAAW,UAAU;AACvC,kBAAU,EAAE,WAAW,WAAW;AAChC,iBAAO;AAAA,YAAS;AAAA;AAAA,YAAwB,SAAS;AAAA,UAAO;AAAA,QAC1D,GAAG,CAAC;AAAA,MACN;AAAA,IACF;AACA,QAAI,SAAS,MAAM;AACjB,UAAI,SAAS;AACb,UAAI,SAAS,YAAY;AACvB,YAAI,YAAY,SAAS;AACzB,YAAI,SAAS,eAAe,UAAU;AACpC,sBAAY;AAAA,QACd;AACA,iBAAS,UAAU,iBAAiB,OAAO,SAAS,CAAC;AAAA,MACvD;AACA,UAAI,SAAS,SAAS,UAAU,SAAS,SAAS;AAChD,iBAAS,UAAU;AAEnB,eAAO,eAAe,EAAE,OAAO,SAAS,UAAU,KAAK,OAAO,eAAe,CAAC;AAAA,MAChF;AACA,UAAI,SAAS,SAAS,aAAa,QAAQ,SAAS;AAClD,iBAAS,UAAU;AAEnB,eAAO,eAAe,EAAE,OAAO,OAAO,UAAU,KAAK,OAAO,eAAe,CAAC;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAUA,WAAS,oBAAoB,KAAK,MAAM,eAAe,QAAQ,OAAO;AACpE,QAAI,UAAU,MAAM;AAClB,eAAS,CAAC;AAAA,IACZ;AACA,QAAI,OAAO,MAAM;AACf,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,kBAAkB,KAAK,IAAI;AAClD,QAAI,gBAAgB;AAClB,UAAIL,OAAM,eAAe,KAAK;AAC9B,UAAI,gBAAgB;AACpB,UAAIA,SAAQ,SAAS;AACnB,eAAO;AAAA,MACT;AACA,UAAIA,KAAI,QAAQ,aAAa,MAAM,GAAG;AACpC,QAAAA,OAAMA,KAAI,MAAM,EAAE;AAClB,wBAAgB;AAAA,MAClB,WAAWA,KAAI,QAAQ,KAAK,MAAM,GAAG;AACnC,QAAAA,OAAMA,KAAI,MAAM,CAAC;AACjB,wBAAgB;AAAA,MAClB;AACA,UAAIA,KAAI,QAAQ,GAAG,MAAM,GAAG;AAC1B,QAAAA,OAAM,MAAMA,OAAM;AAAA,MACpB;AACA,UAAI;AACJ,UAAI,eAAe;AACjB,qBAAa,UAAU,KAAK,WAAW;AACrC,cAAI,OAAO;AACT,mBAAO,SAAS,SAAS,aAAaA,OAAM,GAAG,EAAE,KAAK,KAAK,KAAK;AAAA,UAClE,OAAO;AACL,mBAAO,SAAS,aAAaA,OAAM,GAAG,EAAE,KAAK,GAAG;AAAA,UAClD;AAAA,QACF,GAAG,CAAC,CAAC;AAAA,MACP,OAAO;AACL,qBAAa,UAAUA,IAAG;AAAA,MAC5B;AACA,iBAAW,OAAO,YAAY;AAC5B,YAAI,WAAW,eAAe,GAAG,GAAG;AAClC,cAAI,OAAO,GAAG,KAAK,MAAM;AACvB,mBAAO,GAAG,IAAI,WAAW,GAAG;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,oBAAoB,UAAU,UAAU,GAAG,CAAC,GAAG,MAAM,eAAe,QAAQ,KAAK;AAAA,EAC1F;AAQA,WAAS,UAAU,KAAK,QAAQ,YAAY;AAC1C,QAAI,KAAK,OAAO,WAAW;AACzB,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,wBAAkB,KAAK,0BAA0B;AACjD,aAAO;AAAA,IACT;AAAA,EACF;AAQA,WAAS,oBAAoB,KAAK,OAAO,gBAAgB;AACvD,WAAO,oBAAoB,KAAK,WAAW,MAAM,gBAAgB,KAAK;AAAA,EACxE;AAQA,WAAS,oBAAoB,KAAK,OAAO,gBAAgB;AACvD,WAAO,oBAAoB,KAAK,WAAW,OAAO,gBAAgB,KAAK;AAAA,EACzE;AAOA,WAAS,kBAAkB,KAAK,OAAO;AACrC,WAAO,aAAa,oBAAoB,KAAK,KAAK,GAAG,oBAAoB,KAAK,KAAK,CAAC;AAAA,EACtF;AAOA,WAAS,qBAAqB,KAAK,QAAQ,aAAa;AACtD,QAAI,gBAAgB,MAAM;AACxB,UAAI;AACF,YAAI,iBAAiB,QAAQ,WAAW;AAAA,MAC1C,SAAS,GAAG;AAEV,YAAI,iBAAiB,QAAQ,mBAAmB,WAAW,CAAC;AAC5D,YAAI,iBAAiB,SAAS,oBAAoB,MAAM;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAMA,WAAS,oBAAoB,KAAK;AAChC,QAAI,IAAI,aAAa;AACnB,UAAI;AACF,cAAM,MAAM,IAAI,IAAI,IAAI,WAAW;AACnC,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B,SAAS,GAAG;AACV,0BAAkB,YAAY,EAAE,MAAM,uBAAuB,EAAE,KAAK,IAAI,YAAY,CAAC;AAAA,MACvF;AAAA,IACF;AAAA,EACF;AAOA,WAAS,UAAU,KAAK,QAAQ;AAC9B,WAAO,OAAO,KAAK,IAAI,sBAAsB,CAAC;AAAA,EAChD;AAYA,WAAS,WAAW,MAAM,MAAM,SAAS;AACvC;AAAA,IAA8B,KAAK,YAAY;AAC/C,QAAI,SAAS;AACX,UAAI,mBAAmB,WAAW,OAAO,YAAY,UAAU;AAC7D,eAAO,iBAAiB,MAAM,MAAM,MAAM,MAAM;AAAA,UAC9C,gBAAgB,cAAc,OAAO,KAAK;AAAA,UAC1C,eAAe;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,YAAI,iBAAiB,cAAc,QAAQ,MAAM;AAGjD,YAAK,QAAQ,UAAU,CAAC,kBAAoB,QAAQ,UAAU,CAAC,kBAAkB,CAAC,cAAc,QAAQ,MAAM,GAAI;AAChH,2BAAiB;AAAA,QACnB;AACA,eAAO;AAAA,UAAiB;AAAA,UAAM;AAAA,UAAM,cAAc,QAAQ,MAAM;AAAA,UAAG,QAAQ;AAAA,UACzE;AAAA,YACE,SAAS,QAAQ;AAAA,YACjB,SAAS,QAAQ;AAAA,YACjB,QAAQ,QAAQ;AAAA,YAChB,gBAAgB;AAAA,YAChB,cAAc,QAAQ;AAAA,YACtB,QAAQ,QAAQ;AAAA,YAChB,eAAe;AAAA,UACjB;AAAA,QAAC;AAAA,MACL;AAAA,IACF,OAAO;AACL,aAAO,iBAAiB,MAAM,MAAM,MAAM,MAAM;AAAA,QAC9C,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAMA,WAAS,gBAAgB,KAAK;AAC5B,UAAM,MAAM,CAAC;AACb,WAAO,KAAK;AACV,UAAI,KAAK,GAAG;AACZ,YAAM,IAAI;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAQA,WAAS,WAAW,KAAK,MAAM,eAAe;AAC5C,UAAM,MAAM,IAAI,IAAI,MAAM,SAAS,aAAa,WAAW,SAAS,OAAO,OAAO,MAAM;AACxF,UAAM,SAAS,SAAS,aAAa,WAAW,SAAS,SAAS,OAAO;AACzE,UAAM,WAAW,WAAW,IAAI;AAEhC,QAAI,KAAK,OAAO,kBAAkB;AAChC,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,aAAa,KAAK,oBAAoB,aAAa,EAAE,KAAK,SAAS,GAAG,aAAa,CAAC;AAAA,EAC7F;AAMA,WAAS,mBAAmB,KAAK;AAC/B,QAAI,eAAe,SAAU,QAAO;AACpC,UAAM,WAAW,IAAI,SAAS;AAC9B,eAAW,OAAO,KAAK;AACrB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,YAAI,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,EAAE,YAAY,YAAY;AACtD,cAAI,GAAG,EAAE,QAAQ,SAAS,GAAG;AAAE,qBAAS,OAAO,KAAK,CAAC;AAAA,UAAE,CAAC;AAAA,QAC1D,WAAW,OAAO,IAAI,GAAG,MAAM,YAAY,EAAE,IAAI,GAAG,aAAa,OAAO;AACtE,mBAAS,OAAO,KAAK,KAAK,UAAU,IAAI,GAAG,CAAC,CAAC;AAAA,QAC/C,OAAO;AACL,mBAAS,OAAO,KAAK,IAAI,GAAG,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAQA,WAAS,mBAAmB,UAAU,MAAM,OAAO;AAEjD,WAAO,IAAI,MAAM,OAAO;AAAA,MACtB,KAAK,SAAS,QAAQ,KAAK;AACzB,YAAI,OAAO,QAAQ,SAAU,QAAO,OAAO,GAAG;AAC9C,YAAI,QAAQ,SAAU,QAAO,OAAO;AACpC,YAAI,QAAQ,QAAQ;AAClB,iBAAO,SAAS,OAAO;AACrB,mBAAO,KAAK,KAAK;AACjB,qBAAS,OAAO,MAAM,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,YAAI,OAAO,OAAO,GAAG,MAAM,YAAY;AACrC,iBAAO,WAAW;AAChB,mBAAO,GAAG,EAAE,MAAM,QAAQ,SAAS;AACnC,qBAAS,OAAO,IAAI;AACpB,mBAAO,QAAQ,SAAS,GAAG;AAAE,uBAAS,OAAO,MAAM,CAAC;AAAA,YAAE,CAAC;AAAA,UACzD;AAAA,QACF;AAEA,YAAI,OAAO,GAAG,KAAK,OAAO,GAAG,EAAE,WAAW,GAAG;AAC3C,iBAAO,OAAO,GAAG,EAAE,CAAC;AAAA,QACtB,OAAO;AACL,iBAAO,OAAO,GAAG;AAAA,QACnB;AAAA,MACF;AAAA,MACA,KAAK,SAAS,QAAQ,OAAO,OAAO;AAClC,eAAO,KAAK,IAAI;AAChB,iBAAS,OAAO,IAAI;AACpB,eAAO,QAAQ,SAAS,GAAG;AAAE,mBAAS,OAAO,MAAM,CAAC;AAAA,QAAE,CAAC;AACvD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAMA,WAAS,cAAc,UAAU;AAC/B,WAAO,IAAI,MAAM,UAAU;AAAA,MACzB,KAAK,SAAS,QAAQ,MAAM;AAC1B,YAAI,OAAO,SAAS,UAAU;AAE5B,gBAAM,SAAS,QAAQ,IAAI,QAAQ,IAAI;AAEvC,cAAI,OAAO,WAAW,YAAY;AAChC,mBAAO,WAAW;AAChB,qBAAO,OAAO,MAAM,UAAU,SAAS;AAAA,YACzC;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,SAAS,UAAU;AAErB,iBAAO,MAAM,OAAO,YAAY,QAAQ;AAAA,QAC1C;AACA,YAAI,QAAQ,QAAQ;AAElB,cAAI,OAAO,OAAO,IAAI,MAAM,YAAY;AACtC,mBAAO,WAAW;AAChB,qBAAO,SAAS,IAAI,EAAE,MAAM,UAAU,SAAS;AAAA,YACjD;AAAA,UACF;AAAA,QACF;AACA,cAAM,QAAQ,SAAS,OAAO,IAAI;AAElC,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO;AAAA,QACT,WAAW,MAAM,WAAW,GAAG;AAC7B,iBAAO,MAAM,CAAC;AAAA,QAChB,OAAO;AACL,iBAAO,mBAAmB,QAAQ,MAAM,KAAK;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,KAAK,SAAS,QAAQ,MAAM,OAAO;AACjC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO;AAAA,QACT;AACA,eAAO,OAAO,IAAI;AAClB,YAAI,SAAS,OAAO,MAAM,YAAY,YAAY;AAChD,gBAAM,QAAQ,SAAS,GAAG;AAAE,mBAAO,OAAO,MAAM,CAAC;AAAA,UAAE,CAAC;AAAA,QACtD,WAAW,OAAO,UAAU,YAAY,EAAE,iBAAiB,OAAO;AAChE,iBAAO,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,QAC3C,OAAO;AACL,iBAAO,OAAO,MAAM,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB,SAAS,QAAQ,MAAM;AACrC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,OAAO,IAAI;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AAAA;AAAA,MAEA,SAAS,SAAS,QAAQ;AACxB,eAAO,QAAQ,QAAQ,OAAO,YAAY,MAAM,CAAC;AAAA,MACnD;AAAA,MACA,0BAA0B,SAAS,QAAQ,MAAM;AAC/C,eAAO,QAAQ,yBAAyB,OAAO,YAAY,MAAM,GAAG,IAAI;AAAA,MAC1E;AAAA,IACF,CAAC;AAAA,EACH;AAWA,WAAS,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,WAAW;AAChE,QAAI,UAAU;AACd,QAAI,SAAS;AACb,UAAM,OAAO,OAAO,MAAM,CAAC;AAC3B,QAAI,IAAI,iBAAiB,OAAO,YAAY,aAAa;AACvD,UAAI,UAAU,IAAI,QAAQ,SAAS,UAAU,SAAS;AACpD,kBAAU;AACV,iBAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,OAAO,MAAM;AACf,YAAM,YAAY,EAAE;AAAA,IACtB;AACA,UAAM,kBAAkB,IAAI,WAAW;AACvC,UAAM,SAAS,IAAI,UAAU;AAE7B,QAAI,CAAC,aAAa,GAAG,GAAG;AAEtB,gBAAU,OAAO;AACjB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,IAAI,kBAAkB,UAAU,UAAU,GAAG,CAAC;AAC7D,QAAI,UAAU,QAAQ,UAAU,WAAW;AACzC,wBAAkB,KAAK,oBAAoB,EAAE,QAAQ,yBAAyB,KAAK,WAAW,EAAE,CAAC;AACjG,gBAAU,MAAM;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,gBAAgB,GAAG;AACjC,UAAM,YAAY,QAAQ;AAE1B,QAAI,WAAW;AACb,YAAM,aAAa,gBAAgB,WAAW,YAAY;AAC1D,UAAI,cAAc,MAAM;AACtB,eAAO;AAAA,MACT;AAEA,YAAM,aAAa,gBAAgB,WAAW,YAAY;AAC1D,UAAI,cAAc,MAAM;AACtB,YAAI,MAAM,SAAS,WAAW,YAAY,CAAC,GAAG;AAC5C;AAAA,UAA8B;AAAA,QAChC,OAAO;AACL,oBAAU,OAAO;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,UAAM,kBAAkB,yBAAyB,KAAK,YAAY;AAElE,QAAI,cAAc,QAAW;AAC3B,YAAM,eAAe,SAAS,kBAAkB;AAC9C,eAAO,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,CAAC,CAAC,gBAAgB;AAAA,MACzE;AACA,YAAM,iBAAiB,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,OAAO,KAAK,cAAc,UAAU,gBAAgB;AACvH,UAAI,aAAa,KAAK,gBAAgB,cAAc,MAAM,OAAO;AAC/D,kBAAU,OAAO;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,UAAU;AACd,QAAI,eAAe,yBAAyB,KAAK,SAAS;AAC1D,QAAI,gBAAgB;AACpB,QAAI,YAAY;AAChB,QAAI,cAAc;AAChB,YAAM,cAAc,aAAa,MAAM,GAAG;AAC1C,YAAM,WAAW,YAAY,CAAC,EAAE,KAAK;AACrC,UAAI,aAAa,QAAQ;AACvB,kBAAU,gBAAgB,KAAK,SAAS;AAAA,MAC1C,OAAO;AACL,kBAAU,UAAU,iBAAiB,KAAK,QAAQ,CAAC;AAAA,MACrD;AAEA,sBAAgB,YAAY,CAAC,KAAK,QAAQ,KAAK;AAC/C,gBAAU,gBAAgB,OAAO;AACjC,UAAI,iBAAiB,UAAU,QAAQ,OAAO,QAAQ,cAAc,MAAM;AACxE,kBAAU,OAAO;AACjB,eAAO;AAAA,MACT,WAAW,iBAAiB,SAAS;AACnC,YAAI,QAAQ,KAAK;AACf,oBAAU,OAAO;AACjB,iBAAO;AAAA,QACT,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,WAAW,iBAAiB,WAAW;AACrC,qBAAa,SAAS,YAAY;AAAA,MACpC,WAAW,aAAa,QAAQ,OAAO,MAAM,GAAG;AAC9C,cAAM,gBAAgB,aAAa,MAAM,GAAG;AAC5C,yBAAiB,cAAc,CAAC,KAAK,QAAQ,KAAK;AAAA,MACpD;AAAA,IACF;AAEA,QAAI,QAAQ,KAAK;AACf,UAAI,QAAQ,WAAW;AACrB,qBAAa,SAAS,YAAY;AAAA,MACpC,OAAO;AACL,YAAI,iBAAiB,MAAM;AACzB,cAAI,OAAO;AACT,kBAAM,YAAY,gBAAgB,KAAK;AACvC,gBAAI,aAAa,UAAU,eAAe,UAAU,YAAY,OAAO;AACrE,8BAAgB,UAAU,YAAY;AAAA,YACxC;AAAA,UACF;AACA,cAAI,iBAAiB,MAAM;AACzB,4BAAgB;AAAA,UAClB;AAAA,QACF;AACA,YAAI,QAAQ,kBAAkB,MAAM;AAClC,kBAAQ,iBAAiB,CAAC;AAAA,QAC5B;AACA,YAAI,kBAAkB,WAAW,QAAQ,eAAe,WAAW,GAAG;AACpE,kBAAQ,eAAe,KAAK,WAAW;AACrC,6BAAiB,MAAM,MAAM,KAAK,OAAO,GAAG;AAAA,UAC9C,CAAC;AAAA,QACH,WAAW,kBAAkB,OAAO;AAClC,kBAAQ,eAAe,KAAK,WAAW;AACrC,6BAAiB,MAAM,MAAM,KAAK,OAAO,GAAG;AAAA,UAC9C,CAAC;AAAA,QACH,WAAW,kBAAkB,QAAQ;AACnC,kBAAQ,iBAAiB,CAAC;AAC1B,kBAAQ,eAAe,KAAK,WAAW;AACrC,6BAAiB,MAAM,MAAM,KAAK,OAAO,GAAG;AAAA,UAC9C,CAAC;AAAA,QACH;AACA,kBAAU,OAAO;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,MAAM,IAAI,eAAe;AAC/B,YAAQ,MAAM;AACd,YAAQ,YAAY;AACpB,UAAM,iBAAiB,WAAW;AAChC,cAAQ,MAAM;AACd,cAAQ,YAAY;AACpB,UAAI,QAAQ,kBAAkB,QAC9B,QAAQ,eAAe,SAAS,GAAG;AACjC,cAAM,gBAAgB,QAAQ,eAAe,MAAM;AACnD,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,UAAM,iBAAiB,yBAAyB,KAAK,WAAW;AAChE,QAAI,gBAAgB;AAClB,UAAI,iBAAiB,OAAO,cAAc;AAE1C,UAAI,mBAAmB,QACvB,CAAC,aAAa,KAAK,eAAe,EAAE,QAAQ,gBAAgB,OAAO,CAAC,GAAG;AACrE,kBAAU,OAAO;AACjB,uBAAe;AACf,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,mBAAmB,CAAC,WAAW;AACjC,UAAI,CAAC,QAAQ,eAAe,GAAG;AAC7B,kBAAU,OAAO;AACjB,uBAAe;AACf,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,UAAU,WAAW,KAAK,QAAQ,cAAc;AAEpD,QAAI,SAAS,SAAS,CAAC,aAAa,GAAG,GAAG;AACxC,cAAQ,cAAc,IAAI;AAAA,IAC5B;AAEA,QAAI,IAAI,SAAS;AACf,gBAAU,aAAa,SAAS,IAAI,OAAO;AAAA,IAC7C;AACA,UAAM,UAAU,eAAe,KAAK,IAAI;AACxC,QAAI,SAAS,QAAQ;AACrB,UAAM,cAAc,QAAQ;AAC5B,QAAI,IAAI,QAAQ;AACd,uBAAiB,aAAa,mBAAmB,IAAI,MAAM,CAAC;AAAA,IAC9D;AACA,UAAM,iBAAiB,mBAAmB,kBAAkB,KAAK,KAAK,CAAC;AACvE,UAAM,cAAc,iBAAiB,aAAa,cAAc;AAChE,QAAI,mBAAmB,aAAa,aAAa,GAAG;AAEpD,QAAI,KAAK,OAAO,uBAAuB,SAAS,OAAO;AACrD,uBAAiB,IAAI,yBAAyB,gBAAgB,QAAQ,IAAI,KAAK,MAAM;AAAA,IACvF;AAGA,QAAI,QAAQ,QAAQ,SAAS,IAAI;AAC/B,aAAO,SAAS;AAAA,IAClB;AAQA,UAAM,oBAAoB,oBAAoB,KAAK,YAAY;AAE/D,UAAM,eAAe,gBAAgB,GAAG,EAAE;AAE1C,QAAI,eAAe,KAAK,OAAO,wBAAwB,QAAQ,IAAI,KAAK;AAGxE,UAAM,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,YAAY,cAAc,gBAAgB;AAAA,MAC1C,oBAAoB;AAAA,MACpB,sBAAsB,cAAc,WAAW;AAAA,MAC/C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,IAAI,eAAe,kBAAkB,eAAe,KAAK,OAAO;AAAA,MACjF,SAAS,IAAI,WAAW,kBAAkB,WAAW,KAAK,OAAO;AAAA,MACjE;AAAA,MACA,iBAAiB;AAAA,IACnB;AAEA,QAAI,CAAC,aAAa,KAAK,sBAAsB,aAAa,GAAG;AAC3D,gBAAU,OAAO;AACjB,qBAAe;AACf,aAAO;AAAA,IACT;AAGA,WAAO,cAAc;AACrB,WAAO,cAAc;AACrB,cAAU,cAAc;AACxB,uBAAmB,mBAAmB,cAAc,UAAU;AAC9D,aAAS,cAAc;AACvB,mBAAe,cAAc;AAE7B,QAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,mBAAa,KAAK,0BAA0B,aAAa;AACzD,gBAAU,OAAO;AACjB,qBAAe;AACf,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,KAAK,MAAM,GAAG;AAChC,UAAM,eAAe,UAAU,CAAC;AAChC,UAAM,SAAS,UAAU,CAAC;AAE1B,QAAI,YAAY;AAChB,QAAI,cAAc;AAChB,kBAAY;AACZ,YAAM,YAAY,CAAC,iBAAiB,KAAK,EAAE,KAAK,EAAE;AAClD,UAAI,WAAW;AACb,YAAI,UAAU,QAAQ,GAAG,IAAI,GAAG;AAC9B,uBAAa;AAAA,QACf,OAAO;AACL,uBAAa;AAAA,QACf;AACA,qBAAa,UAAU,gBAAgB;AACvC,YAAI,QAAQ;AACV,uBAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,WAAW,KAAK,WAAW,aAAa,GAAG;AAC9C,wBAAkB,KAAK,oBAAoB,aAAa;AACxD,gBAAU,MAAM;AAChB,qBAAe;AACf,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,KAAK,YAAY,GAAG,WAAW,IAAI;AAC5C,QAAI,iBAAiB,WAAW;AAChC,QAAI,kBAAkB,cAAc;AACpC,QAAI,UAAU,cAAc;AAG5B,QAAI,kBAAkB,WAAW;AAAA,IAEjC,OAAO;AACL,iBAAW,UAAU,SAAS;AAC5B,YAAI,QAAQ,eAAe,MAAM,GAAG;AAClC,gBAAM,cAAc,QAAQ,MAAM;AAClC,+BAAqB,KAAK,QAAQ,WAAW;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAGA,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,WAAW;AACtB,UAAI;AACF,cAAM,YAAY,gBAAgB,GAAG;AACrC,qBAAa,SAAS,eAAe,oBAAoB,GAAG;AAC5D,wBAAgB,KAAK,YAAY;AACjC,YAAI,aAAa,mBAAmB,MAAM;AACxC,kCAAwB,YAAY,WAAW;AAAA,QACjD;AACA,qBAAa,KAAK,qBAAqB,YAAY;AACnD,qBAAa,KAAK,oBAAoB,YAAY;AAGlD,YAAI,CAAC,aAAa,GAAG,GAAG;AACtB,cAAI,sBAAsB;AAC1B,iBAAO,UAAU,SAAS,KAAK,uBAAuB,MAAM;AAC1D,kBAAM,uBAAuB,UAAU,MAAM;AAC7C,gBAAI,aAAa,oBAAoB,GAAG;AACtC,oCAAsB;AAAA,YACxB;AAAA,UACF;AACA,cAAI,qBAAqB;AACvB,yBAAa,qBAAqB,qBAAqB,YAAY;AACnE,yBAAa,qBAAqB,oBAAoB,YAAY;AAAA,UACpE;AAAA,QACF;AACA,kBAAU,OAAO;AAAA,MACnB,SAAS,GAAG;AACV,0BAAkB,KAAK,oBAAoB,aAAa,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC;AACnF,cAAM;AAAA,MACR,UAAE;AACA,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,QAAI,UAAU,WAAW;AACvB,8BAAwB,YAAY,WAAW;AAC/C,wBAAkB,KAAK,qBAAqB,YAAY;AACxD,wBAAkB,KAAK,kBAAkB,YAAY;AACrD,gBAAU,MAAM;AAChB,qBAAe;AAAA,IACjB;AACA,QAAI,UAAU,WAAW;AACvB,8BAAwB,YAAY,WAAW;AAC/C,wBAAkB,KAAK,qBAAqB,YAAY;AACxD,wBAAkB,KAAK,kBAAkB,YAAY;AACrD,gBAAU,MAAM;AAChB,qBAAe;AAAA,IACjB;AACA,QAAI,YAAY,WAAW;AACzB,8BAAwB,YAAY,WAAW;AAC/C,wBAAkB,KAAK,qBAAqB,YAAY;AACxD,wBAAkB,KAAK,gBAAgB,YAAY;AACnD,gBAAU,MAAM;AAChB,qBAAe;AAAA,IACjB;AACA,QAAI,CAAC,aAAa,KAAK,sBAAsB,YAAY,GAAG;AAC1D,gBAAU,OAAO;AACjB,qBAAe;AACf,aAAO;AAAA,IACT;AACA,QAAI,aAAa,2BAA2B,GAAG;AAC/C,QAAI,cAAc,gBAAgB,GAAG;AAErC,YAAQ,CAAC,aAAa,WAAW,YAAY,OAAO,GAAG,SAAS,WAAW;AACzE,cAAQ,CAAC,KAAK,IAAI,MAAM,GAAG,SAASM,SAAQ;AAC1C,QAAAA,QAAO,iBAAiB,WAAW,SAASC,QAAO;AACjD,uBAAa,KAAK,cAAc,WAAW;AAAA,YACzC,kBAAkBA,OAAM;AAAA,YACxB,QAAQA,OAAM;AAAA,YACd,OAAOA,OAAM;AAAA,UACf,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,iBAAa,KAAK,mBAAmB,YAAY;AACjD,UAAM,SAAS,eAAe,OAAO,oBAAoB,KAAK,KAAK,gBAAgB;AACnF,QAAI,KAAK,MAAM;AACf,WAAO;AAAA,EACT;AAaA,WAAS,wBAAwB,KAAK,cAAc;AAClD,UAAM,MAAM,aAAa;AAKzB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,UAAU,KAAK,WAAW,GAAG;AAC/B,wBAAkB,IAAI,kBAAkB,SAAS;AACjD,wBAAkB;AAAA,IACpB,WAAW,UAAU,KAAK,eAAe,GAAG;AAC1C,wBAAkB,IAAI,kBAAkB,aAAa;AACrD,wBAAkB;AAAA,IACpB,WAAW,UAAU,KAAK,kBAAkB,GAAG;AAC7C,wBAAkB,IAAI,kBAAkB,gBAAgB;AACxD,wBAAkB;AAAA,IACpB;AAGA,QAAI,iBAAiB;AACnB,UAAI,oBAAoB,SAAS;AAC/B,eAAO,CAAC;AAAA,MACV,OAAO;AACL,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAKA,UAAM,cAAc,aAAa,SAAS;AAC1C,UAAM,eAAe,aAAa,SAAS;AAE3C,UAAM,UAAU,yBAAyB,KAAK,aAAa;AAC3D,UAAM,aAAa,yBAAyB,KAAK,gBAAgB;AACjE,UAAM,mBAAmB,gBAAgB,GAAG,EAAE;AAE9C,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,QAAI,SAAS;AACX,iBAAW;AACX,aAAO;AAAA,IACT,WAAW,YAAY;AACrB,iBAAW;AACX,aAAO;AAAA,IACT,WAAW,kBAAkB;AAC3B,iBAAW;AACX,aAAO,gBAAgB;AAAA,IACzB;AAEA,QAAI,MAAM;AAER,UAAI,SAAS,SAAS;AACpB,eAAO,CAAC;AAAA,MACV;AAGA,UAAI,SAAS,QAAQ;AACnB,eAAO,gBAAgB;AAAA,MACzB;AAGA,UAAI,aAAa,SAAS,UAAU,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5D,eAAO,OAAO,MAAM,aAAa,SAAS;AAAA,MAC5C;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAOA,WAAS,YAAY,wBAAwB,QAAQ;AACnD,QAAI,SAAS,IAAI,OAAO,uBAAuB,IAAI;AACnD,WAAO,OAAO,KAAK,OAAO,SAAS,EAAE,CAAC;AAAA,EACxC;AAMA,WAAS,wBAAwB,KAAK;AACpC,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,iBAAiB,QAAQ,KAAK;AAE5D,UAAI,0BAA0B,KAAK,OAAO,iBAAiB,CAAC;AAC5D,UAAI,YAAY,yBAAyB,IAAI,MAAM,GAAG;AACpD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAKA,WAAS,YAAY,OAAO;AAC1B,QAAI,OAAO;AACT,YAAM,WAAW,KAAK,OAAO;AAC7B,UAAI,UAAU;AACZ,iBAAS,cAAc;AAAA,MACzB,OAAO;AACL,eAAO,SAAS,QAAQ;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAQA,WAAS,gBAAgB,KAAK,QAAQ;AACpC,QAAI,WAAW,QAAQ;AACrB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,UAAU,iBAAiB,KAAK,MAAM,CAAC;AAC9D,QAAI,kBAAkB,MAAM;AAC1B,wBAAkB,KAAK,oBAAoB,EAAE,OAAO,CAAC;AACrD,YAAM,IAAI,MAAM,qBAAqB,MAAM,EAAE;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAMA,WAAS,mBAAmB,KAAK,cAAc;AAC7C,UAAM,MAAM,aAAa;AACzB,QAAI,SAAS,aAAa;AAC1B,UAAM,MAAM,aAAa;AACzB,UAAM,qBAAqB,aAAa;AAExC,QAAI,CAAC,aAAa,KAAK,qBAAqB,YAAY,EAAG;AAE3D,QAAI,UAAU,KAAK,cAAc,GAAG;AAClC,0BAAoB,KAAK,cAAc,GAAG;AAAA,IAC5C;AAEA,QAAI,UAAU,KAAK,eAAe,GAAG;AACnC,+BAAyB;AACzB,UAAI,eAAe,IAAI,kBAAkB,aAAa;AAEtD,UAAI;AACJ,UAAI,aAAa,QAAQ,GAAG,MAAM,GAAG;AACnC,2BAAmB,UAAU,YAAY;AAEzC,uBAAe,iBAAiB;AAChC,eAAO,iBAAiB;AAAA,MAC1B;AACA,iBAAW,OAAO,cAAc,gBAAgB,EAAE,KAAK,WAAW;AAChE,2BAAmB,YAAY;AAAA,MACjC,CAAC;AACD;AAAA,IACF;AAEA,UAAM,gBAAgB,UAAU,KAAK,cAAc,KAAK,IAAI,kBAAkB,YAAY,MAAM;AAEhG,QAAI,UAAU,KAAK,eAAe,GAAG;AACnC,mBAAa,iBAAiB;AAC9B,WAAK,SAAS,OAAO,IAAI,kBAAkB,aAAa;AACxD,uBAAiB,KAAK,SAAS,OAAO;AACtC;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,mBAAa,iBAAiB;AAC9B,WAAK,SAAS,OAAO;AACrB;AAAA,IACF;AAEA,UAAM,gBAAgB,wBAAwB,KAAK,YAAY;AAE/D,UAAM,mBAAmB,wBAAwB,GAAG;AACpD,UAAM,aAAa,iBAAiB;AACpC,QAAI,UAAU,CAAC,CAAC,iBAAiB;AACjC,QAAI,cAAc,KAAK,OAAO,eAAe,iBAAiB;AAC9D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,iBAAiB,QAAQ;AAC3B,mBAAa,SAAS,gBAAgB,KAAK,iBAAiB,MAAM;AAAA,IACpE;AACA,QAAI,eAAe,IAAI;AACvB,QAAI,gBAAgB,QAAQ,iBAAiB,cAAc;AACzD,qBAAe,iBAAiB;AAAA,IAClC;AAGA,QAAI,UAAU,KAAK,eAAe,GAAG;AACnC,mBAAa,SAAS,gBAAgB,KAAK,IAAI,kBAAkB,aAAa,CAAC;AAAA,IACjF;AAEA,QAAI,UAAU,KAAK,aAAa,GAAG;AACjC,qBAAe,IAAI,kBAAkB,WAAW;AAAA,IAClD;AAEA,QAAI,iBAAiB,IAAI;AAEzB,QAAI,oBAAoB,aAAa;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,YAAY;AAEf,QAAI,iBAAiB,SAAS,CAAC,aAAa,QAAQ,iBAAiB,OAAO,iBAAiB,EAAG;AAEhG,QAAI,CAAC,aAAa,QAAQ,mBAAmB,iBAAiB,EAAG;AAEjE,aAAS,kBAAkB;AAC3B,qBAAiB,kBAAkB;AACnC,cAAU,kBAAkB;AAC5B,kBAAc,kBAAkB;AAChC,qBAAiB,kBAAkB;AACnC,mBAAe,kBAAkB;AAEjC,iBAAa,SAAS;AACtB,iBAAa,SAAS;AACtB,iBAAa,aAAa,CAAC;AAE3B,QAAI,kBAAkB,YAAY;AAChC,UAAI,IAAI,WAAW,KAAK;AACtB,sBAAc,GAAG;AAAA,MACnB;AAEA,qBAAe,KAAK,SAAS,WAAW;AACtC,yBAAiB,UAAU,kBAAkB,gBAAgB,KAAK,GAAG;AAAA,MACvE,CAAC;AAGD,UAAI,cAAc,MAAM;AACtB,iCAAyB;AAAA,MAC3B;AAEA,UAAI,WAAW,qBAAqB,KAAK,YAAY;AAErD,UAAI,CAAC,SAAS,eAAe,aAAa,GAAG;AAC3C,iBAAS,cAAc;AAAA,MACzB;AAEA,aAAO,UAAU,IAAI,KAAK,OAAO,aAAa;AAE9C,UAAI,oBAAoB;AACtB,yBAAiB;AAAA,MACnB;AAEA,UAAI,UAAU,KAAK,eAAe,GAAG;AACnC,yBAAiB,IAAI,kBAAkB,aAAa;AAAA,MACtD;AAEA,YAAM,YAAY,yBAAyB,KAAK,eAAe;AAC/D,YAAM,SAAS,yBAAyB,KAAK,WAAW;AAExD,WAAK,QAAQ,gBAAgB,UAAU;AAAA,QACrC,QAAQ,mBAAmB,UAAU,OAAO,kBAAkB;AAAA,QAC9D;AAAA,QACA,WAAW;AAAA,QACX,QAAQ,aAAa,SAAS;AAAA,QAC9B,gBAAgB;AAAA,QAChB,mBAAmB,WAAW;AAC5B,cAAI,UAAU,KAAK,yBAAyB,GAAG;AAC7C,gBAAI,WAAW;AACf,gBAAI,CAAC,aAAa,GAAG,GAAG;AACtB,yBAAW,YAAY,EAAE;AAAA,YAC3B;AACA,gCAAoB,KAAK,yBAAyB,QAAQ;AAAA,UAC5D;AAAA,QACF;AAAA,QACA,qBAAqB,WAAW;AAC9B,cAAI,UAAU,KAAK,2BAA2B,GAAG;AAC/C,gBAAI,WAAW;AACf,gBAAI,CAAC,aAAa,GAAG,GAAG;AACtB,yBAAW,YAAY,EAAE;AAAA,YAC3B;AACA,gCAAoB,KAAK,2BAA2B,QAAQ;AAAA,UAC9D;AAAA,QACF;AAAA,QACA,oBAAoB,WAAW;AAE7B,cAAI,cAAc,MAAM;AACtB,yBAAa,YAAY,EAAE,MAAM,4BAA4B,aAAa,EAAE,SAAS,cAAc,GAAG,YAAY,CAAC;AACnH,gBAAI,cAAc,SAAS,QAAQ;AACjC,iCAAmB,cAAc,IAAI;AACrC,2BAAa,YAAY,EAAE,MAAM,0BAA0B,EAAE,MAAM,cAAc,KAAK,CAAC;AAAA,YACzF,OAAO;AACL,kCAAoB,cAAc,IAAI;AACtC,2BAAa,YAAY,EAAE,MAAM,0BAA0B,EAAE,MAAM,cAAc,KAAK,CAAC;AAAA,YACzF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,SAAS;AACX,wBAAkB,KAAK,sBAAsB,aAAa,EAAE,OAAO,gCAAgC,IAAI,SAAS,WAAW,aAAa,SAAS,YAAY,GAAG,YAAY,CAAC;AAAA,IAC/K;AAAA,EACF;AAOA,QAAM,aAAa,CAAC;AAMpB,WAAS,gBAAgB;AACvB,WAAO;AAAA,MACL,MAAM,SAAS,KAAK;AAAE,eAAO;AAAA,MAAK;AAAA,MAClC,cAAc,WAAW;AAAE,eAAO;AAAA,MAAK;AAAA,MACvC,SAAS,SAAS,MAAM,KAAK;AAAE,eAAO;AAAA,MAAK;AAAA,MAC3C,mBAAmB,SAAS,MAAM,KAAK,KAAK;AAAE,eAAO;AAAA,MAAK;AAAA,MAC1D,cAAc,SAAS,WAAW;AAAE,eAAO;AAAA,MAAM;AAAA,MACjD,YAAY,SAAS,WAAW,QAAQ,UAAU,YAAY;AAAE,eAAO;AAAA,MAAM;AAAA,MAC7E,kBAAkB,SAAS,KAAK,YAAY,KAAK;AAAE,eAAO;AAAA,MAAK;AAAA,IACjE;AAAA,EACF;AAUA,WAAS,gBAAgB,MAAM,WAAW;AACxC,QAAI,UAAU,MAAM;AAClB,gBAAU,KAAK,WAAW;AAAA,IAC5B;AACA,eAAW,IAAI,IAAI,aAAa,cAAc,GAAG,SAAS;AAAA,EAC5D;AASA,WAAS,gBAAgB,MAAM;AAC7B,WAAO,WAAW,IAAI;AAAA,EACxB;AAUA,WAAS,cAAc,KAAK,oBAAoB,oBAAoB;AAClE,QAAI,sBAAsB,QAAW;AACnC,2BAAqB,CAAC;AAAA,IACxB;AACA,QAAI,OAAO,QAAW;AACpB,aAAO;AAAA,IACT;AACA,QAAI,sBAAsB,QAAW;AACnC,2BAAqB,CAAC;AAAA,IACxB;AACA,UAAM,uBAAuB,kBAAkB,KAAK,QAAQ;AAC5D,QAAI,sBAAsB;AACxB,cAAQ,qBAAqB,MAAM,GAAG,GAAG,SAAS,eAAe;AAC/D,wBAAgB,cAAc,QAAQ,MAAM,EAAE;AAC9C,YAAI,cAAc,MAAM,GAAG,CAAC,KAAK,WAAW;AAC1C,6BAAmB,KAAK,cAAc,MAAM,CAAC,CAAC;AAC9C;AAAA,QACF;AACA,YAAI,mBAAmB,QAAQ,aAAa,IAAI,GAAG;AACjD,gBAAM,YAAY,WAAW,aAAa;AAC1C,cAAI,aAAa,mBAAmB,QAAQ,SAAS,IAAI,GAAG;AAC1D,+BAAmB,KAAK,SAAS;AAAA,UACnC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,cAAc,UAAU,UAAU,GAAG,CAAC,GAAG,oBAAoB,kBAAkB;AAAA,EACxF;AAKA,MAAI,UAAU;AACd,cAAY,EAAE,iBAAiB,oBAAoB,WAAW;AAC5D,cAAU;AAAA,EACZ,CAAC;AASD,WAAS,MAAM,IAAI;AAGjB,QAAI,WAAW,YAAY,EAAE,eAAe,YAAY;AACtD,SAAG;AAAA,IACL,OAAO;AACL,kBAAY,EAAE,iBAAiB,oBAAoB,EAAE;AAAA,IACvD;AAAA,EACF;AAEA,WAAS,wBAAwB;AAC/B,QAAI,KAAK,OAAO,2BAA2B,OAAO;AAChD,YAAM,iBAAiB,KAAK,OAAO,mBAAmB,WAAW,KAAK,OAAO,gBAAgB,MAAM;AACnG,kBAAY,EAAE,KAAK;AAAA,QAAmB;AAAA,QACpC,WAAW,iBAAiB,aACzB,KAAK,OAAO,iBAAiB,uBAC7B,KAAK,OAAO,eAAe,OAAO,KAAK,OAAO,iBAAiB,2DAC/D,KAAK,OAAO,eAAe,MAAM,KAAK,OAAO,iBAAiB;AAAA,MAC1D;AAAA,IACX;AAAA,EACF;AAEA,WAAS,gBAAgB;AAEvB,UAAM,UAAU,YAAY,EAAE,cAAc,0BAA0B;AACtE,QAAI,SAAS;AACX,aAAO,UAAU,QAAQ,OAAO;AAAA,IAClC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,kBAAkB;AACzB,UAAM,aAAa,cAAc;AACjC,QAAI,YAAY;AACd,WAAK,SAAS,aAAa,KAAK,QAAQ,UAAU;AAAA,IACpD;AAAA,EACF;AAGA,QAAM,WAAW;AACf,oBAAgB;AAChB,0BAAsB;AACtB,QAAI,OAAO,YAAY,EAAE;AACzB,gBAAY,IAAI;AAChB,UAAM,eAAe,YAAY,EAAE;AAAA,MACjC;AAAA,IACF;AACA,SAAK,iBAAiB,cAAc,SAAS,KAAK;AAChD,YAAM,SAAS,IAAI;AACnB,YAAM,eAAe,gBAAgB,MAAM;AAC3C,UAAI,gBAAgB,aAAa,KAAK;AACpC,qBAAa,IAAI,MAAM;AAAA,MACzB;AAAA,IACF,CAAC;AAED,UAAM,mBAAmB,OAAO,aAAa,OAAO,WAAW,KAAK,MAAM,IAAI;AAE9E,WAAO,aAAa,SAAS,OAAO;AAClC,UAAI,MAAM,SAAS,MAAM,MAAM,MAAM;AACnC,uBAAe;AACf,gBAAQ,cAAc,SAAS,KAAK;AAClC,uBAAa,KAAK,iBAAiB;AAAA,YACjC,UAAU,YAAY;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL,YAAI,kBAAkB;AACpB,2BAAiB,KAAK;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,cAAU,EAAE,WAAW,WAAW;AAChC,mBAAa,MAAM,aAAa,CAAC,CAAC;AAClC,aAAO;AAAA,IACT,GAAG,CAAC;AAAA,EACN,CAAC;AAED,SAAO;AACT,EAAG;AAqLH,IAAO,mBAAQR;", "names": ["htmx", "str", "selector", "elt", "extensions", "parentElt", "prompt", "target", "event"]}