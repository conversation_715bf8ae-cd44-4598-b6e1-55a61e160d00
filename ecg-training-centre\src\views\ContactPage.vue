<template>
  <div class="contact-page">
    <!-- Header -->
    <section class="bg-gradient-ecg text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">Contact Us</h1>
          <p class="text-xl md:text-2xl text-blue-100">Get in touch with our team</p>
        </div>
      </div>
    </section>

    <!-- Contact Information -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-4">Get In Touch</h2>
          <p class="text-xl text-gray-600">We're here to help with your training needs</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-ecg-blue/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-ecg-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Phone</h3>
            <p class="text-gray-600 mb-2">+233 302 676 756</p>
            <p class="text-gray-600">+233 576 870 291</p>
          </div>

          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-ecg-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-ecg-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Email</h3>
            <p class="text-gray-600"><EMAIL></p>
          </div>

          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Address</h3>
            <p class="text-gray-600 mb-2">GPS: GT-049-4121</p>
            <p class="text-gray-600 mb-2">ECG Training Centre</p>
            <p class="text-gray-600">Community 10 Junction, Tema, Ghana</p>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="bg-gray-50 rounded-2xl p-8 md:p-12">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-ecg-blue mb-4">Send us a Message</h2>
            <p class="text-gray-600">We'll get back to you within 24 hours</p>
          </div>

          <form @submit.prevent="handleSubmit" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Your full name"
                  v-model="formData.name"
                  name="name"
                  required
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:opacity-50"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                <input
                  id="email"
                  type="email"
                  placeholder="Your email address"
                  v-model="formData.email"
                  name="email"
                  required
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:opacity-50"
                />
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                <input
                  id="phone"
                  type="tel"
                  placeholder="Your phone number"
                  v-model="formData.phone"
                  name="phone"
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:opacity-50"
                />
              </div>

              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                <input
                  id="subject"
                  type="text"
                  placeholder="Message subject"
                  v-model="formData.subject"
                  name="subject"
                  required
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:opacity-50"
                />
              </div>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
              <textarea
                id="message"
                placeholder="Your message"
                v-model="formData.message"
                name="message"
                required
                :disabled="loading"
                rows="6"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:opacity-50 resize-y"
              ></textarea>
            </div>

            <div class="text-center">
              <button
                type="submit"
                :disabled="!isFormValid || loading"
                class="bg-ecg-blue text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-ecg-dark-blue transition-colors disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
              >
                {{ loading ? 'Sending...' : 'Send Message' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>

    <!-- Office Hours & Map -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Office Hours -->
          <div>
            <h2 class="text-3xl font-bold text-ecg-blue mb-8">Office Hours</h2>
            <div class="bg-white rounded-xl shadow-lg p-8">
              <div class="space-y-4">
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                  <span class="font-medium text-gray-900">Monday - Friday</span>
                  <span class="text-ecg-blue font-semibold">8:00 AM - 5:00 PM</span>
                </div>
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                  <span class="font-medium text-gray-900">Saturday</span>
                  <span class="text-ecg-blue font-semibold">9:00 AM - 2:00 PM</span>
                </div>
                <div class="flex justify-between items-center py-3">
                  <span class="font-medium text-gray-900">Sunday</span>
                  <span class="text-red-600 font-semibold">Closed</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Map Section -->
          <div>
            <h2 class="text-3xl font-bold text-ecg-blue mb-8">Find Us</h2>
            <div class="bg-white rounded-xl shadow-lg p-8 text-center">
              <div class="w-16 h-16 bg-ecg-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-8 h-8 text-ecg-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                </svg>
              </div>
              <p class="text-gray-600 mb-6">Interactive map will be loaded here</p>
              <button
                @click="openMaps"
                class="bg-ecg-blue text-white px-6 py-3 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
              >
                Open in Maps
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Media -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-ecg-blue mb-8">Follow Us</h2>
        <p class="text-xl text-gray-600 mb-8">Stay connected with ECG Training Centre</p>

        <div class="flex justify-center space-x-6">
          <button
            @click="openSocial('facebook')"
            class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors transform hover:scale-110"
          >
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </button>

          <button
            @click="openSocial('twitter')"
            class="w-16 h-16 bg-blue-400 text-white rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors transform hover:scale-110"
          >
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </button>

          <button
            @click="openSocial('linkedin')"
            class="w-16 h-16 bg-blue-700 text-white rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors transform hover:scale-110"
          >
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { contactService } from '@/services/pocketbase'
import { useHTMX } from '@/composables/useHTMX'

const { loading } = useHTMX()

const contactForm = ref<HTMLFormElement>()
const formData = ref({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

const isFormValid = computed(() =>
  formData.value.name.trim() !== '' &&
  formData.value.email.trim() !== '' &&
  formData.value.subject.trim() !== '' &&
  formData.value.message.trim() !== ''
)

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('Please fill in all required fields')
    return
  }

  try {
    await contactService.sendMessage({
      name: formData.value.name,
      email: formData.value.email,
      phone: formData.value.phone,
      subject: formData.value.subject,
      message: formData.value.message
    })

    alert('Thank you for your message! We will get back to you soon.')

    // Reset form
    formData.value = {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    }
  } catch (error: any) {
    console.error('Failed to send message:', error)
    alert(error.message || 'Failed to send message. Please try again.')
  }
}

const openMaps = () => {
  // Open Google Maps with the location
  const url = 'https://maps.google.com/?q=ECG+Training+Centre,+Community+10+Junction,+Tema,+Ghana'
  window.open(url, '_blank')
}

const openSocial = (platform: string) => {
  const urls = {
    facebook: 'https://facebook.com/ECGTrainingHub',
    twitter: 'https://twitter.com/ECGTrainingHub',
    linkedin: 'https://linkedin.com/ECGTrainingHub'
  }

  if (urls[platform as keyof typeof urls]) {
    window.open(urls[platform as keyof typeof urls], '_blank')
  }
}
</script>

<style scoped>
.contact-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.contact-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.contact-info-section {
  padding: 60px 20px;
  background: #f8f9fa;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.contact-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contact-card h3 {
  color: #1e40af;
  margin: 1rem 0;
}

.contact-form-section {
  padding: 60px 20px;
}

.contact-form-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.contact-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
}

.office-hours-section {
  background: #f8f9fa;
  padding: 40px 20px;
}

.office-hours-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.map-section {
  padding: 60px 20px;
}

.map-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.map-placeholder {
  background: #f8f9fa;
  padding: 60px 20px;
  text-align: center;
  border-radius: 10px;
  border: 2px dashed #ddd;
}

.social-section {
  background: #1e40af;
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.social-section h2 {
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.htmx-indicator {
  display: none;
}

/* Tailwind CSS handles all styling */
</style>
