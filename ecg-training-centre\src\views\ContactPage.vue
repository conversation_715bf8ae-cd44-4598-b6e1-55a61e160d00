<template>
  <f7-page name="contact">
    <f7-navbar title="Contact Us" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Header -->
      <div class="contact-header">
        <div class="container">
          <h1>Contact Us</h1>
          <p>Get in touch with our team</p>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="contact-info-section">
        <div class="container">
          <div class="contact-grid">
            <div class="contact-card">
              <f7-icon ios="f7:phone" md="material:phone" size="30" color="blue"></f7-icon>
              <h3>Phone</h3>
              <p>+233 302 676 756</p>
              <p>+233 576 870 291</p>
            </div>
            
            <div class="contact-card">
              <f7-icon ios="f7:envelope" md="material:email" size="30" color="blue"></f7-icon>
              <h3>Email</h3>
              <p><EMAIL></p>
            </div>
            
            <div class="contact-card">
              <f7-icon ios="f7:location" md="material:location_on" size="30" color="blue"></f7-icon>
              <h3>Address</h3>
              <p>GPS: GT-049-4121</p>
              <p>ECG Training Centre</p>
              <p>Community 10 Junction, Tema, Ghana</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Form -->
      <div class="contact-form-section">
        <div class="container">
          <h2>Send us a Message</h2>
          <form hx-post="/api/contact" hx-target="#contact-result" hx-indicator="#loading">
            <f7-list>
              <f7-list-input
                label="Name"
                type="text"
                placeholder="Your full name"
                name="name"
                required
              ></f7-list-input>
              
              <f7-list-input
                label="Email"
                type="email"
                placeholder="Your email address"
                name="email"
                required
              ></f7-list-input>
              
              <f7-list-input
                label="Phone"
                type="tel"
                placeholder="Your phone number"
                name="phone"
              ></f7-list-input>
              
              <f7-list-input
                label="Subject"
                type="text"
                placeholder="Message subject"
                name="subject"
                required
              ></f7-list-input>
              
              <f7-list-input
                label="Message"
                type="textarea"
                placeholder="Your message"
                name="message"
                required
                :resizable="true"
              ></f7-list-input>
            </f7-list>
            
            <div class="form-actions">
              <f7-button large fill color="blue" type="submit">
                <span id="loading" class="htmx-indicator">Sending...</span>
                <span>Send Message</span>
              </f7-button>
            </div>
          </form>
          
          <div id="contact-result" class="contact-result"></div>
        </div>
      </div>

      <!-- Office Hours -->
      <div class="office-hours-section">
        <div class="container">
          <h2>Office Hours</h2>
          <f7-list>
            <f7-list-item title="Monday - Friday" after="8:00 AM - 5:00 PM"></f7-list-item>
            <f7-list-item title="Saturday" after="9:00 AM - 2:00 PM"></f7-list-item>
            <f7-list-item title="Sunday" after="Closed"></f7-list-item>
          </f7-list>
        </div>
      </div>

      <!-- Map Section -->
      <div class="map-section">
        <div class="container">
          <h2>Find Us</h2>
          <div class="map-placeholder">
            <p>Interactive map will be loaded here</p>
            <f7-button fill color="blue" @click="openMaps">Open in Maps</f7-button>
          </div>
        </div>
      </div>

      <!-- Social Media -->
      <div class="social-section">
        <div class="container">
          <h2>Follow Us</h2>
          <div class="social-links">
            <f7-button round color="blue" @click="openSocial('facebook')">
              <f7-icon ios="f7:logo_facebook" md="material:facebook"></f7-icon>
            </f7-button>
            <f7-button round color="blue" @click="openSocial('twitter')">
              <f7-icon ios="f7:logo_twitter" md="material:twitter"></f7-icon>
            </f7-button>
            <f7-button round color="blue" @click="openSocial('linkedin')">
              <f7-icon ios="f7:logo_linkedin" md="material:linkedin"></f7-icon>
            </f7-button>
          </div>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { f7 } from 'framework7-vue'

const openMaps = () => {
  // Open Google Maps with the location
  const url = 'https://maps.google.com/?q=ECG+Training+Centre,+Community+10+Junction,+Tema,+Ghana'
  window.open(url, '_blank')
}

const openSocial = (platform: string) => {
  const urls = {
    facebook: 'https://facebook.com/ECGTrainingHub',
    twitter: 'https://twitter.com/ECGTrainingHub',
    linkedin: 'https://linkedin.com/ECGTrainingHub'
  }
  
  if (urls[platform as keyof typeof urls]) {
    window.open(urls[platform as keyof typeof urls], '_blank')
  }
}
</script>

<style scoped>
.contact-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.contact-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.contact-info-section {
  padding: 60px 20px;
  background: #f8f9fa;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.contact-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contact-card h3 {
  color: #1e40af;
  margin: 1rem 0;
}

.contact-form-section {
  padding: 60px 20px;
}

.contact-form-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.contact-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
}

.office-hours-section {
  background: #f8f9fa;
  padding: 40px 20px;
}

.office-hours-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.map-section {
  padding: 60px 20px;
}

.map-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.map-placeholder {
  background: #f8f9fa;
  padding: 60px 20px;
  text-align: center;
  border-radius: 10px;
  border: 2px dashed #ddd;
}

.social-section {
  background: #1e40af;
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.social-section h2 {
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline;
}

.htmx-request span:not(.htmx-indicator) {
  display: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
