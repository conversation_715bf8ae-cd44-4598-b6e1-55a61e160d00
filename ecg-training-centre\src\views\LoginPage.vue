<template>
  <div class="login-page min-h-screen bg-gradient-ecg flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <span class="text-white font-bold text-2xl">ECG</span>
        </div>
        <h1 class="text-4xl font-bold text-white mb-2">Welcome Back</h1>
        <p class="text-blue-100">Sign in to your ECG Training Centre account</p>
      </div>

      <!-- Login Form -->
      <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <div>
            <label for="email" class="block text-sm font-medium text-white mb-2">Email</label>
            <input
              id="email"
              type="email"
              placeholder="Enter your email"
              v-model="email"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-white mb-2">Password</label>
            <input
              id="password"
              type="password"
              placeholder="Enter your password"
              v-model="password"
              required
              :disabled="loading"
              class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white transition-all disabled:opacity-50"
            />
          </div>

          <!-- Error Display -->
          <div v-if="error" class="bg-red-500/20 border border-red-400/50 rounded-lg p-4 flex items-center space-x-2">
            <svg class="w-5 h-5 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-red-200">{{ error }}</span>
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            :disabled="!email || !password || loading"
            class="w-full bg-white text-ecg-blue py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            {{ loading ? 'Signing In...' : 'Sign In' }}
          </button>

          <!-- Additional Options -->
          <div class="text-center">
            <a href="#" class="text-blue-200 hover:text-white transition-colors text-sm">Forgot Password?</a>
          </div>
        </form>
      </div>

      <!-- Register Link -->
      <div class="text-center mt-8">
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
          <p class="text-blue-100 mb-4">Don't have an account?</p>
          <button
            @click="goToRegister"
            class="bg-ecg-green text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            Create Account
          </button>
        </div>
      </div>

      <!-- Features -->
      <div class="mt-12">
        <h3 class="text-xl font-semibold text-white text-center mb-8">Why Join ECG Training Centre?</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-white mb-2">Expert Training</h4>
            <p class="text-blue-200 text-sm">Learn from industry professionals</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-white mb-2">Certification</h4>
            <p class="text-blue-200 text-sm">Get recognized certificates</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-white mb-2">Community</h4>
            <p class="text-blue-200 text-sm">Join a network of professionals</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const router = useRouter()

const { login, loading, error, clearError } = useAuth()

const email = ref('')
const password = ref('')

const handleLogin = async () => {
  clearError()
  
  try {
    await login(email.value, password.value)

    alert('Login successful! Welcome back!')
    router.push('/')
  } catch (err: any) {
    console.error('Login failed:', err)
    // Error is already set by the useAuth composable
  }
}

const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped>
.login-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.login-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.login-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.login-form-section {
  padding: 40px 20px;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 5px;
  margin: 15px 20px;
  color: #c33;
}

.additional-options {
  text-align: center;
  padding: 20px;
}

.register-section {
  padding: 20px;
  background: #f8f9fa;
}

.register-prompt {
  text-align: center;
}

.register-prompt p {
  margin-bottom: 1rem;
  color: #666;
}

.features-section {
  padding: 40px 20px;
}

.features-section h3 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.feature-item {
  text-align: center;
  padding: 20px;
}

.feature-item h4 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
}

.feature-item p {
  color: #666;
  font-size: 0.9rem;
}

/* Tailwind CSS handles all styling */
</style>
