<template>
  <f7-page name="login">
    <f7-navbar title="Login" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Login Header -->
      <div class="login-header">
        <div class="container">
          <h1>Welcome Back</h1>
          <p>Sign in to your ECG Training Centre account</p>
        </div>
      </div>

      <!-- Login Form -->
      <div class="login-form-section">
        <div class="container">
          <form @submit.prevent="handleLogin">
            <f7-list>
              <f7-list-input
                label="Email"
                type="email"
                placeholder="Enter your email"
                v-model:value="email"
                required
                :disabled="loading"
              ></f7-list-input>
              
              <f7-list-input
                label="Password"
                type="password"
                placeholder="Enter your password"
                v-model:value="password"
                required
                :disabled="loading"
              ></f7-list-input>
            </f7-list>
            
            <!-- Error Display -->
            <div v-if="error" class="error-message">
              <f7-icon ios="f7:exclamationmark_triangle" md="material:error" size="20" color="red"></f7-icon>
              <span>{{ error }}</span>
            </div>
            
            <!-- Login Button -->
            <div class="form-actions">
              <f7-button 
                large 
                fill 
                color="blue" 
                type="submit"
                :loading="loading"
                :disabled="!email || !password"
              >
                Sign In
              </f7-button>
            </div>
          </form>
          
          <!-- Additional Options -->
          <div class="additional-options">
            <f7-link href="#" color="blue">Forgot Password?</f7-link>
          </div>
        </div>
      </div>

      <!-- Register Link -->
      <div class="register-section">
        <div class="container">
          <f7-card>
            <f7-card-content>
              <div class="register-prompt">
                <p>Don't have an account?</p>
                <f7-button fill color="green" @click="goToRegister">Create Account</f7-button>
              </div>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- Features -->
      <div class="features-section">
        <div class="container">
          <h3>Why Join ECG Training Centre?</h3>
          <div class="features-grid">
            <div class="feature-item">
              <f7-icon ios="f7:book" md="material:school" size="30" color="blue"></f7-icon>
              <h4>Expert Training</h4>
              <p>Learn from industry professionals</p>
            </div>
            <div class="feature-item">
              <f7-icon ios="f7:certificate" md="material:verified" size="30" color="green"></f7-icon>
              <h4>Certification</h4>
              <p>Get recognized certificates</p>
            </div>
            <div class="feature-item">
              <f7-icon ios="f7:person_2" md="material:group" size="30" color="orange"></f7-icon>
              <h4>Community</h4>
              <p>Join a network of professionals</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { f7 } from 'framework7-vue'
import { useAuth } from '@/composables/useAuth'

const { login, loading, error, clearError } = useAuth()

const email = ref('')
const password = ref('')

const handleLogin = async () => {
  clearError()
  
  try {
    await login(email.value, password.value)
    
    f7.dialog.alert('Login successful!', 'Welcome', () => {
      f7.views.main.router.navigate('/')
    })
  } catch (err: any) {
    console.error('Login failed:', err)
    // Error is already set by the useAuth composable
  }
}

const goToRegister = () => {
  f7.views.main.router.navigate('/register/')
}
</script>

<style scoped>
.login-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.login-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.login-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.login-form-section {
  padding: 40px 20px;
}

.form-actions {
  padding: 20px;
  text-align: center;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 5px;
  margin: 15px 20px;
  color: #c33;
}

.additional-options {
  text-align: center;
  padding: 20px;
}

.register-section {
  padding: 20px;
  background: #f8f9fa;
}

.register-prompt {
  text-align: center;
}

.register-prompt p {
  margin-bottom: 1rem;
  color: #666;
}

.features-section {
  padding: 40px 20px;
}

.features-section h3 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.feature-item {
  text-align: center;
  padding: 20px;
}

.feature-item h4 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
}

.feature-item p {
  color: #666;
  font-size: 0.9rem;
}

.container {
  max-width: 500px;
  margin: 0 auto;
}
</style>
