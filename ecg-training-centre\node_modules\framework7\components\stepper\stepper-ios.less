.ios {
  .stepper-button,
  .stepper-button-minus,
  .stepper-button-plus {
    .f7-icons {
      font-size: 22px;
    }
  }
  .stepper-fill,
  .stepper-fill-ios {
    --f7-stepper-button-pressed-bg-color: var(
      --f7-stepper-fill-button-pressed-bg-color,
      var(--f7-theme-color-tint)
    );
  }

  .stepper-small,
  .stepper-small-ios {
    &.stepper-raised,
    &.stepper-raised-ios {
      --f7-stepper-border-width: 0px;
    }
    .stepper-button,
    .stepper-button-minus,
    .stepper-button-plus {
      transition-duration: 200ms;
      &.active-state {
        &:after,
        &:before {
          transition-duration: 200ms;
          background-color: var(--f7-theme-color);
        }
      }
    }
  }
}
