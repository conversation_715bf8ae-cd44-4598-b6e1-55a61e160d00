<template>
  <f7-page name="about">
    <f7-navbar title="About Us" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Hero Section -->
      <div class="about-hero">
        <div class="container">
          <h1>About ECG Training Centre</h1>
          <p>Building capacity for the power sector in West Africa</p>
        </div>
      </div>

      <!-- Mission Section -->
      <div class="mission-section">
        <div class="container">
          <f7-card>
            <f7-card-header>Our Mission</f7-card-header>
            <f7-card-content>
              <p>
                To provide skills and competence-based training for existing and potential power sector 
                workers and companies to bridge the skill gap (quality and quantity) of the power sector 
                in the West African sub region.
              </p>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- About Content -->
      <div class="content-section">
        <div class="container">
          <h2>Welcome to ECG Training Centre</h2>
          <p>
            The Training Centre of ECG was set up with the objectives of offering training to build 
            capacity for local and international trainees, and to offer up-skilling, special or 
            curated programs as well as on-demand courses among other objectives.
          </p>
          <p>
            It is recognized by JICA as a Centre of Excellence for capacity building of Engineers 
            in West Africa.
          </p>

          <h3>Our Achievement</h3>
          <p>
            The Training Centre has evolved to become the beacon for engineering training in the 
            sub-region and provides training and consultancy services for other corporate institutions, 
            serving such entities as NEDCo - Ghana, EPC - Ghana, VRA - Ghana, GRIDCo - Ghana, 
            BPA - Ghana, and LEC of Liberia, EDSA of Sierra Leone, DABS of Afghanistan, and 
            NAWEC of the Gambia, as well as serving other industries like mining, oil, and gas.
          </p>

          <!-- Statistics -->
          <div class="stats-grid">
            <div class="stat-card">
              <h3>1000+</h3>
              <p>Graduates</p>
            </div>
            <div class="stat-card">
              <h3>50+</h3>
              <p>Courses</p>
            </div>
            <div class="stat-card">
              <h3>10+</h3>
              <p>Labs</p>
            </div>
            <div class="stat-card">
              <h3>500+</h3>
              <p>Students</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Info -->
      <div class="contact-info">
        <div class="container">
          <h3>Get in Touch</h3>
          <f7-list>
            <f7-list-item title="Phone" after="+233 302 676 756"></f7-list-item>
            <f7-list-item title="Mobile" after="+233 576 870 291"></f7-list-item>
            <f7-list-item title="Email" after="<EMAIL>"></f7-list-item>
            <f7-list-item title="Address" after="Community 10 Junction, Tema, Ghana"></f7-list-item>
          </f7-list>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
// Component logic here
</script>

<style scoped>
.about-hero {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.about-hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.about-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.mission-section {
  padding: 40px 20px;
  background: #f8f9fa;
}

.content-section {
  padding: 60px 20px;
}

.content-section h2 {
  color: #1e40af;
  margin-bottom: 1.5rem;
}

.content-section h3 {
  color: #059669;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 3rem;
}

.stat-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  border: 2px solid #e9ecef;
}

.stat-card h3 {
  font-size: 2rem;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.contact-info {
  background: #f8f9fa;
  padding: 40px 20px;
}

.contact-info h3 {
  color: #1e40af;
  margin-bottom: 1.5rem;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
