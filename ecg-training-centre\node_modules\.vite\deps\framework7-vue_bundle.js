import {
  accordion_content_default,
  accordion_default,
  accordion_item_default,
  accordion_toggle_default,
  actions_button_default,
  actions_default,
  actions_group_default,
  actions_label_default,
  app_default,
  area_chart_default,
  badge_default,
  block_default,
  block_footer_default,
  block_header_default,
  block_title_default,
  breadcrumbs_collapsed_default,
  breadcrumbs_default,
  breadcrumbs_item_default,
  breadcrumbs_separator_default,
  button_default,
  card_content_default,
  card_default,
  card_footer_default,
  card_header_default,
  checkbox_default,
  chip_default,
  f7,
  f7ready,
  fab_backdrop_default,
  fab_button_default,
  fab_buttons_default,
  fab_default,
  gauge_default,
  icon_default,
  input_default,
  link_default,
  list_button_default,
  list_default,
  list_group_default,
  list_index_default,
  list_input_default,
  list_item_default,
  login_screen_default,
  login_screen_title_default,
  message_default,
  messagebar_attachment_default,
  messagebar_attachments_default,
  messagebar_default,
  messagebar_sheet_default,
  messagebar_sheet_image_default,
  messagebar_sheet_item_default,
  messages_default,
  messages_title_default,
  nav_left_default,
  nav_right_default,
  nav_title_default,
  nav_title_large_default,
  navbar_default,
  page_content_default,
  page_default,
  panel_default,
  photo_browser_default,
  pie_chart_default,
  plugin_default,
  popover_default,
  popup_default,
  preloader_default,
  progressbar_default,
  radio_default,
  range_default,
  routable_modals_default,
  searchbar_default,
  segmented_default,
  sheet_default,
  skeleton_avatar_default,
  skeleton_block_default,
  skeleton_image_default,
  skeleton_text_default,
  stepper_default,
  subnavbar_default,
  swipeout_actions_default,
  swipeout_button_default,
  tab_default,
  tabs_default,
  text_editor_default,
  theme,
  toggle_default,
  toolbar_default,
  treeview_default,
  treeview_item_default,
  useStore,
  use_icon_default,
  view_default,
  views_default
} from "./chunk-54QNRS63.js";
import "./chunk-YVUCKT22.js";
import "./chunk-VZXQDS5F.js";
import "./chunk-PZ5AY32C.js";

// node_modules/framework7-vue/framework7-vue-bundle.js
var framework7_vue_bundle_default = plugin_default;
function registerComponents(app) {
  app.component("f7-accordion-content", accordion_content_default);
  app.component("f7-accordion-item", accordion_item_default);
  app.component("f7-accordion-toggle", accordion_toggle_default);
  app.component("f7-accordion", accordion_default);
  app.component("f7-actions-button", actions_button_default);
  app.component("f7-actions-group", actions_group_default);
  app.component("f7-actions-label", actions_label_default);
  app.component("f7-actions", actions_default);
  app.component("f7-app", app_default);
  app.component("f7-area-chart", area_chart_default);
  app.component("f7-badge", badge_default);
  app.component("f7-block-footer", block_footer_default);
  app.component("f7-block-header", block_header_default);
  app.component("f7-block-title", block_title_default);
  app.component("f7-block", block_default);
  app.component("f7-breadcrumbs-collapsed", breadcrumbs_collapsed_default);
  app.component("f7-breadcrumbs-item", breadcrumbs_item_default);
  app.component("f7-breadcrumbs-separator", breadcrumbs_separator_default);
  app.component("f7-breadcrumbs", breadcrumbs_default);
  app.component("f7-button", button_default);
  app.component("f7-card-content", card_content_default);
  app.component("f7-card-footer", card_footer_default);
  app.component("f7-card-header", card_header_default);
  app.component("f7-card", card_default);
  app.component("f7-checkbox", checkbox_default);
  app.component("f7-chip", chip_default);
  app.component("f7-fab-backdrop", fab_backdrop_default);
  app.component("f7-fab-button", fab_button_default);
  app.component("f7-fab-buttons", fab_buttons_default);
  app.component("f7-fab", fab_default);
  app.component("f7-gauge", gauge_default);
  app.component("f7-icon", icon_default);
  app.component("f7-input", input_default);
  app.component("f7-link", link_default);
  app.component("f7-list-button", list_button_default);
  app.component("f7-list-group", list_group_default);
  app.component("f7-list-index", list_index_default);
  app.component("f7-list-input", list_input_default);
  app.component("f7-list-item", list_item_default);
  app.component("f7-list", list_default);
  app.component("f7-login-screen-title", login_screen_title_default);
  app.component("f7-login-screen", login_screen_default);
  app.component("f7-message", message_default);
  app.component("f7-messagebar-attachment", messagebar_attachment_default);
  app.component("f7-messagebar-attachments", messagebar_attachments_default);
  app.component("f7-messagebar-sheet-image", messagebar_sheet_image_default);
  app.component("f7-messagebar-sheet-item", messagebar_sheet_item_default);
  app.component("f7-messagebar-sheet", messagebar_sheet_default);
  app.component("f7-messagebar", messagebar_default);
  app.component("f7-messages-title", messages_title_default);
  app.component("f7-messages", messages_default);
  app.component("f7-nav-left", nav_left_default);
  app.component("f7-nav-right", nav_right_default);
  app.component("f7-nav-title-large", nav_title_large_default);
  app.component("f7-nav-title", nav_title_default);
  app.component("f7-navbar", navbar_default);
  app.component("f7-page-content", page_content_default);
  app.component("f7-page", page_default);
  app.component("f7-panel", panel_default);
  app.component("f7-photo-browser", photo_browser_default);
  app.component("f7-pie-chart", pie_chart_default);
  app.component("f7-popover", popover_default);
  app.component("f7-popup", popup_default);
  app.component("f7-preloader", preloader_default);
  app.component("f7-progressbar", progressbar_default);
  app.component("f7-radio", radio_default);
  app.component("f7-range", range_default);
  app.component("f7-routable-modals", routable_modals_default);
  app.component("f7-searchbar", searchbar_default);
  app.component("f7-segmented", segmented_default);
  app.component("f7-sheet", sheet_default);
  app.component("f7-skeleton-avatar", skeleton_avatar_default);
  app.component("f7-skeleton-block", skeleton_block_default);
  app.component("f7-skeleton-image", skeleton_image_default);
  app.component("f7-skeleton-text", skeleton_text_default);
  app.component("f7-stepper", stepper_default);
  app.component("f7-subnavbar", subnavbar_default);
  app.component("f7-swipeout-actions", swipeout_actions_default);
  app.component("f7-swipeout-button", swipeout_button_default);
  app.component("f7-tab", tab_default);
  app.component("f7-tabs", tabs_default);
  app.component("f7-text-editor", text_editor_default);
  app.component("f7-toggle", toggle_default);
  app.component("f7-toolbar", toolbar_default);
  app.component("f7-treeview-item", treeview_item_default);
  app.component("f7-treeview", treeview_default);
  app.component("f7-use-icon", use_icon_default);
  app.component("f7-view", view_default);
  app.component("f7-views", views_default);
}
export {
  framework7_vue_bundle_default as default,
  f7,
  accordion_default as f7Accordion,
  accordion_content_default as f7AccordionContent,
  accordion_item_default as f7AccordionItem,
  accordion_toggle_default as f7AccordionToggle,
  actions_default as f7Actions,
  actions_button_default as f7ActionsButton,
  actions_group_default as f7ActionsGroup,
  actions_label_default as f7ActionsLabel,
  app_default as f7App,
  area_chart_default as f7AreaChart,
  badge_default as f7Badge,
  block_default as f7Block,
  block_footer_default as f7BlockFooter,
  block_header_default as f7BlockHeader,
  block_title_default as f7BlockTitle,
  breadcrumbs_default as f7Breadcrumbs,
  breadcrumbs_collapsed_default as f7BreadcrumbsCollapsed,
  breadcrumbs_item_default as f7BreadcrumbsItem,
  breadcrumbs_separator_default as f7BreadcrumbsSeparator,
  button_default as f7Button,
  card_default as f7Card,
  card_content_default as f7CardContent,
  card_footer_default as f7CardFooter,
  card_header_default as f7CardHeader,
  checkbox_default as f7Checkbox,
  chip_default as f7Chip,
  fab_default as f7Fab,
  fab_backdrop_default as f7FabBackdrop,
  fab_button_default as f7FabButton,
  fab_buttons_default as f7FabButtons,
  gauge_default as f7Gauge,
  icon_default as f7Icon,
  input_default as f7Input,
  link_default as f7Link,
  list_default as f7List,
  list_button_default as f7ListButton,
  list_group_default as f7ListGroup,
  list_index_default as f7ListIndex,
  list_input_default as f7ListInput,
  list_item_default as f7ListItem,
  login_screen_default as f7LoginScreen,
  login_screen_title_default as f7LoginScreenTitle,
  message_default as f7Message,
  messagebar_default as f7Messagebar,
  messagebar_attachment_default as f7MessagebarAttachment,
  messagebar_attachments_default as f7MessagebarAttachments,
  messagebar_sheet_default as f7MessagebarSheet,
  messagebar_sheet_image_default as f7MessagebarSheetImage,
  messagebar_sheet_item_default as f7MessagebarSheetItem,
  messages_default as f7Messages,
  messages_title_default as f7MessagesTitle,
  nav_left_default as f7NavLeft,
  nav_right_default as f7NavRight,
  nav_title_default as f7NavTitle,
  nav_title_large_default as f7NavTitleLarge,
  navbar_default as f7Navbar,
  page_default as f7Page,
  page_content_default as f7PageContent,
  panel_default as f7Panel,
  photo_browser_default as f7PhotoBrowser,
  pie_chart_default as f7PieChart,
  popover_default as f7Popover,
  popup_default as f7Popup,
  preloader_default as f7Preloader,
  progressbar_default as f7Progressbar,
  radio_default as f7Radio,
  range_default as f7Range,
  routable_modals_default as f7RoutableModals,
  searchbar_default as f7Searchbar,
  segmented_default as f7Segmented,
  sheet_default as f7Sheet,
  skeleton_avatar_default as f7SkeletonAvatar,
  skeleton_block_default as f7SkeletonBlock,
  skeleton_image_default as f7SkeletonImage,
  skeleton_text_default as f7SkeletonText,
  stepper_default as f7Stepper,
  subnavbar_default as f7Subnavbar,
  swipeout_actions_default as f7SwipeoutActions,
  swipeout_button_default as f7SwipeoutButton,
  tab_default as f7Tab,
  tabs_default as f7Tabs,
  text_editor_default as f7TextEditor,
  toggle_default as f7Toggle,
  toolbar_default as f7Toolbar,
  treeview_default as f7Treeview,
  treeview_item_default as f7TreeviewItem,
  use_icon_default as f7UseIcon,
  view_default as f7View,
  views_default as f7Views,
  f7ready,
  registerComponents,
  theme,
  useStore
};
//# sourceMappingURL=framework7-vue_bundle.js.map
