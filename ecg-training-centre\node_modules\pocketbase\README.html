<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><style>body {
  max-width: 980px;
  margin: 16px auto;
}

body .markdown-body
{
  padding: 45px;
}

@font-face {
  font-family: fontawesome-mini;
  src: url(data:font/woff;charset=utf-8;base64,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) format('woff');
}

.markdown-body {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  color: #333333;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, freesans, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
}

.markdown-body a {
  background: transparent;
}

.markdown-body a:active,
.markdown-body a:hover {
  outline: 0;
}

.markdown-body b,
.markdown-body strong {
  font-weight: bold;
}

.markdown-body mark {
  background: #ff0;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

.markdown-body sub,
.markdown-body sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
.markdown-body sup {
  top: -0.5em;
}
.markdown-body sub {
  bottom: -0.25em;
}

.markdown-body h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.markdown-body img {
  border: 0;
}

.markdown-body hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}

.markdown-body pre {
  overflow: auto;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre,
.markdown-body samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

.markdown-body input {
  color: inherit;
  font: inherit;
  margin: 0;
}

.markdown-body html input[disabled] {
  cursor: default;
}

.markdown-body input {
  line-height: normal;
}

.markdown-body input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}

.markdown-body table {
  border-collapse: collapse;
  border-spacing: 0;
}

.markdown-body td,
.markdown-body th {
  padding: 0;
}

.markdown-body .codehilitetable,
.markdown-body .highlighttable {
  border: 0;
  border-spacing: 0;
}

.markdown-body .codehilitetable tr,
.markdown-body .highlighttable {
  border: 0;
}

.markdown-body .codehilitetable pre,
.markdown-body .codehilitetable div.codehilite,
.markdown-body .highlighttable pre,
.markdown-body .highlighttable div.highlight {
  margin: 0;
}

.markdown-body .linenos,
.markdown-body .code,
.markdown-body .codehilitetable td,
.markdown-body .highlighttable td {
  border: 0;
  padding: 0;
}

.markdown-body td:not(.linenos) .linenodiv {
  padding: 0 !important;
}

.markdown-body .code {
  width: 100%;
}

.markdown-body .linenos div pre,
.markdown-body .linenodiv pre,
.markdown-body .linenodiv {
  border: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-border-top-left-radius: 3px;
  -webkit-border-bottom-left-radius: 3px;
  -moz-border-radius-topleft: 3px;
  -moz-border-radius-bottomleft: 3px;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.markdown-body .code div pre,
.markdown-body .code div {
  border: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-border-top-right-radius: 3px;
  -webkit-border-bottom-right-radius: 3px;
  -moz-border-radius-topright: 3px;
  -moz-border-radius-bottomright: 3px;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.markdown-body * {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.markdown-body input {
  font: 13px Helvetica, arial, freesans, clean, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
  line-height: 1.4;
}

.markdown-body a {
  color: #4183c4;
  text-decoration: none;
}

.markdown-body a:hover,
.markdown-body a:focus,
.markdown-body a:active {
  text-decoration: underline;
}

.markdown-body hr {
  height: 0;
  margin: 15px 0;
  overflow: hidden;
  background: transparent;
  border: 0;
  border-bottom: 1px solid #ddd;
}

.markdown-body hr:before,
.markdown-body hr:after {
  display: table;
  content: " ";
}

.markdown-body hr:after {
  clear: both;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 15px;
  margin-bottom: 15px;
  line-height: 1.1;
}

.markdown-body h1 {
  font-size: 30px;
}

.markdown-body h2 {
  font-size: 21px;
}

.markdown-body h3 {
  font-size: 16px;
}

.markdown-body h4 {
  font-size: 14px;
}

.markdown-body h5 {
  font-size: 12px;
}

.markdown-body h6 {
  font-size: 11px;
}

.markdown-body blockquote {
  margin: 0;
}

.markdown-body ul,
.markdown-body ol {
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body ol ol,
.markdown-body ul ol {
  list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.markdown-body dd {
  margin-left: 0;
}

.markdown-body code,
.markdown-body pre,
.markdown-body samp {
  font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 12px;
}

.markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body kbd {
  background-color: #e7e7e7;
  background-image: -moz-linear-gradient(#fefefe, #e7e7e7);
  background-image: -webkit-linear-gradient(#fefefe, #e7e7e7);
  background-image: linear-gradient(#fefefe, #e7e7e7);
  background-repeat: repeat-x;
  border-radius: 2px;
  border: 1px solid #cfcfcf;
  color: #000;
  padding: 3px 5px;
  line-height: 10px;
  font: 11px Consolas, "Liberation Mono", Menlo, Courier, monospace;
  display: inline-block;
}

.markdown-body>*:first-child {
  margin-top: 0 !important;
}

.markdown-body>*:last-child {
  margin-bottom: 0 !important;
}

.markdown-body .headerlink {
  font: normal 400 16px fontawesome-mini;
  vertical-align: middle;
  margin-left: -16px;
  float: left;
  display: inline-block;
  text-decoration: none;
  opacity: 0;
  color: #333;
}

.markdown-body .headerlink:focus {
  outline: none;
}

.markdown-body h1 .headerlink {
  margin-top: 0.8rem;
}

.markdown-body h2 .headerlink,
.markdown-body h3 .headerlink {
  margin-top: 0.6rem;
}

.markdown-body h4 .headerlink {
  margin-top: 0.2rem;
}

.markdown-body h5 .headerlink,
.markdown-body h6 .headerlink {
  margin-top: 0;
}

.markdown-body .headerlink:hover,
.markdown-body h1:hover .headerlink,
.markdown-body h2:hover .headerlink,
.markdown-body h3:hover .headerlink,
.markdown-body h4:hover .headerlink,
.markdown-body h5:hover .headerlink,
.markdown-body h6:hover .headerlink {
  opacity: 1;
  text-decoration: none;
}

.markdown-body h1 {
  padding-bottom: 0.3em;
  font-size: 2.25em;
  line-height: 1.2;
  border-bottom: 1px solid #eee;
}

.markdown-body h2 {
  padding-bottom: 0.3em;
  font-size: 1.75em;
  line-height: 1.225;
  border-bottom: 1px solid #eee;
}

.markdown-body h3 {
  font-size: 1.5em;
  line-height: 1.43;
}

.markdown-body h4 {
  font-size: 1.25em;
}

.markdown-body h5 {
  font-size: 1em;
}

.markdown-body h6 {
  font-size: 1em;
  color: #777;
}

.markdown-body p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body table,
.markdown-body pre,
.markdown-body .admonition {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body hr {
  height: 4px;
  padding: 0;
  margin: 16px 0;
  background-color: #e7e7e7;
  border: 0 none;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body li>p {
  margin-top: 16px;
}

.markdown-body dl {
  padding: 0;
}

.markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: bold;
}

.markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.markdown-body blockquote {
  padding: 0 15px;
  color: #777;
  border-left: 4px solid #ddd;
}

.markdown-body blockquote>:first-child {
  margin-top: 0;
}

.markdown-body blockquote>:last-child {
  margin-bottom: 0;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
  word-break: normal;
  word-break: keep-all;
}

.markdown-body table th {
  font-weight: bold;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #ddd;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #ccc;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f8f8f8;
}

.markdown-body img {
  max-width: 100%;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.markdown-body code,
.markdown-body samp {
  padding: 0;
  padding-top: 0.2em;
  padding-bottom: 0.2em;
  margin: 0;
  font-size: 85%;
  border-radius: 3px;
}

.markdown-body code:not(.highlight):not(.codehilite), .markdown-body samp {
  background-color: rgba(0,0,0,0.04);
}

.markdown-body code:before,
.markdown-body code:after {
  letter-spacing: -0.2em;
  content: "\00a0";
}

.markdown-body pre>code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.markdown-body .codehilite,
.markdown-body .highlight {
  margin-bottom: 16px;
}

.markdown-body .codehilite pre,
.markdown-body .highlight pre,
.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
}

.markdown-body .codehilite,
.markdown-body .highlight,
.markdown-body pre {
  border-radius: 3px;
}

.markdown-body :not(.highlight) > pre {
  background-color: #f7f7f7;
}

.markdown-body .codehilite pre,
.markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.markdown-body pre {
  word-wrap: normal;
}

.markdown-body pre code {
  display: inline;
  max-width: initial;
  padding: 0;
  margin: 0;
  overflow: initial;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.markdown-body pre code:before,
.markdown-body pre code:after {
  content: normal;
}

/* Admonition */
.markdown-body .admonition {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  position: relative;
  border-radius: 3px;
  border: 1px solid #e0e0e0;
  border-left: 6px solid #333;
  padding: 10px 10px 10px 30px;
}

.markdown-body .admonition table {
  color: #333;
}

.markdown-body .admonition p {
  padding: 0;
}

.markdown-body .admonition-title {
  font-weight: bold;
  margin: 0;
}

.markdown-body .admonition>.admonition-title {
  color: #333;
}

.markdown-body .attention>.admonition-title {
  color: #a6d796;
}

.markdown-body .caution>.admonition-title {
  color: #d7a796;
}

.markdown-body .hint>.admonition-title {
  color: #96c6d7;
}

.markdown-body .danger>.admonition-title {
  color: #c25f77;
}

.markdown-body .question>.admonition-title {
  color: #96a6d7;
}

.markdown-body .note>.admonition-title {
  color: #d7c896;
}

.markdown-body .admonition:before,
.markdown-body .attention:before,
.markdown-body .caution:before,
.markdown-body .hint:before,
.markdown-body .danger:before,
.markdown-body .question:before,
.markdown-body .note:before {
  font: normal normal 16px fontawesome-mini;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  line-height: 1.5;
  color: #333;
  position: absolute;
  left: 0;
  top: 0;
  padding-top: 10px;
  padding-left: 10px;
}

.markdown-body .admonition:before {
  content: "\f056\00a0";
  color: 333;
}

.markdown-body .attention:before {
  content: "\f058\00a0";
  color: #a6d796;
}

.markdown-body .caution:before {
  content: "\f06a\00a0";
  color: #d7a796;
}

.markdown-body .hint:before {
  content: "\f05a\00a0";
  color: #96c6d7;
}

.markdown-body .danger:before {
  content: "\f057\00a0";
  color: #c25f77;
}

.markdown-body .question:before {
  content: "\f059\00a0";
  color: #96a6d7;
}

.markdown-body .note:before {
  content: "\f040\00a0";
  color: #d7c896;
}

.markdown-body .admonition::after {
  content: normal;
}

.markdown-body .attention {
  border-left: 6px solid #a6d796;
}

.markdown-body .caution {
  border-left: 6px solid #d7a796;
}

.markdown-body .hint {
  border-left: 6px solid #96c6d7;
}

.markdown-body .danger {
  border-left: 6px solid #c25f77;
}

.markdown-body .question {
  border-left: 6px solid #96a6d7;
}

.markdown-body .note {
  border-left: 6px solid #d7c896;
}

.markdown-body .admonition>*:first-child {
  margin-top: 0 !important;
}

.markdown-body .admonition>*:last-child {
  margin-bottom: 0 !important;
}

/* progress bar*/
.markdown-body .progress {
  display: block;
  width: 300px;
  margin: 10px 0;
  height: 24px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background-color: #ededed;
  position: relative;
  box-shadow: inset -1px 1px 3px rgba(0, 0, 0, .1);
}

.markdown-body .progress-label {
  position: absolute;
  text-align: center;
  font-weight: bold;
  width: 100%; margin: 0;
  line-height: 24px;
  color: #333;
  text-shadow: 1px 1px 0 #fefefe, -1px -1px 0 #fefefe, -1px 1px 0 #fefefe, 1px -1px 0 #fefefe, 0 1px 0 #fefefe, 0 -1px 0 #fefefe, 1px 0 0 #fefefe, -1px 0 0 #fefefe, 1px 1px 2px #000;
  -webkit-font-smoothing: antialiased !important;
  white-space: nowrap;
  overflow: hidden;
}

.markdown-body .progress-bar {
  height: 24px;
  float: left;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background-color: #96c6d7;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .5), inset 0 -1px 0 rgba(0, 0, 0, .1);
  background-size: 30px 30px;
  background-image: -webkit-linear-gradient(
    135deg, rgba(255, 255, 255, .4) 27%,
    transparent 27%,
    transparent 52%, rgba(255, 255, 255, .4) 52%,
    rgba(255, 255, 255, .4) 77%,
    transparent 77%, transparent
  );
  background-image: -moz-linear-gradient(
    135deg,
    rgba(255, 255, 255, .4) 27%, transparent 27%,
    transparent 52%, rgba(255, 255, 255, .4) 52%,
    rgba(255, 255, 255, .4) 77%, transparent 77%,
    transparent
  );
  background-image: -ms-linear-gradient(
    135deg,
    rgba(255, 255, 255, .4) 27%, transparent 27%,
    transparent 52%, rgba(255, 255, 255, .4) 52%,
    rgba(255, 255, 255, .4) 77%, transparent 77%,
    transparent
  );
  background-image: -o-linear-gradient(
    135deg,
    rgba(255, 255, 255, .4) 27%, transparent 27%,
    transparent 52%, rgba(255, 255, 255, .4) 52%,
    rgba(255, 255, 255, .4) 77%, transparent 77%,
    transparent
  );
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, .4) 27%, transparent 27%,
    transparent 52%, rgba(255, 255, 255, .4) 52%,
    rgba(255, 255, 255, .4) 77%, transparent 77%,
    transparent
  );
}

.markdown-body .progress-100plus .progress-bar {
  background-color: #a6d796;
}

.markdown-body .progress-80plus .progress-bar {
  background-color: #c6d796;
}

.markdown-body .progress-60plus .progress-bar {
  background-color: #d7c896;
}

.markdown-body .progress-40plus .progress-bar {
  background-color: #d7a796;
}

.markdown-body .progress-20plus .progress-bar {
  background-color: #d796a6;
}

.markdown-body .progress-0plus .progress-bar {
  background-color: #c25f77;
}

.markdown-body .candystripe-animate .progress-bar{
  -webkit-animation: animate-stripes 3s linear infinite;
  -moz-animation: animate-stripes 3s linear infinite;
  animation: animate-stripes 3s linear infinite;
}

@-webkit-keyframes animate-stripes {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 60px 0;
  }
}

@-moz-keyframes animate-stripes {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 60px 0;
  }
}

@keyframes animate-stripes {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 60px 0;
  }
}

.markdown-body .gloss .progress-bar {
  box-shadow:
    inset 0 4px 12px rgba(255, 255, 255, .7),
    inset 0 -12px 0 rgba(0, 0, 0, .05);
}

/* MultiMarkdown Critic Blocks */
.markdown-body .critic_mark {
  background: #ff0;
}

.markdown-body .critic_delete {
  color: #c82829;
  text-decoration: line-through;
}

.markdown-body .critic_insert {
  color: #718c00 ;
  text-decoration: underline;
}

.markdown-body .critic_comment {
  color: #8e908c;
  font-style: italic;
}

.markdown-body .headeranchor {
  font: normal normal 16px fontawesome-mini;
  line-height: 1;
  display: inline-block;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.headeranchor:before {
  content: '\e157';
}

.markdown-body .task-list-item {
  list-style-type: none;
}

.markdown-body .task-list-item+.task-list-item {
  margin-top: 3px;
}

.markdown-body .task-list-item input {
  margin: 0 4px 0.25em -20px;
  vertical-align: middle;
}

.markdown-body diagram-div, .markdown-body div.uml-sequence-diagram, .markdown-body, div.uml-flowchart {
  overflow: auto;
}

/* Media */
@media only screen and (min-width: 480px) {
  .markdown-body {
    font-size:14px;
  }
}

@media only screen and (min-width: 768px) {
  .markdown-body {
    font-size:16px;
  }
}

@media print {
  .markdown-body * {
    background: transparent !important;
    color: black !important;
    filter:none !important;
    -ms-filter: none !important;
  }

  .markdown-body {
    font-size:12pt;
    max-width:100%;
    outline:none;
    border: 0;
  }

  .markdown-body a,
  .markdown-body a:visited {
    text-decoration: underline;
  }

  .markdown-body .headeranchor-link {
    display: none;
  }

  .markdown-body a[href]:after {
    content: " (" attr(href) ")";
  }

  .markdown-body abbr[title]:after {
    content: " (" attr(title) ")";
  }

  .markdown-body .ir a:after,
  .markdown-body a[href^="javascript:"]:after,
  .markdown-body a[href^="#"]:after {
    content: "";
  }

  .markdown-body pre {
    white-space: pre;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .markdown-body pre,
  .markdown-body blockquote {
    border: 1px solid #999;
    padding-right: 1em;
    page-break-inside: avoid;
  }

  .markdown-body .progress,
  .markdown-body .progress-bar {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  .markdown-body .progress {
    border: 1px solid #ddd;
  }

  .markdown-body .progress-bar {
    height: 22px;
    border-right: 1px solid #ddd;
  }

  .markdown-body tr,
  .markdown-body img {
    page-break-inside: avoid;
  }

  .markdown-body img {
    max-width: 100% !important;
  }

  .markdown-body p,
  .markdown-body h2,
  .markdown-body h3 {
    orphans: 3;
    widows: 3;
  }

  .markdown-body h2,
  .markdown-body h3 {
    page-break-after: avoid;
  }
}
</style><style>/*GitHub*/
.highlight {background-color:#f7f7f7;color:#333333;}
.highlight .hll {background-color:#ffffcc;}
.highlight .c{color:#999988;font-style:italic}
.highlight .err{color:#a61717;background-color:#e3d2d2}
.highlight .k{font-weight:bold}
.highlight .o{font-weight:bold}
.highlight .cm{color:#999988;font-style:italic}
.highlight .cp{color:#999999;font-weight:bold}
.highlight .c1{color:#999988;font-style:italic}
.highlight .cs{color:#999999;font-weight:bold;font-style:italic}
.highlight .gd{color:#000000;background-color:#ffdddd}
.highlight .ge{font-style:italic}
.highlight .gr{color:#aa0000}
.highlight .gh{color:#999999}
.highlight .gi{color:#000000;background-color:#ddffdd}
.highlight .go{color:#888888}
.highlight .gp{color:#555555}
.highlight .gs{font-weight:bold}
.highlight .gu{color:#800080;font-weight:bold}
.highlight .gt{color:#aa0000}
.highlight .kc{font-weight:bold}
.highlight .kd{font-weight:bold}
.highlight .kn{font-weight:bold}
.highlight .kp{font-weight:bold}
.highlight .kr{font-weight:bold}
.highlight .kt{color:#445588;font-weight:bold}
.highlight .m{color:#009999}
.highlight .s{color:#dd1144}
.highlight .n{color:#333333}
.highlight .na{color:teal}
.highlight .nb{color:#0086b3}
.highlight .nc{color:#445588;font-weight:bold}
.highlight .no{color:teal}
.highlight .ni{color:purple}
.highlight .ne{color:#990000;font-weight:bold}
.highlight .nf{color:#990000;font-weight:bold}
.highlight .nn{color:#555555}
.highlight .nt{color:navy}
.highlight .nv{color:teal}
.highlight .ow{font-weight:bold}
.highlight .w{color:#bbbbbb}
.highlight .mf{color:#009999}
.highlight .mh{color:#009999}
.highlight .mi{color:#009999}
.highlight .mo{color:#009999}
.highlight .sb{color:#dd1144}
.highlight .sc{color:#dd1144}
.highlight .sd{color:#dd1144}
.highlight .s2{color:#dd1144}
.highlight .se{color:#dd1144}
.highlight .sh{color:#dd1144}
.highlight .si{color:#dd1144}
.highlight .sx{color:#dd1144}
.highlight .sr{color:#009926}
.highlight .s1{color:#dd1144}
.highlight .ss{color:#990073}
.highlight .bp{color:#999999}
.highlight .vc{color:teal}
.highlight .vg{color:teal}
.highlight .vi{color:teal}
.highlight .il{color:#009999}
.highlight .gc{color:#999;background-color:#EAF2F5}
</style><title>README</title></head><body><article class="markdown-body"><h1 id="pocketbase-javascript-sdk">PocketBase JavaScript SDK<a class="headerlink" href="#pocketbase-javascript-sdk" title="Permanent link"></a></h1>
<p>Official JavaScript SDK (browser and node) for interacting with the <a href="https://pocketbase.io/docs">PocketBase API</a>.</p>
<ul>
<li><a href="#installation">Installation</a></li>
<li><a href="#usage">Usage</a></li>
<li><a href="#caveats">Caveats</a><ul>
<li><a href="#binding-filter-parameters">Binding filter parameters</a></li>
<li><a href="#file-upload">File upload</a></li>
<li><a href="#error-handling">Error handling</a></li>
<li><a href="#auth-store">Auth store</a><ul>
<li><a href="#localauthstore-default">LocalAuthStore (default)</a></li>
<li><a href="#asyncauthstore">AsyncAuthStore (<em>usually used with React Native</em>)</a></li>
<li><a href="#custom-auth-store">Custom auth store</a></li>
<li><a href="#common-auth-store-fields-and-methods">Common auth store fields and methods</a></li>
</ul>
</li>
<li><a href="#auto-cancellation">Auto cancellation</a></li>
<li><a href="#specify-typescript-definitions">Specify TypeScript definitions</a></li>
<li><a href="#custom-request-options">Custom request options</a></li>
<li><a href="#send-hooks">Send hooks</a></li>
<li><a href="#ssr-integration">SSR integration</a></li>
<li><a href="#security">Security</a></li>
</ul>
</li>
<li><a href="#definitions">Definitions</a></li>
<li><a href="#development">Development</a></li>
</ul>
<h2 id="installation">Installation<a class="headerlink" href="#installation" title="Permanent link"></a></h2>
<h3 id="browser-manually-via-script-tag">Browser (manually via script tag)<a class="headerlink" href="#browser-manually-via-script-tag" title="Permanent link"></a></h3>
<div class="highlight"><pre><span class="p">&lt;</span><span class="nt">script</span> <span class="na">src</span><span class="o">=</span><span class="s">&quot;/path/to/dist/pocketbase.umd.js&quot;</span><span class="p">&gt;&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
<span class="p">&lt;</span><span class="nt">script</span> <span class="na">type</span><span class="o">=</span><span class="s">&quot;text/javascript&quot;</span><span class="p">&gt;</span>
    <span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s2">&quot;https://example.com&quot;</span><span class="p">)</span>
    <span class="p">...</span>
<span class="p">&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
</pre></div>

<p><em>OR if you are using ES modules:</em>
<div class="highlight"><pre><span class="p">&lt;</span><span class="nt">script</span> <span class="na">type</span><span class="o">=</span><span class="s">&quot;module&quot;</span><span class="p">&gt;</span>
    <span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;/path/to/dist/pocketbase.es.mjs&#39;</span>

    <span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s2">&quot;https://example.com&quot;</span><span class="p">)</span>
    <span class="p">...</span>
<span class="p">&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
</pre></div></p>
<h3 id="nodejs-via-npm">Node.js (via npm)<a class="headerlink" href="#nodejs-via-npm" title="Permanent link"></a></h3>
<div class="highlight"><pre>npm install pocketbase --save
</pre></div>

<div class="highlight"><pre><span class="c1">// Using ES modules (default)</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span>

<span class="c1">// OR if you are using CommonJS modules</span>
<span class="kr">const</span> <span class="nx">PocketBase</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="s1">&#39;pocketbase/cjs&#39;</span><span class="p">)</span>
</pre></div>

<blockquote>
<p>🔧 For <strong>Node &lt; 17</strong> you&rsquo;ll need to load a <code>fetch()</code> polyfill.
I recommend <a href="https://github.com/lquixada/cross-fetch">lquixada/cross-fetch</a>:
<div class="highlight"><pre><span class="c1">// npm install cross-fetch --save</span>
<span class="kr">import</span> <span class="s1">&#39;cross-fetch/polyfill&#39;</span><span class="p">;</span>
</pre></div></p>
</blockquote>
<hr />
<blockquote>
<p>🔧 Node doesn&rsquo;t have native <code>EventSource</code> implementation, so in order to use the realtime subscriptions you&rsquo;ll need to load a <code>EventSource</code> polyfill.
<div class="highlight"><pre><span class="c1">// for server: npm install eventsource --save</span>
<span class="kr">import</span> <span class="nx">eventsource</span> <span class="nx">from</span> <span class="s1">&#39;eventsource&#39;</span><span class="p">;</span>

<span class="c1">// for React Native: npm install react-native-sse --save</span>
<span class="kr">import</span> <span class="nx">eventsource</span> <span class="nx">from</span> <span class="s2">&quot;react-native-sse&quot;</span><span class="p">;</span>

<span class="nx">global</span><span class="p">.</span><span class="nx">EventSource</span> <span class="o">=</span> <span class="nx">eventsource</span><span class="p">;</span>
</pre></div></p>
</blockquote>
<h2 id="usage">Usage<a class="headerlink" href="#usage" title="Permanent link"></a></h2>
<div class="highlight"><pre><span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

<span class="p">...</span>

<span class="c1">// list and filter &quot;example&quot; collection records</span>
<span class="kr">const</span> <span class="nx">result</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span>
    <span class="nx">filter</span><span class="o">:</span> <span class="s1">&#39;status = true &amp;&amp; created &gt; &quot;2022-08-01 10:00:00&quot;&#39;</span>
<span class="p">});</span>

<span class="c1">// authenticate as auth collection record</span>
<span class="kr">const</span> <span class="nx">userData</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span> <span class="s1">&#39;123456&#39;</span><span class="p">);</span>

<span class="c1">// or as super-admin</span>
<span class="kr">const</span> <span class="nx">adminData</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span> <span class="s1">&#39;123456&#39;</span><span class="p">);</span>

<span class="c1">// and much more...</span>
</pre></div>

<blockquote>
<p>More detailed API docs and copy-paste examples could be found in the <a href="https://pocketbase.io/docs/api-records/">API documentation for each service</a>.</p>
</blockquote>
<h2 id="caveats">Caveats<a class="headerlink" href="#caveats" title="Permanent link"></a></h2>
<h3 id="binding-filter-parameters">Binding filter parameters<a class="headerlink" href="#binding-filter-parameters" title="Permanent link"></a></h3>
<p>The SDK comes with a helper <code>pb.filter(expr, params)</code> method to generate a filter string with placeholder parameters (<code>{:paramName}</code>) populated from an object.</p>
<p><strong>This method is also recommended when using the SDK in Node/Deno/Bun server-side list queries and accepting untrusted user input as <code>filter</code> string arguments, because it will take care to properly escape the generated string expression, avoiding eventual string injection attacks</strong> (<em>on the client-side this is not much of an issue</em>).</p>
<div class="highlight"><pre><span class="kr">const</span> <span class="nx">record</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s2">&quot;example&quot;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span>
  <span class="c1">// the same as: &quot;title ~ &#39;te\\&#39;st&#39; &amp;&amp; (totalA = 123 || totalB = 123)&quot;</span>
  <span class="nx">filter</span><span class="o">:</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">filter</span><span class="p">(</span><span class="s2">&quot;title ~ {:title} &amp;&amp; (totalA = {:num} || totalB = {:num})&quot;</span><span class="p">,</span> <span class="p">{</span> <span class="nx">title</span><span class="o">:</span> <span class="s2">&quot;te&#39;st&quot;</span><span class="p">,</span> <span class="nx">num</span><span class="o">:</span> <span class="mi">123</span> <span class="p">})</span>
<span class="p">})</span>
</pre></div>

<p>The supported placeholder parameter values are:</p>
<ul>
<li><code>string</code> (<em>single quotes will be autoescaped</em>)</li>
<li><code>number</code></li>
<li><code>boolean</code></li>
<li><code>Date</code> object (<em>will be stringified into the format expected by PocketBase</em>)</li>
<li><code>null</code></li>
<li>anything else is converted to a string using <code>JSON.stringify()</code></li>
</ul>
<h3 id="file-upload">File upload<a class="headerlink" href="#file-upload" title="Permanent link"></a></h3>
<p>PocketBase Web API supports file upload via <code>multipart/form-data</code> requests,
which means that to upload a file it is enough to provide either a <a href="https://developer.mozilla.org/en-US/docs/Web/API/FormData"><code>FormData</code></a> instance OR plain object with <code>File</code>/<code>Blob</code> prop values.</p>
<ul>
<li>
<p>Using <code>FormData</code> as body:
    <div class="highlight"><pre><span class="c1">// the standard way to create multipart/form-data body</span>
<span class="kr">const</span> <span class="nx">data</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">FormData</span><span class="p">();</span>
<span class="nx">data</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;title&#39;</span><span class="p">,</span> <span class="s1">&#39;lorem ipsum...&#39;</span><span class="p">)</span>
<span class="nx">data</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;document&#39;</span><span class="p">,</span> <span class="k">new</span> <span class="nx">File</span><span class="p">(...))</span>

<span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">create</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
</pre></div></p>
</li>
<li>
<p>Using plain object as body <em>(this is the same as above and it will be converted to <code>FormData</code> behind the scenes)</em>:
    <div class="highlight"><pre><span class="kr">const</span> <span class="nx">data</span> <span class="o">=</span> <span class="p">{</span>
  <span class="s1">&#39;title&#39;</span><span class="o">:</span>    <span class="s1">&#39;lorem ipsum...&#39;</span><span class="p">,</span>
  <span class="s1">&#39;document&#39;</span><span class="o">:</span> <span class="k">new</span> <span class="nx">File</span><span class="p">(...),</span>
<span class="p">};</span>

<span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">create</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
</pre></div></p>
</li>
</ul>
<h3 id="error-handling">Error handling<a class="headerlink" href="#error-handling" title="Permanent link"></a></h3>
<p>All services return a standard <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>-based response, so the error handling is straightforward:
<div class="highlight"><pre><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">50</span><span class="p">).</span><span class="nx">then</span><span class="p">((</span><span class="nx">result</span><span class="p">)</span> <span class="p">{</span>
  <span class="c1">// success...</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Result:&#39;</span><span class="p">,</span> <span class="nx">result</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="p">{</span>
  <span class="c1">// error...</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Error:&#39;</span><span class="p">,</span> <span class="nx">error</span><span class="p">);</span>
<span class="p">});</span>

<span class="c1">// OR if you are using the async/await syntax:</span>
<span class="k">try</span> <span class="p">{</span>
  <span class="kr">const</span> <span class="nx">result</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">50</span><span class="p">);</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Result:&#39;</span><span class="p">,</span> <span class="nx">result</span><span class="p">);</span>
<span class="p">}</span> <span class="k">catch</span> <span class="p">(</span><span class="nx">error</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Error:&#39;</span><span class="p">,</span> <span class="nx">error</span><span class="p">);</span>
<span class="p">}</span>
</pre></div></p>
<p>The response error is normalized and always returned as <code>ClientResponseError</code> object with the following public fields that you could use:
<div class="highlight"><pre><span class="nx">ClientResponseError</span> <span class="p">{</span>
    <span class="nx">url</span><span class="o">:</span>           <span class="nx">string</span><span class="p">,</span>     <span class="c1">// requested url</span>
    <span class="nx">status</span><span class="o">:</span>        <span class="nx">number</span><span class="p">,</span>     <span class="c1">// response status code</span>
    <span class="nx">response</span><span class="o">:</span>      <span class="p">{</span> <span class="p">...</span> <span class="p">},</span>    <span class="c1">// the API JSON error response</span>
    <span class="nx">isAbort</span><span class="o">:</span>       <span class="kr">boolean</span><span class="p">,</span>    <span class="c1">// is abort/cancellation error</span>
    <span class="nx">originalError</span><span class="o">:</span> <span class="nb">Error</span><span class="o">|</span><span class="kc">null</span><span class="p">,</span> <span class="c1">// the original non-normalized error</span>
<span class="p">}</span>
</pre></div></p>
<h3 id="auth-store">Auth store<a class="headerlink" href="#auth-store" title="Permanent link"></a></h3>
<p>The SDK keeps track of the authenticated token and auth model for you via the <code>pb.authStore</code> instance.</p>
<h5 id="localauthstore-default">LocalAuthStore (default)<a class="headerlink" href="#localauthstore-default" title="Permanent link"></a></h5>
<p>The default <a href="https://github.com/pocketbase/js-sdk/blob/master/src/stores/LocalAuthStore.ts"><code>LocalAuthStore</code></a> uses the browser&rsquo;s <code>LocalStorage</code> if available, otherwise - will fallback to runtime/memory (aka. on page refresh or service restart you&rsquo;ll have to authenticate again).</p>
<p>Conveniently, the default store also takes care to automatically sync the auth store state between multiple tabs.</p>
<blockquote>
<p><em><strong>NB!</strong> Deno also supports <code>LocalStorage</code> but keep in mind that, unlike in browsers where the client is the only user, by default Deno <code>LocalStorage</code> will be shared by all clients making requests to your server!</em></p>
</blockquote>
<h5 id="asyncauthstore">AsyncAuthStore<a class="headerlink" href="#asyncauthstore" title="Permanent link"></a></h5>
<p>The SDK comes also with a helper <a href="https://github.com/pocketbase/js-sdk/blob/master/src/stores/AsyncAuthStore.ts"><code>AsyncAuthStore</code></a> that you can use to integrate with any 3rd party async storage implementation (<em>usually this is needed when working with React Native</em>):
<div class="highlight"><pre><span class="kr">import</span> <span class="nx">AsyncStorage</span> <span class="nx">from</span> <span class="s1">&#39;@react-native-async-storage/async-storage&#39;</span><span class="p">;</span>
<span class="kr">import</span> <span class="nx">PocketBase</span><span class="p">,</span> <span class="p">{</span> <span class="nx">AsyncAuthStore</span> <span class="p">}</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">const</span> <span class="nx">store</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">AsyncAuthStore</span><span class="p">({</span>
    <span class="nx">save</span><span class="o">:</span>    <span class="nx">async</span> <span class="p">(</span><span class="nx">serialized</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="nx">AsyncStorage</span><span class="p">.</span><span class="nx">setItem</span><span class="p">(</span><span class="s1">&#39;pb_auth&#39;</span><span class="p">,</span> <span class="nx">serialized</span><span class="p">),</span>
    <span class="nx">initial</span><span class="o">:</span> <span class="nx">AsyncStorage</span><span class="p">.</span><span class="nx">getItem</span><span class="p">(</span><span class="s1">&#39;pb_auth&#39;</span><span class="p">),</span>
<span class="p">});</span>

<span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">,</span> <span class="nx">store</span><span class="p">)</span>
</pre></div></p>
<h5 id="custom-auth-store">Custom auth store<a class="headerlink" href="#custom-auth-store" title="Permanent link"></a></h5>
<p>In some situations it could be easier to create your own custom auth store. For this you can extend <a href="https://github.com/pocketbase/js-sdk/blob/master/src/stores/BaseAuthStore.ts"><code>BaseAuthStore</code></a> and pass the new custom instance as constructor argument to the client:</p>
<div class="highlight"><pre><span class="kr">import</span> <span class="nx">PocketBase</span><span class="p">,</span> <span class="p">{</span> <span class="nx">BaseAuthStore</span> <span class="p">}</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">class</span> <span class="nx">CustomAuthStore</span> <span class="kr">extends</span> <span class="nx">BaseAuthStore</span> <span class="p">{</span>
    <span class="nx">save</span><span class="p">(</span><span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span> <span class="p">{</span>
        <span class="kr">super</span><span class="p">.</span><span class="nx">save</span><span class="p">(</span><span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">);</span>

        <span class="c1">// your custom business logic...</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">,</span> <span class="k">new</span> <span class="nx">CustomAuthStore</span><span class="p">());</span>
</pre></div>

<h5 id="common-auth-store-fields-and-methods">Common auth store fields and methods<a class="headerlink" href="#common-auth-store-fields-and-methods" title="Permanent link"></a></h5>
<p>The default <code>pb.authStore</code> extends <a href="https://github.com/pocketbase/js-sdk/blob/master/src/stores/BaseAuthStore.ts"><code>BaseAuthStore</code></a> and has the following public members that you can use:</p>
<div class="highlight"><pre><span class="nx">BaseAuthStore</span> <span class="p">{</span>
    <span class="c1">// base fields</span>
    <span class="nx">model</span><span class="o">:</span>        <span class="nx">RecordModel</span><span class="o">|</span><span class="nx">AdminModel</span><span class="o">|</span><span class="kc">null</span> <span class="c1">// the authenticated auth record or admin model</span>
    <span class="nx">token</span><span class="o">:</span>        <span class="nx">string</span> <span class="c1">// the authenticated token</span>
    <span class="nx">isValid</span><span class="o">:</span>      <span class="kr">boolean</span> <span class="c1">// checks if the store has existing and unexpired token</span>
    <span class="nx">isAdmin</span><span class="o">:</span>      <span class="kr">boolean</span> <span class="c1">// checks if the store state is for admin</span>
    <span class="nx">isAuthRecord</span><span class="o">:</span> <span class="kr">boolean</span> <span class="c1">// checks if the store state is for an auth record</span>

    <span class="c1">// main methods</span>
    <span class="nx">clear</span><span class="p">()</span>            <span class="c1">// &quot;logout&quot; the authenticated record or admin model</span>
    <span class="nx">save</span><span class="p">(</span><span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span> <span class="c1">// update the store with the new auth data</span>
    <span class="nx">onChange</span><span class="p">(</span><span class="nx">callback</span><span class="p">,</span> <span class="nx">fireImmediately</span> <span class="o">=</span> <span class="kc">false</span><span class="p">)</span> <span class="c1">// register a callback that will be called on store change</span>

    <span class="c1">// cookie parse and serialize helpers</span>
    <span class="nx">loadFromCookie</span><span class="p">(</span><span class="nx">cookieHeader</span><span class="p">,</span> <span class="nx">key</span> <span class="o">=</span> <span class="s1">&#39;pb_auth&#39;</span><span class="p">)</span>
    <span class="nx">exportToCookie</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">key</span> <span class="o">=</span> <span class="s1">&#39;pb_auth&#39;</span><span class="p">)</span>
<span class="p">}</span>
</pre></div>

<p>To <em>&ldquo;logout&rdquo;</em> an authenticated record or admin you can call <code>pb.authStore.clear()</code>.</p>
<p>To <em>&ldquo;listen&rdquo;</em> for changes in the auth store, you can register a new listener via <code>pb.authStore.onChange</code>, eg:
<div class="highlight"><pre><span class="c1">// triggered everytime on store change</span>
<span class="kr">const</span> <span class="nx">removeListener1</span> <span class="o">=</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">onChange</span><span class="p">((</span><span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;New store data 1:&#39;</span><span class="p">,</span> <span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span>
<span class="p">});</span>

<span class="c1">// triggered once right after registration and everytime on store change</span>
<span class="kr">const</span> <span class="nx">removeListener2</span> <span class="o">=</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">onChange</span><span class="p">((</span><span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;New store data 2:&#39;</span><span class="p">,</span> <span class="nx">token</span><span class="p">,</span> <span class="nx">model</span><span class="p">)</span>
<span class="p">},</span> <span class="kc">true</span><span class="p">);</span>

<span class="c1">// (optional) removes the attached listeners</span>
<span class="nx">removeListener1</span><span class="p">();</span>
<span class="nx">removeListener2</span><span class="p">();</span>
</pre></div></p>
<h3 id="auto-cancellation">Auto cancellation<a class="headerlink" href="#auto-cancellation" title="Permanent link"></a></h3>
<p>The SDK client will auto cancel duplicated pending requests for you.
For example, if you have the following 3 duplicated endpoint calls, only the last one will be executed, while the first 2 will be cancelled with <code>ClientResponseError</code> error:</p>
<div class="highlight"><pre><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span> <span class="c1">// cancelled</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span> <span class="c1">// cancelled</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span> <span class="c1">// executed</span>
</pre></div>

<p>To change this behavior per request basis, you can adjust the <code>requestKey: null|string</code> special query parameter.
Set it to <code>null</code> to unset the default request identifier and to disable auto cancellation for the specific request.
Or set it to a unique string that will be used as request identifier and based on which pending requests will be matched (default to <code>HTTP_METHOD + path</code>, eg. &ldquo;GET /api/users&rdquo;)</p>
<p>Example:</p>
<div class="highlight"><pre><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">);</span>                        <span class="c1">// cancelled</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">);</span>                        <span class="c1">// executed</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span> <span class="nx">requestKey</span><span class="o">:</span> <span class="s2">&quot;test&quot;</span> <span class="p">})</span> <span class="c1">// cancelled</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span> <span class="nx">requestKey</span><span class="o">:</span> <span class="s2">&quot;test&quot;</span> <span class="p">})</span> <span class="c1">// executed</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span> <span class="nx">requestKey</span><span class="o">:</span> <span class="kc">null</span> <span class="p">})</span>   <span class="c1">// executed</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span> <span class="nx">requestKey</span><span class="o">:</span> <span class="kc">null</span> <span class="p">})</span>   <span class="c1">// executed</span>

<span class="c1">// globally disable auto cancellation</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">autoCancellation</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>

<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">);</span> <span class="c1">// executed</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">);</span> <span class="c1">// executed</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">);</span> <span class="c1">// executed</span>
</pre></div>

<p><strong>If you want to globally disable the auto cancellation behavior, you could set <code>pb.autoCancellation(false)</code>.</strong></p>
<p>To manually cancel pending requests, you could use <code>pb.cancelAllRequests()</code> or <code>pb.cancelRequest(requestKey)</code>.</p>
<h3 id="specify-typescript-definitions">Specify TypeScript definitions<a class="headerlink" href="#specify-typescript-definitions" title="Permanent link"></a></h3>
<p>You could specify custom TypeScript definitions for your Record models using generics:</p>
<div class="highlight"><pre><span class="kr">interface</span> <span class="nx">Task</span> <span class="p">{</span>
  <span class="c1">// type the collection fields you want to use...</span>
  <span class="nx">id</span>:   <span class="kt">string</span><span class="p">;</span>
  <span class="nx">name</span>: <span class="kt">string</span><span class="p">;</span>
<span class="p">}</span>

<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;tasks&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="o">&lt;</span><span class="nx">Task</span><span class="o">&gt;</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span> <span class="c1">// -&gt; results in Promise&lt;ListResult&lt;Task&gt;&gt;</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;tasks&#39;</span><span class="p">).</span><span class="nx">getOne</span><span class="o">&lt;</span><span class="nx">Task</span><span class="o">&gt;</span><span class="p">(</span><span class="s2">&quot;RECORD_ID&quot;</span><span class="p">)</span>  <span class="c1">// -&gt; results in Promise&lt;Task&gt;</span>
</pre></div>

<p>Alternatively, if you don&rsquo;t want to type the generic argument every time you can define a global PocketBase type using type assertion:</p>
<div class="highlight"><pre><span class="kr">interface</span> <span class="nx">Task</span> <span class="p">{</span>
  <span class="nx">id</span>:   <span class="kt">string</span><span class="p">;</span>
  <span class="nx">name</span>: <span class="kt">string</span><span class="p">;</span>
<span class="p">}</span>

<span class="kr">interface</span> <span class="nx">Post</span> <span class="p">{</span>
  <span class="nx">id</span>:     <span class="kt">string</span><span class="p">;</span>
  <span class="nx">title</span>:  <span class="kt">string</span><span class="p">;</span>
  <span class="nx">active</span>: <span class="kt">boolean</span><span class="p">;</span>
<span class="p">}</span>

<span class="kr">interface</span> <span class="nx">TypedPocketBase</span> <span class="kr">extends</span> <span class="nx">PocketBase</span> <span class="p">{</span>
  <span class="nx">collection</span><span class="p">(</span><span class="nx">idOrName</span>: <span class="kt">string</span><span class="p">)</span><span class="o">:</span> <span class="nx">RecordService</span> <span class="c1">// default fallback for any other collection</span>
  <span class="nx">collection</span><span class="p">(</span><span class="nx">idOrName</span><span class="o">:</span> <span class="s1">&#39;tasks&#39;</span><span class="p">)</span><span class="o">:</span> <span class="nx">RecordService</span><span class="o">&lt;</span><span class="nx">Task</span><span class="o">&gt;</span>
  <span class="nx">collection</span><span class="p">(</span><span class="nx">idOrName</span><span class="o">:</span> <span class="s1">&#39;posts&#39;</span><span class="p">)</span><span class="o">:</span> <span class="nx">RecordService</span><span class="o">&lt;</span><span class="nx">Post</span><span class="o">&gt;</span>
<span class="p">}</span>

<span class="p">...</span>

<span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s2">&quot;http://127.0.0.1:8090&quot;</span><span class="p">)</span> <span class="kr">as</span> <span class="nx">TypedPocketBase</span><span class="p">;</span>

<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;tasks&#39;</span><span class="p">).</span><span class="nx">getOne</span><span class="p">(</span><span class="s2">&quot;RECORD_ID&quot;</span><span class="p">)</span> <span class="c1">// -&gt; results in Promise&lt;Task&gt;</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;posts&#39;</span><span class="p">).</span><span class="nx">getOne</span><span class="p">(</span><span class="s2">&quot;RECORD_ID&quot;</span><span class="p">)</span> <span class="c1">// -&gt; results in Promise&lt;Post&gt;</span>
</pre></div>

<h3 id="custom-request-options">Custom request options<a class="headerlink" href="#custom-request-options" title="Permanent link"></a></h3>
<p>All API services accept an optional <code>options</code> argument (usually the last one and of type <a href="https://github.com/pocketbase/js-sdk/blob/master/src/tools/options.ts"><code>SendOptions</code></a>), that can be used to provide:</p>
<ul>
<li>custom headers for a single request</li>
<li>custom fetch options</li>
<li>or even your own <code>fetch</code> implementation</li>
</ul>
<p>For example:</p>
<div class="highlight"><pre><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="p">{</span>
    <span class="nx">expand</span><span class="o">:</span>          <span class="s1">&#39;someRel&#39;</span><span class="p">,</span>
    <span class="nx">otherQueryParam</span><span class="o">:</span> <span class="s1">&#39;123&#39;</span><span class="p">,</span>

    <span class="c1">// custom headers</span>
    <span class="nx">headers</span><span class="o">:</span> <span class="p">{</span>
        <span class="s1">&#39;X-Custom-Header&#39;</span><span class="o">:</span> <span class="s1">&#39;example&#39;</span><span class="p">,</span>
    <span class="p">},</span>

    <span class="c1">// custom fetch options</span>
    <span class="nx">keepalive</span><span class="o">:</span> <span class="kc">false</span><span class="p">,</span>
    <span class="nx">cache</span><span class="o">:</span>     <span class="s1">&#39;no-store&#39;</span><span class="p">,</span>

    <span class="c1">// or custom fetch implementation</span>
    <span class="nx">fetch</span><span class="o">:</span> <span class="nx">async</span> <span class="p">(</span><span class="nx">url</span><span class="p">,</span> <span class="nx">config</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span> <span class="p">...</span> <span class="p">},</span>
<span class="p">})</span>
</pre></div>

<p><em>Note that for backward compatability and to minimize the verbosity, any &ldquo;unknown&rdquo; top-level field will be treated as query parameter.</em></p>
<h3 id="send-hooks">Send hooks<a class="headerlink" href="#send-hooks" title="Permanent link"></a></h3>
<p>Sometimes you may want to modify the request data globally or to customize the response.</p>
<p>To accomplish this, the SDK provides 2 function hooks:</p>
<ul>
<li>
<p><code>beforeSend</code> - triggered right before sending the <code>fetch</code> request, allowing you to inspect/modify the request config.
    <div class="highlight"><pre><span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

<span class="nx">pb</span><span class="p">.</span><span class="nx">beforeSend</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">url</span><span class="p">,</span> <span class="nx">options</span><span class="p">)</span> <span class="p">{</span>
    <span class="c1">// For list of the possible request options properties check</span>
    <span class="c1">// https://developer.mozilla.org/en-US/docs/Web/API/fetch#options</span>
    <span class="nx">options</span><span class="p">.</span><span class="nx">headers</span> <span class="o">=</span> <span class="nb">Object</span><span class="p">.</span><span class="nx">assign</span><span class="p">({},</span> <span class="nx">options</span><span class="p">.</span><span class="nx">headers</span><span class="p">,</span> <span class="p">{</span>
        <span class="s1">&#39;X-Custom-Header&#39;</span><span class="o">:</span> <span class="s1">&#39;example&#39;</span><span class="p">,</span>
    <span class="p">});</span>

    <span class="k">return</span> <span class="p">{</span> <span class="nx">url</span><span class="p">,</span> <span class="nx">options</span> <span class="p">};</span>
<span class="p">};</span>
</pre></div></p>
</li>
<li>
<p><code>afterSend</code> - triggered after successfully sending the <code>fetch</code> request, allowing you to inspect/modify the response object and its parsed data.
    <div class="highlight"><pre><span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

<span class="nx">pb</span><span class="p">.</span><span class="nx">afterSend</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">response</span><span class="p">,</span> <span class="nx">data</span><span class="p">)</span> <span class="p">{</span>
    <span class="c1">// do something with the response state</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">response</span><span class="p">.</span><span class="nx">status</span><span class="p">);</span>

    <span class="k">return</span> <span class="nb">Object</span><span class="p">.</span><span class="nx">assign</span><span class="p">(</span><span class="nx">data</span><span class="p">,</span> <span class="p">{</span>
        <span class="c1">// extend the data...</span>
        <span class="s2">&quot;additionalField&quot;</span><span class="o">:</span> <span class="mi">123</span><span class="p">,</span>
    <span class="p">});</span>
<span class="p">};</span>
</pre></div></p>
</li>
</ul>
<h3 id="ssr-integration">SSR integration<a class="headerlink" href="#ssr-integration" title="Permanent link"></a></h3>
<p>Unfortunately, <strong>there is no &ldquo;one size fits all&rdquo; solution</strong> because each framework handle SSR differently (<em>and even in a single framework there is more than one way of doing things</em>).</p>
<p>But in general, the idea is to use a cookie based flow:</p>
<ol>
<li>Create a new <code>PocketBase</code> instance for each server-side request</li>
<li>&ldquo;Load/Feed&rdquo; your <code>pb.authStore</code> with data from the request cookie</li>
<li>Perform your application server-side actions</li>
<li>Before returning the response to the client, update the cookie with the latest <code>pb.authStore</code> state</li>
</ol>
<p>All <a href="https://github.com/pocketbase/js-sdk/blob/master/src/stores/BaseAuthStore.ts"><code>BaseAuthStore</code></a> instances have 2 helper methods that
should make working with cookies a little bit easier:</p>
<div class="highlight"><pre><span class="c1">// update the store with the parsed data from the cookie string</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">loadFromCookie</span><span class="p">(</span><span class="s1">&#39;pb_auth=...&#39;</span><span class="p">);</span>

<span class="c1">// exports the store data as cookie, with option to extend the default SameSite, Secure, HttpOnly, Path and Expires attributes</span>
<span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">exportToCookie</span><span class="p">({</span> <span class="nx">httpOnly</span><span class="o">:</span> <span class="kc">false</span> <span class="p">});</span> <span class="c1">// Output: &#39;pb_auth=...&#39;</span>
</pre></div>

<p>Below you could find several examples:</p>
<details>
  <summary><strong>SvelteKit</strong></summary>

One way to integrate with SvelteKit SSR could be to create the PocketBase client in a [hook handle](https://kit.svelte.dev/docs/hooks#handle)
and pass it to the other server-side actions using the `event.locals`.

<div class="highlight"><pre><span class="c1">// src/hooks.server.js</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="cm">/** @type {import(&#39;@sveltejs/kit&#39;).Handle} */</span>
<span class="kr">export</span> <span class="nx">async</span> <span class="kd">function</span> <span class="nx">handle</span><span class="p">({</span> <span class="nx">event</span><span class="p">,</span> <span class="nx">resolve</span> <span class="p">})</span> <span class="p">{</span>
    <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

    <span class="c1">// load the store data from the request cookie string</span>
    <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">loadFromCookie</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">request</span><span class="p">.</span><span class="nx">headers</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="s1">&#39;cookie&#39;</span><span class="p">)</span> <span class="o">||</span> <span class="s1">&#39;&#39;</span><span class="p">);</span>

    <span class="k">try</span> <span class="p">{</span>
        <span class="c1">// get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)</span>
        <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">isValid</span> <span class="o">&amp;&amp;</span> <span class="nx">await</span> <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authRefresh</span><span class="p">();</span>
    <span class="p">}</span> <span class="k">catch</span> <span class="p">(</span><span class="nx">_</span><span class="p">)</span> <span class="p">{</span>
        <span class="c1">// clear the auth store on failed refresh</span>
        <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">clear</span><span class="p">();</span>
    <span class="p">}</span>

    <span class="kr">const</span> <span class="nx">response</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">resolve</span><span class="p">(</span><span class="nx">event</span><span class="p">);</span>

    <span class="c1">// send back the default &#39;pb_auth&#39; cookie to the client with the latest store state</span>
    <span class="nx">response</span><span class="p">.</span><span class="nx">headers</span><span class="p">.</span><span class="nx">append</span><span class="p">(</span><span class="s1">&#39;set-cookie&#39;</span><span class="p">,</span> <span class="nx">event</span><span class="p">.</span><span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">exportToCookie</span><span class="p">());</span>

    <span class="k">return</span> <span class="nx">response</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>

And then, in some of your server-side actions, you could directly access the previously created `event.locals.pb` instance:

<div class="highlight"><pre><span class="c1">// src/routes/login/+server.js</span>
<span class="cm">/**</span>
<span class="cm"> * Creates a `POST /login` server-side endpoint</span>
<span class="cm"> *</span>
<span class="cm"> * @type {import(&#39;./$types&#39;).RequestHandler}</span>
<span class="cm"> */</span>
<span class="kr">export</span> <span class="nx">async</span> <span class="kd">function</span> <span class="nx">POST</span><span class="p">({</span> <span class="nx">request</span><span class="p">,</span> <span class="nx">locals</span> <span class="p">})</span> <span class="p">{</span>
    <span class="kr">const</span> <span class="p">{</span> <span class="nx">email</span><span class="p">,</span> <span class="nx">password</span> <span class="p">}</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">request</span><span class="p">.</span><span class="nx">json</span><span class="p">();</span>

    <span class="kr">const</span> <span class="p">{</span> <span class="nx">token</span><span class="p">,</span> <span class="nx">record</span> <span class="p">}</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="nx">email</span><span class="p">,</span> <span class="nx">password</span><span class="p">);</span>

    <span class="k">return</span> <span class="k">new</span> <span class="nx">Response</span><span class="p">(</span><span class="s1">&#39;Success...&#39;</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>

For proper `locals.pb` type detection, you can also add `PocketBase` in your your global types definition:

<div class="highlight"><pre><span class="c1">// src/app.d.ts</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">declare</span> <span class="nx">global</span> <span class="p">{</span>
    <span class="kr">declare</span> <span class="nx">namespace</span> <span class="nx">App</span> <span class="p">{</span>
        <span class="kr">interface</span> <span class="nx">Locals</span> <span class="p">{</span>
            <span class="nx">pb</span>: <span class="kt">PocketBase</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>
</pre></div>
</details>

<details>
  <summary><strong>Astro</strong></summary>

To integrate with Astro SSR, you could create the PocketBase client in the [Middleware](https://docs.astro.build/en/guides/middleware) and pass it to the Astro components using the `Astro.locals`.

<div class="highlight"><pre><span class="c1">// src/middleware/index.ts</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">import</span> <span class="p">{</span> <span class="nx">defineMiddleware</span> <span class="p">}</span> <span class="nx">from</span> <span class="s1">&#39;astro/middleware&#39;</span><span class="p">;</span>

<span class="kr">export</span> <span class="kr">const</span> <span class="nx">onRequest</span> <span class="o">=</span> <span class="nx">defineMiddleware</span><span class="p">(</span><span class="nx">async</span> <span class="p">({</span> <span class="nx">locals</span><span class="p">,</span> <span class="nx">request</span> <span class="p">}</span><span class="o">:</span> <span class="nx">any</span><span class="p">,</span> <span class="nx">next</span><span class="o">:</span> <span class="p">()</span> <span class="o">=&gt;</span> <span class="nx">any</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

    <span class="c1">// load the store data from the request cookie string</span>
    <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">loadFromCookie</span><span class="p">(</span><span class="nx">request</span><span class="p">.</span><span class="nx">headers</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="s1">&#39;cookie&#39;</span><span class="p">)</span> <span class="o">||</span> <span class="s1">&#39;&#39;</span><span class="p">);</span>

    <span class="k">try</span> <span class="p">{</span>
        <span class="c1">// get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)</span>
        <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">isValid</span> <span class="o">&amp;&amp;</span> <span class="nx">await</span> <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authRefresh</span><span class="p">();</span>
    <span class="p">}</span> <span class="k">catch</span> <span class="p">(</span><span class="nx">_</span><span class="p">)</span> <span class="p">{</span>
        <span class="c1">// clear the auth store on failed refresh</span>
        <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">clear</span><span class="p">();</span>
    <span class="p">}</span>

    <span class="kr">const</span> <span class="nx">response</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">next</span><span class="p">();</span>

    <span class="c1">// send back the default &#39;pb_auth&#39; cookie to the client with the latest store state</span>
    <span class="nx">response</span><span class="p">.</span><span class="nx">headers</span><span class="p">.</span><span class="nx">append</span><span class="p">(</span><span class="s1">&#39;set-cookie&#39;</span><span class="p">,</span> <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">exportToCookie</span><span class="p">());</span>

    <span class="k">return</span> <span class="nx">response</span><span class="p">;</span>
<span class="p">});</span>
</pre></div>

And then, in your Astro file's component script, you could directly access the previously created `locals.pb` instance:

<div class="highlight"><pre><span class="c1">// src/pages/index.astro</span>
<span class="o">---</span>
<span class="kr">const</span> <span class="nx">locals</span> <span class="o">=</span> <span class="nx">Astro</span><span class="p">.</span><span class="nx">locals</span><span class="p">;</span>

<span class="kr">const</span> <span class="nx">userAuth</span> <span class="o">=</span> <span class="nx">async</span> <span class="p">()</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="kr">const</span> <span class="p">{</span> <span class="nx">token</span><span class="p">,</span> <span class="nx">record</span> <span class="p">}</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">locals</span><span class="p">.</span><span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span> <span class="s1">&#39;123456&#39;</span><span class="p">);</span>

    <span class="k">return</span> <span class="k">new</span> <span class="nx">Response</span><span class="p">(</span><span class="s1">&#39;Success...&#39;</span><span class="p">);</span>
<span class="p">};</span>
<span class="o">---</span>
</pre></div>

Although middleware functionality is available in both `SSG` and `SSR` projects, you would likely want to handle any sensitive data on the server side. Update your `output` configuration to `'server'`:

<div class="highlight"><pre>// astro.config.mjs
import { defineConfig } from &#39;astro/config&#39;;

export default defineConfig({
    output: &#39;server&#39;
});
</pre></div>
</details>

<details>
  <summary><strong>Nuxt 3</strong></summary>

One way to integrate with Nuxt 3 SSR could be to create the PocketBase client in a [nuxt plugin](https://v3.nuxtjs.org/guide/directory-structure/plugins)
and provide it as a helper to the `nuxtApp` instance:

<div class="highlight"><pre><span class="c1">// plugins/pocketbase.js</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span> <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">export</span> <span class="k">default</span> <span class="nx">defineNuxtPlugin</span><span class="p">(</span><span class="nx">async</span> <span class="p">()</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

  <span class="kr">const</span> <span class="nx">cookie</span> <span class="o">=</span> <span class="nx">useCookie</span><span class="p">(</span><span class="s1">&#39;pb_auth&#39;</span><span class="p">,</span> <span class="p">{</span>
    <span class="nx">path</span><span class="o">:</span>     <span class="s1">&#39;/&#39;</span><span class="p">,</span>
    <span class="nx">secure</span><span class="o">:</span>   <span class="kc">true</span><span class="p">,</span>
    <span class="nx">sameSite</span><span class="o">:</span> <span class="s1">&#39;strict&#39;</span><span class="p">,</span>
    <span class="nx">httpOnly</span><span class="o">:</span> <span class="kc">false</span><span class="p">,</span> <span class="c1">// change to &quot;true&quot; if you want only server-side access</span>
    <span class="nx">maxAge</span><span class="o">:</span>   <span class="mi">604800</span><span class="p">,</span>
  <span class="p">})</span>

  <span class="c1">// load the store data from the cookie value</span>
  <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">save</span><span class="p">(</span><span class="nx">cookie</span><span class="p">.</span><span class="nx">value</span><span class="o">?</span><span class="p">.</span><span class="nx">token</span><span class="p">,</span> <span class="nx">cookie</span><span class="p">.</span><span class="nx">value</span><span class="o">?</span><span class="p">.</span><span class="nx">model</span><span class="p">);</span>

  <span class="c1">// send back the default &#39;pb_auth&#39; cookie to the client with the latest store state</span>
  <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">onChange</span><span class="p">(()</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">cookie</span><span class="p">.</span><span class="nx">value</span> <span class="o">=</span> <span class="p">{</span>
      <span class="nx">token</span><span class="o">:</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">token</span><span class="p">,</span>
      <span class="nx">model</span><span class="o">:</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">model</span><span class="p">,</span>
    <span class="p">};</span>
  <span class="p">});</span>

  <span class="k">try</span> <span class="p">{</span>
      <span class="c1">// get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)</span>
      <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">isValid</span> <span class="o">&amp;&amp;</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authRefresh</span><span class="p">();</span>
  <span class="p">}</span> <span class="k">catch</span> <span class="p">(</span><span class="nx">_</span><span class="p">)</span> <span class="p">{</span>
      <span class="c1">// clear the auth store on failed refresh</span>
      <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">clear</span><span class="p">();</span>
  <span class="p">}</span>

  <span class="k">return</span> <span class="p">{</span>
    <span class="nx">provide</span><span class="o">:</span> <span class="p">{</span> <span class="nx">pb</span> <span class="p">}</span>
  <span class="p">}</span>
<span class="p">});</span>
</pre></div>

And then in your component you could access it like this:

<div class="highlight"><pre><span class="p">&lt;</span><span class="nt">template</span><span class="p">&gt;</span>
  <span class="p">&lt;</span><span class="nt">div</span><span class="p">&gt;</span>
    Show: {{ data }}
  <span class="p">&lt;/</span><span class="nt">div</span><span class="p">&gt;</span>
<span class="p">&lt;/</span><span class="nt">template</span><span class="p">&gt;</span>

<span class="p">&lt;</span><span class="nt">script</span> <span class="na">setup</span><span class="p">&gt;</span>
  <span class="kr">const</span> <span class="p">{</span> <span class="nx">data</span> <span class="p">}</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">useAsyncData</span><span class="p">(</span><span class="nx">async</span> <span class="p">(</span><span class="nx">nuxtApp</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="c1">// fetch and return all &quot;example&quot; records...</span>
    <span class="kr">const</span> <span class="nx">records</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">nuxtApp</span><span class="p">.</span><span class="nx">$pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getFullList</span><span class="p">();</span>

    <span class="k">return</span> <span class="nx">structuredClone</span><span class="p">(</span><span class="nx">records</span><span class="p">);</span>
  <span class="p">})</span>
<span class="p">&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
</pre></div>
</details>

<details>
  <summary><strong>Nuxt 2</strong></summary>

One way to integrate with Nuxt 2 SSR could be to create the PocketBase client in a [nuxt plugin](https://nuxtjs.org/docs/directory-structure/plugins#plugins-directory) and provide it as a helper to the `$root` context:

<div class="highlight"><pre><span class="c1">// plugins/pocketbase.js</span>
<span class="kr">import</span> <span class="nx">PocketBase</span> <span class="nx">from</span>  <span class="s1">&#39;pocketbase&#39;</span><span class="p">;</span>

<span class="kr">export</span> <span class="k">default</span> <span class="nx">async</span> <span class="p">(</span><span class="nx">ctx</span><span class="p">,</span> <span class="nx">inject</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="s1">&#39;http://127.0.0.1:8090&#39;</span><span class="p">);</span>

  <span class="c1">// load the store data from the request cookie string</span>
  <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">loadFromCookie</span><span class="p">(</span><span class="nx">ctx</span><span class="p">.</span><span class="nx">req</span><span class="o">?</span><span class="p">.</span><span class="nx">headers</span><span class="o">?</span><span class="p">.</span><span class="nx">cookie</span> <span class="o">||</span> <span class="s1">&#39;&#39;</span><span class="p">);</span>

  <span class="c1">// send back the default &#39;pb_auth&#39; cookie to the client with the latest store state</span>
  <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">onChange</span><span class="p">(()</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">ctx</span><span class="p">.</span><span class="nx">res</span><span class="o">?</span><span class="p">.</span><span class="nx">setHeader</span><span class="p">(</span><span class="s1">&#39;set-cookie&#39;</span><span class="p">,</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">exportToCookie</span><span class="p">());</span>
  <span class="p">});</span>

  <span class="k">try</span> <span class="p">{</span>
      <span class="c1">// get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)</span>
      <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">isValid</span> <span class="o">&amp;&amp;</span> <span class="nx">await</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;users&#39;</span><span class="p">).</span><span class="nx">authRefresh</span><span class="p">();</span>
  <span class="p">}</span> <span class="k">catch</span> <span class="p">(</span><span class="nx">_</span><span class="p">)</span> <span class="p">{</span>
      <span class="c1">// clear the auth store on failed refresh</span>
      <span class="nx">pb</span><span class="p">.</span><span class="nx">authStore</span><span class="p">.</span><span class="nx">clear</span><span class="p">();</span>
  <span class="p">}</span>

  <span class="nx">inject</span><span class="p">(</span><span class="s1">&#39;pocketbase&#39;</span><span class="p">,</span> <span class="nx">pb</span><span class="p">);</span>
<span class="p">};</span>
</pre></div>

And then in your component you could access it like this:

<div class="highlight"><pre><span class="p">&lt;</span><span class="nt">template</span><span class="p">&gt;</span>
  <span class="p">&lt;</span><span class="nt">div</span><span class="p">&gt;</span>
    Show: {{ items }}
  <span class="p">&lt;/</span><span class="nt">div</span><span class="p">&gt;</span>
<span class="p">&lt;/</span><span class="nt">template</span><span class="p">&gt;</span>

<span class="p">&lt;</span><span class="nt">script</span><span class="p">&gt;</span>
  <span class="kr">export</span> <span class="k">default</span> <span class="p">{</span>
    <span class="nx">async</span> <span class="nx">asyncData</span><span class="p">({</span> <span class="nx">$pocketbase</span> <span class="p">})</span> <span class="p">{</span>
      <span class="c1">// fetch and return all &quot;example&quot; records...</span>
      <span class="kr">const</span> <span class="nx">items</span> <span class="o">=</span> <span class="nx">await</span> <span class="nx">$pocketbase</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="s1">&#39;example&#39;</span><span class="p">).</span><span class="nx">getFullList</span><span class="p">();</span>

      <span class="k">return</span> <span class="p">{</span> <span class="nx">items</span> <span class="p">}</span>
    <span class="p">}</span>
  <span class="p">}</span>
<span class="p">&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
</pre></div>
</details>

<details>
  <summary><strong>Next.js</strong></summary>

Next.js doesn't seem to have a central place where you can read/modify the server request and response.
[There is support for middlewares](https://nextjs.org/docs/advanced-features/middleware),
but they are very limited and, at the time of writing, you can't pass data from a middleware to the `getServerSideProps` functions (https://github.com/vercel/next.js/discussions/31792).

One way to integrate with Next.js SSR could be to create a custom `PocketBase` instance in each of your `getServerSideProps`:

<div class="highlight"><pre>import PocketBase from &#39;pocketbase&#39;;

// you can place this helper in a separate file so that it can be reused
async function initPocketBase(req, res) {
  const pb = new PocketBase(&#39;http://127.0.0.1:8090&#39;);

  // load the store data from the request cookie string
  pb.authStore.loadFromCookie(req?.headers?.cookie || &#39;&#39;);

  // send back the default &#39;pb_auth&#39; cookie to the client with the latest store state
  pb.authStore.onChange(() =&gt; {
    res?.setHeader(&#39;set-cookie&#39;, pb.authStore.exportToCookie());
  });

  try {
      // get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)
      pb.authStore.isValid &amp;&amp; await pb.collection(&#39;users&#39;).authRefresh();
  } catch (_) {
      // clear the auth store on failed refresh
      pb.authStore.clear();
  }

  return pb
}

export async function getServerSideProps({ req, res }) {
  const pb = await initPocketBase(req, res)

  // fetch example records...
  const result = await pb.collection(&#39;example&#39;).getList(1, 30);

  return {
    props: {
      // ...
    },
  }
}

export default function Home() {
  return (
    &lt;div&gt;Hello world!&lt;/div&gt;
  )
}
</pre></div>
</details>

<h3 id="security">Security<a class="headerlink" href="#security" title="Permanent link"></a></h3>
<p>The most common frontend related vulnerability is XSS (and CSRF when dealing with cookies).
Fortunately, modern browsers can detect and mitigate most of this type of attacks if <a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP">Content Security Policy (CSP)</a> is provided.</p>
<p><strong>To prevent a malicious user or 3rd party script to steal your PocketBase auth token, it is recommended to configure a basic CSP for your application (either as <code>meta</code> tag or HTTP header).</strong></p>
<p>This is out of the scope of the SDK, but you could find more resources about CSP at:</p>
<ul>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP">https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP</a></li>
<li><a href="https://content-security-policy.com">https://content-security-policy.com</a></li>
</ul>
<p><strong>Depending on how and where you use the JS SDK, it is also recommended to use the helper <code>pb.filter(expr, params)</code> when constructing filter strings with untrested user input to avoid eventual string injection attacks (see <a href="#binding-filter-parameters">Binding filter parameters</a>).</strong></p>
<h2 id="definitions">Definitions<a class="headerlink" href="#definitions" title="Permanent link"></a></h2>
<h3 id="creating-new-client-instance">Creating new client instance<a class="headerlink" href="#creating-new-client-instance" title="Permanent link"></a></h3>
<div class="highlight"><pre><span class="kr">const</span> <span class="nx">pb</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">PocketBase</span><span class="p">(</span><span class="nx">baseURL</span> <span class="o">=</span> <span class="s1">&#39;/&#39;</span><span class="p">,</span> <span class="nx">authStore</span> <span class="o">=</span> <span class="nx">LocalAuthStore</span><span class="p">);</span>
</pre></div>

<h3 id="instance-methods">Instance methods<a class="headerlink" href="#instance-methods" title="Permanent link"></a></h3>
<blockquote>
<p>Each instance method returns the <code>PocketBase</code> instance allowing chaining.</p>
</blockquote>
<table>
<thead>
<tr>
<th align="left">Method</th>
<th align="left">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"><code>pb.send(path, sendOptions = {})</code></td>
<td align="left">Sends an api http request.</td>
</tr>
<tr>
<td align="left"><code>pb.autoCancellation(enable)</code></td>
<td align="left">Globally enable or disable auto cancellation for pending duplicated requests.</td>
</tr>
<tr>
<td align="left"><code>pb.cancelAllRequests()</code></td>
<td align="left">Cancels all pending requests.</td>
</tr>
<tr>
<td align="left"><code>pb.cancelRequest(cancelKey)</code></td>
<td align="left">Cancels single request by its cancellation token key.</td>
</tr>
<tr>
<td align="left"><code>pb.buildURL(path)</code></td>
<td align="left">Builds a full client url by safely concatenating the provided path.</td>
</tr>
</tbody>
</table>
<h3 id="services">Services<a class="headerlink" href="#services" title="Permanent link"></a></h3>
<blockquote>
<p>Each service call returns a <code>Promise</code> object with the API response.</p>
</blockquote>
<h5 id="recordservice">RecordService<a class="headerlink" href="#recordservice" title="Permanent link"></a></h5>
<h6 id="crud-handlers"><em>Crud handlers</em><a class="headerlink" href="#crud-handlers" title="Permanent link"></a></h6>
<div class="highlight"><pre><span class="c1">// Returns a paginated records list.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">getList</span><span class="p">(</span><span class="nx">page</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="nx">perPage</span> <span class="o">=</span> <span class="mi">30</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a list with all records batch fetched at once</span>
<span class="c1">// (by default 200 items per request; to change it set the `batch` param).</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">getFullList</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns the first found record matching the specified filter.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">getFirstListItem</span><span class="p">(</span><span class="nx">filter</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a single record by its id.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">getOne</span><span class="p">(</span><span class="nx">recordId</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Creates (aka. register) a new record.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">create</span><span class="p">(</span><span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Updates an existing record by its id.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">update</span><span class="p">(</span><span class="nx">recordId</span><span class="p">,</span> <span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Deletes a single record by its id.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="k">delete</span><span class="p">(</span><span class="nx">recordId</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<h6 id="realtime-handlers"><em>Realtime handlers</em><a class="headerlink" href="#realtime-handlers" title="Permanent link"></a></h6>
<div class="highlight"><pre><span class="c1">// Subscribe to realtime changes to the specified topic (&quot;*&quot; or recordId).</span>
<span class="c1">//</span>
<span class="c1">// It is safe to subscribe multiple times to the same topic.</span>
<span class="c1">//</span>
<span class="c1">// You can use the returned UnsubscribeFunc to remove a single registered subscription.</span>
<span class="c1">// If you want to remove all subscriptions related to the topic use unsubscribe(topic).</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">topic</span><span class="p">,</span> <span class="nx">callback</span><span class="p">);</span>

<span class="c1">// Unsubscribe from all registered subscriptions to the specified topic (&quot;*&quot; or recordId).</span>
<span class="c1">// If topic is not set, then it will remove all registered collection subscriptions.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">unsubscribe</span><span class="p">([</span><span class="nx">topic</span><span class="p">]);</span>
</pre></div>

<h6 id="auth-handlers"><em>Auth handlers</em><a class="headerlink" href="#auth-handlers" title="Permanent link"></a></h6>
<blockquote>
<p>Available only for &ldquo;auth&rdquo; type collections.</p>
</blockquote>
<div class="highlight"><pre><span class="c1">// Returns all available application auth methods.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">listAuthMethods</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Authenticates a record with their username/email and password.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="nx">usernameOrEmail</span><span class="p">,</span> <span class="nx">password</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Authenticates a record with OAuth2 provider without custom redirects, deeplinks or even page reload.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">authWithOAuth2</span><span class="p">(</span><span class="nx">authConfig</span><span class="p">);</span>

<span class="c1">// Authenticates a record with OAuth2 code.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">authWithOAuth2Code</span><span class="p">(</span><span class="nx">provider</span><span class="p">,</span> <span class="nx">code</span><span class="p">,</span> <span class="nx">codeVerifier</span><span class="p">,</span> <span class="nx">redirectUrl</span><span class="p">,</span> <span class="nx">createData</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Refreshes the current authenticated record model and auth token.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">authRefresh</span><span class="p">(</span><span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Sends a user password reset email.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">requestPasswordReset</span><span class="p">(</span><span class="nx">email</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Confirms a record password reset request.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">confirmPasswordReset</span><span class="p">(</span><span class="nx">resetToken</span><span class="p">,</span> <span class="nx">newPassword</span><span class="p">,</span> <span class="nx">newPasswordConfirm</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Sends a record verification email request.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">requestVerification</span><span class="p">(</span><span class="nx">email</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Confirms a record email verification request.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">confirmVerification</span><span class="p">(</span><span class="nx">verificationToken</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Sends a record email change request to the provider email.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">requestEmailChange</span><span class="p">(</span><span class="nx">newEmail</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Confirms record new email address.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">confirmEmailChange</span><span class="p">(</span><span class="nx">emailChangeToken</span><span class="p">,</span> <span class="nx">userPassword</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Lists all linked external auth providers for the specified record.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">listExternalAuths</span><span class="p">(</span><span class="nx">recordId</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Unlinks a single external auth provider relation from the specified record.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collection</span><span class="p">(</span><span class="nx">collectionIdOrName</span><span class="p">).</span><span class="nx">unlinkExternalAuth</span><span class="p">(</span><span class="nx">recordId</span><span class="p">,</span> <span class="nx">provider</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="fileservice">FileService<a class="headerlink" href="#fileservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Builds and returns an absolute record file url for the provided filename.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">files</span><span class="p">.</span><span class="nx">getUrl</span><span class="p">(</span><span class="nx">record</span><span class="p">,</span> <span class="nx">filename</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Requests a new private file access token for the current auth model (admin or record).</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">files</span><span class="p">.</span><span class="nx">getToken</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="adminservice">AdminService<a class="headerlink" href="#adminservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Authenticates an admin account by its email and password.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">authWithPassword</span><span class="p">(</span><span class="nx">email</span><span class="p">,</span> <span class="nx">password</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Refreshes the current admin authenticated model and token.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">authRefresh</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Sends an admin password reset email.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">requestPasswordReset</span><span class="p">(</span><span class="nx">email</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Confirms an admin password reset request.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">confirmPasswordReset</span><span class="p">(</span><span class="nx">resetToken</span><span class="p">,</span> <span class="nx">newPassword</span><span class="p">,</span> <span class="nx">newPasswordConfirm</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a paginated admins list.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">getList</span><span class="p">(</span><span class="nx">page</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="nx">perPage</span> <span class="o">=</span> <span class="mi">30</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a list with all admins batch fetched at once</span>
<span class="c1">// (by default 200 items per request; to change it set the `batch` query param).</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">getFullList</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns the first found admin matching the specified filter.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">getFirstListItem</span><span class="p">(</span><span class="nx">filter</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a single admin by their id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">getOne</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Creates a new admin.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">create</span><span class="p">(</span><span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Updates an existing admin by their id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="nx">update</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Deletes a single admin by their id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">admins</span><span class="p">.</span><span class="k">delete</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="collectionservice">CollectionService<a class="headerlink" href="#collectionservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Returns a paginated collections list.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">getList</span><span class="p">(</span><span class="nx">page</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="nx">perPage</span> <span class="o">=</span> <span class="mi">30</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a list with all collections batch fetched at once</span>
<span class="c1">// (by default 200 items per request; to change it set the `batch` query param).</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">getFullList</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns the first found collection matching the specified filter.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">getFirstListItem</span><span class="p">(</span><span class="nx">filter</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a single collection by its id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">getOne</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Creates (aka. register) a new collection.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">create</span><span class="p">(</span><span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Updates an existing collection by its id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="nx">update</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Deletes a single collection by its id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="k">delete</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Imports the provided collections.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">collections</span><span class="p">.</span><span class="kr">import</span><span class="p">(</span><span class="nx">collections</span><span class="p">,</span> <span class="nx">deleteMissing</span> <span class="o">=</span> <span class="kc">false</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="logservice">LogService<a class="headerlink" href="#logservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Returns a paginated log requests list.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">logs</span><span class="p">.</span><span class="nx">getRequestsList</span><span class="p">(</span><span class="nx">page</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="nx">perPage</span> <span class="o">=</span> <span class="mi">30</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Returns a single log request by its id.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">logs</span><span class="p">.</span><span class="nx">getRequest</span><span class="p">(</span><span class="nx">id</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="settingsservice">SettingsService<a class="headerlink" href="#settingsservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Returns a map with all available app settings.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">settings</span><span class="p">.</span><span class="nx">getAll</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Bulk updates app settings.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">settings</span><span class="p">.</span><span class="nx">update</span><span class="p">(</span><span class="nx">bodyParams</span> <span class="o">=</span> <span class="p">{},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Performs a S3 storage connection test.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">settings</span><span class="p">.</span><span class="nx">testS3</span><span class="p">(</span><span class="nx">filesystem</span> <span class="o">=</span> <span class="s2">&quot;storage&quot;</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Sends a test email (verification, password-reset, email-change).</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">settings</span><span class="p">.</span><span class="nx">testEmail</span><span class="p">(</span><span class="nx">toEmail</span><span class="p">,</span> <span class="nx">template</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Generates a new Apple OAuth2 client secret.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">settings</span><span class="p">.</span><span class="nx">generateAppleClientSecret</span><span class="p">(</span><span class="nx">clientId</span><span class="p">,</span> <span class="nx">teamId</span><span class="p">,</span> <span class="nx">keyId</span><span class="p">,</span> <span class="nx">privateKey</span><span class="p">,</span> <span class="nx">duration</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<hr />
<h5 id="realtimeservice">RealtimeService<a class="headerlink" href="#realtimeservice" title="Permanent link"></a></h5>
<blockquote>
<p>This service is usually used with custom realtime actions.
For records realtime subscriptions you can use the subscribe/unsubscribe
methods available in the <code>pb.collection()</code> RecordService.</p>
</blockquote>
<div class="highlight"><pre><span class="c1">// Initialize the realtime connection (if not already) and register the subscription listener.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">realtime</span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">topic</span><span class="p">,</span> <span class="nx">callback</span><span class="p">);</span>

<span class="c1">// Unsubscribe from all subscription listeners with the specified topic.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">realtime</span><span class="p">.</span><span class="nx">unsubscribe</span><span class="p">(</span><span class="nx">topic</span><span class="o">?</span><span class="p">);</span>

<span class="c1">// Unsubscribe from all subscription listeners starting with the specified topic prefix.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">realtime</span><span class="p">.</span><span class="nx">unsubscribeByPrefix</span><span class="p">(</span><span class="nx">topicPrefix</span><span class="p">);</span>

<span class="c1">// Unsubscribe from all subscriptions matching the specified topic and listener function.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">realtime</span><span class="p">.</span><span class="nx">unsubscribeByTopicAndListener</span><span class="p">(</span><span class="nx">topic</span><span class="p">,</span> <span class="nx">callback</span><span class="p">);</span>
</pre></div>

<hr />
<h5 id="backupservice">BackupService<a class="headerlink" href="#backupservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Returns list with all available backup files.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="nx">getFullList</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Initializes a new backup.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="nx">create</span><span class="p">(</span><span class="nx">basename</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Upload an existing app data backup.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="nx">upload</span><span class="p">({</span> <span class="nx">file</span><span class="o">:</span> <span class="nx">File</span><span class="o">/</span><span class="nx">Blob</span> <span class="p">},</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Deletes a single backup by its name.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="k">delete</span><span class="p">(</span><span class="nx">key</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Initializes an app data restore from an existing backup.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="nx">restore</span><span class="p">(</span><span class="nx">key</span><span class="p">,</span> <span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>

<span class="c1">// Builds a download url for a single existing backup using an</span>
<span class="c1">// admin file token and the backup file key.</span>
<span class="err">🔐</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">backups</span><span class="p">.</span><span class="nx">getDownloadUrl</span><span class="p">(</span><span class="nx">token</span><span class="p">,</span> <span class="nx">key</span><span class="p">);</span>
</pre></div>

<hr />
<h5 id="healthservice">HealthService<a class="headerlink" href="#healthservice" title="Permanent link"></a></h5>
<div class="highlight"><pre><span class="c1">// Checks the health status of the api.</span>
<span class="err">🔓</span> <span class="nx">pb</span><span class="p">.</span><span class="nx">health</span><span class="p">.</span><span class="nx">check</span><span class="p">(</span><span class="nx">options</span> <span class="o">=</span> <span class="p">{});</span>
</pre></div>

<h2 id="development">Development<a class="headerlink" href="#development" title="Permanent link"></a></h2>
<div class="highlight"><pre><span class="c1"># run unit tests</span>
npm <span class="nb">test</span>

<span class="c1"># build and minify for production</span>
npm run build
</pre></div></article></body></html>
