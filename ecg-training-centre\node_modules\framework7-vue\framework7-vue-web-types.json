{"$schema": "http://json-schema.org/draft-04/schema#", "framework": "vue", "name": "framework7-vue", "version": "8.3.4", "contributions": {"html": {"types-syntax": "typescript", "tags": [{"name": "F7Accordion", "source": {"symbol": "F7Accordion"}, "attributes": [{"name": "accordionOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7AccordionC<PERSON>nt", "source": {"symbol": "F7AccordionC<PERSON>nt"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7AccordionItem", "source": {"symbol": "F7AccordionItem"}, "attributes": [{"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "accordion:beforeopen"}, {"name": "accordion:open"}, {"name": "accordion:opened"}, {"name": "accordion:beforeclose"}, {"name": "accordion:close"}, {"name": "accordion:closed"}]}, {"name": "F7AccordionToggle", "source": {"symbol": "F7AccordionToggle"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Actions", "source": {"symbol": "F7Actions"}, "attributes": [{"name": "tabletFullscreen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "grid", "value": {"kind": "expression", "type": "boolean"}}, {"name": "target", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "convertToPopover", "value": {"kind": "expression", "type": "boolean"}}, {"name": "forceToPopover", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeByOutsideClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeOnEscape", "value": {"kind": "expression", "type": "boolean"}}, {"name": "containerEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "actions:open"}, {"name": "actions:opened"}, {"name": "actions:close"}, {"name": "actions:closed"}, {"name": "update:opened"}]}, {"name": "F7ActionsButton", "source": {"symbol": "F7ActionsButton"}, "attributes": [{"name": "strong", "value": {"kind": "expression", "type": "boolean"}}, {"name": "close", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}]}, {"name": "F7ActionsGroup", "source": {"symbol": "F7ActionsGroup"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7ActionsLabel", "source": {"symbol": "F7ActionsLabel"}, "attributes": [{"name": "strong", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}]}, {"name": "F7App", "source": {"symbol": "F7App"}, "attributes": [{"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "theme", "value": {"kind": "expression", "type": "string"}}, {"name": "routes", "value": {"kind": "expression", "type": "array"}}, {"name": "store", "value": {"kind": "expression", "type": "object"}}, {"name": "darkMode", "value": {"kind": "expression", "type": ["boolean", "string"]}, "default": false}, {"name": "colors", "value": {"kind": "expression", "type": "object"}}, {"name": "lazyModulesPath", "value": {"kind": "expression", "type": "string"}}, {"name": "initOnDeviceReady", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosTranslucentBars", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosTranslucentModals", "value": {"kind": "expression", "type": "boolean"}}, {"name": "userAgent", "value": {"kind": "expression", "type": "string"}}, {"name": "url", "value": {"kind": "expression", "type": "string"}}, {"name": "accordion", "value": {"kind": "expression", "type": "object"}}, {"name": "actions", "value": {"kind": "expression", "type": "object"}}, {"name": "areaChart", "value": {"kind": "expression", "type": "object"}}, {"name": "autocomplete", "value": {"kind": "expression", "type": "object"}}, {"name": "calendar", "value": {"kind": "expression", "type": "object"}}, {"name": "card", "value": {"kind": "expression", "type": "object"}}, {"name": "colorPicker", "value": {"kind": "expression", "type": "object"}}, {"name": "dialog", "value": {"kind": "expression", "type": "object"}}, {"name": "form", "value": {"kind": "expression", "type": "object"}}, {"name": "gauge", "value": {"kind": "expression", "type": "object"}}, {"name": "infiniteScroll", "value": {"kind": "expression", "type": "object"}}, {"name": "input", "value": {"kind": "expression", "type": "object"}}, {"name": "listIndex", "value": {"kind": "expression", "type": "object"}}, {"name": "loginScreen", "value": {"kind": "expression", "type": "object"}}, {"name": "messagebar", "value": {"kind": "expression", "type": "object"}}, {"name": "messages", "value": {"kind": "expression", "type": "object"}}, {"name": "navbar", "value": {"kind": "expression", "type": "object"}}, {"name": "notification", "value": {"kind": "expression", "type": "object"}}, {"name": "panel", "value": {"kind": "expression", "type": "object"}}, {"name": "photoBrowser", "value": {"kind": "expression", "type": "object"}}, {"name": "picker", "value": {"kind": "expression", "type": "object"}}, {"name": "<PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "object"}}, {"name": "popover", "value": {"kind": "expression", "type": "object"}}, {"name": "popup", "value": {"kind": "expression", "type": "object"}}, {"name": "range", "value": {"kind": "expression", "type": "object"}}, {"name": "searchbar", "value": {"kind": "expression", "type": "object"}}, {"name": "sheet", "value": {"kind": "expression", "type": "object"}}, {"name": "smartSelect", "value": {"kind": "expression", "type": "object"}}, {"name": "sortable", "value": {"kind": "expression", "type": "object"}}, {"name": "statusbar", "value": {"kind": "expression", "type": "object"}}, {"name": "stepper", "value": {"kind": "expression", "type": "object"}}, {"name": "swipeout", "value": {"kind": "expression", "type": "object"}}, {"name": "textEditor", "value": {"kind": "expression", "type": "object"}}, {"name": "toast", "value": {"kind": "expression", "type": "object"}}, {"name": "toolbar", "value": {"kind": "expression", "type": "object"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "object"}}, {"name": "view", "value": {"kind": "expression", "type": "object"}}, {"name": "virtualList", "value": {"kind": "expression", "type": "object"}}, {"name": "clicks", "value": {"kind": "expression", "type": "object"}}, {"name": "serviceWorker", "value": {"kind": "expression", "type": "object"}}, {"name": "touch", "value": {"kind": "expression", "type": "object"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7AreaChart", "source": {"symbol": "F7AreaChart"}, "attributes": [{"name": "lineChart", "value": {"kind": "expression", "type": "boolean"}}, {"name": "datasets", "value": {"kind": "expression", "type": "array"}}, {"name": "axis", "value": {"kind": "expression", "type": "boolean"}}, {"name": "axisLabels", "value": {"kind": "expression", "type": "array"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "boolean"}}, {"name": "legend", "value": {"kind": "expression", "type": "boolean"}}, {"name": "toggleDatasets", "value": {"kind": "expression", "type": "boolean"}}, {"name": "width", "value": {"kind": "expression", "type": "number"}, "default": 640}, {"name": "height", "value": {"kind": "expression", "type": "number"}, "default": 320}, {"name": "maxAxis<PERSON>abels", "value": {"kind": "expression", "type": "number"}, "default": 8}, {"name": "formatAxisLabel", "value": {"kind": "expression", "type": "function"}}, {"name": "formatLegendLabel", "value": {"kind": "expression", "type": "function"}}, {"name": "formatTooltip", "value": {"kind": "expression", "type": "function"}}, {"name": "formatTooltipAxisLabel", "value": {"kind": "expression", "type": "function"}}, {"name": "formatTooltipTotal", "value": {"kind": "expression", "type": "function"}}, {"name": "formatTooltipDataset", "value": {"kind": "expression", "type": "function"}}], "events": [{"name": "select"}]}, {"name": "F7Badge", "source": {"symbol": "F7Badge"}, "attributes": [{"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Block", "source": {"symbol": "F7Block"}, "attributes": [{"name": "inset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "insetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "insetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strong", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabs", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tab", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "tab:hide"}, {"name": "tab:show"}]}, {"name": "F7BlockFooter", "source": {"symbol": "F7BlockFooter"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7BlockHeader", "source": {"symbol": "F7BlockHeader"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7BlockTitle", "source": {"symbol": "F7BlockTitle"}, "attributes": [{"name": "large", "value": {"kind": "expression", "type": "boolean"}}, {"name": "medium", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Breadcrumbs", "source": {"symbol": "F7Breadcrumbs"}, "attributes": []}, {"name": "F7BreadcrumbsCollapsed", "source": {"symbol": "F7BreadcrumbsCollapsed"}, "attributes": []}, {"name": "F7BreadcrumbsItem", "source": {"symbol": "F7BreadcrumbsItem"}, "attributes": [{"name": "active", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7BreadcrumbsSeparator", "source": {"symbol": "F7BreadcrumbsSeparator"}, "attributes": []}, {"name": "F7Button", "source": {"symbol": "F7Button"}, "attributes": [{"name": "text", "value": {"kind": "expression", "type": "string"}}, {"name": "tabLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "tabLinkActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "type", "value": {"kind": "expression", "type": "string"}}, {"name": "href", "value": {"kind": "expression", "type": ["string", "boolean"]}, "default": "#"}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "round", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fill", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fillMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fillIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tonal", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tonalMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tonalIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "large", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "small", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raised", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "active", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "preloader", "value": {"kind": "expression", "type": "boolean"}}, {"name": "preloaderSize", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "preloaderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "loading", "value": {"kind": "expression", "type": "boolean"}}, {"name": "icon", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMaterial", "value": {"kind": "expression", "type": "string"}}, {"name": "iconF7", "value": {"kind": "expression", "type": "string"}}, {"name": "iconIos", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMd", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "iconSize", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadgeColor", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchbarEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarClear", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardPreventOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "back", "value": {"kind": "expression", "type": "boolean"}}, {"name": "external", "value": {"kind": "expression", "type": "boolean"}}, {"name": "force", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadCurrent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadAll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPrevious", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routeTabId", "value": {"kind": "expression", "type": "string"}}, {"name": "view", "value": {"kind": "expression", "type": "string"}}, {"name": "routeProps", "value": {"kind": "expression", "type": "object"}}, {"name": "preventRouter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "openIn", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7Card", "source": {"symbol": "F7Card"}, "slots": [{"name": "default"}, {"name": "content"}, {"name": "header"}, {"name": "footer"}], "attributes": [{"name": "title", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "content", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "footer", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "raised", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "headerDivider", "value": {"kind": "expression", "type": "boolean"}}, {"name": "footerDiv<PERSON>", "value": {"kind": "expression", "type": "boolean"}}, {"name": "expandable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "expandableAnimateWidth", "value": {"kind": "expression", "type": "boolean"}}, {"name": "expandableOpened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideNavbarOnOpen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideToolbarOnOpen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideStatusbarOnOpen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "scrollableEl", "value": {"kind": "expression", "type": "string"}}, {"name": "swipeToClose", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": "string"}}, {"name": "padding", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "card:beforeopen"}, {"name": "card:open"}, {"name": "card:opened"}, {"name": "card:close"}, {"name": "card:closed"}, {"name": "update:expandableOpened"}]}, {"name": "F7CardContent", "source": {"symbol": "F7CardContent"}, "attributes": [{"name": "padding", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7<PERSON>ard<PERSON>ooter", "source": {"symbol": "F7<PERSON>ard<PERSON>ooter"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7CardHeader", "source": {"symbol": "F7CardHeader"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Checkbox", "source": {"symbol": "F7Checkbox"}, "attributes": [{"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "indeterminate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "value", "value": {"kind": "expression", "type": ["number", "string", "boolean"]}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "update:checked"}, {"name": "change"}]}, {"name": "F7Chip", "source": {"symbol": "F7Chip"}, "slots": [{"name": "text"}, {"name": "default"}, {"name": "media"}], "attributes": [{"name": "media", "value": {"kind": "expression", "type": "string"}}, {"name": "text", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "deleteable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediaBgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "mediaTextColor", "value": {"kind": "expression", "type": "string"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "icon", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMaterial", "value": {"kind": "expression", "type": "string"}}, {"name": "iconF7", "value": {"kind": "expression", "type": "string"}}, {"name": "iconIos", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMd", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "iconSize", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadgeColor", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "delete"}]}, {"name": "F7Fab", "source": {"symbol": "F7Fab"}, "slots": [{"name": "default"}, {"name": "link"}, {"name": "root"}, {"name": "text"}], "attributes": [{"name": "morphTo", "value": {"kind": "expression", "type": "string"}}, {"name": "href", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "text", "value": {"kind": "expression", "type": "string"}}, {"name": "position", "value": {"kind": "expression", "type": "string"}, "default": "right-bottom"}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}]}, {"name": "F7FabBackdrop", "source": {"symbol": "F7FabBackdrop"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7FabButton", "source": {"symbol": "F7FabButton"}, "attributes": [{"name": "fabClose", "value": {"kind": "expression", "type": "boolean"}}, {"name": "label", "value": {"kind": "expression", "type": "string"}}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}]}, {"name": "F7FabButtons", "source": {"symbol": "F7FabButtons"}, "attributes": [{"name": "position", "value": {"kind": "expression", "type": "string"}, "default": "top"}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Gauge", "source": {"symbol": "F7Gauge"}, "attributes": [{"name": "type", "value": {"kind": "expression", "type": "string"}, "default": "circle"}, {"name": "value", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 0}, {"name": "size", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 200}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}, "default": "transparent"}, {"name": "borderBgColor", "value": {"kind": "expression", "type": "string"}, "default": "#eeeeee"}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}, "default": "#000000"}, {"name": "borderWidth", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 10}, {"name": "valueText", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "valueTextColor", "value": {"kind": "expression", "type": "string"}, "default": "#000000"}, {"name": "valueFontSize", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 31}, {"name": "valueFontWeight", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 500}, {"name": "labelText", "value": {"kind": "expression", "type": "string"}}, {"name": "labelTextColor", "value": {"kind": "expression", "type": "string"}, "default": "#888888"}, {"name": "labelFontSize", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 14}, {"name": "labelFontWeight", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 400}]}, {"name": "F7Icon", "source": {"symbol": "F7Icon"}, "attributes": [{"name": "material", "value": {"kind": "expression", "type": "string"}}, {"name": "f7", "value": {"kind": "expression", "type": "string"}}, {"name": "icon", "value": {"kind": "expression", "type": "string"}}, {"name": "ios", "value": {"kind": "expression", "type": "string"}}, {"name": "md", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "size", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Input", "source": {"symbol": "F7Input"}, "attributes": [{"name": "type", "value": {"kind": "expression", "type": "string"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array", "date", "object"]}}, {"name": "inputmode", "value": {"kind": "expression", "type": "string"}}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}}, {"name": "inputId", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "size", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "accept", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "autocomplete", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autocorrect", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autocapitalize", "value": {"kind": "expression", "type": ["string"]}}, {"name": "spellcheck", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autofocus", "value": {"kind": "expression", "type": "boolean"}}, {"name": "autosave", "value": {"kind": "expression", "type": "string"}}, {"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "max", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "min", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "step", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "maxlength", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "minlength", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "multiple", "value": {"kind": "expression", "type": "boolean"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "required", "value": {"kind": "expression", "type": "boolean"}}, {"name": "inputStyle", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "pattern", "value": {"kind": "expression", "type": "string"}}, {"name": "validate", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "validateOnBlur", "value": {"kind": "expression", "type": "boolean"}}, {"name": "onValidate", "value": {"kind": "expression", "type": "function"}}, {"name": "tabindex", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "resizable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "clearButton", "value": {"kind": "expression", "type": "boolean"}}, {"name": "noFormStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "noStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "errorMessage", "value": {"kind": "expression", "type": "string"}}, {"name": "errorMessageForce", "value": {"kind": "expression", "type": "boolean"}}, {"name": "info", "value": {"kind": "expression", "type": "string"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "wrap", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "dropdown", "value": {"kind": "expression", "type": ["string", "boolean"]}, "default": "auto"}, {"name": "calendarParams", "value": {"kind": "expression", "type": "object"}}, {"name": "colorPickerParams", "value": {"kind": "expression", "type": "object"}}, {"name": "textEditorParams", "value": {"kind": "expression", "type": "object"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "input"}, {"name": "focus"}, {"name": "blur"}, {"name": "change"}, {"name": "textarea:resize"}, {"name": "input:note<PERSON>y"}, {"name": "input:empty"}, {"name": "input:clear"}, {"name": "texteditor:change"}, {"name": "calendar:change"}, {"name": "colorpicker:change"}, {"name": "update:value"}]}, {"name": "F7Link", "source": {"symbol": "F7Link"}, "attributes": [{"name": "noLinkClass", "value": {"kind": "expression", "type": "boolean"}}, {"name": "text", "value": {"kind": "expression", "type": "string"}}, {"name": "tabLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "tabLinkActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabbar<PERSON>abel", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iconOnly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "badge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "badgeColor", "value": {"kind": "expression", "type": ["string"]}}, {"name": "href", "value": {"kind": "expression", "type": ["string", "boolean"]}, "default": "#"}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "smartSelect", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smartSelectParams", "value": {"kind": "expression", "type": "object"}}, {"name": "icon", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMaterial", "value": {"kind": "expression", "type": "string"}}, {"name": "iconF7", "value": {"kind": "expression", "type": "string"}}, {"name": "iconIos", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMd", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "iconSize", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadgeColor", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchbarEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarClear", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardPreventOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "back", "value": {"kind": "expression", "type": "boolean"}}, {"name": "external", "value": {"kind": "expression", "type": "boolean"}}, {"name": "force", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadCurrent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadAll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPrevious", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routeTabId", "value": {"kind": "expression", "type": "string"}}, {"name": "view", "value": {"kind": "expression", "type": "string"}}, {"name": "routeProps", "value": {"kind": "expression", "type": "object"}}, {"name": "preventRouter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "openIn", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7List", "source": {"symbol": "F7List"}, "slots": [{"name": "before-list"}, {"name": "after-list"}, {"name": "list"}], "attributes": [{"name": "inset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "insetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "insetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xsmallInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediumInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInset", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInsetIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xlargeInsetMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strong", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outlineMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "dividers", "value": {"kind": "expression", "type": "boolean"}}, {"name": "dividersIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "dividersMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediaList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableTapHold", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableEnabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableMoveElements", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "contactsList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "simpleList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "linksList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "menuList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "no<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}}, {"name": "chevronCenter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tab", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "form", "value": {"kind": "expression", "type": "boolean"}}, {"name": "formStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "virtualList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "virtualListParams", "value": {"kind": "expression", "type": "object"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "submit"}, {"name": "sortable:enable"}, {"name": "sortable:disable"}, {"name": "sortable:sort"}, {"name": "sortable:move"}, {"name": "virtual:itembeforeinsert"}, {"name": "virtual:beforeclear"}, {"name": "virtual:itemsbeforeinsert"}, {"name": "virtual:itemsafterinsert"}, {"name": "tab:hide"}, {"name": "tab:show"}]}, {"name": "F7ListButton", "source": {"symbol": "F7ListButton"}, "attributes": [{"name": "title", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "text", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "tabLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "tabLinkActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "link", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "href", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchbarEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarClear", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardPreventOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "back", "value": {"kind": "expression", "type": "boolean"}}, {"name": "external", "value": {"kind": "expression", "type": "boolean"}}, {"name": "force", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadCurrent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadAll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPrevious", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routeTabId", "value": {"kind": "expression", "type": "string"}}, {"name": "view", "value": {"kind": "expression", "type": "string"}}, {"name": "routeProps", "value": {"kind": "expression", "type": "object"}}, {"name": "preventRouter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "openIn", "value": {"kind": "expression", "type": "string"}}], "events": [{"name": "click"}]}, {"name": "F7ListGroup", "source": {"symbol": "F7ListGroup"}, "attributes": [{"name": "mediaList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "simpleList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableTapHold", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableMoveElements", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7ListIndex", "source": {"symbol": "F7ListIndex"}, "attributes": [{"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "listEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "indexes", "value": {"kind": "expression", "type": ["string", "array"]}, "default": "auto"}, {"name": "scrollList", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "label", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "iosItemHeight", "value": {"kind": "expression", "type": "number"}, "default": 14}, {"name": "mdItemHeight", "value": {"kind": "expression", "type": "number"}, "default": 14}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "listindex:select"}]}, {"name": "F7ListInput", "source": {"symbol": "F7ListInput"}, "slots": [{"name": "default"}, {"name": "info"}, {"name": "error-message"}, {"name": "label"}, {"name": "input"}, {"name": "root-start"}, {"name": "root"}, {"name": "root-end"}, {"name": "content-start"}, {"name": "content"}, {"name": "content-end"}, {"name": "inner-start"}, {"name": "inner"}, {"name": "inner-end"}, {"name": "media"}], "attributes": [{"name": "sortable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "media", "value": {"kind": "expression", "type": "string"}}, {"name": "dropdown", "value": {"kind": "expression", "type": ["string", "boolean"]}, "default": "auto"}, {"name": "wrap", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "input", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "type", "value": {"kind": "expression", "type": "string"}, "default": "text"}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array", "date", "object"]}}, {"name": "inputmode", "value": {"kind": "expression", "type": "string"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "required", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}}, {"name": "inputId", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "size", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "accept", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "autocomplete", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autocorrect", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autocapitalize", "value": {"kind": "expression", "type": ["string"]}}, {"name": "spellcheck", "value": {"kind": "expression", "type": ["string"]}}, {"name": "autofocus", "value": {"kind": "expression", "type": "boolean"}}, {"name": "autosave", "value": {"kind": "expression", "type": "string"}}, {"name": "max", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "min", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "step", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "maxlength", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "minlength", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "multiple", "value": {"kind": "expression", "type": "boolean"}}, {"name": "inputStyle", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "pattern", "value": {"kind": "expression", "type": "string"}}, {"name": "validate", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "validateOnBlur", "value": {"kind": "expression", "type": "boolean"}}, {"name": "onValidate", "value": {"kind": "expression", "type": "function"}}, {"name": "tabindex", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "resizable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "clearButton", "value": {"kind": "expression", "type": "boolean"}}, {"name": "noFormStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "noStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreStoreData", "value": {"kind": "expression", "type": "boolean"}}, {"name": "errorMessage", "value": {"kind": "expression", "type": "string"}}, {"name": "errorMessageForce", "value": {"kind": "expression", "type": "boolean"}}, {"name": "info", "value": {"kind": "expression", "type": "string"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "label", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "floatingLabel", "value": {"kind": "expression", "type": "boolean"}}, {"name": "calendarParams", "value": {"kind": "expression", "type": "object"}}, {"name": "colorPickerParams", "value": {"kind": "expression", "type": "object"}}, {"name": "textEditorParams", "value": {"kind": "expression", "type": "object"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "textarea:resize"}, {"name": "input:note<PERSON>y"}, {"name": "input:empty"}, {"name": "input:clear"}, {"name": "texteditor:change"}, {"name": "calendar:change"}, {"name": "colorpicker:change"}, {"name": "change"}, {"name": "focus"}, {"name": "blur"}, {"name": "input"}, {"name": "update:value"}]}, {"name": "F7ListItem", "source": {"symbol": "F7ListItem"}, "slots": [{"name": "root-start"}, {"name": "root"}, {"name": "root-end"}, {"name": "content-start"}, {"name": "content"}, {"name": "content-end"}, {"name": "inner-start"}, {"name": "default"}, {"name": "inner"}, {"name": "inner-end"}, {"name": "media"}, {"name": "before-title"}, {"name": "title"}, {"name": "after-title"}, {"name": "subtitle"}, {"name": "text"}, {"name": "header"}, {"name": "footer"}, {"name": "after-start"}, {"name": "after"}, {"name": "after-end"}], "attributes": [{"name": "title", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "text", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "media", "value": {"kind": "expression", "type": "string"}}, {"name": "subtitle", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "header", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "footer", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "link", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "target", "value": {"kind": "expression", "type": "string"}}, {"name": "tabLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "tabLinkActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "selected", "value": {"kind": "expression", "type": "boolean"}}, {"name": "after", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "badge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "badgeColor", "value": {"kind": "expression", "type": "string"}}, {"name": "mediaItem", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mediaList", "value": {"kind": "expression", "type": "boolean"}}, {"name": "groupTitle", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeout", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeout<PERSON>pened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sortableOpposite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionItem", "value": {"kind": "expression", "type": "boolean"}}, {"name": "accordionItemOpened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smartSelect", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smartSelectParams", "value": {"kind": "expression", "type": "object"}}, {"name": "no<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}}, {"name": "chevronCenter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "checkbox", "value": {"kind": "expression", "type": "boolean"}}, {"name": "radio", "value": {"kind": "expression", "type": "boolean"}}, {"name": "radioIcon", "value": {"kind": "expression", "type": "string"}}, {"name": "checkboxIcon", "value": {"kind": "expression", "type": "string"}}, {"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "indeterminate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array"]}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "required", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "virtualListIndex", "value": {"kind": "expression", "type": "number"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchbarEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarClear", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardPreventOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "back", "value": {"kind": "expression", "type": "boolean"}}, {"name": "external", "value": {"kind": "expression", "type": "boolean"}}, {"name": "force", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadCurrent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadAll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPrevious", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routeTabId", "value": {"kind": "expression", "type": "string"}}, {"name": "view", "value": {"kind": "expression", "type": "string"}}, {"name": "routeProps", "value": {"kind": "expression", "type": "object"}}, {"name": "preventRouter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "openIn", "value": {"kind": "expression", "type": "string"}}], "events": [{"name": "click"}, {"name": "swipeout"}, {"name": "swipeout:overswipeenter"}, {"name": "swipeout:overswipeexit"}, {"name": "swipeout:deleted"}, {"name": "swipeout:delete"}, {"name": "swipeout:close"}, {"name": "swipeout:closed"}, {"name": "swipeout:open"}, {"name": "swipeout:opened"}, {"name": "accordion:beforeclose"}, {"name": "accordion:close"}, {"name": "accordion:closed"}, {"name": "accordion:beforeopen"}, {"name": "accordion:open"}, {"name": "accordion:opened"}, {"name": "change"}, {"name": "update:checked"}]}, {"name": "F7LoginScreen", "source": {"symbol": "F7LoginScreen"}, "attributes": [{"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "containerEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "loginscreen:open"}, {"name": "loginscreen:opened"}, {"name": "loginscreen:close"}, {"name": "loginscreen:closed"}, {"name": "update:opened"}]}, {"name": "F7LoginScreenTitle", "source": {"symbol": "F7LoginScreenTitle"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Message", "source": {"symbol": "F7Message"}, "slots": [{"name": "default"}, {"name": "start"}, {"name": "end"}, {"name": "content-start"}, {"name": "content-end"}, {"name": "bubble-start"}, {"name": "bubble-end"}, {"name": "header"}, {"name": "footer"}, {"name": "text"}, {"name": "name"}, {"name": "image"}, {"name": "text-header"}, {"name": "text-footer"}], "attributes": [{"name": "text", "value": {"kind": "expression", "type": "string"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "avatar", "value": {"kind": "expression", "type": "string"}}, {"name": "type", "value": {"kind": "expression", "type": "string"}, "default": "sent"}, {"name": "image", "value": {"kind": "expression", "type": "string"}}, {"name": "header", "value": {"kind": "expression", "type": "string"}}, {"name": "footer", "value": {"kind": "expression", "type": "string"}}, {"name": "textHeader", "value": {"kind": "expression", "type": "string"}}, {"name": "textFooter", "value": {"kind": "expression", "type": "string"}}, {"name": "first", "value": {"kind": "expression", "type": "boolean"}}, {"name": "last", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sameName", "value": {"kind": "expression", "type": "boolean"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}}, {"name": "<PERSON>Footer", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sameAvatar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "typing", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}, {"name": "click:name"}, {"name": "click:text"}, {"name": "click:avatar"}, {"name": "click:header"}, {"name": "click:footer"}, {"name": "click:bubble"}]}, {"name": "F7Messagebar", "source": {"symbol": "F7Messagebar"}, "slots": [{"name": "default"}, {"name": "before-area"}, {"name": "after-area"}, {"name": "send-link"}, {"name": "before-inner"}, {"name": "after-inner"}, {"name": "inner-start"}, {"name": "inner-end"}], "attributes": [{"name": "sheetVisible", "value": {"kind": "expression", "type": "boolean"}}, {"name": "attachmentsVisible", "value": {"kind": "expression", "type": "boolean"}}, {"name": "top", "value": {"kind": "expression", "type": "boolean"}}, {"name": "resizable", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "bottomOffset", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "topOffset", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "maxHeight", "value": {"kind": "expression", "type": "number"}}, {"name": "resizePage", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "sendLink", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array"]}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "textareaId", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}, "default": "Message"}, {"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "change"}, {"name": "input"}, {"name": "focus"}, {"name": "blur"}, {"name": "submit"}, {"name": "send"}, {"name": "click"}, {"name": "messagebar:attachmentdelete"}, {"name": "messagebar:attachmentclick"}, {"name": "messagebar:resizepage"}, {"name": "update:value"}]}, {"name": "F7MessagebarAttachment", "source": {"symbol": "F7MessagebarAttachment"}, "attributes": [{"name": "image", "value": {"kind": "expression", "type": "string"}}, {"name": "deletable", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "attachment:click"}, {"name": "attachment:delete"}]}, {"name": "F7MessagebarAttachments", "source": {"symbol": "F7MessagebarAttachments"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7MessagebarSheet", "source": {"symbol": "F7MessagebarSheet"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7MessagebarSheetImage", "source": {"symbol": "F7MessagebarSheetImage"}, "attributes": [{"name": "image", "value": {"kind": "expression", "type": "string"}}, {"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "checked"}, {"name": "unchecked"}, {"name": "change"}, {"name": "update:checked"}]}, {"name": "F7MessagebarSheetItem", "source": {"symbol": "F7MessagebarSheetItem"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Messages", "source": {"symbol": "F7Messages"}, "attributes": [{"name": "autoLayout", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "messages", "value": {"kind": "expression", "type": "array"}}, {"name": "newMessagesFirst", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "scrollMessages", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "scrollMessagesOnEdge", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "typing", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "firstMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "lastMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "tailMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "sameNameMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "sameHeaderMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "sameFooterMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "sameAvatarMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "customClassMessageRule", "value": {"kind": "expression", "type": "function"}}, {"name": "renderMessage", "value": {"kind": "expression", "type": "function"}}, {"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7MessagesTitle", "source": {"symbol": "F7MessagesTitle"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7NavLeft", "source": {"symbol": "F7NavLeft"}, "attributes": [{"name": "backLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "backLinkUrl", "value": {"kind": "expression", "type": "string"}}, {"name": "backLinkForce", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backLinkShowText", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sliding", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "back:click"}, {"name": "click:back"}]}, {"name": "F7NavRight", "source": {"symbol": "F7NavRight"}, "attributes": [{"name": "sliding", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7NavTitle", "source": {"symbol": "F7NavTitle"}, "attributes": [{"name": "title", "value": {"kind": "expression", "type": "string"}}, {"name": "subtitle", "value": {"kind": "expression", "type": "string"}}, {"name": "sliding", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7NavTitleLarge", "source": {"symbol": "F7NavTitleLarge"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Navbar", "source": {"symbol": "F7Navbar"}, "slots": [{"name": "default"}, {"name": "before-inner"}, {"name": "after-inner"}, {"name": "left"}, {"name": "right"}, {"name": "title"}, {"name": "title-large"}], "attributes": [{"name": "backLink", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "backLinkUrl", "value": {"kind": "expression", "type": "string"}}, {"name": "backLinkForce", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backLinkShowText", "value": {"kind": "expression", "type": "boolean"}}, {"name": "sliding", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "title", "value": {"kind": "expression", "type": "string"}}, {"name": "subtitle", "value": {"kind": "expression", "type": "string"}}, {"name": "hidden", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "innerClass", "value": {"kind": "expression", "type": "string"}}, {"name": "innerClassName", "value": {"kind": "expression", "type": "string"}}, {"name": "large", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeTransparent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transparent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "title<PERSON>arge", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "navbar:hide"}, {"name": "navbar:show"}, {"name": "navbar:expand"}, {"name": "navbar:collapse"}, {"name": "navbar:transparentshow"}, {"name": "navbar:transparenthide"}, {"name": "click:back"}, {"name": "back:click"}]}, {"name": "F7Page", "source": {"symbol": "F7Page"}, "slots": [{"name": "default"}, {"name": "fixed"}], "attributes": [{"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "withSubnavbar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "subnavbar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "with<PERSON><PERSON><PERSON><PERSON>arge", "value": {"kind": "expression", "type": "boolean"}}, {"name": "navbarLarge", "value": {"kind": "expression", "type": "boolean"}}, {"name": "no<PERSON>av<PERSON>", "value": {"kind": "expression", "type": "boolean"}}, {"name": "noToolbar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabs", "value": {"kind": "expression", "type": "boolean"}}, {"name": "pageContent", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "noSwipeback", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptrDistance", "value": {"kind": "expression", "type": "number"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "ptrBottom", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptrMousewheel", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infinite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infiniteTop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infiniteDistance", "value": {"kind": "expression", "type": "number"}}, {"name": "infinitePreloader", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "hideBarsOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideNavbarOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideToolbarOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "messagesContent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "loginScreen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "page:mounted"}, {"name": "page:init"}, {"name": "page:reinit"}, {"name": "page:beforein"}, {"name": "page:beforeout"}, {"name": "page:afterout"}, {"name": "page:afterin"}, {"name": "page:<PERSON><PERSON><PERSON>"}, {"name": "page:<PERSON><PERSON><PERSON>"}, {"name": "page:tabshow"}, {"name": "page:tabhide"}, {"name": "ptr:pullstart"}, {"name": "ptr:pullmove"}, {"name": "ptr:pullend"}, {"name": "ptr:refresh"}, {"name": "ptr:done"}, {"name": "infinite"}]}, {"name": "F7PageContent", "source": {"symbol": "F7PageContent"}, "attributes": [{"name": "tab", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptrDistance", "value": {"kind": "expression", "type": "number"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "ptrBottom", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ptrMousewheel", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infinite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infiniteTop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "infiniteDistance", "value": {"kind": "expression", "type": "number"}}, {"name": "infinitePreloader", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "hideBarsOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideNavbarOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hideToolbarOnScroll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "messagesContent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "loginScreen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "ptr:pullstart"}, {"name": "ptr:pullmove"}, {"name": "ptr:pullend"}, {"name": "ptr:refresh"}, {"name": "ptr:done"}, {"name": "infinite"}, {"name": "ptrPullStart"}, {"name": "ptrPullMove"}, {"name": "ptrPullEnd"}, {"name": "ptrRefresh"}, {"name": "ptrDone"}, {"name": "tab:hide"}, {"name": "tab:show"}]}, {"name": "F7Panel", "source": {"symbol": "F7Panel"}, "attributes": [{"name": "side", "value": {"kind": "expression", "type": "string"}}, {"name": "effect", "value": {"kind": "expression", "type": "string"}}, {"name": "cover", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reveal", "value": {"kind": "expression", "type": "boolean"}}, {"name": "push", "value": {"kind": "expression", "type": "boolean"}}, {"name": "floating", "value": {"kind": "expression", "type": "boolean"}}, {"name": "left", "value": {"kind": "expression", "type": "boolean"}}, {"name": "right", "value": {"kind": "expression", "type": "boolean"}}, {"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "resizable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "backdropEl", "value": {"kind": "expression", "type": "string"}}, {"name": "containerEl", "value": {"kind": "expression", "type": "string"}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "visibleBreakpoint", "value": {"kind": "expression", "type": "number"}}, {"name": "collapsedBreakpoint", "value": {"kind": "expression", "type": "number"}}, {"name": "swipe", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeNoFollow", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeOnlyClose", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeActiveArea", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "swipe<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "panel:open"}, {"name": "panel:opened"}, {"name": "panel:close"}, {"name": "panel:closed"}, {"name": "click"}, {"name": "panel:<PERSON><PERSON><PERSON>"}, {"name": "panel:swipe"}, {"name": "panel:swipe<PERSON>n"}, {"name": "panel:breakpoint"}, {"name": "panel:collapsedbreakpoint"}, {"name": "panel:resize"}, {"name": "update:opened"}]}, {"name": "F7PhotoBrowser", "source": {"symbol": "F7PhotoBrowser"}, "attributes": [{"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "params", "value": {"kind": "expression", "type": "object"}}, {"name": "photos", "value": {"kind": "expression", "type": "array"}}, {"name": "thumbs", "value": {"kind": "expression", "type": "array"}}, {"name": "exposition", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "expositionHideCaptions", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "type", "value": {"kind": "expression", "type": "string"}}, {"name": "navbar", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "toolbar", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "theme", "value": {"kind": "expression", "type": "string"}}, {"name": "captionsTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "iconsColor", "value": {"kind": "expression", "type": "string"}}, {"name": "swipeToClose", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "pageBackLinkText", "value": {"kind": "expression", "type": "string"}}, {"name": "popupCloseLinkIcon", "value": {"kind": "expression", "type": "boolean"}}, {"name": "popupCloseLinkText", "value": {"kind": "expression", "type": "string"}}, {"name": "navbarOfText", "value": {"kind": "expression", "type": "string"}}, {"name": "navbarShowCount", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swiper", "value": {"kind": "expression", "type": "object"}}, {"name": "url", "value": {"kind": "expression", "type": "string"}}, {"name": "routableModals", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "virtualSlides", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "view", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "renderNavbar", "value": {"kind": "expression", "type": "function"}}, {"name": "renderToolbar", "value": {"kind": "expression", "type": "function"}}, {"name": "renderCaption", "value": {"kind": "expression", "type": "function"}}, {"name": "renderObject", "value": {"kind": "expression", "type": "function"}}, {"name": "renderLazyPhoto", "value": {"kind": "expression", "type": "function"}}, {"name": "renderPhoto", "value": {"kind": "expression", "type": "function"}}, {"name": "renderPage", "value": {"kind": "expression", "type": "function"}}, {"name": "renderPopup", "value": {"kind": "expression", "type": "function"}}, {"name": "renderStandalone", "value": {"kind": "expression", "type": "function"}}, {"name": "renderThumb", "value": {"kind": "expression", "type": "function"}}], "events": [{"name": "photobrowser:open"}, {"name": "photobrowser:close"}, {"name": "photobrowser:opened"}, {"name": "photobrowser:closed"}, {"name": "photobrowser:swipetoclose"}]}, {"name": "F7<PERSON><PERSON><PERSON><PERSON>", "source": {"symbol": "F7<PERSON><PERSON><PERSON><PERSON>"}, "attributes": [{"name": "size", "value": {"kind": "expression", "type": "number"}, "default": 320}, {"name": "tooltip", "value": {"kind": "expression", "type": "boolean"}}, {"name": "datasets", "value": {"kind": "expression", "type": "array"}}, {"name": "formatTooltip", "value": {"kind": "expression", "type": "function"}}], "events": [{"name": "select"}]}, {"name": "F7Popover", "source": {"symbol": "F7Popover"}, "attributes": [{"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "targetEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "arrow", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeByOutsideClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeOnEscape", "value": {"kind": "expression", "type": "boolean"}}, {"name": "containerEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "verticalPosition", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "popover:open"}, {"name": "popover:opened"}, {"name": "popover:close"}, {"name": "popover:closed"}, {"name": "update:opened"}]}, {"name": "F7Popup", "source": {"symbol": "F7Popup"}, "attributes": [{"name": "tabletFullscreen", "value": {"kind": "expression", "type": "boolean"}}, {"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeOnEscape", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeToClose", "value": {"kind": "expression", "type": ["boolean", "string"]}, "default": false}, {"name": "swi<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "push", "value": {"kind": "expression", "type": "boolean"}}, {"name": "containerEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "popup:swipestart"}, {"name": "popup:swipe<PERSON><PERSON>"}, {"name": "popup:swipe<PERSON>"}, {"name": "popup:swipec<PERSON>"}, {"name": "popup:open"}, {"name": "popup:opened"}, {"name": "popup:close"}, {"name": "popup:closed"}, {"name": "update:opened"}]}, {"name": "F7Preloader", "source": {"symbol": "F7Preloader"}, "attributes": [{"name": "size", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Progressbar", "source": {"symbol": "F7Progressbar"}, "attributes": [{"name": "progress", "value": {"kind": "expression", "type": "number"}}, {"name": "infinite", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Radio", "source": {"symbol": "F7Radio"}, "attributes": [{"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": ["number", "string"]}}, {"name": "value", "value": {"kind": "expression", "type": ["number", "string", "boolean"]}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "update:checked"}, {"name": "change"}]}, {"name": "F7Range", "source": {"symbol": "F7Range"}, "attributes": [{"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "value", "value": {"kind": "expression", "type": ["number", "array", "string"]}, "default": 0}, {"name": "min", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 0}, {"name": "max", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 100}, {"name": "step", "value": {"kind": "expression", "type": ["number", "string"]}, "default": 1}, {"name": "label", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "dual", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "vertical", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "verticalReversed", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "draggableBar", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "formatLabel", "value": {"kind": "expression", "type": "function"}}, {"name": "scale", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "scaleSteps", "value": {"kind": "expression", "type": "number"}, "default": 5}, {"name": "scaleSubSteps", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "formatScaleLabel", "value": {"kind": "expression", "type": "function"}}, {"name": "limitKnobPosition", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "input", "value": {"kind": "expression", "type": "boolean"}}, {"name": "inputId", "value": {"kind": "expression", "type": "string"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "range:change"}, {"name": "range:changed"}, {"name": "rangeChange"}, {"name": "rangeChanged"}, {"name": "update:value"}]}, {"name": "F7RoutableModals", "source": {"symbol": "F7RoutableModals"}, "attributes": []}, {"name": "F7Searchbar", "source": {"symbol": "F7Searchbar"}, "slots": [{"name": "default"}, {"name": "before-inner"}, {"name": "after-inner"}, {"name": "inner-start"}, {"name": "inner-end"}, {"name": "input-wrap-start"}, {"name": "input-wrap-end"}], "attributes": [{"name": "outline", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "form", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}, "default": "Search"}, {"name": "spellcheck", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disable<PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "disableButtonText", "value": {"kind": "expression", "type": "string"}, "default": "Cancel"}, {"name": "clearButton", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array"]}}, {"name": "inputEvents", "value": {"kind": "expression", "type": "string"}, "default": "change input compositionend"}, {"name": "expandable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "inline", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchContainer", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "searchIn", "value": {"kind": "expression", "type": "string"}, "default": ".item-title"}, {"name": "searchItem", "value": {"kind": "expression", "type": "string"}, "default": "li"}, {"name": "searchGroup", "value": {"kind": "expression", "type": "string"}, "default": ".list-group"}, {"name": "searchGroupTitle", "value": {"kind": "expression", "type": "string"}, "default": ".list-group-title"}, {"name": "foundEl", "value": {"kind": "expression", "type": ["string", "object"]}, "default": ".searchbar-found"}, {"name": "notFoundEl", "value": {"kind": "expression", "type": ["string", "object"]}, "default": ".searchbar-not-found"}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "hideOnEnableEl", "value": {"kind": "expression", "type": ["string", "object"]}, "default": ".searchbar-hide-on-enable"}, {"name": "hideOnSearchEl", "value": {"kind": "expression", "type": ["string", "object"]}, "default": ".searchbar-hide-on-search"}, {"name": "ignore", "value": {"kind": "expression", "type": "string"}, "default": ".searchbar-ignore"}, {"name": "customSearch", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "removeDiacritics", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "hideGroupTitles", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "hideGroups", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "change"}, {"name": "input"}, {"name": "focus"}, {"name": "blur"}, {"name": "submit"}, {"name": "click:clear"}, {"name": "click:disable"}, {"name": "searchbar:search"}, {"name": "searchbar:clear"}, {"name": "searchbar:enable"}, {"name": "searchbar:disable"}, {"name": "update:value"}]}, {"name": "F7Segmented", "source": {"symbol": "F7Segmented"}, "attributes": [{"name": "raised", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "round", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strong", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "strongMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tag", "value": {"kind": "expression", "type": "string"}, "default": "div"}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7Sheet", "source": {"symbol": "F7Sheet"}, "slots": [{"name": "default"}, {"name": "static"}, {"name": "fixed"}], "attributes": [{"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "top", "value": {"kind": "expression", "type": "boolean"}}, {"name": "bottom", "value": {"kind": "expression", "type": "boolean"}}, {"name": "position", "value": {"kind": "expression", "type": "string"}}, {"name": "backdrop", "value": {"kind": "expression", "type": "boolean"}}, {"name": "backdropEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "closeByBackdropClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeByOutsideClick", "value": {"kind": "expression", "type": "boolean"}}, {"name": "closeOnEscape", "value": {"kind": "expression", "type": "boolean"}}, {"name": "push", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeToClose", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeToStep", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swi<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "containerEl", "value": {"kind": "expression", "type": ["string", "object"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "sheet:stepprogress"}, {"name": "sheet:stepopen"}, {"name": "sheet:stepclose"}, {"name": "sheet:open"}, {"name": "sheet:opened"}, {"name": "sheet:close"}, {"name": "sheet:closed"}, {"name": "update:opened"}]}, {"name": "F7SkeletonAvatar", "source": {"symbol": "F7SkeletonAvatar"}, "attributes": [{"name": "tag", "value": {"kind": "expression", "type": "string"}, "default": "span"}, {"name": "size", "value": {"kind": "expression", "type": "number"}, "default": 48}, {"name": "borderRadius", "value": {"kind": "expression", "type": ["string", "number"]}, "default": "50%"}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "showIcon", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "effect", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7SkeletonBlock", "source": {"symbol": "F7SkeletonBlock"}, "attributes": [{"name": "tag", "value": {"kind": "expression", "type": "string"}, "default": "div"}, {"name": "width", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "height", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "borderRadius", "value": {"kind": "expression", "type": "string"}}, {"name": "effect", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7SkeletonImage", "source": {"symbol": "F7SkeletonImage"}, "attributes": [{"name": "tag", "value": {"kind": "expression", "type": "string"}, "default": "span"}, {"name": "width", "value": {"kind": "expression", "type": "number"}, "default": 1200}, {"name": "height", "value": {"kind": "expression", "type": "number"}, "default": 600}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "showIcon", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "effect", "value": {"kind": "expression", "type": "string"}}, {"name": "borderRadius", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7SkeletonText", "source": {"symbol": "F7SkeletonText"}, "attributes": [{"name": "tag", "value": {"kind": "expression", "type": "string"}, "default": "span"}, {"name": "effect", "value": {"kind": "expression", "type": "string"}}]}, {"name": "F7Stepper", "source": {"symbol": "F7Stepper"}, "attributes": [{"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "value", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "min", "value": {"kind": "expression", "type": "number"}, "default": 0}, {"name": "max", "value": {"kind": "expression", "type": "number"}, "default": 100}, {"name": "step", "value": {"kind": "expression", "type": "number"}, "default": 1}, {"name": "formatValue", "value": {"kind": "expression", "type": "function"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "inputId", "value": {"kind": "expression", "type": "string"}}, {"name": "input", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "inputType", "value": {"kind": "expression", "type": "string"}, "default": "text"}, {"name": "inputReadonly", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "autorepeat", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "autorepeatDynamic", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "wraps", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "manualInputMode", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "decimalPoint", "value": {"kind": "expression", "type": "number"}, "default": 4}, {"name": "buttonsEndInputMode", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "buttonsOnly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "round", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "roundIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fill", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fillMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "fillIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "large", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "largeIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "small", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "smallIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raised", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "raisedIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "input"}, {"name": "change"}, {"name": "stepper:minusclick"}, {"name": "stepper:plusclick"}, {"name": "stepper:change"}, {"name": "update:value"}]}, {"name": "F7Subnavbar", "source": {"symbol": "F7Subnavbar"}, "attributes": [{"name": "sliding", "value": {"kind": "expression", "type": "boolean"}}, {"name": "title", "value": {"kind": "expression", "type": "string"}}, {"name": "inner", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7SwipeoutActions", "source": {"symbol": "F7SwipeoutActions"}, "attributes": [{"name": "left", "value": {"kind": "expression", "type": "boolean"}}, {"name": "right", "value": {"kind": "expression", "type": "boolean"}}, {"name": "side", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7SwipeoutButton", "source": {"symbol": "F7SwipeoutButton"}, "attributes": [{"name": "text", "value": {"kind": "expression", "type": "string"}}, {"name": "confirmTitle", "value": {"kind": "expression", "type": "string"}}, {"name": "confirmText", "value": {"kind": "expression", "type": "string"}}, {"name": "overswipe", "value": {"kind": "expression", "type": "boolean"}}, {"name": "close", "value": {"kind": "expression", "type": "boolean"}}, {"name": "delete", "value": {"kind": "expression", "type": "boolean"}}, {"name": "href", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "click"}]}, {"name": "F7Tab", "source": {"symbol": "F7Tab"}, "attributes": [{"name": "tabActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "tab:show"}, {"name": "tab:hide"}]}, {"name": "F7Tabs", "source": {"symbol": "F7Tabs"}, "attributes": [{"name": "animated", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swipeable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "swiperParams", "value": {"kind": "expression", "type": "object"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7TextEditor", "source": {"symbol": "F7TextEditor"}, "attributes": [{"name": "mode", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": "string"}}, {"name": "buttons", "value": {"kind": "expression", "type": "array"}}, {"name": "customButtons", "value": {"kind": "expression", "type": "object"}}, {"name": "dividers", "value": {"kind": "expression", "type": "boolean"}}, {"name": "imageUrlText", "value": {"kind": "expression", "type": "string"}}, {"name": "linkUrlText", "value": {"kind": "expression", "type": "string"}}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}}, {"name": "clearFormattingOnPaste", "value": {"kind": "expression", "type": "boolean"}}, {"name": "resizable", "value": {"kind": "expression", "type": "boolean"}, "default": false}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "texteditor:change"}, {"name": "texteditor:input"}, {"name": "texteditor:focus"}, {"name": "texteditor:blur"}, {"name": "texteditor:buttonclick"}, {"name": "texteditor:keyboardopen"}, {"name": "texteditor:keyboardclose"}, {"name": "texteditor:popoveropen"}, {"name": "texteditor:popoverclose"}, {"name": "texteditor:insertlink"}, {"name": "texteditor:insertimage"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "texteditorInput"}, {"name": "texteditorFocus"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "F7Toggle", "source": {"symbol": "F7Toggle"}, "attributes": [{"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "checked", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disabled", "value": {"kind": "expression", "type": "boolean"}}, {"name": "readonly", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "value", "value": {"kind": "expression", "type": ["string", "number", "array"]}}, {"name": "tooltip", "value": {"kind": "expression", "type": "string"}}, {"name": "tooltipTrigger", "value": {"kind": "expression", "type": "string"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "change"}, {"name": "toggle:change"}, {"name": "update:checked"}]}, {"name": "F7Toolbar", "source": {"symbol": "F7Toolbar"}, "slots": [{"name": "default"}, {"name": "before-inner"}, {"name": "after-inner"}], "attributes": [{"name": "tabbar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "icons", "value": {"kind": "expression", "type": "boolean"}}, {"name": "scrollable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hidden", "value": {"kind": "expression", "type": "boolean"}}, {"name": "outline", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "position", "value": {"kind": "expression", "type": "string"}}, {"name": "topMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "topIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "top", "value": {"kind": "expression", "type": "boolean"}}, {"name": "bottomMd", "value": {"kind": "expression", "type": "boolean"}}, {"name": "bottomIos", "value": {"kind": "expression", "type": "boolean"}}, {"name": "bottom", "value": {"kind": "expression", "type": "boolean"}}, {"name": "inner", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "toolbar:hide"}, {"name": "toolbar:show"}]}, {"name": "F7Treeview", "source": {"symbol": "F7Treeview"}, "attributes": [{"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7TreeviewItem", "source": {"symbol": "F7TreeviewItem"}, "slots": [{"name": "root-start"}, {"name": "root"}, {"name": "root-end"}, {"name": "content-start"}, {"name": "content"}, {"name": "content-end"}, {"name": "children-start"}, {"name": "default"}, {"name": "media"}, {"name": "label-start"}, {"name": "label"}], "attributes": [{"name": "toggle", "value": {"kind": "expression", "type": "boolean"}}, {"name": "itemToggle", "value": {"kind": "expression", "type": "boolean"}}, {"name": "selectable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "selected", "value": {"kind": "expression", "type": "boolean"}}, {"name": "opened", "value": {"kind": "expression", "type": "boolean"}}, {"name": "label", "value": {"kind": "expression", "type": "string"}}, {"name": "loadChildren", "value": {"kind": "expression", "type": "boolean"}}, {"name": "link", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}, {"name": "searchbarEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarClear", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "searchbarToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "panelToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popupClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "actionsClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "popoverClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "loginScreenClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sheetClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableEnable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableDisable", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "sortableToggle", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardPreventOpen", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "cardClose", "value": {"kind": "expression", "type": ["boolean", "string"]}}, {"name": "icon", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMaterial", "value": {"kind": "expression", "type": "string"}}, {"name": "iconF7", "value": {"kind": "expression", "type": "string"}}, {"name": "iconIos", "value": {"kind": "expression", "type": "string"}}, {"name": "iconMd", "value": {"kind": "expression", "type": "string"}}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}}, {"name": "iconSize", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadge", "value": {"kind": "expression", "type": ["string", "number"]}}, {"name": "iconBadgeColor", "value": {"kind": "expression", "type": "string"}}, {"name": "back", "value": {"kind": "expression", "type": "boolean"}}, {"name": "external", "value": {"kind": "expression", "type": "boolean"}}, {"name": "force", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "ignoreCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadCurrent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadAll", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPrevious", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routeTabId", "value": {"kind": "expression", "type": "string"}}, {"name": "view", "value": {"kind": "expression", "type": "string"}}, {"name": "routeProps", "value": {"kind": "expression", "type": "object"}}, {"name": "preventRouter", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "openIn", "value": {"kind": "expression", "type": "string"}}], "events": [{"name": "click"}, {"name": "treeview:open"}, {"name": "treeview:close"}, {"name": "treeview:loadchildren"}]}, {"name": "F7UseIcon", "source": {"symbol": "F7UseIcon"}, "attributes": [{"name": "icon", "value": {"kind": "expression", "type": "object"}}]}, {"name": "F7View", "source": {"symbol": "F7View"}, "attributes": [{"name": "tab", "value": {"kind": "expression", "type": "boolean"}}, {"name": "tabActive", "value": {"kind": "expression", "type": "boolean"}}, {"name": "name", "value": {"kind": "expression", "type": "string"}}, {"name": "initRouterOnTabShow", "value": {"kind": "expression", "type": "boolean"}}, {"name": "router", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "linksView", "value": {"kind": "expression", "type": ["object", "string"]}}, {"name": "url", "value": {"kind": "expression", "type": "string"}}, {"name": "main", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xhrCache", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xhrCacheIgnore", "value": {"kind": "expression", "type": "array"}}, {"name": "xhrCacheIgnoreGetParameters", "value": {"kind": "expression", "type": "boolean"}}, {"name": "xhrCacheDuration", "value": {"kind": "expression", "type": "number"}}, {"name": "preloadPreviousPage", "value": {"kind": "expression", "type": "boolean"}}, {"name": "allowDuplicateUrls", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadPages", "value": {"kind": "expression", "type": "boolean"}}, {"name": "reloadDetail", "value": {"kind": "expression", "type": "boolean"}}, {"name": "masterDetailResizable", "value": {"kind": "expression", "type": "boolean"}}, {"name": "masterDetailBreakpoint", "value": {"kind": "expression", "type": "number"}}, {"name": "removeElements", "value": {"kind": "expression", "type": "boolean"}}, {"name": "removeElementsWithTimeout", "value": {"kind": "expression", "type": "boolean"}}, {"name": "removeElementsTimeout", "value": {"kind": "expression", "type": "number"}}, {"name": "restoreScrollTopOnBack", "value": {"kind": "expression", "type": "boolean"}}, {"name": "loadInitialPage", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosSwipeBack", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosSwipeBackAnimateShadow", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosSwipeBackAnimateOpacity", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosSwipeBackActiveArea", "value": {"kind": "expression", "type": "number"}}, {"name": "iosSwipeBackThreshold", "value": {"kind": "expression", "type": "number"}}, {"name": "mdSwipeBack", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mdSwipeBackAnimateShadow", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mdSwipeBackAnimateOpacity", "value": {"kind": "expression", "type": "boolean"}}, {"name": "mdSwipeBackActiveArea", "value": {"kind": "expression", "type": "number"}}, {"name": "mdSwipeBackThreshold", "value": {"kind": "expression", "type": "number"}}, {"name": "browserHistory", "value": {"kind": "expression", "type": "boolean"}}, {"name": "browserHistoryRoot", "value": {"kind": "expression", "type": "string"}}, {"name": "browserHistoryAnimate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "browserHistoryAnimateOnLoad", "value": {"kind": "expression", "type": "boolean"}}, {"name": "browserHistorySeparator", "value": {"kind": "expression", "type": "string"}}, {"name": "browserHistoryOnLoad", "value": {"kind": "expression", "type": "boolean"}}, {"name": "browserHistoryInitialMatch", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "browserHistoryStoreHistory", "value": {"kind": "expression", "type": "boolean"}}, {"name": "animate", "value": {"kind": "expression", "type": "boolean"}}, {"name": "transition", "value": {"kind": "expression", "type": "string"}}, {"name": "iosDynamicNavbar", "value": {"kind": "expression", "type": "boolean"}}, {"name": "iosAnimateNavbarBackIcon", "value": {"kind": "expression", "type": "boolean"}}, {"name": "materialPageLoadDelay", "value": {"kind": "expression", "type": "number"}}, {"name": "passRouteQueryToRequest", "value": {"kind": "expression", "type": "boolean"}}, {"name": "passRouteParamsToRequest", "value": {"kind": "expression", "type": "boolean"}}, {"name": "routes", "value": {"kind": "expression", "type": "array"}}, {"name": "routesAdd", "value": {"kind": "expression", "type": "array"}}, {"name": "routesBeforeEnter", "value": {"kind": "expression", "type": ["function", "array"]}}, {"name": "routesBeforeLeave", "value": {"kind": "expression", "type": ["function", "array"]}}, {"name": "unloadTabContent", "value": {"kind": "expression", "type": "boolean"}}, {"name": "init", "value": {"kind": "expression", "type": "boolean"}, "default": true}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}], "events": [{"name": "view:init"}, {"name": "view:resize"}, {"name": "swipeback:move"}, {"name": "swipeback:beforechange"}, {"name": "swipeback:afterchange"}, {"name": "swipeback:<PERSON><PERSON><PERSON>"}, {"name": "swipeback:afterreset"}, {"name": "tab:hide"}, {"name": "tab:show"}]}, {"name": "F7Views", "source": {"symbol": "F7Views"}, "attributes": [{"name": "tabs", "value": {"kind": "expression", "type": "boolean"}}, {"name": "color", "value": {"kind": "expression", "type": "string"}}, {"name": "colorTheme", "value": {"kind": "expression", "type": "string"}}, {"name": "textColor", "value": {"kind": "expression", "type": "string"}}, {"name": "bgColor", "value": {"kind": "expression", "type": "string"}}, {"name": "borderColor", "value": {"kind": "expression", "type": "string"}}, {"name": "rippleColor", "value": {"kind": "expression", "type": "string"}}, {"name": "dark", "value": {"kind": "expression", "type": "boolean"}}]}, {"name": "F7ready", "source": {"symbol": "F7ready"}, "attributes": []}]}}}