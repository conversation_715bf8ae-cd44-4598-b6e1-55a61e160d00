// HTMX integration service for dynamic content loading
// This service provides utilities for working with HTMX in a Vue.js application

export class HTMXService {
  private static instance: HTMXService
  private htmxLoaded = false

  private constructor() {
    this.initializeHTMX()
  }

  public static getInstance(): HTMXService {
    if (!HTMXService.instance) {
      HTMXService.instance = new HTMXService()
    }
    return HTMXService.instance
  }

  private async initializeHTMX() {
    if (typeof window !== 'undefined' && !window.htmx) {
      try {
        // HTMX is already imported in main.ts, but we can configure it here
        await this.waitForHTMX()
        this.configureHTMX()
        this.htmxLoaded = true
      } catch (error) {
        console.error('Failed to initialize HTMX:', error)
      }
    }
  }

  private waitForHTMX(): Promise<void> {
    return new Promise((resolve, reject) => {
      let attempts = 0
      const maxAttempts = 50

      const checkHTMX = () => {
        if (window.htmx) {
          resolve()
        } else if (attempts < maxAttempts) {
          attempts++
          setTimeout(checkHTMX, 100)
        } else {
          reject(new Error('HTMX failed to load'))
        }
      }

      checkHTMX()
    })
  }

  private configureHTMX() {
    if (!window.htmx) return

    // Configure HTMX settings
    window.htmx.config.globalViewTransitions = true
    window.htmx.config.scrollBehavior = 'smooth'
    
    // Add custom headers for API requests
    document.body.addEventListener('htmx:configRequest', (event: any) => {
      event.detail.headers['Content-Type'] = 'application/json'
      
      // Add authentication token if available
      const token = localStorage.getItem('pocketbase_auth')
      if (token) {
        const authData = JSON.parse(token)
        if (authData.token) {
          event.detail.headers['Authorization'] = `Bearer ${authData.token}`
        }
      }
    })

    // Handle loading states
    document.body.addEventListener('htmx:beforeRequest', (event: any) => {
      this.showLoadingIndicator(event.target)
    })

    document.body.addEventListener('htmx:afterRequest', (event: any) => {
      this.hideLoadingIndicator(event.target)
    })

    // Handle errors
    document.body.addEventListener('htmx:responseError', (event: any) => {
      this.handleHTMXError(event)
    })

    // Handle successful responses
    document.body.addEventListener('htmx:afterSwap', (event: any) => {
      this.handleSuccessfulSwap(event)
    })
  }

  private showLoadingIndicator(element: HTMLElement) {
    const indicator = element.querySelector('.htmx-indicator')
    if (indicator) {
      indicator.classList.add('htmx-request')
    }
    
    // Add loading class to the element
    element.classList.add('htmx-loading')
  }

  private hideLoadingIndicator(element: HTMLElement) {
    const indicator = element.querySelector('.htmx-indicator')
    if (indicator) {
      indicator.classList.remove('htmx-request')
    }
    
    // Remove loading class from the element
    element.classList.remove('htmx-loading')
  }

  private handleHTMXError(event: any) {
    console.error('HTMX Error:', event.detail)
    
    const target = event.target
    const errorMessage = this.getErrorMessage(event.detail.xhr.status)
    
    // Show error message
    this.showErrorMessage(target, errorMessage)
  }

  private handleSuccessfulSwap(event: any) {
    // Re-initialize any Vue components or Framework7 components in the swapped content
    this.reinitializeComponents(event.target)
  }

  private getErrorMessage(status: number): string {
    switch (status) {
      case 400:
        return 'Invalid request. Please check your input.'
      case 401:
        return 'Authentication required. Please log in.'
      case 403:
        return 'Access denied. You do not have permission.'
      case 404:
        return 'Resource not found.'
      case 500:
        return 'Server error. Please try again later.'
      default:
        return 'An error occurred. Please try again.'
    }
  }

  private showErrorMessage(element: HTMLElement, message: string) {
    // Create or update error message element
    let errorElement = element.querySelector('.htmx-error-message') as HTMLElement
    
    if (!errorElement) {
      errorElement = document.createElement('div')
      errorElement.className = 'htmx-error-message'
      element.appendChild(errorElement)
    }
    
    errorElement.innerHTML = `
      <div class="error-alert">
        <i class="f7-icons">exclamationmark_triangle</i>
        <span>${message}</span>
      </div>
    `
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.remove()
      }
    }, 5000)
  }

  private reinitializeComponents(element: HTMLElement) {
    // This would reinitialize any Framework7 components in the swapped content
    // For now, we'll just trigger a custom event that components can listen to
    const event = new CustomEvent('htmx:componentReinit', {
      detail: { element }
    })
    element.dispatchEvent(event)
  }

  // Public methods for manual HTMX operations
  public async loadContent(url: string, target: string, method = 'GET') {
    if (!this.htmxLoaded) {
      await this.waitForHTMX()
    }

    const targetElement = document.querySelector(target)
    if (!targetElement) {
      throw new Error(`Target element ${target} not found`)
    }

    window.htmx.ajax(method, url, {
      target: target,
      swap: 'innerHTML'
    })
  }

  public async submitForm(formElement: HTMLFormElement, target?: string) {
    if (!this.htmxLoaded) {
      await this.waitForHTMX()
    }

    const formData = new FormData(formElement)
    const url = formElement.action || window.location.href
    const method = formElement.method || 'POST'

    window.htmx.ajax(method, url, {
      values: Object.fromEntries(formData),
      target: target || formElement,
      swap: 'outerHTML'
    })
  }

  public isHTMXLoaded(): boolean {
    return this.htmxLoaded && !!window.htmx
  }
}

// Global HTMX service instance
export const htmxService = HTMXService.getInstance()

// Type declarations for HTMX
declare global {
  interface Window {
    htmx: {
      ajax: (method: string, url: string, options?: any) => void
      config: {
        globalViewTransitions: boolean
        scrollBehavior: string
        [key: string]: any
      }
      [key: string]: any
    }
  }
}

// CSS for HTMX indicators and loading states
export const htmxStyles = `
  .htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in;
  }
  
  .htmx-request .htmx-indicator {
    opacity: 1;
  }
  
  .htmx-loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  .htmx-error-message {
    margin-top: 10px;
  }
  
  .error-alert {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 5px;
    color: #c33;
    font-size: 14px;
  }
  
  .htmx-swapping {
    opacity: 0;
    transition: opacity 200ms ease-out;
  }
  
  .htmx-settling {
    opacity: 1;
  }
`

export default htmxService
