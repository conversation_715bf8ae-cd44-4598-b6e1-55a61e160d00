import { ref, computed } from 'vue'
import { authService, pb } from '@/services/pocketbase'

// Global auth state
const user = ref(pb.authStore.model)
const isAuthenticated = ref(pb.authStore.isValid)

// Listen for auth changes
pb.authStore.onChange((token, model) => {
  user.value = model
  isAuthenticated.value = pb.authStore.isValid
})

export function useAuth() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  const login = async (email: string, password: string) => {
    loading.value = true
    error.value = null
    
    try {
      const authData = await authService.login(email, password)
      user.value = authData.record
      isAuthenticated.value = true
      return authData
    } catch (err: any) {
      error.value = err.message || 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const register = async (userData: {
    email: string
    password: string
    passwordConfirm: string
    name: string
    phone?: string
  }) => {
    loading.value = true
    error.value = null
    
    try {
      const record = await authService.register(userData)
      return record
    } catch (err: any) {
      error.value = err.message || 'Registration failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    authService.logout()
    user.value = null
    isAuthenticated.value = false
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user: computed(() => user.value),
    isAuthenticated: computed(() => isAuthenticated.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    
    // Actions
    login,
    register,
    logout,
    clearError
  }
}
