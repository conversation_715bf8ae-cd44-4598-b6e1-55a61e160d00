<template>
  <f7-page name="test">
    <f7-navbar title="Feature Test Page" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <div class="container">
        <h1>ECG Training Centre - Feature Test</h1>
        
        <!-- Navigation Test -->
        <f7-card>
          <f7-card-header>Navigation Test</f7-card-header>
          <f7-card-content>
            <f7-list>
              <f7-list-item link="/" title="Home Page"></f7-list-item>
              <f7-list-item link="/about/" title="About Page"></f7-list-item>
              <f7-list-item link="/courses/" title="Courses Page"></f7-list-item>
              <f7-list-item link="/contact/" title="Contact Page"></f7-list-item>
              <f7-list-item link="/login/" title="Login Page"></f7-list-item>
              <f7-list-item link="/register/" title="Register Page"></f7-list-item>
              <f7-list-item link="/profile/" title="Profile Page"></f7-list-item>
            </f7-list>
          </f7-card-content>
        </f7-card>

        <!-- Payment Test -->
        <f7-card>
          <f7-card-header>Payment System Test</f7-card-header>
          <f7-card-content>
            <p>Test payment integration with sample course:</p>
            <f7-button 
              fill 
              color="blue" 
              @click="testPayment"
            >
              Test Payment Flow
            </f7-button>
          </f7-card-content>
        </f7-card>

        <!-- Newsletter Test -->
        <f7-card>
          <f7-card-header>Newsletter Test</f7-card-header>
          <f7-card-content>
            <form @submit.prevent="testNewsletter">
              <f7-list>
                <f7-list-input
                  type="email"
                  placeholder="Test email"
                  v-model:value="testEmail"
                ></f7-list-input>
              </f7-list>
              <f7-button fill color="green" type="submit">Test Newsletter</f7-button>
            </form>
          </f7-card-content>
        </f7-card>

        <!-- HTMX Test -->
        <f7-card>
          <f7-card-header>HTMX Test</f7-card-header>
          <f7-card-content>
            <div id="htmx-test-result">
              <p>Click button to test HTMX functionality:</p>
            </div>
            <f7-button 
              fill 
              color="orange" 
              @click="testHTMX"
            >
              Test HTMX
            </f7-button>
          </f7-card-content>
        </f7-card>

        <!-- Authentication Test -->
        <f7-card>
          <f7-card-header>Authentication Test</f7-card-header>
          <f7-card-content>
            <div v-if="!isAuthenticated">
              <p>User not logged in</p>
              <f7-button fill color="blue" href="/login/">Go to Login</f7-button>
            </div>
            <div v-else>
              <p>User logged in: {{ user?.name || 'Unknown' }}</p>
              <f7-button color="red" @click="testLogout">Logout</f7-button>
            </div>
          </f7-card-content>
        </f7-card>

        <!-- Backend Test -->
        <f7-card>
          <f7-card-header>Backend Connection Test</f7-card-header>
          <f7-card-content>
            <p>PocketBase Status: <span :class="backendStatus.class">{{ backendStatus.text }}</span></p>
            <f7-button fill color="purple" @click="testBackend">Test Backend</f7-button>
          </f7-card-content>
        </f7-card>

        <!-- Mobile Features Test -->
        <f7-card>
          <f7-card-header>Mobile Features Test</f7-card-header>
          <f7-card-content>
            <p>Device Type: {{ deviceInfo.type }}</p>
            <p>Screen Size: {{ deviceInfo.width }}x{{ deviceInfo.height }}</p>
            <p>Touch Support: {{ deviceInfo.touch ? 'Yes' : 'No' }}</p>
            <f7-button fill color="teal" @click="testMobileFeatures">Test Mobile Features</f7-button>
          </f7-card-content>
        </f7-card>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { f7 } from 'framework7-vue'
import { useAuth } from '@/composables/useAuth'
import { useNewsletter } from '@/composables/useNewsletter'
import { useHTMX } from '@/composables/useHTMX'
import { pb } from '@/services/pocketbase'

const { isAuthenticated, user, logout } = useAuth()
const { subscribe } = useNewsletter()
const { loadContent } = useHTMX()

const testEmail = ref('')
const backendStatus = ref({
  text: 'Unknown',
  class: 'text-gray'
})

const deviceInfo = ref({
  type: 'Unknown',
  width: 0,
  height: 0,
  touch: false
})

onMounted(() => {
  updateDeviceInfo()
  testBackend()
})

const updateDeviceInfo = () => {
  deviceInfo.value = {
    type: f7.device.desktop ? 'Desktop' : f7.device.tablet ? 'Tablet' : 'Mobile',
    width: window.innerWidth,
    height: window.innerHeight,
    touch: 'ontouchstart' in window
  }
}

const testPayment = () => {
  f7.views.main.router.navigate('/payment/test-course/power-systems')
}

const testNewsletter = async () => {
  if (!testEmail.value) {
    f7.dialog.alert('Please enter an email address', 'Test Newsletter')
    return
  }

  try {
    await subscribe(testEmail.value)
    f7.toast.create({
      text: 'Newsletter test successful!',
      position: 'center',
      closeTimeout: 2000
    }).open()
    testEmail.value = ''
  } catch (error: any) {
    f7.dialog.alert(error.message || 'Newsletter test failed', 'Error')
  }
}

const testHTMX = () => {
  const resultDiv = document.getElementById('htmx-test-result')
  if (resultDiv) {
    resultDiv.innerHTML = `
      <p style="color: green;">✅ HTMX Test Successful!</p>
      <p>Timestamp: ${new Date().toLocaleTimeString()}</p>
    `
  }
}

const testLogout = () => {
  logout()
  f7.toast.create({
    text: 'Logout test successful!',
    position: 'center',
    closeTimeout: 2000
  }).open()
}

const testBackend = async () => {
  try {
    // Test PocketBase connection
    const health = await fetch('http://localhost:8090/api/health')
    if (health.ok) {
      backendStatus.value = {
        text: 'Connected ✅',
        class: 'text-green'
      }
    } else {
      throw new Error('Health check failed')
    }
  } catch (error) {
    backendStatus.value = {
      text: 'Disconnected ❌',
      class: 'text-red'
    }
  }
}

const testMobileFeatures = () => {
  updateDeviceInfo()
  
  f7.dialog.alert(
    `Device: ${deviceInfo.value.type}\nScreen: ${deviceInfo.value.width}x${deviceInfo.value.height}\nTouch: ${deviceInfo.value.touch}`,
    'Mobile Features Test'
  )
}
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.text-green {
  color: #059669;
  font-weight: bold;
}

.text-red {
  color: #dc3545;
  font-weight: bold;
}

.text-gray {
  color: #666;
}
</style>
