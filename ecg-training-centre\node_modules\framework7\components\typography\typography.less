/* === Typography === */
@import './typography-vars.less';

.rules-loop(@values, @ruleName) {
  each(@values, {
    .@{ruleName}-@{value} {
      @{ruleName}: @value !important;
    }
  });
}

// Display
@display: flex, block, inline-flex, inline-block, inline, none;
.rules-loop(@display, display);

// Flex Shrink
@shrinks: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10;
.rules-loop(@shrinks, flex-shrink);

// Flex Direction
@directions: row, row-reverse, column, column-reverse;
.rules-loop(@directions, flex-direction);

// Justify Content
@justify: flex-start, center, flex-end, space-between, space-around, space-evenly, stretch, start,
  end, left, right;
.rules-loop(@justify, justify-content);

// Align Content
@alignContent: flex-start, flex-end, center, space-between, space-around, stretch;
.rules-loop(@alignContent, align-content);

// Align Items
@alignItems: baseline, flex-start, flex-end, center, stretch;
.rules-loop(@alignItems, align-items);

// Align Self
@alignSelf: flex-start, flex-end, center, stretch;
.rules-loop(@alignSelf, align-self);

// Text Align
@textAlign: left, center, right, justify;
.rules-loop(@textAlign, text-align);

// Float
@float: left, right, none;
.rules-loop(@float, float);

// Vertical Align
@verticalAlign: bottom, middle, top;
.rules-loop(@verticalAlign, vertical-align);

// Padding
.no-padding {
  padding: 0 !important;
}
.no-padding-left {
  padding-left: 0 !important;
}
.no-padding-right {
  padding-right: 0 !important;
}
.no-padding-horizontal {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.no-padding-top {
  padding-top: 0 !important;
}
.no-padding-bottom {
  padding-bottom: 0 !important;
}
.no-padding-vertical {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
// Margin
.no-margin {
  margin: 0 !important;
}
.no-margin-left {
  margin-left: 0 !important;
}
.no-margin-right {
  margin-right: 0 !important;
}
.no-margin-horizontal {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.no-margin-top {
  margin-top: 0 !important;
}
.no-margin-bottom {
  margin-bottom: 0 !important;
}
.no-margin-vertical {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

// Width
.width-auto {
  width: auto !important;
}
.width-100 {
  width: 100% !important;
}

// Padding
.padding {
  padding: var(--f7-typography-padding) !important;
}
.padding-half {
  padding: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-top {
  padding-top: var(--f7-typography-padding) !important;
}
.padding-top-half {
  padding-top: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-bottom {
  padding-bottom: var(--f7-typography-padding) !important;
}
.padding-bottom-half {
  padding-bottom: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-left {
  padding-left: var(--f7-typography-padding) !important;
}
.padding-left-half {
  padding-left: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-right {
  padding-right: var(--f7-typography-padding) !important;
}
.padding-right-half {
  padding-right: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-vertical {
  padding-top: var(--f7-typography-padding) !important;
  padding-bottom: var(--f7-typography-padding) !important;
}
.padding-vertical-half {
  padding-top: calc(var(--f7-typography-padding) / 2) !important;
  padding-bottom: calc(var(--f7-typography-padding) / 2) !important;
}
.padding-horizontal {
  padding-left: var(--f7-typography-padding) !important;
  padding-right: var(--f7-typography-padding) !important;
}
.padding-horizontal-half {
  padding-left: calc(var(--f7-typography-padding) / 2) !important;
  padding-right: calc(var(--f7-typography-padding) / 2) !important;
}
// Margin
.margin {
  margin: var(--f7-typography-margin) !important;
}
.margin-half {
  margin: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-top {
  margin-top: var(--f7-typography-margin) !important;
}
.margin-top-half {
  margin-top: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-bottom {
  margin-bottom: var(--f7-typography-margin) !important;
}
.margin-bottom-half {
  margin-bottom: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-left {
  margin-left: var(--f7-typography-margin) !important;
}
.margin-left-half {
  margin-left: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-right {
  margin-right: var(--f7-typography-margin) !important;
}
.margin-right-half {
  margin-right: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-vertical {
  margin-top: var(--f7-typography-margin) !important;
  margin-bottom: var(--f7-typography-margin) !important;
}
.margin-vertical-half {
  margin-top: calc(var(--f7-typography-margin) / 2) !important;
  margin-bottom: calc(var(--f7-typography-margin) / 2) !important;
}
.margin-horizontal {
  margin-left: var(--f7-typography-margin) !important;
  margin-right: var(--f7-typography-margin) !important;
}
.margin-horizontal-half {
  margin-left: calc(var(--f7-typography-margin) / 2) !important;
  margin-right: calc(var(--f7-typography-margin) / 2) !important;
}
// Colors
[class*='text-color-'] {
  color: var(--f7-theme-color-text-color) !important;
}
[class*='bg-color-'] {
  background-color: var(--f7-theme-color-bg-color) !important;
}
[class*='border-color-'] {
  border-color: var(--f7-theme-color-border-color) !important;
}

.if-ios-theme({
  @import './typography-ios.less';
});
.if-md-theme({
  @import './typography-md.less';
});
