import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// HTMX
import 'htmx.org'

import App from './App.vue'

// Import views
import HomePage from './views/HomePage.vue'
import AboutPage from './views/AboutPage.vue'
import CoursesPage from './views/CoursesPage.vue'
import ContactPage from './views/ContactPage.vue'
import LoginPage from './views/LoginPage.vue'
import RegisterPage from './views/RegisterPage.vue'
import ProfilePage from './views/ProfilePage.vue'
import PaymentPage from './views/PaymentPage.vue'

// Router configuration
const routes = [
  { path: '/', component: HomePage },
  { path: '/about', component: AboutPage },
  { path: '/courses', component: CoursesPage },
  { path: '/contact', component: ContactPage },
  { path: '/login', component: LoginPage },
  { path: '/register', component: RegisterPage },
  { path: '/profile', component: ProfilePage },
  { path: '/payment/:courseId/:category', component: PaymentPage, props: true },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
