import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Framework7 imports
import Framework7 from 'framework7/lite-bundle'
import Framework7Vue, { registerComponents } from 'framework7-vue/bundle'

// Framework7 CSS
import 'framework7/css/bundle'

// HTMX
import 'htmx.org'
import { htmxService, htmxStyles } from './services/htmx'

import App from './App.vue'
import router from './router'

// Init Framework7-Vue Plugin
Framework7.use(Framework7Vue)

const app = createApp(App)

// Register Framework7 Vue components
registerComponents(app)

app.use(createPinia())
app.use(router)

// Add HTMX styles
const styleElement = document.createElement('style')
styleElement.textContent = htmxStyles
document.head.appendChild(styleElement)

app.mount('#app')
