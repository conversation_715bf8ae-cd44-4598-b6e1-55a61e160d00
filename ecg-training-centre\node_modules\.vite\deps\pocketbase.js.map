{"version": 3, "sources": ["../../pocketbase/src/ClientResponseError.ts", "../../pocketbase/src/tools/cookie.ts", "../../pocketbase/src/tools/jwt.ts", "../../pocketbase/src/stores/BaseAuthStore.ts", "../../pocketbase/src/stores/LocalAuthStore.ts", "../../pocketbase/src/services/BaseService.ts", "../../pocketbase/src/services/SettingsService.ts", "../../pocketbase/src/tools/options.ts", "../../pocketbase/src/services/RealtimeService.ts", "../../pocketbase/src/services/CrudService.ts", "../../pocketbase/src/tools/legacy.ts", "../../pocketbase/src/tools/refresh.ts", "../../pocketbase/src/services/RecordService.ts", "../../pocketbase/src/services/CollectionService.ts", "../../pocketbase/src/services/LogService.ts", "../../pocketbase/src/services/HealthService.ts", "../../pocketbase/src/services/FileService.ts", "../../pocketbase/src/services/BackupService.ts", "../../pocketbase/src/services/CronService.ts", "../../pocketbase/src/tools/formdata.ts", "../../pocketbase/src/services/BatchService.ts", "../../pocketbase/src/Client.ts", "../../pocketbase/src/stores/AsyncAuthStore.ts"], "sourcesContent": ["/**\n * ClientResponseError is a custom Error class that is intended to wrap\n * and normalize any error thrown by `Client.send()`.\n */\nexport class ClientResponseError extends Error {\n    url: string = \"\";\n    status: number = 0;\n    response: { [key: string]: any } = {};\n    isAbort: boolean = false;\n    originalError: any = null;\n\n    constructor(errData?: any) {\n        super(\"ClientResponseError\");\n\n        // Set the prototype explicitly.\n        // https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, ClientResponseError.prototype);\n\n        if (errData !== null && typeof errData === \"object\") {\n            this.url = typeof errData.url === \"string\" ? errData.url : \"\";\n            this.status = typeof errData.status === \"number\" ? errData.status : 0;\n            this.isAbort = !!errData.isAbort;\n            this.originalError = errData.originalError;\n\n            if (errData.response !== null && typeof errData.response === \"object\") {\n                this.response = errData.response;\n            } else if (errData.data !== null && typeof errData.data === \"object\") {\n                this.response = errData.data;\n            } else {\n                this.response = {};\n            }\n        }\n\n        if (!this.originalError && !(errData instanceof ClientResponseError)) {\n            this.originalError = errData;\n        }\n\n        if (typeof DOMException !== \"undefined\" && errData instanceof DOMException) {\n            this.isAbort = true;\n        }\n\n        this.name = \"ClientResponseError \" + this.status;\n        this.message = this.response?.message;\n        if (!this.message) {\n            if (this.isAbort) {\n                this.message =\n                    \"The request was autocancelled. You can find more info in https://github.com/pocketbase/js-sdk#auto-cancellation.\";\n            } else if (this.originalError?.cause?.message?.includes(\"ECONNREFUSED ::1\")) {\n                this.message =\n                    \"Failed to connect to the PocketBase server. Try changing the SDK URL from localhost to 127.0.0.1 (https://github.com/pocketbase/js-sdk/issues/21).\";\n            } else {\n                this.message = \"Something went wrong.\";\n            }\n        }\n\n        // set this.cause so that JS debugging tools can automatically connect\n        // the dots between the original error and the wrapped one\n        this.cause = this.originalError;\n    }\n\n    /**\n     * Alias for `this.response` for backward compatibility.\n     */\n    get data() {\n        return this.response;\n    }\n\n    /**\n     * Make a POJO's copy of the current error class instance.\n     * @see https://github.com/vuex-orm/vuex-orm/issues/255\n     */\n    toJSON() {\n        return { ...this };\n    }\n}\n", "/**\n * -------------------------------------------------------------------\n * Simple cookie parse and serialize utilities mostly based on the\n * node module https://github.com/jshttp/cookie.\n * -------------------------------------------------------------------\n */\n\n/**\n * RegExp to match field-content in RFC 7230 sec 3.2\n *\n * field-content = field-vchar [ 1*( SP / HTAB ) field-vchar ]\n * field-vchar   = VCHAR / obs-text\n * obs-text      = %x80-FF\n */\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n\nexport interface ParseOptions {\n    decode?: (val: string) => string;\n}\n\n/**\n * Parses the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function cookieParse(str: string, options?: ParseOptions): { [key: string]: any } {\n    const result: { [key: string]: any } = {};\n\n    if (typeof str !== \"string\") {\n        return result;\n    }\n\n    const opt = Object.assign({}, options || {});\n    const decode = opt.decode || defaultDecode;\n\n    let index = 0;\n    while (index < str.length) {\n        const eqIdx = str.indexOf(\"=\", index);\n\n        // no more cookie pairs\n        if (eqIdx === -1) {\n            break;\n        }\n\n        let endIdx = str.indexOf(\";\", index);\n\n        if (endIdx === -1) {\n            endIdx = str.length;\n        } else if (endIdx < eqIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n\n        const key = str.slice(index, eqIdx).trim();\n\n        // only assign once\n        if (undefined === result[key]) {\n            let val = str.slice(eqIdx + 1, endIdx).trim();\n\n            // quoted values\n            if (val.charCodeAt(0) === 0x22) {\n                val = val.slice(1, -1);\n            }\n\n            try {\n                result[key] = decode(val);\n            } catch (_) {\n                result[key] = val; // no decoding\n            }\n        }\n\n        index = endIdx + 1;\n    }\n\n    return result;\n}\n\nexport interface SerializeOptions {\n    encode?: (val: string | number | boolean) => string;\n    maxAge?: number;\n    domain?: string;\n    path?: string;\n    expires?: Date;\n    httpOnly?: boolean;\n    secure?: boolean;\n    priority?: string;\n    sameSite?: boolean | string;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize the a name value pair into a cookie string suitable for\n * http headers. An optional options object specified cookie parameters.\n *\n * ```js\n * cookieSerialize('foo', 'bar', { httpOnly: true }) // \"foo=bar; httpOnly\"\n * ```\n */\nexport function cookieSerialize(\n    name: string,\n    val: string,\n    options?: SerializeOptions,\n): string {\n    const opt = Object.assign({}, options || {});\n    const encode = opt.encode || defaultEncode;\n\n    if (!fieldContentRegExp.test(name)) {\n        throw new TypeError(\"argument name is invalid\");\n    }\n\n    const value = encode(val);\n\n    if (value && !fieldContentRegExp.test(value)) {\n        throw new TypeError(\"argument val is invalid\");\n    }\n\n    let result = name + \"=\" + value;\n\n    if (opt.maxAge != null) {\n        const maxAge = opt.maxAge - 0;\n\n        if (isNaN(maxAge) || !isFinite(maxAge)) {\n            throw new TypeError(\"option maxAge is invalid\");\n        }\n\n        result += \"; Max-Age=\" + Math.floor(maxAge);\n    }\n\n    if (opt.domain) {\n        if (!fieldContentRegExp.test(opt.domain)) {\n            throw new TypeError(\"option domain is invalid\");\n        }\n\n        result += \"; Domain=\" + opt.domain;\n    }\n\n    if (opt.path) {\n        if (!fieldContentRegExp.test(opt.path)) {\n            throw new TypeError(\"option path is invalid\");\n        }\n\n        result += \"; Path=\" + opt.path;\n    }\n\n    if (opt.expires) {\n        if (!isDate(opt.expires) || isNaN(opt.expires.valueOf())) {\n            throw new TypeError(\"option expires is invalid\");\n        }\n\n        result += \"; Expires=\" + opt.expires.toUTCString();\n    }\n\n    if (opt.httpOnly) {\n        result += \"; HttpOnly\";\n    }\n\n    if (opt.secure) {\n        result += \"; Secure\";\n    }\n\n    if (opt.priority) {\n        const priority =\n            typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n\n        switch (priority) {\n            case \"low\":\n                result += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                result += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                result += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(\"option priority is invalid\");\n        }\n    }\n\n    if (opt.sameSite) {\n        const sameSite =\n            typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n        switch (sameSite) {\n            case true:\n                result += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                result += \"; SameSite=Lax\";\n                break;\n            case \"strict\":\n                result += \"; SameSite=Strict\";\n                break;\n            case \"none\":\n                result += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(\"option sameSite is invalid\");\n        }\n    }\n\n    return result;\n}\n\n/**\n * Default URL-decode string value function.\n * Optimized to skip native call when no `%`.\n */\nfunction defaultDecode(val: string): string {\n    return val.indexOf(\"%\") !== -1 ? decodeURIComponent(val) : val;\n}\n\n/**\n * Default URL-encode value function.\n */\nfunction defaultEncode(val: string | number | boolean): string {\n    return encodeURIComponent(val);\n}\n\n/**\n * Determines if value is a Date.\n */\nfunction isDate(val: any): boolean {\n    return Object.prototype.toString.call(val) === \"[object Date]\" || val instanceof Date;\n}\n", "// @todo remove after https://github.com/reactwg/react-native-releases/issues/287\nconst isReactNative =\n    (typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\") ||\n    (typeof global !== \"undefined\" && (global as any).HermesInternal);\n\nlet atobPolyfill: Function;\nif (typeof atob === \"function\" && !isReactNative) {\n    atobPolyfill = atob;\n} else {\n    /**\n     * The code was extracted from:\n     * https://github.com/davidchambers/Base64.js\n     */\n    atobPolyfill = (input: any) => {\n        const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n        let str = String(input).replace(/=+$/, \"\");\n        if (str.length % 4 == 1) {\n            throw new Error(\n                \"'atob' failed: The string to be decoded is not correctly encoded.\",\n            );\n        }\n\n        for (\n            // initialize result and counters\n            var bc = 0, bs, buffer, idx = 0, output = \"\";\n            // get next character\n            (buffer = str.charAt(idx++));\n            // character found in table? initialize bit storage and add its ascii value;\n            ~buffer &&\n            ((bs = bc % 4 ? (bs as any) * 64 + buffer : buffer),\n            // and if not first of each 4 characters,\n            // convert the first 8 bits to one ascii character\n            bc++ % 4)\n                ? (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6))))\n                : 0\n        ) {\n            // try to find character in table (0-63, not found => -1)\n            buffer = chars.indexOf(buffer);\n        }\n\n        return output;\n    };\n}\n\n/**\n * Returns JWT token's payload data.\n */\nexport function getTokenPayload(token: string): { [key: string]: any } {\n    if (token) {\n        try {\n            const encodedPayload = decodeURIComponent(\n                atobPolyfill(token.split(\".\")[1])\n                    .split(\"\")\n                    .map(function (c: string) {\n                        return \"%\" + (\"00\" + c.charCodeAt(0).toString(16)).slice(-2);\n                    })\n                    .join(\"\"),\n            );\n\n            return JSON.parse(encodedPayload) || {};\n        } catch (e) {}\n    }\n\n    return {};\n}\n\n/**\n * Checks whether a JWT token is expired or not.\n * Tokens without `exp` payload key are considered valid.\n * Tokens with empty payload (eg. invalid token strings) are considered expired.\n *\n * @param token The token to check.\n * @param [expirationThreshold] Time in seconds that will be subtracted from the token `exp` property.\n */\nexport function isTokenExpired(token: string, expirationThreshold = 0): boolean {\n    let payload = getTokenPayload(token);\n\n    if (\n        Object.keys(payload).length > 0 &&\n        (!payload.exp || payload.exp - expirationThreshold > Date.now() / 1000)\n    ) {\n        return false;\n    }\n\n    return true;\n}\n", "import { cookieParse, cookieSerialize, SerializeOptions } from \"@/tools/cookie\";\nimport { isTokenExpired, getTokenPayload } from \"@/tools/jwt\";\nimport { RecordModel } from \"@/tools/dtos\";\n\nexport type AuthRecord = RecordModel | null;\n\nexport type AuthModel = AuthRecord; // for backward compatibility\n\nexport type OnStoreChangeFunc = (token: string, record: AuthRecord) => void;\n\nconst defaultCookieKey = \"pb_auth\";\n\n/**\n * Base AuthStore class that stores the auth state in runtime memory (aka. only for the duration of the store instane).\n *\n * Usually you wouldn't use it directly and instead use the builtin LocalAuthStore, AsyncAuthStore\n * or extend it with your own custom implementation.\n */\nexport class BaseAuthStore {\n    protected baseToken: string = \"\";\n    protected baseModel: AuthRecord = null;\n\n    private _onChangeCallbacks: Array<OnStoreChangeFunc> = [];\n\n    /**\n     * Retrieves the stored token (if any).\n     */\n    get token(): string {\n        return this.baseToken;\n    }\n\n    /**\n     * Retrieves the stored model data (if any).\n     */\n    get record(): AuthRecord {\n        return this.baseModel;\n    }\n\n    /**\n     * @deprecated use `record` instead.\n     */\n    get model(): AuthRecord {\n        return this.baseModel;\n    }\n\n    /**\n     * Loosely checks if the store has valid token (aka. existing and unexpired exp claim).\n     */\n    get isValid(): boolean {\n        return !isTokenExpired(this.token);\n    }\n\n    /**\n     * Loosely checks whether the currently loaded store state is for superuser.\n     *\n     * Alternatively you can also compare directly `pb.authStore.record?.collectionName`.\n     */\n    get isSuperuser(): boolean {\n        let payload = getTokenPayload(this.token);\n\n        return (\n            payload.type == \"auth\" &&\n            (this.record?.collectionName == \"_superusers\" ||\n                // fallback in case the record field is not populated and assuming\n                // that the collection crc32 checksum id wasn't manually changed\n                (!this.record?.collectionName &&\n                    payload.collectionId == \"pbc_3142635823\"))\n        );\n    }\n\n    /**\n     * @deprecated use `isSuperuser` instead or simply check the record.collectionName property.\n     */\n    get isAdmin(): boolean {\n        console.warn(\n            \"Please replace pb.authStore.isAdmin with pb.authStore.isSuperuser OR simply check the value of pb.authStore.record?.collectionName\",\n        );\n        return this.isSuperuser;\n    }\n\n    /**\n     * @deprecated use `!isSuperuser` instead or simply check the record.collectionName property.\n     */\n    get isAuthRecord(): boolean {\n        console.warn(\n            \"Please replace pb.authStore.isAuthRecord with !pb.authStore.isSuperuser OR simply check the value of pb.authStore.record?.collectionName\",\n        );\n        return getTokenPayload(this.token).type == \"auth\" && !this.isSuperuser;\n    }\n\n    /**\n     * Saves the provided new token and model data in the auth store.\n     */\n    save(token: string, record?: AuthRecord): void {\n        this.baseToken = token || \"\";\n        this.baseModel = record || null;\n\n        this.triggerChange();\n    }\n\n    /**\n     * Removes the stored token and model data form the auth store.\n     */\n    clear(): void {\n        this.baseToken = \"\";\n        this.baseModel = null;\n        this.triggerChange();\n    }\n\n    /**\n     * Parses the provided cookie string and updates the store state\n     * with the cookie's token and model data.\n     *\n     * NB! This function doesn't validate the token or its data.\n     * Usually this isn't a concern if you are interacting only with the\n     * PocketBase API because it has the proper server-side security checks in place,\n     * but if you are using the store `isValid` state for permission controls\n     * in a node server (eg. SSR), then it is recommended to call `authRefresh()`\n     * after loading the cookie to ensure an up-to-date token and model state.\n     * For example:\n     *\n     * ```js\n     * pb.authStore.loadFromCookie(\"cookie string...\");\n     *\n     * try {\n     *     // get an up-to-date auth store state by veryfing and refreshing the loaded auth model (if any)\n     *     pb.authStore.isValid && await pb.collection('users').authRefresh();\n     * } catch (_) {\n     *     // clear the auth store on failed refresh\n     *     pb.authStore.clear();\n     * }\n     * ```\n     */\n    loadFromCookie(cookie: string, key = defaultCookieKey): void {\n        const rawData = cookieParse(cookie || \"\")[key] || \"\";\n\n        let data: { [key: string]: any } = {};\n        try {\n            data = JSON.parse(rawData);\n            // normalize\n            if (typeof data === null || typeof data !== \"object\" || Array.isArray(data)) {\n                data = {};\n            }\n        } catch (_) {}\n\n        this.save(data.token || \"\", data.record || data.model || null);\n    }\n\n    /**\n     * Exports the current store state as cookie string.\n     *\n     * By default the following optional attributes are added:\n     * - Secure\n     * - HttpOnly\n     * - SameSite=Strict\n     * - Path=/\n     * - Expires={the token expiration date}\n     *\n     * NB! If the generated cookie exceeds 4096 bytes, this method will\n     * strip the model data to the bare minimum to try to fit within the\n     * recommended size in https://www.rfc-editor.org/rfc/rfc6265#section-6.1.\n     */\n    exportToCookie(options?: SerializeOptions, key = defaultCookieKey): string {\n        const defaultOptions: SerializeOptions = {\n            secure: true,\n            sameSite: true,\n            httpOnly: true,\n            path: \"/\",\n        };\n\n        // extract the token expiration date\n        const payload = getTokenPayload(this.token);\n        if (payload?.exp) {\n            defaultOptions.expires = new Date(payload.exp * 1000);\n        } else {\n            defaultOptions.expires = new Date(\"1970-01-01\");\n        }\n\n        // merge with the user defined options\n        options = Object.assign({}, defaultOptions, options);\n\n        const rawData = {\n            token: this.token,\n            record: this.record ? JSON.parse(JSON.stringify(this.record)) : null,\n        };\n\n        let result = cookieSerialize(key, JSON.stringify(rawData), options);\n\n        const resultLength =\n            typeof Blob !== \"undefined\" ? new Blob([result]).size : result.length;\n\n        // strip down the model data to the bare minimum\n        if (rawData.record && resultLength > 4096) {\n            rawData.record = { id: rawData.record?.id, email: rawData.record?.email };\n            const extraProps = [\"collectionId\", \"collectionName\", \"verified\"];\n            for (const prop in this.record) {\n                if (extraProps.includes(prop)) {\n                    rawData.record[prop] = this.record[prop];\n                }\n            }\n            result = cookieSerialize(key, JSON.stringify(rawData), options);\n        }\n\n        return result;\n    }\n\n    /**\n     * Register a callback function that will be called on store change.\n     *\n     * You can set the `fireImmediately` argument to true in order to invoke\n     * the provided callback right after registration.\n     *\n     * Returns a removal function that you could call to \"unsubscribe\" from the changes.\n     */\n    onChange(callback: OnStoreChangeFunc, fireImmediately = false): () => void {\n        this._onChangeCallbacks.push(callback);\n\n        if (fireImmediately) {\n            callback(this.token, this.record);\n        }\n\n        return () => {\n            for (let i = this._onChangeCallbacks.length - 1; i >= 0; i--) {\n                if (this._onChangeCallbacks[i] == callback) {\n                    delete this._onChangeCallbacks[i]; // removes the function reference\n                    this._onChangeCallbacks.splice(i, 1); // reindex the array\n                    return;\n                }\n            }\n        };\n    }\n\n    protected triggerChange(): void {\n        for (const callback of this._onChangeCallbacks) {\n            callback && callback(this.token, this.record);\n        }\n    }\n}\n", "import { BaseAuthStore, AuthRecord } from \"@/stores/BaseAuthStore\";\n\n/**\n * The default token store for browsers with auto fallback\n * to runtime/memory if local storage is undefined (e.g. in node env).\n */\nexport class LocalAuthStore extends BaseAuthStore {\n    private storageFallback: { [key: string]: any } = {};\n    private storageKey: string;\n\n    constructor(storageKey = \"pocketbase_auth\") {\n        super();\n\n        this.storageKey = storageKey;\n\n        this._bindStorageEvent();\n    }\n\n    /**\n     * @inheritdoc\n     */\n    get token(): string {\n        const data = this._storageGet(this.storageKey) || {};\n\n        return data.token || \"\";\n    }\n\n    /**\n     * @inheritdoc\n     */\n    get record(): AuthRecord {\n        const data = this._storageGet(this.storageKey) || {};\n\n        return data.record || data.model || null;\n    }\n\n    /**\n     * @deprecated use `record` instead.\n     */\n    get model(): AuthRecord {\n        return this.record;\n    }\n\n    /**\n     * @inheritdoc\n     */\n    save(token: string, record?: AuthRecord) {\n        this._storageSet(this.storageKey, {\n            token: token,\n            record: record,\n        });\n\n        super.save(token, record);\n    }\n\n    /**\n     * @inheritdoc\n     */\n    clear() {\n        this._storageRemove(this.storageKey);\n\n        super.clear();\n    }\n\n    // ---------------------------------------------------------------\n    // Internal helpers:\n    // ---------------------------------------------------------------\n\n    /**\n     * Retrieves `key` from the browser's local storage\n     * (or runtime/memory if local storage is undefined).\n     */\n    private _storageGet(key: string): any {\n        if (typeof window !== \"undefined\" && window?.localStorage) {\n            const rawValue = window.localStorage.getItem(key) || \"\";\n            try {\n                return JSON.parse(rawValue);\n            } catch (e) {\n                // not a json\n                return rawValue;\n            }\n        }\n\n        // fallback\n        return this.storageFallback[key];\n    }\n\n    /**\n     * Stores a new data in the browser's local storage\n     * (or runtime/memory if local storage is undefined).\n     */\n    private _storageSet(key: string, value: any) {\n        if (typeof window !== \"undefined\" && window?.localStorage) {\n            // store in local storage\n            let normalizedVal = value;\n            if (typeof value !== \"string\") {\n                normalizedVal = JSON.stringify(value);\n            }\n            window.localStorage.setItem(key, normalizedVal);\n        } else {\n            // store in fallback\n            this.storageFallback[key] = value;\n        }\n    }\n\n    /**\n     * Removes `key` from the browser's local storage and the runtime/memory.\n     */\n    private _storageRemove(key: string) {\n        // delete from local storage\n        if (typeof window !== \"undefined\" && window?.localStorage) {\n            window.localStorage?.removeItem(key);\n        }\n\n        // delete from fallback\n        delete this.storageFallback[key];\n    }\n\n    /**\n     * Updates the current store state on localStorage change.\n     */\n    private _bindStorageEvent() {\n        if (\n            typeof window === \"undefined\" ||\n            !window?.localStorage ||\n            !window.addEventListener\n        ) {\n            return;\n        }\n\n        window.addEventListener(\"storage\", (e) => {\n            if (e.key != this.storageKey) {\n                return;\n            }\n\n            const data = this._storageGet(this.storageKey) || {};\n\n            super.save(data.token || \"\", data.record || data.model || null);\n        });\n    }\n}\n", "import Client from \"@/Client\";\n\n/**\n * BaseService class that should be inherited from all API services.\n */\nexport abstract class BaseService {\n    readonly client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { CommonOptions } from \"@/tools/options\";\n\ninterface appleClientSecret {\n    secret: string;\n}\n\nexport class SettingsService extends BaseService {\n    /**\n     * Fetch all available app settings.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getAll(options?: CommonOptions): Promise<{ [key: string]: any }> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/settings\", options);\n    }\n\n    /**\n     * Bulk updates app settings.\n     *\n     * @throws {ClientResponseError}\n     */\n    async update(\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: CommonOptions,\n    ): Promise<{ [key: string]: any }> {\n        options = Object.assign(\n            {\n                method: \"PATCH\",\n                body: bodyParams,\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/settings\", options);\n    }\n\n    /**\n     * Performs a S3 filesystem connection test.\n     *\n     * The currently supported `filesystem` are \"storage\" and \"backups\".\n     *\n     * @throws {ClientResponseError}\n     */\n    async testS3(\n        filesystem: string = \"storage\",\n        options?: CommonOptions,\n    ): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: {\n                    filesystem: filesystem,\n                },\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/settings/test/s3\", options).then(() => true);\n    }\n\n    /**\n     * Sends a test email.\n     *\n     * The possible `emailTemplate` values are:\n     * - verification\n     * - password-reset\n     * - email-change\n     *\n     * @throws {ClientResponseError}\n     */\n    async testEmail(\n        collectionIdOrName: string,\n        toEmail: string,\n        emailTemplate: string,\n        options?: CommonOptions,\n    ): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: {\n                    email: toEmail,\n                    template: emailTemplate,\n                    collection: collectionIdOrName,\n                },\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/settings/test/email\", options).then(() => true);\n    }\n\n    /**\n     * Generates a new Apple OAuth2 client secret.\n     *\n     * @throws {ClientResponseError}\n     */\n    async generateAppleClientSecret(\n        clientId: string,\n        teamId: string,\n        keyId: string,\n        privateKey: string,\n        duration: number,\n        options?: CommonOptions,\n    ): Promise<appleClientSecret> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: {\n                    clientId,\n                    teamId,\n                    keyId,\n                    privateKey,\n                    duration,\n                },\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/settings/apple/generate-client-secret\", options);\n    }\n}\n", "export interface SendOptions extends RequestInit {\n    // for backward compatibility and to minimize the verbosity,\n    // any top-level field that doesn't exist in RequestInit or the\n    // fields below will be treated as query parameter.\n    [key: string]: any;\n\n    /**\n     * Optional custom fetch function to use for sending the request.\n     */\n    fetch?: (url: RequestInfo | URL, config?: RequestInit) => Promise<Response>;\n\n    /**\n     * Custom headers to send with the requests.\n     */\n    headers?: { [key: string]: string };\n\n    /**\n     * The body of the request (serialized automatically for json requests).\n     */\n    body?: any;\n\n    /**\n     * Query parameters that will be appended to the request url.\n     */\n    query?: { [key: string]: any };\n\n    /**\n     * @deprecated use `query` instead\n     *\n     * for backward-compatibility `params` values are merged with `query`,\n     * but this option may get removed in the final v1 release\n     */\n    params?: { [key: string]: any };\n\n    /**\n     * The request identifier that can be used to cancel pending requests.\n     */\n    requestKey?: string | null;\n\n    /**\n     * @deprecated use `requestKey:string` instead\n     */\n    $cancelKey?: string;\n\n    /**\n     * @deprecated use `requestKey:null` instead\n     */\n    $autoCancel?: boolean;\n}\n\nexport interface CommonOptions extends SendOptions {\n    fields?: string;\n}\n\nexport interface ListOptions extends CommonOptions {\n    page?: number;\n    perPage?: number;\n    sort?: string;\n    filter?: string;\n    skipTotal?: boolean;\n}\n\nexport interface FullListOptions extends ListOptions {\n    batch?: number;\n}\n\nexport interface RecordOptions extends CommonOptions {\n    expand?: string;\n}\n\nexport interface RecordListOptions extends ListOptions, RecordOptions {}\n\nexport interface RecordFullListOptions extends FullListOptions, RecordOptions {}\n\nexport interface RecordSubscribeOptions extends SendOptions {\n    fields?: string;\n    filter?: string;\n    expand?: string;\n}\n\nexport interface LogStatsOptions extends CommonOptions {\n    filter?: string;\n}\n\nexport interface FileOptions extends CommonOptions {\n    thumb?: string;\n    download?: boolean;\n}\n\nexport interface AuthOptions extends CommonOptions {\n    /**\n     * If autoRefreshThreshold is set it will take care to auto refresh\n     * when necessary the auth data before each request to ensure that\n     * the auth state is always valid.\n     *\n     * The value must be in seconds, aka. the amount of seconds\n     * that will be subtracted from the current token `exp` claim in order\n     * to determine whether it is going to expire within the specified time threshold.\n     *\n     * For example, if you want to auto refresh the token if it is\n     * going to expire in the next 30mins (or already has expired),\n     * it can be set to `1800`\n     */\n    autoRefreshThreshold?: number;\n}\n\n// -------------------------------------------------------------------\n\n// list of known SendOptions keys (everything else is treated as query param)\nconst knownSendOptionsKeys = [\n    \"requestKey\",\n    \"$cancelKey\",\n    \"$autoCancel\",\n    \"fetch\",\n    \"headers\",\n    \"body\",\n    \"query\",\n    \"params\",\n    // ---,\n    \"cache\",\n    \"credentials\",\n    \"headers\",\n    \"integrity\",\n    \"keepalive\",\n    \"method\",\n    \"mode\",\n    \"redirect\",\n    \"referrer\",\n    \"referrerPolicy\",\n    \"signal\",\n    \"window\",\n];\n\n// modifies in place the provided options by moving unknown send options as query parameters.\nexport function normalizeUnknownQueryParams(options?: SendOptions): void {\n    if (!options) {\n        return;\n    }\n\n    options.query = options.query || {};\n    for (let key in options) {\n        if (knownSendOptionsKeys.includes(key)) {\n            continue;\n        }\n\n        options.query[key] = options[key];\n        delete options[key];\n    }\n}\n\nexport function serializeQueryParams(params: { [key: string]: any }): string {\n    const result: Array<string> = [];\n\n    for (const key in params) {\n        const encodedKey = encodeURIComponent(key);\n        const arrValue = Array.isArray(params[key]) ? params[key] : [params[key]];\n\n        for (let v of arrValue) {\n            v = prepareQueryParamValue(v);\n            if (v === null) {\n                continue;\n            }\n            result.push(encodedKey + \"=\" + v);\n        }\n    }\n\n    return result.join(\"&\");\n}\n\n// encodes and normalizes the provided query param value.\nfunction prepareQueryParamValue(value: any): null | string {\n    if (value === null || typeof value === \"undefined\") {\n        return null;\n    }\n\n    if (value instanceof Date) {\n        return encodeURIComponent(value.toISOString().replace(\"T\", \" \"));\n    }\n\n    if (typeof value === \"object\") {\n        return encodeURIComponent(JSON.stringify(value));\n    }\n\n    return encodeURIComponent(value);\n}\n", "import { ClientResponseError } from \"@/ClientResponseError\";\nimport { BaseService } from \"@/services/BaseService\";\nimport { SendOptions, normalizeUnknownQueryParams } from \"@/tools/options\";\n\ninterface promiseCallbacks {\n    resolve: Function;\n    reject: Function;\n}\n\ntype Subscriptions = { [key: string]: Array<EventListener> };\n\nexport type UnsubscribeFunc = () => Promise<void>;\n\nexport class RealtimeService extends BaseService {\n    clientId: string = \"\";\n\n    private eventSource: EventSource | null = null;\n    private subscriptions: Subscriptions = {};\n    private lastSentSubscriptions: Array<string> = [];\n    private connectTimeoutId: any;\n    private maxConnectTimeout: number = 15000;\n    private reconnectTimeoutId: any;\n    private reconnectAttempts: number = 0;\n    private maxReconnectAttempts: number = Infinity;\n    private predefinedReconnectIntervals: Array<number> = [\n        200, 300, 500, 1000, 1200, 1500, 2000,\n    ];\n    private pendingConnects: Array<promiseCallbacks> = [];\n\n    /**\n     * Returns whether the realtime connection has been established.\n     */\n    get isConnected(): boolean {\n        return !!this.eventSource && !!this.clientId && !this.pendingConnects.length;\n    }\n\n    /**\n     * An optional hook that is invoked when the realtime client disconnects\n     * either when unsubscribing from all subscriptions or when the\n     * connection was interrupted or closed by the server.\n     *\n     * The received argument could be used to determine whether the disconnect\n     * is a result from unsubscribing (`activeSubscriptions.length == 0`)\n     * or because of network/server error (`activeSubscriptions.length > 0`).\n     *\n     * If you want to listen for the opposite, aka. when the client connection is established,\n     * subscribe to the `PB_CONNECT` event.\n     */\n    onDisconnect?: (activeSubscriptions: Array<string>) => void;\n\n    /**\n     * Register the subscription listener.\n     *\n     * You can subscribe multiple times to the same topic.\n     *\n     * If the SSE connection is not started yet,\n     * this method will also initialize it.\n     */\n    async subscribe(\n        topic: string,\n        callback: (data: any) => void,\n        options?: SendOptions,\n    ): Promise<UnsubscribeFunc> {\n        if (!topic) {\n            throw new Error(\"topic must be set.\");\n        }\n\n        let key = topic;\n\n        // serialize and append the topic options (if any)\n        if (options) {\n            options = Object.assign({}, options); // shallow copy\n            normalizeUnknownQueryParams(options);\n            const serialized =\n                \"options=\" +\n                encodeURIComponent(\n                    JSON.stringify({ query: options.query, headers: options.headers }),\n                );\n            key += (key.includes(\"?\") ? \"&\" : \"?\") + serialized;\n        }\n\n        const listener = function (e: Event) {\n            const msgEvent = e as MessageEvent;\n\n            let data;\n            try {\n                data = JSON.parse(msgEvent?.data);\n            } catch {}\n\n            callback(data || {});\n        };\n\n        // store the listener\n        if (!this.subscriptions[key]) {\n            this.subscriptions[key] = [];\n        }\n        this.subscriptions[key].push(listener);\n\n        if (!this.isConnected) {\n            // initialize sse connection\n            await this.connect();\n        } else if (this.subscriptions[key].length === 1) {\n            // send the updated subscriptions (if it is the first for the key)\n            await this.submitSubscriptions();\n        } else {\n            // only register the listener\n            this.eventSource?.addEventListener(key, listener);\n        }\n\n        return async (): Promise<void> => {\n            return this.unsubscribeByTopicAndListener(topic, listener);\n        };\n    }\n\n    /**\n     * Unsubscribe from all subscription listeners with the specified topic.\n     *\n     * If `topic` is not provided, then this method will unsubscribe\n     * from all active subscriptions.\n     *\n     * This method is no-op if there are no active subscriptions.\n     *\n     * The related sse connection will be autoclosed if after the\n     * unsubscribe operation there are no active subscriptions left.\n     */\n    async unsubscribe(topic?: string): Promise<void> {\n        let needToSubmit = false;\n\n        if (!topic) {\n            // remove all subscriptions\n            this.subscriptions = {};\n        } else {\n            // remove all listeners related to the topic\n            const subs = this.getSubscriptionsByTopic(topic);\n            for (let key in subs) {\n                if (!this.hasSubscriptionListeners(key)) {\n                    continue; // already unsubscribed\n                }\n\n                for (let listener of this.subscriptions[key]) {\n                    this.eventSource?.removeEventListener(key, listener);\n                }\n                delete this.subscriptions[key];\n\n                // mark for subscriptions change submit if there are no other listeners\n                if (!needToSubmit) {\n                    needToSubmit = true;\n                }\n            }\n        }\n\n        if (!this.hasSubscriptionListeners()) {\n            // no other active subscriptions -> close the sse connection\n            this.disconnect();\n        } else if (needToSubmit) {\n            await this.submitSubscriptions();\n        }\n    }\n\n    /**\n     * Unsubscribe from all subscription listeners starting with the specified topic prefix.\n     *\n     * This method is no-op if there are no active subscriptions with the specified topic prefix.\n     *\n     * The related sse connection will be autoclosed if after the\n     * unsubscribe operation there are no active subscriptions left.\n     */\n    async unsubscribeByPrefix(keyPrefix: string): Promise<void> {\n        let hasAtleastOneTopic = false;\n        for (let key in this.subscriptions) {\n            // \"?\" so that it can be used as end delimiter for the prefix\n            if (!(key + \"?\").startsWith(keyPrefix)) {\n                continue;\n            }\n\n            hasAtleastOneTopic = true;\n            for (let listener of this.subscriptions[key]) {\n                this.eventSource?.removeEventListener(key, listener);\n            }\n            delete this.subscriptions[key];\n        }\n\n        if (!hasAtleastOneTopic) {\n            return; // nothing to unsubscribe from\n        }\n\n        if (this.hasSubscriptionListeners()) {\n            // submit the deleted subscriptions\n            await this.submitSubscriptions();\n        } else {\n            // no other active subscriptions -> close the sse connection\n            this.disconnect();\n        }\n    }\n\n    /**\n     * Unsubscribe from all subscriptions matching the specified topic and listener function.\n     *\n     * This method is no-op if there are no active subscription with\n     * the specified topic and listener.\n     *\n     * The related sse connection will be autoclosed if after the\n     * unsubscribe operation there are no active subscriptions left.\n     */\n    async unsubscribeByTopicAndListener(\n        topic: string,\n        listener: EventListener,\n    ): Promise<void> {\n        let needToSubmit = false;\n\n        const subs = this.getSubscriptionsByTopic(topic);\n        for (let key in subs) {\n            if (\n                !Array.isArray(this.subscriptions[key]) ||\n                !this.subscriptions[key].length\n            ) {\n                continue; // already unsubscribed\n            }\n\n            let exist = false;\n            for (let i = this.subscriptions[key].length - 1; i >= 0; i--) {\n                if (this.subscriptions[key][i] !== listener) {\n                    continue;\n                }\n\n                exist = true; // has at least one matching listener\n                delete this.subscriptions[key][i]; // removes the function reference\n                this.subscriptions[key].splice(i, 1); // reindex the array\n                this.eventSource?.removeEventListener(key, listener);\n            }\n            if (!exist) {\n                continue;\n            }\n\n            // remove the key from the subscriptions list if there are no other listeners\n            if (!this.subscriptions[key].length) {\n                delete this.subscriptions[key];\n            }\n\n            // mark for subscriptions change submit if there are no other listeners\n            if (!needToSubmit && !this.hasSubscriptionListeners(key)) {\n                needToSubmit = true;\n            }\n        }\n\n        if (!this.hasSubscriptionListeners()) {\n            // no other active subscriptions -> close the sse connection\n            this.disconnect();\n        } else if (needToSubmit) {\n            await this.submitSubscriptions();\n        }\n    }\n\n    private hasSubscriptionListeners(keyToCheck?: string): boolean {\n        this.subscriptions = this.subscriptions || {};\n\n        // check the specified key\n        if (keyToCheck) {\n            return !!this.subscriptions[keyToCheck]?.length;\n        }\n\n        // check for at least one non-empty subscription\n        for (let key in this.subscriptions) {\n            if (!!this.subscriptions[key]?.length) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    private async submitSubscriptions(): Promise<void> {\n        if (!this.clientId) {\n            return; // no client/subscriber\n        }\n\n        // optimistic update\n        this.addAllSubscriptionListeners();\n\n        this.lastSentSubscriptions = this.getNonEmptySubscriptionKeys();\n\n        return this.client\n            .send(\"/api/realtime\", {\n                method: \"POST\",\n                body: {\n                    clientId: this.clientId,\n                    subscriptions: this.lastSentSubscriptions,\n                },\n                requestKey: this.getSubscriptionsCancelKey(),\n            })\n            .catch((err) => {\n                if (err?.isAbort) {\n                    return; // silently ignore aborted pending requests\n                }\n                throw err;\n            });\n    }\n\n    private getSubscriptionsCancelKey(): string {\n        return \"realtime_\" + this.clientId;\n    }\n\n    private getSubscriptionsByTopic(topic: string): Subscriptions {\n        const result: Subscriptions = {};\n\n        // \"?\" so that it can be used as end delimiter for the topic\n        topic = topic.includes(\"?\") ? topic : topic + \"?\";\n\n        for (let key in this.subscriptions) {\n            if ((key + \"?\").startsWith(topic)) {\n                result[key] = this.subscriptions[key];\n            }\n        }\n\n        return result;\n    }\n\n    private getNonEmptySubscriptionKeys(): Array<string> {\n        const result: Array<string> = [];\n\n        for (let key in this.subscriptions) {\n            if (this.subscriptions[key].length) {\n                result.push(key);\n            }\n        }\n\n        return result;\n    }\n\n    private addAllSubscriptionListeners(): void {\n        if (!this.eventSource) {\n            return;\n        }\n\n        this.removeAllSubscriptionListeners();\n\n        for (let key in this.subscriptions) {\n            for (let listener of this.subscriptions[key]) {\n                this.eventSource.addEventListener(key, listener);\n            }\n        }\n    }\n\n    private removeAllSubscriptionListeners(): void {\n        if (!this.eventSource) {\n            return;\n        }\n\n        for (let key in this.subscriptions) {\n            for (let listener of this.subscriptions[key]) {\n                this.eventSource.removeEventListener(key, listener);\n            }\n        }\n    }\n\n    private async connect(): Promise<void> {\n        if (this.reconnectAttempts > 0) {\n            // immediately resolve the promise to avoid indefinitely\n            // blocking the client during reconnection\n            return;\n        }\n\n        return new Promise((resolve, reject) => {\n            this.pendingConnects.push({ resolve, reject });\n\n            if (this.pendingConnects.length > 1) {\n                // all promises will be resolved once the connection is established\n                return;\n            }\n\n            this.initConnect();\n        });\n    }\n\n    private initConnect() {\n        this.disconnect(true);\n\n        // wait up to 15s for connect\n        clearTimeout(this.connectTimeoutId);\n        this.connectTimeoutId = setTimeout(() => {\n            this.connectErrorHandler(new Error(\"EventSource connect took too long.\"));\n        }, this.maxConnectTimeout);\n\n        this.eventSource = new EventSource(this.client.buildURL(\"/api/realtime\"));\n\n        this.eventSource.onerror = (_) => {\n            this.connectErrorHandler(\n                new Error(\"Failed to establish realtime connection.\"),\n            );\n        };\n\n        this.eventSource.addEventListener(\"PB_CONNECT\", (e) => {\n            const msgEvent = e as MessageEvent;\n            this.clientId = msgEvent?.lastEventId;\n\n            this.submitSubscriptions()\n                .then(async () => {\n                    let retries = 3;\n                    while (this.hasUnsentSubscriptions() && retries > 0) {\n                        retries--;\n                        // resubscribe to ensure that the latest topics are submitted\n                        //\n                        // This is needed because missed topics could happen on reconnect\n                        // if after the pending sent `submitSubscriptions()` call another `subscribe()`\n                        // was made before the submit was able to complete.\n                        await this.submitSubscriptions();\n                    }\n                })\n                .then(() => {\n                    for (let p of this.pendingConnects) {\n                        p.resolve();\n                    }\n\n                    // reset connect meta\n                    this.pendingConnects = [];\n                    this.reconnectAttempts = 0;\n                    clearTimeout(this.reconnectTimeoutId);\n                    clearTimeout(this.connectTimeoutId);\n\n                    // propagate the PB_CONNECT event\n                    const connectSubs = this.getSubscriptionsByTopic(\"PB_CONNECT\");\n                    for (let key in connectSubs) {\n                        for (let listener of connectSubs[key]) {\n                            listener(e);\n                        }\n                    }\n                })\n                .catch((err) => {\n                    this.clientId = \"\";\n                    this.connectErrorHandler(err);\n                });\n        });\n    }\n\n    private hasUnsentSubscriptions(): boolean {\n        const latestTopics = this.getNonEmptySubscriptionKeys();\n        if (latestTopics.length != this.lastSentSubscriptions.length) {\n            return true;\n        }\n\n        for (const t of latestTopics) {\n            if (!this.lastSentSubscriptions.includes(t)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    private connectErrorHandler(err: any) {\n        clearTimeout(this.connectTimeoutId);\n        clearTimeout(this.reconnectTimeoutId);\n\n        if (\n            // wasn't previously connected -> direct reject\n            (!this.clientId && !this.reconnectAttempts) ||\n            // was previously connected but the max reconnection limit has been reached\n            this.reconnectAttempts > this.maxReconnectAttempts\n        ) {\n            for (let p of this.pendingConnects) {\n                p.reject(new ClientResponseError(err));\n            }\n            this.pendingConnects = [];\n            this.disconnect();\n            return;\n        }\n\n        // otherwise -> reconnect in the background\n        this.disconnect(true);\n        const timeout =\n            this.predefinedReconnectIntervals[this.reconnectAttempts] ||\n            this.predefinedReconnectIntervals[\n                this.predefinedReconnectIntervals.length - 1\n            ];\n        this.reconnectAttempts++;\n        this.reconnectTimeoutId = setTimeout(() => {\n            this.initConnect();\n        }, timeout);\n    }\n\n    private disconnect(fromReconnect = false): void {\n        if (this.clientId && this.onDisconnect) {\n            this.onDisconnect(Object.keys(this.subscriptions));\n        }\n\n        clearTimeout(this.connectTimeoutId);\n        clearTimeout(this.reconnectTimeoutId);\n        this.removeAllSubscriptionListeners();\n        this.client.cancelRequest(this.getSubscriptionsCancelKey());\n        this.eventSource?.close();\n        this.eventSource = null;\n        this.clientId = \"\";\n\n        if (!fromReconnect) {\n            this.reconnectAttempts = 0;\n\n            // resolve any remaining connect promises\n            //\n            // this is done to avoid unnecessary throwing errors in case\n            // unsubscribe is called before the pending connect promises complete\n            // (see https://github.com/pocketbase/pocketbase/discussions/2897#discussioncomment-6423818)\n            for (let p of this.pendingConnects) {\n                p.resolve();\n            }\n            this.pendingConnects = [];\n        }\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { ClientResponseError } from \"@/ClientResponseError\";\nimport { ListResult } from \"@/tools/dtos\";\nimport { CommonOptions, ListOptions, FullListOptions } from \"@/tools/options\";\n\nexport abstract class CrudService<M> extends BaseService {\n    /**\n     * Base path for the crud actions (without trailing slash, eg. '/admins').\n     */\n    abstract get baseCrudPath(): string;\n\n    /**\n     * Response data decoder.\n     */\n    decode<T = M>(data: { [key: string]: any }): T {\n        return data as T;\n    }\n\n    /**\n     * Returns a promise with all list items batch fetched at once\n     * (by default 500 items per request; to change it set the `batch` query param).\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getFullList<T = M>(options?: FullListOptions): Promise<Array<T>>;\n\n    /**\n     * Legacy version of getFullList with explicitly specified batch size.\n     */\n    async getFullList<T = M>(batch?: number, options?: ListOptions): Promise<Array<T>>;\n\n    async getFullList<T = M>(\n        batchOrqueryParams?: number | FullListOptions,\n        options?: ListOptions,\n    ): Promise<Array<T>> {\n        if (typeof batchOrqueryParams == \"number\") {\n            return this._getFullList<T>(batchOrqueryParams, options);\n        }\n\n        options = Object.assign({}, batchOrqueryParams, options);\n\n        let batch = 500;\n        if (options.batch) {\n            batch = options.batch;\n            delete options.batch;\n        }\n\n        return this._getFullList<T>(batch, options);\n    }\n\n    /**\n     * Returns paginated items list.\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getList<T = M>(\n        page = 1,\n        perPage = 30,\n        options?: ListOptions,\n    ): Promise<ListResult<T>> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        options.query = Object.assign(\n            {\n                page: page,\n                perPage: perPage,\n            },\n            options.query,\n        );\n\n        return this.client.send(this.baseCrudPath, options).then((responseData: any) => {\n            responseData.items =\n                responseData.items?.map((item: any) => {\n                    return this.decode<T>(item);\n                }) || [];\n\n            return responseData;\n        });\n    }\n\n    /**\n     * Returns the first found item by the specified filter.\n     *\n     * Internally it calls `getList(1, 1, { filter, skipTotal })` and\n     * returns the first found item.\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * For consistency with `getOne`, this method will throw a 404\n     * ClientResponseError if no item was found.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getFirstListItem<T = M>(filter: string, options?: CommonOptions): Promise<T> {\n        options = Object.assign(\n            {\n                requestKey: \"one_by_filter_\" + this.baseCrudPath + \"_\" + filter,\n            },\n            options,\n        );\n\n        options.query = Object.assign(\n            {\n                filter: filter,\n                skipTotal: 1,\n            },\n            options.query,\n        );\n\n        return this.getList<T>(1, 1, options).then((result) => {\n            if (!result?.items?.length) {\n                throw new ClientResponseError({\n                    status: 404,\n                    response: {\n                        code: 404,\n                        message: \"The requested resource wasn't found.\",\n                        data: {},\n                    },\n                });\n            }\n\n            return result.items[0];\n        });\n    }\n\n    /**\n     * Returns single item by its id.\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * If `id` is empty it will throw a 404 error.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getOne<T = M>(id: string, options?: CommonOptions): Promise<T> {\n        if (!id) {\n            throw new ClientResponseError({\n                url: this.client.buildURL(this.baseCrudPath + \"/\"),\n                status: 404,\n                response: {\n                    code: 404,\n                    message: \"Missing required record id.\",\n                    data: {},\n                },\n            });\n        }\n\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(this.baseCrudPath + \"/\" + encodeURIComponent(id), options)\n            .then((responseData: any) => this.decode<T>(responseData));\n    }\n\n    /**\n     * Creates a new item.\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async create<T = M>(\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: CommonOptions,\n    ): Promise<T> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: bodyParams,\n            },\n            options,\n        );\n\n        return this.client\n            .send(this.baseCrudPath, options)\n            .then((responseData: any) => this.decode<T>(responseData));\n    }\n\n    /**\n     * Updates an existing item by its id.\n     *\n     * You can use the generic T to supply a wrapper type of the crud model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async update<T = M>(\n        id: string,\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: CommonOptions,\n    ): Promise<T> {\n        options = Object.assign(\n            {\n                method: \"PATCH\",\n                body: bodyParams,\n            },\n            options,\n        );\n\n        return this.client\n            .send(this.baseCrudPath + \"/\" + encodeURIComponent(id), options)\n            .then((responseData: any) => this.decode<T>(responseData));\n    }\n\n    /**\n     * Deletes an existing item by its id.\n     *\n     * @throws {ClientResponseError}\n     */\n    async delete(id: string, options?: CommonOptions): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"DELETE\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(this.baseCrudPath + \"/\" + encodeURIComponent(id), options)\n            .then(() => true);\n    }\n\n    /**\n     * Returns a promise with all list items batch fetched at once.\n     */\n    protected _getFullList<T = M>(\n        batchSize = 500,\n        options?: ListOptions,\n    ): Promise<Array<T>> {\n        options = options || {};\n        options.query = Object.assign(\n            {\n                skipTotal: 1,\n            },\n            options.query,\n        );\n\n        let result: Array<T> = [];\n\n        let request = async (page: number): Promise<Array<any>> => {\n            return this.getList(page, batchSize || 500, options).then((list) => {\n                const castedList = list as any as ListResult<T>;\n                const items = castedList.items;\n\n                result = result.concat(items);\n\n                if (items.length == list.perPage) {\n                    return request(page + 1);\n                }\n\n                return result;\n            });\n        };\n\n        return request(1);\n    }\n}\n", "import { SendOptions } from \"@/tools/options\";\n\nexport function normalizeLegacyOptionsArgs(\n    legacyWarn: string,\n    baseOptions: SendOptions,\n    bodyOrOptions?: any,\n    query?: any,\n): SendOptions {\n    const hasBodyOrOptions = typeof bodyOrOptions !== \"undefined\";\n    const hasQuery = typeof query !== \"undefined\";\n\n    if (!hasQuery && !hasBodyOrOptions) {\n        return baseOptions;\n    }\n\n    if (hasQuery) {\n        console.warn(legacyWarn);\n        baseOptions.body = Object.assign({}, baseOptions.body, bodyOrOptions);\n        baseOptions.query = Object.assign({}, baseOptions.query, query);\n\n        return baseOptions;\n    }\n\n    return Object.assign(baseOptions, bodyOrOptions);\n}\n", "import Client from \"@/Client\";\nimport { isTokenExpired } from \"@/tools/jwt\";\n\n// reset previous auto refresh registrations\nexport function resetAutoRefresh(client: Client) {\n    (client as any)._resetAutoRefresh?.();\n}\n\nexport function registerAutoRefresh(\n    client: Client,\n    threshold: number,\n    refreshFunc: () => Promise<any>,\n    reauthenticateFunc: () => Promise<any>,\n) {\n    resetAutoRefresh(client);\n\n    const oldBeforeSend = client.beforeSend;\n    const oldModel = client.authStore.record;\n\n    // unset the auto refresh in case the auth store was cleared\n    // OR a new model was authenticated\n    const unsubStoreChange = client.authStore.onChange((newToken, model) => {\n        if (\n            !newToken ||\n            model?.id != oldModel?.id ||\n            ((model?.collectionId || oldModel?.collectionId) &&\n                model?.collectionId != oldModel?.collectionId)\n        ) {\n            resetAutoRefresh(client);\n        }\n    });\n\n    // initialize a reset function and attach it dynamically to the client\n    (client as any)._resetAutoRefresh = function () {\n        unsubStoreChange();\n        client.beforeSend = oldBeforeSend;\n        delete (client as any)._resetAutoRefresh;\n    };\n\n    client.beforeSend = async (url, sendOptions) => {\n        const oldToken = client.authStore.token;\n\n        if (sendOptions.query?.autoRefresh) {\n            return oldBeforeSend ? oldBeforeSend(url, sendOptions) : { url, sendOptions };\n        }\n\n        let isValid = client.authStore.isValid;\n        if (\n            // is loosely valid\n            isValid &&\n            // but it is going to expire in the next \"threshold\" seconds\n            isTokenExpired(client.authStore.token, threshold)\n        ) {\n            try {\n                await refreshFunc();\n            } catch (_) {\n                isValid = false;\n            }\n        }\n\n        // still invalid -> reauthenticate\n        if (!isValid) {\n            await reauthenticateFunc();\n        }\n\n        // the request wasn't sent with a custom token\n        const headers = sendOptions.headers || {};\n        for (let key in headers) {\n            if (\n                key.toLowerCase() == \"authorization\" &&\n                // the request wasn't sent with a custom token\n                oldToken == headers[key] &&\n                client.authStore.token\n            ) {\n                // set the latest store token\n                headers[key] = client.authStore.token;\n                break;\n            }\n        }\n        sendOptions.headers = headers;\n\n        return oldBeforeSend ? oldBeforeSend(url, sendOptions) : { url, sendOptions };\n    };\n}\n", "import Client from \"@/Client\";\nimport { ClientResponseError } from \"@/ClientResponseError\";\nimport { RealtimeService, UnsubscribeFunc } from \"@/services/RealtimeService\";\nimport { BaseAuthStore } from \"@/stores/BaseAuthStore\";\nimport { CrudService } from \"@/services/CrudService\";\nimport { ListResult, RecordModel } from \"@/tools/dtos\";\nimport { normalizeLegacyOptionsArgs } from \"@/tools/legacy\";\nimport {\n    CommonOptions,\n    RecordFullListOptions,\n    RecordListOptions,\n    RecordOptions,\n    SendOptions,\n    RecordSubscribeOptions,\n} from \"@/tools/options\";\nimport { getTokenPayload } from \"@/tools/jwt\";\nimport { registerAutoRefresh, resetAutoRefresh } from \"@/tools/refresh\";\n\nexport interface RecordAuthResponse<T = RecordModel> {\n    /**\n     * The signed PocketBase auth record.\n     */\n    record: T;\n\n    /**\n     * The PocketBase record auth token.\n     *\n     * If you are looking for the OAuth2 access and refresh tokens\n     * they are available under the `meta.accessToken` and `meta.refreshToken` props.\n     */\n    token: string;\n\n    /**\n     * Auth meta data usually filled when OAuth2 is used.\n     */\n    meta?: { [key: string]: any };\n}\n\nexport interface AuthProviderInfo {\n    name: string;\n    displayName: string;\n    state: string;\n    authURL: string;\n    codeVerifier: string;\n    codeChallenge: string;\n    codeChallengeMethod: string;\n}\n\nexport interface AuthMethodsList {\n    mfa: {\n        enabled: boolean;\n        duration: number;\n    };\n    otp: {\n        enabled: boolean;\n        duration: number;\n    };\n    password: {\n        enabled: boolean;\n        identityFields: Array<string>;\n    };\n    oauth2: {\n        enabled: boolean;\n        providers: Array<AuthProviderInfo>;\n    };\n}\n\nexport interface RecordSubscription<T = RecordModel> {\n    action: string; // eg. create, update, delete\n    record: T;\n}\n\nexport type OAuth2UrlCallback = (url: string) => void | Promise<void>;\n\nexport interface OAuth2AuthConfig extends SendOptions {\n    // the name of the OAuth2 provider (eg. \"google\")\n    provider: string;\n\n    // custom scopes to overwrite the default ones\n    scopes?: Array<string>;\n\n    // optional record create data\n    createData?: { [key: string]: any };\n\n    // optional callback that is triggered after the OAuth2 sign-in/sign-up url generation\n    urlCallback?: OAuth2UrlCallback;\n\n    // optional query params to send with the PocketBase auth request (eg. fields, expand, etc.)\n    query?: RecordOptions;\n}\n\nexport interface OTPResponse {\n    otpId: string;\n}\n\nexport class RecordService<M = RecordModel> extends CrudService<M> {\n    readonly collectionIdOrName: string;\n\n    constructor(client: Client, collectionIdOrName: string) {\n        super(client);\n\n        this.collectionIdOrName = collectionIdOrName;\n    }\n\n    /**\n     * @inheritdoc\n     */\n    get baseCrudPath(): string {\n        return this.baseCollectionPath + \"/records\";\n    }\n\n    /**\n     * Returns the current collection service base path.\n     */\n    get baseCollectionPath(): string {\n        return \"/api/collections/\" + encodeURIComponent(this.collectionIdOrName);\n    }\n\n    /**\n     * Returns whether the current service collection is superusers.\n     */\n    get isSuperusers(): boolean {\n        return (\n            this.collectionIdOrName == \"_superusers\" ||\n            this.collectionIdOrName == \"_pbc_2773867675\"\n        );\n    }\n\n    // ---------------------------------------------------------------\n    // Realtime handlers\n    // ---------------------------------------------------------------\n\n    /**\n     * Subscribe to realtime changes to the specified topic (\"*\" or record id).\n     *\n     * If `topic` is the wildcard \"*\", then this method will subscribe to\n     * any record changes in the collection.\n     *\n     * If `topic` is a record id, then this method will subscribe only\n     * to changes of the specified record id.\n     *\n     * It's OK to subscribe multiple times to the same topic.\n     * You can use the returned `UnsubscribeFunc` to remove only a single subscription.\n     * Or use `unsubscribe(topic)` if you want to remove all subscriptions attached to the topic.\n     */\n    async subscribe<T = M>(\n        topic: string,\n        callback: (data: RecordSubscription<T>) => void,\n        options?: RecordSubscribeOptions,\n    ): Promise<UnsubscribeFunc> {\n        if (!topic) {\n            throw new Error(\"Missing topic.\");\n        }\n\n        if (!callback) {\n            throw new Error(\"Missing subscription callback.\");\n        }\n\n        return this.client.realtime.subscribe(\n            this.collectionIdOrName + \"/\" + topic,\n            callback,\n            options,\n        );\n    }\n\n    /**\n     * Unsubscribe from all subscriptions of the specified topic\n     * (\"*\" or record id).\n     *\n     * If `topic` is not set, then this method will unsubscribe from\n     * all subscriptions associated to the current collection.\n     */\n    async unsubscribe(topic?: string): Promise<void> {\n        // unsubscribe from the specified topic\n        if (topic) {\n            return this.client.realtime.unsubscribe(\n                this.collectionIdOrName + \"/\" + topic,\n            );\n        }\n\n        // unsubscribe from everything related to the collection\n        return this.client.realtime.unsubscribeByPrefix(this.collectionIdOrName);\n    }\n\n    // ---------------------------------------------------------------\n    // Crud handers\n    // ---------------------------------------------------------------\n    /**\n     * @inheritdoc\n     */\n    async getFullList<T = M>(options?: RecordFullListOptions): Promise<Array<T>>;\n\n    /**\n     * @inheritdoc\n     */\n    async getFullList<T = M>(\n        batch?: number,\n        options?: RecordListOptions,\n    ): Promise<Array<T>>;\n\n    /**\n     * @inheritdoc\n     */\n    async getFullList<T = M>(\n        batchOrOptions?: number | RecordFullListOptions,\n        options?: RecordListOptions,\n    ): Promise<Array<T>> {\n        if (typeof batchOrOptions == \"number\") {\n            return super.getFullList<T>(batchOrOptions, options);\n        }\n\n        const params = Object.assign({}, batchOrOptions, options);\n\n        return super.getFullList<T>(params);\n    }\n\n    /**\n     * @inheritdoc\n     */\n    async getList<T = M>(\n        page = 1,\n        perPage = 30,\n        options?: RecordListOptions,\n    ): Promise<ListResult<T>> {\n        return super.getList<T>(page, perPage, options);\n    }\n\n    /**\n     * @inheritdoc\n     */\n    async getFirstListItem<T = M>(\n        filter: string,\n        options?: RecordListOptions,\n    ): Promise<T> {\n        return super.getFirstListItem<T>(filter, options);\n    }\n\n    /**\n     * @inheritdoc\n     */\n    async getOne<T = M>(id: string, options?: RecordOptions): Promise<T> {\n        return super.getOne<T>(id, options);\n    }\n\n    /**\n     * @inheritdoc\n     */\n    async create<T = M>(\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: RecordOptions,\n    ): Promise<T> {\n        return super.create<T>(bodyParams, options);\n    }\n\n    /**\n     * @inheritdoc\n     *\n     * If the current `client.authStore.record` matches with the updated id, then\n     * on success the `client.authStore.record` will be updated with the new response record fields.\n     */\n    async update<T = M>(\n        id: string,\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: RecordOptions,\n    ): Promise<T> {\n        return super.update<RecordModel>(id, bodyParams, options).then((item) => {\n            if (\n                // is record auth\n                this.client.authStore.record?.id === item?.id &&\n                (this.client.authStore.record?.collectionId === this.collectionIdOrName ||\n                    this.client.authStore.record?.collectionName ===\n                        this.collectionIdOrName)\n            ) {\n                let authExpand = Object.assign({}, this.client.authStore.record.expand);\n                let authRecord = Object.assign({}, this.client.authStore.record, item);\n                if (authExpand) {\n                    // for now \"merge\" only top-level expand\n                    authRecord.expand = Object.assign(authExpand, item.expand);\n                }\n\n                this.client.authStore.save(this.client.authStore.token, authRecord);\n            }\n\n            return item as any as T;\n        });\n    }\n\n    /**\n     * @inheritdoc\n     *\n     * If the current `client.authStore.record` matches with the deleted id,\n     * then on success the `client.authStore` will be cleared.\n     */\n    async delete(id: string, options?: CommonOptions): Promise<boolean> {\n        return super.delete(id, options).then((success) => {\n            if (\n                success &&\n                // is record auth\n                this.client.authStore.record?.id === id &&\n                (this.client.authStore.record?.collectionId === this.collectionIdOrName ||\n                    this.client.authStore.record?.collectionName ===\n                        this.collectionIdOrName)\n            ) {\n                this.client.authStore.clear();\n            }\n\n            return success;\n        });\n    }\n\n    // ---------------------------------------------------------------\n    // Auth handlers\n    // ---------------------------------------------------------------\n\n    /**\n     * Prepare successful collection authorization response.\n     */\n    protected authResponse<T = M>(responseData: any): RecordAuthResponse<T> {\n        const record = this.decode(responseData?.record || {});\n\n        this.client.authStore.save(responseData?.token, record as any);\n\n        return Object.assign({}, responseData, {\n            // normalize common fields\n            token: responseData?.token || \"\",\n            record: record as any as T,\n        });\n    }\n\n    /**\n     * Returns all available collection auth methods.\n     *\n     * @throws {ClientResponseError}\n     */\n    async listAuthMethods(options?: CommonOptions): Promise<AuthMethodsList> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n                // @todo remove after deleting the pre v0.23 API response fields\n                fields: \"mfa,otp,password,oauth2\",\n            },\n            options,\n        );\n\n        return this.client.send(this.baseCollectionPath + \"/auth-methods\", options);\n    }\n\n    /**\n     * Authenticate a single auth collection record via its username/email and password.\n     *\n     * On success, this method also automatically updates\n     * the client's AuthStore data and returns:\n     * - the authentication token\n     * - the authenticated record model\n     *\n     * @throws {ClientResponseError}\n     */\n    async authWithPassword<T = M>(\n        usernameOrEmail: string,\n        password: string,\n        options?: RecordOptions,\n    ): Promise<RecordAuthResponse<T>> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: {\n                    identity: usernameOrEmail,\n                    password: password,\n                },\n            },\n            options,\n        );\n\n        // note: consider to deprecate\n        let autoRefreshThreshold;\n        if (this.isSuperusers) {\n            autoRefreshThreshold = options.autoRefreshThreshold;\n            delete options.autoRefreshThreshold;\n            if (!options.autoRefresh) {\n                resetAutoRefresh(this.client);\n            }\n        }\n\n        let authData = await this.client.send(\n            this.baseCollectionPath + \"/auth-with-password\",\n            options,\n        );\n\n        authData = this.authResponse<T>(authData);\n\n        if (autoRefreshThreshold && this.isSuperusers) {\n            registerAutoRefresh(\n                this.client,\n                autoRefreshThreshold,\n                () => this.authRefresh({ autoRefresh: true }),\n                () =>\n                    this.authWithPassword<T>(\n                        usernameOrEmail,\n                        password,\n                        Object.assign({ autoRefresh: true }, options),\n                    ),\n            );\n        }\n\n        return authData;\n    }\n\n    /**\n     * Authenticate a single auth collection record with OAuth2 code.\n     *\n     * If you don't have an OAuth2 code you may also want to check `authWithOAuth2` method.\n     *\n     * On success, this method also automatically updates\n     * the client's AuthStore data and returns:\n     * - the authentication token\n     * - the authenticated record model\n     * - the OAuth2 account data (eg. name, email, avatar, etc.)\n     *\n     * @throws {ClientResponseError}\n     */\n    async authWithOAuth2Code<T = M>(\n        provider: string,\n        code: string,\n        codeVerifier: string,\n        redirectURL: string,\n        createData?: { [key: string]: any },\n        options?: RecordOptions,\n    ): Promise<RecordAuthResponse<T>>;\n\n    /**\n     * @deprecated\n     * Consider using authWithOAuth2Code(provider, code, codeVerifier, redirectURL, createdData, options?).\n     */\n    async authWithOAuth2Code<T = M>(\n        provider: string,\n        code: string,\n        codeVerifier: string,\n        redirectURL: string,\n        createData?: { [key: string]: any },\n        body?: any,\n        query?: any,\n    ): Promise<RecordAuthResponse<T>>;\n\n    async authWithOAuth2Code<T = M>(\n        provider: string,\n        code: string,\n        codeVerifier: string,\n        redirectURL: string,\n        createData?: { [key: string]: any },\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<RecordAuthResponse<T>> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                provider: provider,\n                code: code,\n                codeVerifier: codeVerifier,\n                redirectURL: redirectURL,\n                createData: createData,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of authWithOAuth2Code(provider, code, codeVerifier, redirectURL, createData?, body?, query?) is deprecated. Consider replacing it with authWithOAuth2Code(provider, code, codeVerifier, redirectURL, createData?, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/auth-with-oauth2\", options)\n            .then((data) => this.authResponse<T>(data));\n    }\n\n    /**\n     * @deprecated This form of authWithOAuth2 is deprecated.\n     *\n     * Please use `authWithOAuth2Code()` OR its simplified realtime version\n     * as shown in https://pocketbase.io/docs/authentication/#oauth2-integration.\n     */\n    async authWithOAuth2<T = M>(\n        provider: string,\n        code: string,\n        codeVerifier: string,\n        redirectURL: string,\n        createData?: { [key: string]: any },\n        bodyParams?: { [key: string]: any },\n        queryParams?: RecordOptions,\n    ): Promise<RecordAuthResponse<T>>;\n\n    /**\n     * Authenticate a single auth collection record with OAuth2\n     * **without custom redirects, deeplinks or even page reload**.\n     *\n     * This method initializes a one-off realtime subscription and will\n     * open a popup window with the OAuth2 vendor page to authenticate.\n     * Once the external OAuth2 sign-in/sign-up flow is completed, the popup\n     * window will be automatically closed and the OAuth2 data sent back\n     * to the user through the previously established realtime connection.\n     *\n     * You can specify an optional `urlCallback` prop to customize\n     * the default url `window.open` behavior.\n     *\n     * On success, this method also automatically updates\n     * the client's AuthStore data and returns:\n     * - the authentication token\n     * - the authenticated record model\n     * - the OAuth2 account data (eg. name, email, avatar, etc.)\n     *\n     * Example:\n     *\n     * ```js\n     * const authData = await pb.collection(\"users\").authWithOAuth2({\n     *     provider: \"google\",\n     * })\n     * ```\n     *\n     * Note1: When creating the OAuth2 app in the provider dashboard\n     * you have to configure `https://yourdomain.com/api/oauth2-redirect`\n     * as redirect URL.\n     *\n     * Note2: Safari may block the default `urlCallback` popup because\n     * it doesn't allow `window.open` calls as part of an `async` click functions.\n     * To workaround this you can either change your click handler to not be marked as `async`\n     * OR manually call `window.open` before your `async` function and use the\n     * window reference in your own custom `urlCallback` (see https://github.com/pocketbase/pocketbase/discussions/2429#discussioncomment-5943061).\n     * For example:\n     * ```js\n     * <button id=\"btn\">Login with Gitlab</button>\n     * ...\n     * document.getElementById(\"btn\").addEventListener(\"click\", () => {\n     *     pb.collection(\"users\").authWithOAuth2({\n     *         provider: \"gitlab\",\n     *     }).then((authData) => {\n     *         console.log(authData)\n     *     }).catch((err) => {\n     *         console.log(err, err.originalError);\n     *     });\n     * })\n     * ```\n     *\n     * @throws {ClientResponseError}\n     */\n    async authWithOAuth2<T = M>(\n        options: OAuth2AuthConfig,\n    ): Promise<RecordAuthResponse<T>>;\n\n    authWithOAuth2<T = M>(...args: any): Promise<RecordAuthResponse<T>> {\n        // fallback to legacy format\n        if (args.length > 1 || typeof args?.[0] === \"string\") {\n            console.warn(\n                \"PocketBase: This form of authWithOAuth2() is deprecated and may get removed in the future. Please replace with authWithOAuth2Code() OR use the authWithOAuth2() realtime form as shown in https://pocketbase.io/docs/authentication/#oauth2-integration.\",\n            );\n            return this.authWithOAuth2Code<T>(\n                args?.[0] || \"\",\n                args?.[1] || \"\",\n                args?.[2] || \"\",\n                args?.[3] || \"\",\n                args?.[4] || {},\n                args?.[5] || {},\n                args?.[6] || {},\n            );\n        }\n\n        const config = args?.[0] || {};\n\n        // open a new popup window in case config.urlCallback is not set\n        //\n        // note: it is opened before any async calls due to Safari restrictions\n        // (see https://github.com/pocketbase/pocketbase/discussions/2429#discussioncomment-5943061)\n        let eagerDefaultPopup: Window | null = null;\n        if (!config.urlCallback) {\n            eagerDefaultPopup = openBrowserPopup(undefined);\n        }\n\n        // initialize a one-off realtime service\n        const realtime = new RealtimeService(this.client);\n\n        function cleanup() {\n            eagerDefaultPopup?.close();\n            realtime.unsubscribe();\n        }\n\n        const requestKeyOptions: SendOptions = {};\n        const requestKey = config.requestKey;\n        if (requestKey) {\n            requestKeyOptions.requestKey = requestKey;\n        }\n\n        return this.listAuthMethods(requestKeyOptions)\n            .then((authMethods) => {\n                const provider = authMethods.oauth2.providers.find(\n                    (p) => p.name === config.provider,\n                );\n                if (!provider) {\n                    throw new ClientResponseError(\n                        new Error(`Missing or invalid provider \"${config.provider}\".`),\n                    );\n                }\n\n                const redirectURL = this.client.buildURL(\"/api/oauth2-redirect\");\n\n                // find the AbortController associated with the current request key (if any)\n                const cancelController = requestKey\n                    ? this.client[\"cancelControllers\"]?.[requestKey]\n                    : undefined;\n                if (cancelController) {\n                    cancelController.signal.onabort = () => {\n                        cleanup();\n                    };\n                }\n\n                return new Promise(async (resolve, reject) => {\n                    try {\n                        await realtime.subscribe(\"@oauth2\", async (e) => {\n                            const oldState = realtime.clientId;\n\n                            try {\n                                if (!e.state || oldState !== e.state) {\n                                    throw new Error(\"State parameters don't match.\");\n                                }\n\n                                if (e.error || !e.code) {\n                                    throw new Error(\n                                        \"OAuth2 redirect error or missing code: \" +\n                                            e.error,\n                                    );\n                                }\n\n                                // clear the non SendOptions props\n                                const options = Object.assign({}, config);\n                                delete options.provider;\n                                delete options.scopes;\n                                delete options.createData;\n                                delete options.urlCallback;\n\n                                // reset the cancelController listener as it will be triggered by the next api call\n                                if (cancelController?.signal?.onabort) {\n                                    cancelController.signal.onabort = null;\n                                }\n\n                                const authData = await this.authWithOAuth2Code<T>(\n                                    provider.name,\n                                    e.code,\n                                    provider.codeVerifier,\n                                    redirectURL,\n                                    config.createData,\n                                    options,\n                                );\n\n                                resolve(authData);\n                            } catch (err) {\n                                reject(new ClientResponseError(err));\n                            }\n\n                            cleanup();\n                        });\n\n                        const replacements: { [key: string]: any } = {\n                            state: realtime.clientId,\n                        };\n                        if (config.scopes?.length) {\n                            replacements[\"scope\"] = config.scopes.join(\" \");\n                        }\n\n                        const url = this._replaceQueryParams(\n                            provider.authURL + redirectURL,\n                            replacements,\n                        );\n\n                        let urlCallback =\n                            config.urlCallback ||\n                            function (url: string) {\n                                if (eagerDefaultPopup) {\n                                    eagerDefaultPopup.location.href = url;\n                                } else {\n                                    // it could have been blocked due to its empty initial url,\n                                    // try again...\n                                    eagerDefaultPopup = openBrowserPopup(url);\n                                }\n                            };\n\n                        await urlCallback(url);\n                    } catch (err) {\n                        cleanup();\n                        reject(new ClientResponseError(err));\n                    }\n                });\n            })\n            .catch((err) => {\n                cleanup();\n                throw err; // rethrow\n            }) as Promise<RecordAuthResponse<T>>;\n    }\n\n    /**\n     * Refreshes the current authenticated record instance and\n     * returns a new token and record data.\n     *\n     * On success this method also automatically updates the client's AuthStore.\n     *\n     * @throws {ClientResponseError}\n     */\n    async authRefresh<T = M>(options?: RecordOptions): Promise<RecordAuthResponse<T>>;\n\n    /**\n     * @deprecated\n     * Consider using authRefresh(options?).\n     */\n    async authRefresh<T = M>(body?: any, query?: any): Promise<RecordAuthResponse<T>>;\n\n    async authRefresh<T = M>(\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<RecordAuthResponse<T>> {\n        let options: any = {\n            method: \"POST\",\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of authRefresh(body?, query?) is deprecated. Consider replacing it with authRefresh(options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/auth-refresh\", options)\n            .then((data) => this.authResponse<T>(data));\n    }\n\n    /**\n     * Sends auth record password reset request.\n     *\n     * @throws {ClientResponseError}\n     */\n    async requestPasswordReset(email: string, options?: CommonOptions): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using requestPasswordReset(email, options?).\n     */\n    async requestPasswordReset(email: string, body?: any, query?: any): Promise<boolean>;\n\n    async requestPasswordReset(\n        email: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                email: email,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of requestPasswordReset(email, body?, query?) is deprecated. Consider replacing it with requestPasswordReset(email, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/request-password-reset\", options)\n            .then(() => true);\n    }\n\n    /**\n     * Confirms auth record password reset request.\n     *\n     * @throws {ClientResponseError}\n     */\n    async confirmPasswordReset(\n        passwordResetToken: string,\n        password: string,\n        passwordConfirm: string,\n        options?: CommonOptions,\n    ): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using confirmPasswordReset(passwordResetToken, password, passwordConfirm, options?).\n     */\n    async confirmPasswordReset(\n        passwordResetToken: string,\n        password: string,\n        passwordConfirm: string,\n        body?: any,\n        query?: any,\n    ): Promise<boolean>;\n\n    async confirmPasswordReset(\n        passwordResetToken: string,\n        password: string,\n        passwordConfirm: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                token: passwordResetToken,\n                password: password,\n                passwordConfirm: passwordConfirm,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of confirmPasswordReset(token, password, passwordConfirm, body?, query?) is deprecated. Consider replacing it with confirmPasswordReset(token, password, passwordConfirm, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/confirm-password-reset\", options)\n            .then(() => true);\n    }\n\n    /**\n     * Sends auth record verification email request.\n     *\n     * @throws {ClientResponseError}\n     */\n    async requestVerification(email: string, options?: CommonOptions): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using requestVerification(email, options?).\n     */\n    async requestVerification(email: string, body?: any, query?: any): Promise<boolean>;\n\n    async requestVerification(\n        email: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                email: email,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of requestVerification(email, body?, query?) is deprecated. Consider replacing it with requestVerification(email, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/request-verification\", options)\n            .then(() => true);\n    }\n\n    /**\n     * Confirms auth record email verification request.\n     *\n     * If the current `client.authStore.record` matches with the auth record from the token,\n     * then on success the `client.authStore.record.verified` will be updated to `true`.\n     *\n     * @throws {ClientResponseError}\n     */\n    async confirmVerification(\n        verificationToken: string,\n        options?: CommonOptions,\n    ): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using confirmVerification(verificationToken, options?).\n     */\n    async confirmVerification(\n        verificationToken: string,\n        body?: any,\n        query?: any,\n    ): Promise<boolean>;\n\n    async confirmVerification(\n        verificationToken: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                token: verificationToken,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of confirmVerification(token, body?, query?) is deprecated. Consider replacing it with confirmVerification(token, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/confirm-verification\", options)\n            .then(() => {\n                // on success manually update the current auth record verified state\n                const payload = getTokenPayload(verificationToken);\n                const model = this.client.authStore.record;\n                if (\n                    model &&\n                    !model.verified &&\n                    model.id === payload.id &&\n                    model.collectionId === payload.collectionId\n                ) {\n                    model.verified = true;\n                    this.client.authStore.save(this.client.authStore.token, model);\n                }\n\n                return true;\n            });\n    }\n\n    /**\n     * Sends an email change request to the authenticated record model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async requestEmailChange(newEmail: string, options?: CommonOptions): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using requestEmailChange(newEmail, options?).\n     */\n    async requestEmailChange(newEmail: string, body?: any, query?: any): Promise<boolean>;\n\n    async requestEmailChange(\n        newEmail: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                newEmail: newEmail,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of requestEmailChange(newEmail, body?, query?) is deprecated. Consider replacing it with requestEmailChange(newEmail, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/request-email-change\", options)\n            .then(() => true);\n    }\n\n    /**\n     * Confirms auth record's new email address.\n     *\n     * If the current `client.authStore.record` matches with the auth record from the token,\n     * then on success the `client.authStore` will be cleared.\n     *\n     * @throws {ClientResponseError}\n     */\n    async confirmEmailChange(\n        emailChangeToken: string,\n        password: string,\n        options?: CommonOptions,\n    ): Promise<boolean>;\n\n    /**\n     * @deprecated\n     * Consider using confirmEmailChange(emailChangeToken, password, options?).\n     */\n    async confirmEmailChange(\n        emailChangeToken: string,\n        password: string,\n        body?: any,\n        query?: any,\n    ): Promise<boolean>;\n\n    async confirmEmailChange(\n        emailChangeToken: string,\n        password: string,\n        bodyOrOptions?: any,\n        query?: any,\n    ): Promise<boolean> {\n        let options: any = {\n            method: \"POST\",\n            body: {\n                token: emailChangeToken,\n                password: password,\n            },\n        };\n\n        options = normalizeLegacyOptionsArgs(\n            \"This form of confirmEmailChange(token, password, body?, query?) is deprecated. Consider replacing it with confirmEmailChange(token, password, options?).\",\n            options,\n            bodyOrOptions,\n            query,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/confirm-email-change\", options)\n            .then(() => {\n                const payload = getTokenPayload(emailChangeToken);\n                const model = this.client.authStore.record;\n                if (\n                    model &&\n                    model.id === payload.id &&\n                    model.collectionId === payload.collectionId\n                ) {\n                    this.client.authStore.clear();\n                }\n\n                return true;\n            });\n    }\n\n    /**\n     * @deprecated use collection(\"_externalAuths\").*\n     *\n     * Lists all linked external auth providers for the specified auth record.\n     *\n     * @throws {ClientResponseError}\n     */\n    async listExternalAuths(\n        recordId: string,\n        options?: CommonOptions,\n    ): Promise<Array<RecordModel>> {\n        return this.client.collection(\"_externalAuths\").getFullList(\n            Object.assign({}, options, {\n                filter: this.client.filter(\"recordRef = {:id}\", { id: recordId }),\n            }),\n        );\n    }\n\n    /**\n     * @deprecated use collection(\"_externalAuths\").*\n     *\n     * Unlink a single external auth provider from the specified auth record.\n     *\n     * @throws {ClientResponseError}\n     */\n    async unlinkExternalAuth(\n        recordId: string,\n        provider: string,\n        options?: CommonOptions,\n    ): Promise<boolean> {\n        const ea = await this.client.collection(\"_externalAuths\").getFirstListItem(\n            this.client.filter(\"recordRef = {:recordId} && provider = {:provider}\", {\n                recordId,\n                provider,\n            }),\n        );\n\n        return this.client\n            .collection(\"_externalAuths\")\n            .delete(ea.id, options)\n            .then(() => true);\n    }\n\n    /**\n     * Sends auth record OTP to the provided email.\n     *\n     * @throws {ClientResponseError}\n     */\n    async requestOTP(email: string, options?: CommonOptions): Promise<OTPResponse> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: { email: email },\n            },\n            options,\n        );\n\n        return this.client.send(this.baseCollectionPath + \"/request-otp\", options);\n    }\n\n    /**\n     * Authenticate a single auth collection record via OTP.\n     *\n     * On success, this method also automatically updates\n     * the client's AuthStore data and returns:\n     * - the authentication token\n     * - the authenticated record model\n     *\n     * @throws {ClientResponseError}\n     */\n    async authWithOTP<T = M>(\n        otpId: string,\n        password: string,\n        options?: CommonOptions,\n    ): Promise<RecordAuthResponse<T>> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: { otpId, password },\n            },\n            options,\n        );\n\n        return this.client\n            .send(this.baseCollectionPath + \"/auth-with-otp\", options)\n            .then((data) => this.authResponse<T>(data));\n    }\n\n    /**\n     * Impersonate authenticates with the specified recordId and\n     * returns a new client with the received auth token in a memory store.\n     *\n     * If `duration` is 0 the generated auth token will fallback\n     * to the default collection auth token duration.\n     *\n     * This action currently requires superusers privileges.\n     *\n     * @throws {ClientResponseError}\n     */\n    async impersonate(\n        recordId: string,\n        duration: number,\n        options?: CommonOptions,\n    ): Promise<Client> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: { duration: duration },\n            },\n            options,\n        );\n        options.headers = options.headers || {};\n        if (!options.headers.Authorization) {\n            options.headers.Authorization = this.client.authStore.token;\n        }\n\n        // create a new client loaded with the impersonated auth state\n        // ---\n        const client = new Client(\n            this.client.baseURL,\n            new BaseAuthStore(),\n            this.client.lang,\n        );\n\n        const authData = await client.send(\n            this.baseCollectionPath + \"/impersonate/\" + encodeURIComponent(recordId),\n            options,\n        );\n\n        client.authStore.save(authData?.token, this.decode(authData?.record || {}));\n        // ---\n\n        return client;\n    }\n\n    // ---------------------------------------------------------------\n\n    // very rudimentary url query params replacement because at the moment\n    // URL (and URLSearchParams) doesn't seem to be fully supported in React Native\n    //\n    // note: for details behind some of the decode/encode parsing check https://unixpapa.com/js/querystring.html\n    private _replaceQueryParams(\n        url: string,\n        replacements: { [key: string]: any } = {},\n    ): string {\n        let urlPath = url;\n        let query = \"\";\n\n        const queryIndex = url.indexOf(\"?\");\n        if (queryIndex >= 0) {\n            urlPath = url.substring(0, url.indexOf(\"?\"));\n            query = url.substring(url.indexOf(\"?\") + 1);\n        }\n\n        const parsedParams: { [key: string]: string } = {};\n\n        // parse the query parameters\n        const rawParams = query.split(\"&\");\n        for (const param of rawParams) {\n            if (param == \"\") {\n                continue;\n            }\n\n            const pair = param.split(\"=\");\n            parsedParams[decodeURIComponent(pair[0].replace(/\\+/g, \" \"))] =\n                decodeURIComponent((pair[1] || \"\").replace(/\\+/g, \" \"));\n        }\n\n        // apply the replacements\n        for (let key in replacements) {\n            if (!replacements.hasOwnProperty(key)) {\n                continue;\n            }\n\n            if (replacements[key] == null) {\n                delete parsedParams[key];\n            } else {\n                parsedParams[key] = replacements[key];\n            }\n        }\n\n        // construct back the full query string\n        query = \"\";\n        for (let key in parsedParams) {\n            if (!parsedParams.hasOwnProperty(key)) {\n                continue;\n            }\n\n            if (query != \"\") {\n                query += \"&\";\n            }\n\n            query +=\n                encodeURIComponent(key.replace(/%20/g, \"+\")) +\n                \"=\" +\n                encodeURIComponent(parsedParams[key].replace(/%20/g, \"+\"));\n        }\n\n        return query != \"\" ? urlPath + \"?\" + query : urlPath;\n    }\n}\n\nfunction openBrowserPopup(url?: string): Window | null {\n    if (typeof window === \"undefined\" || !window?.open) {\n        throw new ClientResponseError(\n            new Error(\n                `Not in a browser context - please pass a custom urlCallback function.`,\n            ),\n        );\n    }\n\n    let width = 1024;\n    let height = 768;\n\n    let windowWidth = window.innerWidth;\n    let windowHeight = window.innerHeight;\n\n    // normalize window size\n    width = width > windowWidth ? windowWidth : width;\n    height = height > windowHeight ? windowHeight : height;\n\n    let left = windowWidth / 2 - width / 2;\n    let top = windowHeight / 2 - height / 2;\n\n    // note: we don't use the noopener and noreferrer attributes since\n    // for some reason browser blocks such windows then url is undefined/blank\n    return window.open(\n        url,\n        \"popup_window\",\n        \"width=\" +\n            width +\n            \",height=\" +\n            height +\n            \",top=\" +\n            top +\n            \",left=\" +\n            left +\n            \",resizable,menubar=no\",\n    );\n}\n", "import { CrudService } from \"@/services/CrudService\";\nimport { CollectionModel } from \"@/tools/dtos\";\nimport { CommonOptions } from \"@/tools/options\";\n\nexport class CollectionService extends CrudService<CollectionModel> {\n    /**\n     * @inheritdoc\n     */\n    get baseCrudPath(): string {\n        return \"/api/collections\";\n    }\n\n    /**\n     * Imports the provided collections.\n     *\n     * If `deleteMissing` is `true`, all local collections and their fields,\n     * that are not present in the imported configuration, WILL BE DELETED\n     * (including their related records data)!\n     *\n     * @throws {ClientResponseError}\n     */\n    async import(\n        collections: Array<CollectionModel>,\n        deleteMissing: boolean = false,\n        options?: CommonOptions,\n    ): Promise<true> {\n        options = Object.assign(\n            {\n                method: \"PUT\",\n                body: {\n                    collections: collections,\n                    deleteMissing: deleteMissing,\n                },\n            },\n            options,\n        );\n\n        return this.client.send(this.baseCrudPath + \"/import\", options).then(() => true);\n    }\n\n    /**\n     * Returns type indexed map with scaffolded collection models\n     * populated with their default field values.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getScaffolds(\n        options?: CommonOptions,\n    ): Promise<{ [key: string]: CollectionModel }> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(this.baseCrudPath + \"/meta/scaffolds\", options);\n    }\n\n    /**\n     * Deletes all records associated with the specified collection.\n     *\n     * @throws {ClientResponseError}\n     */\n    async truncate(collectionIdOrName: string, options?: CommonOptions): Promise<true> {\n        options = Object.assign(\n            {\n                method: \"DELETE\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(\n                this.baseCrudPath +\n                    \"/\" +\n                    encodeURIComponent(collectionIdOrName) +\n                    \"/truncate\",\n                options,\n            )\n            .then(() => true);\n    }\n}\n", "import { ClientResponseError } from \"@/ClientResponseError\";\nimport { BaseService } from \"@/services/BaseService\";\nimport { ListResult, LogModel } from \"@/tools/dtos\";\nimport { CommonOptions, ListOptions, LogStatsOptions } from \"@/tools/options\";\n\nexport interface HourlyStats {\n    total: number;\n    date: string;\n}\n\nexport class LogService extends BaseService {\n    /**\n     * Returns paginated logs list.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getList(\n        page = 1,\n        perPage = 30,\n        options?: ListOptions,\n    ): Promise<ListResult<LogModel>> {\n        options = Object.assign({ method: \"GET\" }, options);\n\n        options.query = Object.assign(\n            {\n                page: page,\n                perPage: perPage,\n            },\n            options.query,\n        );\n\n        return this.client.send(\"/api/logs\", options);\n    }\n\n    /**\n     * Returns a single log by its id.\n     *\n     * If `id` is empty it will throw a 404 error.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getOne(id: string, options?: CommonOptions): Promise<LogModel> {\n        if (!id) {\n            throw new ClientResponseError({\n                url: this.client.buildURL(\"/api/logs/\"),\n                status: 404,\n                response: {\n                    code: 404,\n                    message: \"Missing required log id.\",\n                    data: {},\n                },\n            });\n        }\n\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/logs/\" + encodeURIComponent(id), options);\n    }\n\n    /**\n     * Returns logs statistics.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getStats(options?: LogStatsOptions): Promise<Array<HourlyStats>> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/logs/stats\", options);\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { CommonOptions } from \"@/tools/options\";\n\nexport interface HealthCheckResponse {\n    code: number;\n    message: string;\n    data: { [key: string]: any };\n}\n\nexport class HealthService extends BaseService {\n    /**\n     * Checks the health status of the api.\n     *\n     * @throws {ClientResponseError}\n     */\n    async check(options?: CommonOptions): Promise<HealthCheckResponse> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/health\", options);\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { CommonOptions, FileOptions } from \"@/tools/options\";\n\nexport class FileService extends BaseService {\n    /**\n     * @deprecated Please replace with `pb.files.getURL()`.\n     */\n    getUrl(\n        record: { [key: string]: any },\n        filename: string,\n        queryParams: FileOptions = {},\n    ): string {\n        console.warn(\"Please replace pb.files.getUrl() with pb.files.getURL()\");\n        return this.getURL(record, filename, queryParams);\n    }\n\n    /**\n     * Builds and returns an absolute record file url for the provided filename.\n     */\n    getURL(\n        record: { [key: string]: any },\n        filename: string,\n        queryParams: FileOptions = {},\n    ): string {\n        if (\n            !filename ||\n            !record?.id ||\n            !(record?.collectionId || record?.collectionName)\n        ) {\n            return \"\";\n        }\n\n        const parts = [];\n        parts.push(\"api\");\n        parts.push(\"files\");\n        parts.push(encodeURIComponent(record.collectionId || record.collectionName));\n        parts.push(encodeURIComponent(record.id));\n        parts.push(encodeURIComponent(filename));\n\n        let result = this.client.buildURL(parts.join(\"/\"));\n\n        if (Object.keys(queryParams).length) {\n            // normalize the download query param for consistency with the Dart sdk\n            if (queryParams.download === false) {\n                delete queryParams.download;\n            }\n\n            const params = new URLSearchParams(queryParams);\n\n            result += (result.includes(\"?\") ? \"&\" : \"?\") + params;\n        }\n\n        return result;\n    }\n\n    /**\n     * Requests a new private file access token for the current auth model.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getToken(options?: CommonOptions): Promise<string> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(\"/api/files/token\", options)\n            .then((data) => data?.token || \"\");\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { CommonOptions } from \"@/tools/options\";\n\nexport interface BackupFileInfo {\n    key: string;\n    size: number;\n    modified: string;\n}\n\nexport class BackupService extends BaseService {\n    /**\n     * Returns list with all available backup files.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getFullList(options?: CommonOptions): Promise<Array<BackupFileInfo>> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/backups\", options);\n    }\n\n    /**\n     * Initializes a new backup.\n     *\n     * @throws {ClientResponseError}\n     */\n    async create(basename: string, options?: CommonOptions): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: {\n                    name: basename,\n                },\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/backups\", options).then(() => true);\n    }\n\n    /**\n     * Uploads an existing backup file.\n     *\n     * Example:\n     *\n     * ```js\n     * await pb.backups.upload({\n     *     file: new Blob([...]),\n     * });\n     * ```\n     *\n     * @throws {ClientResponseError}\n     */\n    async upload(\n        bodyParams: { [key: string]: any } | FormData,\n        options?: CommonOptions,\n    ): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: bodyParams,\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/backups/upload\", options).then(() => true);\n    }\n\n    /**\n     * Deletes a single backup file.\n     *\n     * @throws {ClientResponseError}\n     */\n    async delete(key: string, options?: CommonOptions): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"DELETE\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(`/api/backups/${encodeURIComponent(key)}`, options)\n            .then(() => true);\n    }\n\n    /**\n     * Initializes an app data restore from an existing backup.\n     *\n     * @throws {ClientResponseError}\n     */\n    async restore(key: string, options?: CommonOptions): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(`/api/backups/${encodeURIComponent(key)}/restore`, options)\n            .then(() => true);\n    }\n\n    /**\n     * @deprecated Please use `getDownloadURL()`.\n     */\n    getDownloadUrl(token: string, key: string): string {\n        console.warn(\n            \"Please replace pb.backups.getDownloadUrl() with pb.backups.getDownloadURL()\",\n        );\n        return this.getDownloadURL(token, key);\n    }\n\n    /**\n     * Builds a download url for a single existing backup using a\n     * superuser file token and the backup file key.\n     *\n     * The file token can be generated via `pb.files.getToken()`.\n     */\n    getDownloadURL(token: string, key: string): string {\n        return this.client.buildURL(\n            `/api/backups/${encodeURIComponent(key)}?token=${encodeURIComponent(token)}`,\n        );\n    }\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { CommonOptions } from \"@/tools/options\";\n\nexport interface CronJob {\n    id: string;\n    expression: string;\n}\n\nexport class CronService extends BaseService {\n    /**\n     * Returns list with all registered cron jobs.\n     *\n     * @throws {ClientResponseError}\n     */\n    async getFullList(options?: CommonOptions): Promise<Array<CronJob>> {\n        options = Object.assign(\n            {\n                method: \"GET\",\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/crons\", options);\n    }\n\n    /**\n     * Runs the specified cron job.\n     *\n     * @throws {ClientResponseError}\n     */\n    async run(jobId: string, options?: CommonOptions): Promise<boolean> {\n        options = Object.assign(\n            {\n                method: \"POST\",\n            },\n            options,\n        );\n\n        return this.client\n            .send(`/api/crons/${encodeURIComponent(jobId)}`, options)\n            .then(() => true);\n    }\n}\n", "/**\n * Checks if the specified value is a file (aka. File, Blob, RN file object).\n */\nexport function isFile(val: any): boolean {\n    return (\n        (typeof Blob !== \"undefined\" && val instanceof Blob) ||\n        (typeof File !== \"undefined\" && val instanceof File) ||\n        // check for React Native file object format\n        // (see https://github.com/pocketbase/pocketbase/discussions/2002#discussioncomment-5254168)\n        (val !== null &&\n            typeof val === \"object\" &&\n            val.uri &&\n            ((typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\") ||\n                (typeof global !== \"undefined\" && (global as any).HermesInternal)))\n    );\n}\n\n/**\n * Loosely checks if the specified body is a FormData instance.\n */\nexport function isFormData(body: any): boolean {\n    return (\n        body &&\n        // we are checking the constructor name because FormData\n        // is not available natively in some environments and the\n        // polyfill(s) may not be globally accessible\n        (body.constructor.name === \"FormData\" ||\n            // fallback to global FormData instance check\n            // note: this is needed because the constructor.name could be different in case of\n            //       custom global FormData implementation, eg. React Native on Android/iOS\n            (typeof FormData !== \"undefined\" && body instanceof FormData))\n    );\n}\n\n/**\n * Checks if the submitted body object has at least one Blob/File field value.\n */\nexport function hasFileField(body: { [key: string]: any }): boolean {\n    for (const key in body) {\n        const values = Array.isArray(body[key]) ? body[key] : [body[key]];\n        for (const v of values) {\n            if (isFile(v)) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n}\n\n/**\n * Converts analyzes the provided body and converts it to FormData\n * in case a plain object with File/Blob values is used.\n */\nexport function convertToFormDataIfNeeded(body: any): any {\n    if (\n        typeof FormData === \"undefined\" ||\n        typeof body === \"undefined\" ||\n        typeof body !== \"object\" ||\n        body === null ||\n        isFormData(body) ||\n        !hasFileField(body)\n    ) {\n        return body;\n    }\n\n    const form = new FormData();\n\n    for (const key in body) {\n        const val = body[key];\n\n        // skip undefined values for consistency with JSON.stringify\n        // (see https://github.com/pocketbase/pocketbase/issues/6731#issuecomment-2812382827)\n        if (typeof val === \"undefined\") {\n            continue;\n        }\n\n        if (typeof val === \"object\" && !hasFileField({ data: val })) {\n            // send json-like values as jsonPayload to avoid the implicit string value normalization\n            let payload: { [key: string]: any } = {};\n            payload[key] = val;\n            form.append(\"@jsonPayload\", JSON.stringify(payload));\n        } else {\n            // in case of mixed string and file/blob\n            const normalizedVal = Array.isArray(val) ? val : [val];\n            for (let v of normalizedVal) {\n                form.append(key, v);\n            }\n        }\n    }\n\n    return form;\n}\n\n/**\n * Converts the provided FormData instance into a plain object.\n *\n * For consistency with the server multipart/form-data inferring,\n * the following normalization rules are applied for plain multipart string values:\n *   - \"true\" is converted to the json \"true\"\n *   - \"false\" is converted to the json \"false\"\n *   - numeric strings are converted to json number ONLY if the resulted\n *     minimal number string representation is the same as the provided raw string\n *     (aka. scientific notations, \"Infinity\", \"0.0\", \"0001\", etc. are kept as string)\n *   - any other string (empty string too) is left as it is\n */\nexport function convertFormDataToObject(formData: FormData): { [key: string]: any } {\n    let result: { [key: string]: any } = {};\n\n    formData.forEach((v, k) => {\n        if (k === \"@jsonPayload\" && typeof v == \"string\") {\n            try {\n                let parsed = JSON.parse(v);\n                Object.assign(result, parsed);\n            } catch (err) {\n                console.warn(\"@jsonPayload error:\", err);\n            }\n        } else {\n            if (typeof result[k] !== \"undefined\") {\n                if (!Array.isArray(result[k])) {\n                    result[k] = [result[k]];\n                }\n                result[k].push(inferFormDataValue(v));\n            } else {\n                result[k] = inferFormDataValue(v);\n            }\n        }\n    });\n\n    return result;\n}\n\nconst inferNumberCharsRegex = /^[\\-\\.\\d]+$/;\n\nfunction inferFormDataValue(value: any): any {\n    if (typeof value != \"string\") {\n        return value;\n    }\n\n    if (value == \"true\") {\n        return true;\n    }\n\n    if (value == \"false\") {\n        return false;\n    }\n\n    // note: expects the provided raw string to match exactly with the minimal string representation of the parsed number\n    if (\n        (value[0] === \"-\" || (value[0] >= \"0\" && value[0] <= \"9\")) &&\n        inferNumberCharsRegex.test(value)\n    ) {\n        let num = +value;\n        if (\"\" + num === value) {\n            return num;\n        }\n    }\n\n    return value;\n}\n", "import { BaseService } from \"@/services/BaseService\";\nimport { isFile, isFormData, convertFormDataToObject } from \"@/tools/formdata\";\nimport {\n    SendOptions,\n    RecordOptions,\n    normalizeUnknownQueryParams,\n    serializeQueryParams,\n} from \"@/tools/options\";\n\nexport interface BatchRequest {\n    method: string;\n    url: string;\n    json?: { [key: string]: any };\n    files?: { [key: string]: Array<any> };\n    headers?: { [key: string]: string };\n}\n\nexport interface BatchRequestResult {\n    status: number;\n    body: any;\n}\n\nexport class BatchService extends BaseService {\n    private requests: Array<BatchRequest> = [];\n    private subs: { [key: string]: SubBatchService } = {};\n\n    /**\n     * Starts constructing a batch request entry for the specified collection.\n     */\n    collection(collectionIdOrName: string): SubBatchService {\n        if (!this.subs[collectionIdOrName]) {\n            this.subs[collectionIdOrName] = new SubBatchService(\n                this.requests,\n                collectionIdOrName,\n            );\n        }\n\n        return this.subs[collectionIdOrName];\n    }\n\n    /**\n     * Sends the batch requests.\n     *\n     * @throws {ClientResponseError}\n     */\n    async send(options?: SendOptions): Promise<Array<BatchRequestResult>> {\n        const formData = new FormData();\n\n        const jsonData = [];\n\n        for (let i = 0; i < this.requests.length; i++) {\n            const req = this.requests[i];\n\n            jsonData.push({\n                method: req.method,\n                url: req.url,\n                headers: req.headers,\n                body: req.json,\n            });\n\n            if (req.files) {\n                for (let key in req.files) {\n                    const files = req.files[key] || [];\n                    for (let file of files) {\n                        formData.append(\"requests.\" + i + \".\" + key, file);\n                    }\n                }\n            }\n        }\n\n        formData.append(\"@jsonPayload\", JSON.stringify({ requests: jsonData }));\n\n        options = Object.assign(\n            {\n                method: \"POST\",\n                body: formData,\n            },\n            options,\n        );\n\n        return this.client.send(\"/api/batch\", options);\n    }\n}\n\nexport class SubBatchService {\n    private requests: Array<BatchRequest> = [];\n    private readonly collectionIdOrName: string;\n\n    constructor(requests: Array<BatchRequest>, collectionIdOrName: string) {\n        this.requests = requests;\n        this.collectionIdOrName = collectionIdOrName;\n    }\n\n    /**\n     * Registers a record upsert request into the current batch queue.\n     *\n     * The request will be executed as update if `bodyParams` have a valid existing record `id` value, otherwise - create.\n     */\n    upsert(\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: RecordOptions,\n    ): void {\n        options = Object.assign(\n            {\n                body: bodyParams || {},\n            },\n            options,\n        );\n\n        const request: BatchRequest = {\n            method: \"PUT\",\n            url:\n                \"/api/collections/\" +\n                encodeURIComponent(this.collectionIdOrName) +\n                \"/records\",\n        };\n\n        this.prepareRequest(request, options);\n\n        this.requests.push(request);\n    }\n\n    /**\n     * Registers a record create request into the current batch queue.\n     */\n    create(\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: RecordOptions,\n    ): void {\n        options = Object.assign(\n            {\n                body: bodyParams || {},\n            },\n            options,\n        );\n\n        const request: BatchRequest = {\n            method: \"POST\",\n            url:\n                \"/api/collections/\" +\n                encodeURIComponent(this.collectionIdOrName) +\n                \"/records\",\n        };\n\n        this.prepareRequest(request, options);\n\n        this.requests.push(request);\n    }\n\n    /**\n     * Registers a record update request into the current batch queue.\n     */\n    update(\n        id: string,\n        bodyParams?: { [key: string]: any } | FormData,\n        options?: RecordOptions,\n    ): void {\n        options = Object.assign(\n            {\n                body: bodyParams || {},\n            },\n            options,\n        );\n\n        const request: BatchRequest = {\n            method: \"PATCH\",\n            url:\n                \"/api/collections/\" +\n                encodeURIComponent(this.collectionIdOrName) +\n                \"/records/\" +\n                encodeURIComponent(id),\n        };\n\n        this.prepareRequest(request, options);\n\n        this.requests.push(request);\n    }\n\n    /**\n     * Registers a record delete request into the current batch queue.\n     */\n    delete(id: string, options?: SendOptions): void {\n        options = Object.assign({}, options);\n\n        const request: BatchRequest = {\n            method: \"DELETE\",\n            url:\n                \"/api/collections/\" +\n                encodeURIComponent(this.collectionIdOrName) +\n                \"/records/\" +\n                encodeURIComponent(id),\n        };\n\n        this.prepareRequest(request, options);\n\n        this.requests.push(request);\n    }\n\n    private prepareRequest(request: BatchRequest, options: SendOptions) {\n        normalizeUnknownQueryParams(options);\n\n        request.headers = options.headers;\n        request.json = {};\n        request.files = {};\n\n        // serialize query parameters\n        // -----------------------------------------------------------\n        if (typeof options.query !== \"undefined\") {\n            const query = serializeQueryParams(options.query);\n            if (query) {\n                request.url += (request.url.includes(\"?\") ? \"&\" : \"?\") + query;\n            }\n        }\n\n        // extract json and files body data\n        // -----------------------------------------------------------\n        let body = options.body;\n        if (isFormData(body)) {\n            body = convertFormDataToObject(body);\n        }\n\n        for (const key in body) {\n            const val = body[key];\n\n            if (isFile(val)) {\n                request.files[key] = request.files[key] || [];\n                request.files[key].push(val);\n            } else if (Array.isArray(val)) {\n                const foundFiles = [];\n                const foundRegular = [];\n                for (const v of val) {\n                    if (isFile(v)) {\n                        foundFiles.push(v);\n                    } else {\n                        foundRegular.push(v);\n                    }\n                }\n\n                if (foundFiles.length > 0 && foundFiles.length == val.length) {\n                    // only files\n                    // ---\n                    request.files[key] = request.files[key] || [];\n                    for (let file of foundFiles) {\n                        request.files[key].push(file);\n                    }\n                } else {\n                    // empty or mixed array (both regular and File/Blob values)\n                    // ---\n                    request.json[key] = foundRegular;\n\n                    if (foundFiles.length > 0) {\n                        // add \"+\" to append if not already since otherwise\n                        // the existing regular files will be deleted\n                        // (the mixed values order is preserved only within their corresponding groups)\n                        let fileKey = key;\n                        if (!key.startsWith(\"+\") && !key.endsWith(\"+\")) {\n                            fileKey += \"+\";\n                        }\n\n                        request.files[fileKey] = request.files[fileKey] || [];\n                        for (let file of foundFiles) {\n                            request.files[fileKey].push(file);\n                        }\n                    }\n                }\n            } else {\n                request.json[key] = val;\n            }\n        }\n    }\n}\n", "import { ClientResponseError } from \"@/ClientResponseError\";\nimport { BaseAuthStore } from \"@/stores/BaseAuthStore\";\nimport { LocalAuthStore } from \"@/stores/LocalAuthStore\";\nimport { SettingsService } from \"@/services/SettingsService\";\nimport { RecordService } from \"@/services/RecordService\";\nimport { CollectionService } from \"@/services/CollectionService\";\nimport { LogService } from \"@/services/LogService\";\nimport { RealtimeService } from \"@/services/RealtimeService\";\nimport { HealthService } from \"@/services/HealthService\";\nimport { FileService } from \"@/services/FileService\";\nimport { BackupService } from \"@/services/BackupService\";\nimport { CronService } from \"@/services/CronService\";\nimport { BatchService } from \"@/services/BatchService\";\nimport { RecordModel } from \"@/tools/dtos\";\nimport {\n    SendOptions,\n    FileOptions,\n    normalizeUnknownQueryParams,\n    serializeQueryParams,\n} from \"@/tools/options\";\nimport { isFormData, convertToFormDataIfNeeded } from \"@/tools/formdata\";\n\nexport interface BeforeSendResult {\n    [key: string]: any; // for backward compatibility\n    url?: string;\n    options?: { [key: string]: any };\n}\n\n/**\n * PocketBase JS Client.\n */\nexport default class Client {\n    /**\n     * The base PocketBase backend url address (eg. 'http://127.0.0.1.8090').\n     */\n    baseURL: string;\n\n    /**\n     * Legacy getter alias for baseURL.\n     * @deprecated Please replace with baseURL.\n     */\n    get baseUrl(): string {\n        return this.baseURL;\n    }\n\n    /**\n     * Legacy setter alias for baseURL.\n     * @deprecated Please replace with baseURL.\n     */\n    set baseUrl(v: string) {\n        this.baseURL = v;\n    }\n\n    /**\n     * Hook that get triggered right before sending the fetch request,\n     * allowing you to inspect and modify the url and request options.\n     *\n     * For list of the possible options check https://developer.mozilla.org/en-US/docs/Web/API/fetch#options\n     *\n     * You can return a non-empty result object `{ url, options }` to replace the url and request options entirely.\n     *\n     * Example:\n     * ```js\n     * const pb = new PocketBase(\"https://example.com\")\n     *\n     * pb.beforeSend = function (url, options) {\n     *     options.headers = Object.assign({}, options.headers, {\n     *         'X-Custom-Header': 'example',\n     *     })\n     *\n     *     return { url, options }\n     * }\n     *\n     * // use the created client as usual...\n     * ```\n     */\n    beforeSend?: (\n        url: string,\n        options: SendOptions,\n    ) => BeforeSendResult | Promise<BeforeSendResult>;\n\n    /**\n     * Hook that get triggered after successfully sending the fetch request,\n     * allowing you to inspect/modify the response object and its parsed data.\n     *\n     * Returns the new Promise resolved `data` that will be returned to the client.\n     *\n     * Example:\n     * ```js\n     * const pb = new PocketBase(\"https://example.com\")\n     *\n     * pb.afterSend = function (response, data, options) {\n     *     if (response.status != 200) {\n     *         throw new ClientResponseError({\n     *             url:      response.url,\n     *             status:   response.status,\n     *             response: { ... },\n     *         })\n     *     }\n     *\n     *     return data;\n     * }\n     *\n     * // use the created client as usual...\n     * ```\n     */\n    afterSend?: ((response: Response, data: any) => any) &\n        ((response: Response, data: any, options: SendOptions) => any);\n\n    /**\n     * Optional language code (default to `en-US`) that will be sent\n     * with the requests to the server as `Accept-Language` header.\n     */\n    lang: string;\n\n    /**\n     * A replaceable instance of the local auth store service.\n     */\n    authStore: BaseAuthStore;\n\n    /**\n     * An instance of the service that handles the **Settings APIs**.\n     */\n    readonly settings: SettingsService;\n\n    /**\n     * An instance of the service that handles the **Collection APIs**.\n     */\n    readonly collections: CollectionService;\n\n    /**\n     * An instance of the service that handles the **File APIs**.\n     */\n    readonly files: FileService;\n\n    /**\n     * An instance of the service that handles the **Log APIs**.\n     */\n    readonly logs: LogService;\n\n    /**\n     * An instance of the service that handles the **Realtime APIs**.\n     */\n    readonly realtime: RealtimeService;\n\n    /**\n     * An instance of the service that handles the **Health APIs**.\n     */\n    readonly health: HealthService;\n\n    /**\n     * An instance of the service that handles the **Backup APIs**.\n     */\n    readonly backups: BackupService;\n\n    /**\n     * An instance of the service that handles the **Cron APIs**.\n     */\n    readonly crons: CronService;\n\n    private cancelControllers: { [key: string]: AbortController } = {};\n    private recordServices: { [key: string]: RecordService } = {};\n    private enableAutoCancellation: boolean = true;\n\n    constructor(baseURL = \"/\", authStore?: BaseAuthStore | null, lang = \"en-US\") {\n        this.baseURL = baseURL;\n        this.lang = lang;\n\n        if (authStore) {\n            this.authStore = authStore;\n        } else if (typeof window != \"undefined\" && !!(window as any).Deno) {\n            // note: to avoid common security issues we fallback to runtime/memory store in case the code is running in Deno env\n            this.authStore = new BaseAuthStore();\n        } else {\n            this.authStore = new LocalAuthStore();\n        }\n\n        // common services\n        this.collections = new CollectionService(this);\n        this.files = new FileService(this);\n        this.logs = new LogService(this);\n        this.settings = new SettingsService(this);\n        this.realtime = new RealtimeService(this);\n        this.health = new HealthService(this);\n        this.backups = new BackupService(this);\n        this.crons = new CronService(this);\n    }\n\n    /**\n     * @deprecated\n     * With PocketBase v0.23.0 admins are converted to a regular auth\n     * collection named \"_superusers\", aka. you can use directly collection(\"_superusers\").\n     */\n    get admins(): RecordService {\n        return this.collection(\"_superusers\");\n    }\n\n    /**\n     * Creates a new batch handler for sending multiple transactional\n     * create/update/upsert/delete collection requests in one network call.\n     *\n     * Example:\n     * ```js\n     * const batch = pb.createBatch();\n     *\n     * batch.collection(\"example1\").create({ ... })\n     * batch.collection(\"example2\").update(\"RECORD_ID\", { ... })\n     * batch.collection(\"example3\").delete(\"RECORD_ID\")\n     * batch.collection(\"example4\").upsert({ ... })\n     *\n     * await batch.send()\n     * ```\n     */\n    createBatch(): BatchService {\n        return new BatchService(this);\n    }\n\n    /**\n     * Returns the RecordService associated to the specified collection.\n     */\n    collection<M = RecordModel>(idOrName: string): RecordService<M> {\n        if (!this.recordServices[idOrName]) {\n            this.recordServices[idOrName] = new RecordService(this, idOrName);\n        }\n\n        return this.recordServices[idOrName];\n    }\n\n    /**\n     * Globally enable or disable auto cancellation for pending duplicated requests.\n     */\n    autoCancellation(enable: boolean): Client {\n        this.enableAutoCancellation = !!enable;\n\n        return this;\n    }\n\n    /**\n     * Cancels single request by its cancellation key.\n     */\n    cancelRequest(requestKey: string): Client {\n        if (this.cancelControllers[requestKey]) {\n            this.cancelControllers[requestKey].abort();\n            delete this.cancelControllers[requestKey];\n        }\n\n        return this;\n    }\n\n    /**\n     * Cancels all pending requests.\n     */\n    cancelAllRequests(): Client {\n        for (let k in this.cancelControllers) {\n            this.cancelControllers[k].abort();\n        }\n\n        this.cancelControllers = {};\n\n        return this;\n    }\n\n    /**\n     * Constructs a filter expression with placeholders populated from a parameters object.\n     *\n     * Placeholder parameters are defined with the `{:paramName}` notation.\n     *\n     * The following parameter values are supported:\n     *\n     * - `string` (_single quotes are autoescaped_)\n     * - `number`\n     * - `boolean`\n     * - `Date` object (_stringified into the PocketBase datetime format_)\n     * - `null`\n     * - everything else is converted to a string using `JSON.stringify()`\n     *\n     * Example:\n     *\n     * ```js\n     * pb.collection(\"example\").getFirstListItem(pb.filter(\n     *    'title ~ {:title} && created >= {:created}',\n     *    { title: \"example\", created: new Date()}\n     * ))\n     * ```\n     */\n    filter(raw: string, params?: { [key: string]: any }): string {\n        if (!params) {\n            return raw;\n        }\n\n        for (let key in params) {\n            let val = params[key];\n            switch (typeof val) {\n                case \"boolean\":\n                case \"number\":\n                    val = \"\" + val;\n                    break;\n                case \"string\":\n                    val = \"'\" + val.replace(/'/g, \"\\\\'\") + \"'\";\n                    break;\n                default:\n                    if (val === null) {\n                        val = \"null\";\n                    } else if (val instanceof Date) {\n                        val = \"'\" + val.toISOString().replace(\"T\", \" \") + \"'\";\n                    } else {\n                        val = \"'\" + JSON.stringify(val).replace(/'/g, \"\\\\'\") + \"'\";\n                    }\n            }\n            raw = raw.replaceAll(\"{:\" + key + \"}\", val);\n        }\n\n        return raw;\n    }\n\n    /**\n     * @deprecated Please use `pb.files.getURL()`.\n     */\n    getFileUrl(\n        record: { [key: string]: any },\n        filename: string,\n        queryParams: FileOptions = {},\n    ): string {\n        console.warn(\"Please replace pb.getFileUrl() with pb.files.getURL()\");\n        return this.files.getURL(record, filename, queryParams);\n    }\n\n    /**\n     * @deprecated Please use `pb.buildURL()`.\n     */\n    buildUrl(path: string): string {\n        console.warn(\"Please replace pb.buildUrl() with pb.buildURL()\");\n        return this.buildURL(path);\n    }\n\n    /**\n     * Builds a full client url by safely concatenating the provided path.\n     */\n    buildURL(path: string): string {\n        let url = this.baseURL;\n\n        // construct an absolute base url if in a browser environment\n        if (\n            typeof window !== \"undefined\" &&\n            !!window.location &&\n            !url.startsWith(\"https://\") &&\n            !url.startsWith(\"http://\")\n        ) {\n            url = window.location.origin?.endsWith(\"/\")\n                ? window.location.origin.substring(0, window.location.origin.length - 1)\n                : window.location.origin || \"\";\n\n            if (!this.baseURL.startsWith(\"/\")) {\n                url += window.location.pathname || \"/\";\n                url += url.endsWith(\"/\") ? \"\" : \"/\";\n            }\n\n            url += this.baseURL;\n        }\n\n        // concatenate the path\n        if (path) {\n            url += url.endsWith(\"/\") ? \"\" : \"/\"; // append trailing slash if missing\n            url += path.startsWith(\"/\") ? path.substring(1) : path;\n        }\n\n        return url;\n    }\n\n    /**\n     * Sends an api http request.\n     *\n     * @throws {ClientResponseError}\n     */\n    async send<T = any>(path: string, options: SendOptions): Promise<T> {\n        options = this.initSendOptions(path, options);\n\n        // build url + path\n        let url = this.buildURL(path);\n\n        if (this.beforeSend) {\n            const result = Object.assign({}, await this.beforeSend(url, options));\n            if (\n                typeof result.url !== \"undefined\" ||\n                typeof result.options !== \"undefined\"\n            ) {\n                url = result.url || url;\n                options = result.options || options;\n            } else if (Object.keys(result).length) {\n                // legacy behavior\n                options = result as SendOptions;\n                console?.warn &&\n                    console.warn(\n                        \"Deprecated format of beforeSend return: please use `return { url, options }`, instead of `return options`.\",\n                    );\n            }\n        }\n\n        // serialize the query parameters\n        if (typeof options.query !== \"undefined\") {\n            const query = serializeQueryParams(options.query);\n            if (query) {\n                url += (url.includes(\"?\") ? \"&\" : \"?\") + query;\n            }\n            delete options.query;\n        }\n\n        // ensures that the json body is serialized\n        if (\n            this.getHeader(options.headers, \"Content-Type\") == \"application/json\" &&\n            options.body &&\n            typeof options.body !== \"string\"\n        ) {\n            options.body = JSON.stringify(options.body);\n        }\n\n        const fetchFunc = options.fetch || fetch;\n\n        // send the request\n        return fetchFunc(url, options)\n            .then(async (response) => {\n                let data: any = {};\n\n                try {\n                    data = await response.json();\n                } catch (_) {\n                    // all api responses are expected to return json\n                    // with the exception of the realtime event and 204\n                }\n\n                if (this.afterSend) {\n                    data = await this.afterSend(response, data, options);\n                }\n\n                if (response.status >= 400) {\n                    throw new ClientResponseError({\n                        url: response.url,\n                        status: response.status,\n                        data: data,\n                    });\n                }\n\n                return data as T;\n            })\n            .catch((err) => {\n                // wrap to normalize all errors\n                throw new ClientResponseError(err);\n            });\n    }\n\n    /**\n     * Shallow copy the provided object and takes care to initialize\n     * any options required to preserve the backward compatability.\n     *\n     * @param  {SendOptions} options\n     * @return {SendOptions}\n     */\n    private initSendOptions(path: string, options: SendOptions): SendOptions {\n        options = Object.assign({ method: \"GET\" } as SendOptions, options);\n\n        // auto convert the body to FormData, if needed\n        options.body = convertToFormDataIfNeeded(options.body);\n\n        // move unknown send options as query parameters\n        normalizeUnknownQueryParams(options);\n\n        // requestKey normalizations for backward-compatibility\n        // ---\n        options.query = Object.assign({}, options.params, options.query);\n        if (typeof options.requestKey === \"undefined\") {\n            if (options.$autoCancel === false || options.query.$autoCancel === false) {\n                options.requestKey = null;\n            } else if (options.$cancelKey || options.query.$cancelKey) {\n                options.requestKey = options.$cancelKey || options.query.$cancelKey;\n            }\n        }\n        // remove the deprecated special cancellation params from the other query params\n        delete options.$autoCancel;\n        delete options.query.$autoCancel;\n        delete options.$cancelKey;\n        delete options.query.$cancelKey;\n        // ---\n\n        // add the json header, if not explicitly set\n        // (for FormData body the Content-Type header should be skipped since the boundary is autogenerated)\n        if (\n            this.getHeader(options.headers, \"Content-Type\") === null &&\n            !isFormData(options.body)\n        ) {\n            options.headers = Object.assign({}, options.headers, {\n                \"Content-Type\": \"application/json\",\n            });\n        }\n\n        // add Accept-Language header, if not explicitly set\n        if (this.getHeader(options.headers, \"Accept-Language\") === null) {\n            options.headers = Object.assign({}, options.headers, {\n                \"Accept-Language\": this.lang,\n            });\n        }\n\n        // check if Authorization header can be added\n        if (\n            // has valid token\n            this.authStore.token &&\n            // auth header is not explicitly set\n            this.getHeader(options.headers, \"Authorization\") === null\n        ) {\n            options.headers = Object.assign({}, options.headers, {\n                Authorization: this.authStore.token,\n            });\n        }\n\n        // handle auto cancelation for duplicated pending request\n        if (this.enableAutoCancellation && options.requestKey !== null) {\n            const requestKey = options.requestKey || (options.method || \"GET\") + path;\n\n            delete options.requestKey;\n\n            // cancel previous pending requests\n            this.cancelRequest(requestKey);\n\n            const controller = new AbortController();\n            this.cancelControllers[requestKey] = controller;\n            options.signal = controller.signal;\n        }\n\n        return options;\n    }\n\n    /**\n     * Extracts the header with the provided name in case-insensitive manner.\n     * Returns `null` if no header matching the name is found.\n     */\n    private getHeader(\n        headers: { [key: string]: string } | undefined,\n        name: string,\n    ): string | null {\n        headers = headers || {};\n        name = name.toLowerCase();\n\n        for (let key in headers) {\n            if (key.toLowerCase() == name) {\n                return headers[key];\n            }\n        }\n\n        return null;\n    }\n}\n", "import { BaseAuthStore, AuthRecord } from \"@/stores/BaseAuthStore\";\n\nexport type AsyncSaveFunc = (serializedPayload: string) => Promise<void>;\n\nexport type AsyncClearFunc = () => Promise<void>;\n\ntype queueFunc = () => Promise<void>;\n\n/**\n * AsyncAuthStore is a helper auth store implementation\n * that could be used with any external async persistent layer\n * (key-value db, local file, etc.).\n *\n * Here is an example with the React Native AsyncStorage package:\n *\n * ```\n * import AsyncStorage from \"@react-native-async-storage/async-storage\";\n * import PocketBase, { AsyncAuthStore } from \"pocketbase\";\n *\n * const store = new AsyncAuthStore({\n *     save:    async (serialized) => AsyncStorage.setItem(\"pb_auth\", serialized),\n *     initial: AsyncStorage.getItem(\"pb_auth\"),\n * });\n *\n * const pb = new PocketBase(\"https://example.com\", store)\n * ```\n */\nexport class AsyncAuthStore extends BaseAuthStore {\n    private saveFunc: AsyncSaveFunc;\n    private clearFunc?: AsyncClearFunc;\n    private queue: Array<queueFunc> = [];\n\n    constructor(config: {\n        // The async function that is called every time\n        // when the auth store state needs to be persisted.\n        save: AsyncSaveFunc;\n\n        /// An *optional* async function that is called every time\n        /// when the auth store needs to be cleared.\n        ///\n        /// If not explicitly set, `saveFunc` with empty data will be used.\n        clear?: AsyncClearFunc;\n\n        // An *optional* initial data to load into the store.\n        initial?: string | Promise<any>;\n    }) {\n        super();\n\n        this.saveFunc = config.save;\n        this.clearFunc = config.clear;\n\n        this._enqueue(() => this._loadInitial(config.initial));\n    }\n\n    /**\n     * @inheritdoc\n     */\n    save(token: string, record?: AuthRecord): void {\n        super.save(token, record);\n\n        let value = \"\";\n        try {\n            value = JSON.stringify({ token, record });\n        } catch (err) {\n            console.warn(\"AsyncAuthStore: failed to stringify the new state\");\n        }\n\n        this._enqueue(() => this.saveFunc(value));\n    }\n\n    /**\n     * @inheritdoc\n     */\n    clear(): void {\n        super.clear();\n\n        if (this.clearFunc) {\n            this._enqueue(() => this.clearFunc!());\n        } else {\n            this._enqueue(() => this.saveFunc(\"\"));\n        }\n    }\n\n    /**\n     * Initializes the auth store state.\n     */\n    private async _loadInitial(payload?: string | Promise<any>) {\n        try {\n            payload = await payload;\n\n            if (payload) {\n                let parsed;\n                if (typeof payload === \"string\") {\n                    parsed = JSON.parse(payload) || {};\n                } else if (typeof payload === \"object\") {\n                    parsed = payload;\n                }\n\n                this.save(parsed.token || \"\", parsed.record || parsed.model || null);\n            }\n        } catch (_) {}\n    }\n\n    /**\n     * Appends an async function to the queue.\n     */\n    private _enqueue(asyncCallback: () => Promise<void>) {\n        this.queue.push(asyncCallback);\n\n        if (this.queue.length == 1) {\n            this._dequeue();\n        }\n    }\n\n    /**\n     * Starts the queue processing.\n     */\n    private _dequeue() {\n        if (!this.queue.length) {\n            return;\n        }\n\n        this.queue[0]().finally(() => {\n            this.queue.shift();\n\n            if (!this.queue.length) {\n                return;\n            }\n\n            this._dequeue();\n        });\n    }\n}\n"], "mappings": ";;;AAIM,IAAOA,sBAAP,MAAOA,6BAA4BC,MAAAA;EAOrC,YAAYC,IAAAA;AACRC,UAAM,qBAAA,GAPVC,KAAGC,MAAW,IACdD,KAAME,SAAW,GACjBF,KAAQG,WAA2B,CAAA,GACnCH,KAAOI,UAAAA,OACPJ,KAAaK,gBAAQ,MAOjBC,OAAOC,eAAeP,MAAMJ,qBAAoBY,SAAAA,GAEhC,SAAZV,MAAuC,YAAA,OAAZA,OAC3BE,KAAKC,MAA6B,YAAA,OAAhBH,GAAQG,MAAmBH,GAAQG,MAAM,IAC3DD,KAAKE,SAAmC,YAAA,OAAnBJ,GAAQI,SAAsBJ,GAAQI,SAAS,GACpEF,KAAKI,UAAAA,CAAAA,CAAYN,GAAQM,SACzBJ,KAAKK,gBAAgBP,GAAQO,eAEJ,SAArBP,GAAQK,YAAiD,YAAA,OAArBL,GAAQK,WAC5CH,KAAKG,WAAWL,GAAQK,WACA,SAAjBL,GAAQW,QAAyC,YAAA,OAAjBX,GAAQW,OAC/CT,KAAKG,WAAWL,GAAQW,OAExBT,KAAKG,WAAW,CAAA,IAInBH,KAAKK,iBAAmBP,cAAmBF,yBAC5CI,KAAKK,gBAAgBP,KAGG,eAAA,OAAjBY,gBAAgCZ,cAAmBY,iBAC1DV,KAAKI,UAAAA,OAGTJ,KAAKW,OAAO,yBAAyBX,KAAKE,QAC1CF,KAAKY,UAAUZ,KAAKG,UAAUS,SACzBZ,KAAKY,YACFZ,KAAKI,UACLJ,KAAKY,UACD,qHACGZ,KAAKK,eAAeQ,OAAOD,SAASE,SAAS,kBAAA,IACpDd,KAAKY,UACD,uJAEJZ,KAAKY,UAAU,0BAMvBZ,KAAKa,QAAQb,KAAKK;EACrB;EAKD,IAAA,OAAII;AACA,WAAOT,KAAKG;EACf;EAMD,SAAAY;AACI,WAAO,EAAA,GAAKf,KAAAA;EACf;AAAA;AC3DL,IAAMgB,IAAqB;AAUX,SAAAC,YAAYC,IAAaC,IAAAA;AACrC,QAAMC,KAAiC,CAAA;AAEvC,MAAmB,YAAA,OAARF,GACP,QAAOE;AAGX,QACMC,KADMf,OAAOgB,OAAO,CAAA,GAAIH,MAAW,CAAA,CAAA,EACtBE,UAAUE;AAE7B,MAAIC,KAAQ;AACZ,SAAOA,KAAQN,GAAIO,UAAQ;AACvB,UAAMC,KAAQR,GAAIS,QAAQ,KAAKH,EAAAA;AAG/B,QAAA,OAAIE,GACA;AAGJ,QAAIE,KAASV,GAAIS,QAAQ,KAAKH,EAAAA;AAE9B,QAAA,OAAII,GACAA,CAAAA,KAASV,GAAIO;aACNG,KAASF,IAAO;AAEvBF,MAAAA,KAAQN,GAAIW,YAAY,KAAKH,KAAQ,CAAA,IAAK;AAC1C;IACH;AAED,UAAMI,IAAMZ,GAAIa,MAAMP,IAAOE,EAAAA,EAAOM,KAAAA;AAGpC,QAAA,WAAkBZ,GAAOU,CAAAA,GAAM;AAC3B,UAAIG,KAAMf,GAAIa,MAAML,KAAQ,GAAGE,EAAAA,EAAQI,KAAAA;AAGb,aAAtBC,GAAIC,WAAW,CAAA,MACfD,KAAMA,GAAIF,MAAM,GAAA,EAAI;AAGxB,UAAA;AACIX,QAAAA,GAAOU,CAAAA,IAAOT,GAAOY,EAAAA;MACxB,SAAQE,IAAAA;AACLf,QAAAA,GAAOU,CAAAA,IAAOG;MACjB;IACJ;AAEDT,IAAAA,KAAQI,KAAS;EACpB;AAED,SAAOR;AACX;AAAA,SAwBgBgB,gBACZzB,IACAsB,IACAd,IAAAA;AAEA,QAAMkB,KAAM/B,OAAOgB,OAAO,CAAA,GAAIH,MAAW,CAAA,CAAA,GACnCmB,KAASD,GAAIC,UAAUC;AAE7B,MAAA,CAAKvB,EAAmBwB,KAAK7B,EAAAA,EACzB,OAAM,IAAI8B,UAAU,0BAAA;AAGxB,QAAMC,IAAQJ,GAAOL,EAAAA;AAErB,MAAIS,KAAAA,CAAU1B,EAAmBwB,KAAKE,CAAAA,EAClC,OAAM,IAAID,UAAU,yBAAA;AAGxB,MAAIrB,IAAST,KAAO,MAAM+B;AAE1B,MAAkB,QAAdL,GAAIM,QAAgB;AACpB,UAAMA,KAASN,GAAIM,SAAS;AAE5B,QAAIC,MAAMD,EAAAA,KAAAA,CAAYE,SAASF,EAAAA,EAC3B,OAAM,IAAIF,UAAU,0BAAA;AAGxBrB,SAAU,eAAe0B,KAAKC,MAAMJ,EAAAA;EACvC;AAED,MAAIN,GAAIW,QAAQ;AACZ,QAAA,CAAKhC,EAAmBwB,KAAKH,GAAIW,MAAAA,EAC7B,OAAM,IAAIP,UAAU,0BAAA;AAGxBrB,SAAU,cAAciB,GAAIW;EAC/B;AAED,MAAIX,GAAIY,MAAM;AACV,QAAA,CAAKjC,EAAmBwB,KAAKH,GAAIY,IAAAA,EAC7B,OAAM,IAAIR,UAAU,wBAAA;AAGxBrB,SAAU,YAAYiB,GAAIY;EAC7B;AAED,MAAIZ,GAAIa,SAAS;AACb,QAAA,CA6ER,SAASC,OAAOlB,IAAAA;AACZ,aAA+C,oBAAxC3B,OAAOE,UAAU4C,SAASC,KAAKpB,EAAAA,KAA4BA,cAAeqB;IACrF,EA/EoBjB,GAAIa,OAAAA,KAAYN,MAAMP,GAAIa,QAAQK,QAAAA,CAAAA,EAC1C,OAAM,IAAId,UAAU,2BAAA;AAGxBrB,SAAU,eAAeiB,GAAIa,QAAQM,YAAAA;EACxC;AAUD,MARInB,GAAIoB,aACJrC,KAAU,eAGViB,GAAIqB,WACJtC,KAAU,aAGViB,GAAIsB,UAAU;AAId,YAF4B,YAAA,OAAjBtB,GAAIsB,WAAwBtB,GAAIsB,SAASC,YAAAA,IAAgBvB,GAAIsB,UAAAA;MAGpE,KAAK;AACDvC,aAAU;AACV;MACJ,KAAK;AACDA,aAAU;AACV;MACJ,KAAK;AACDA,aAAU;AACV;MACJ;AACI,cAAM,IAAIqB,UAAU,4BAAA;IAAA;EAE/B;AAED,MAAIJ,GAAIwB,UAAU;AAId,YAF4B,YAAA,OAAjBxB,GAAIwB,WAAwBxB,GAAIwB,SAASD,YAAAA,IAAgBvB,GAAIwB,UAAAA;MAGpE,KAAA;AACIzC,aAAU;AACV;MACJ,KAAK;AACDA,aAAU;AACV;MACJ,KAAK;AACDA,aAAU;AACV;MACJ,KAAK;AACDA,aAAU;AACV;MACJ;AACI,cAAM,IAAIqB,UAAU,4BAAA;IAAA;EAE/B;AAED,SAAOrB;AACX;AAMA,SAASG,cAAcU,IAAAA;AACnB,SAAA,OAAOA,GAAIN,QAAQ,GAAA,IAAcmC,mBAAmB7B,EAAAA,IAAOA;AAC/D;AAKA,SAASM,cAAcN,IAAAA;AACnB,SAAO8B,mBAAmB9B,EAAAA;AAC9B;ACzNA,IAAM+B,IACoB,eAAA,OAAdC,aAAmD,kBAAtBA,UAAUC,WAC5B,eAAA,OAAXC,UAA2BA,OAAeC;AAEtD,IAAIC;AA2CE,SAAUC,gBAAgBC,IAAAA;AAC5B,MAAIA,GACA,KAAA;AACI,UAAMC,KAAiBV,mBACnBO,EAAaE,GAAME,MAAM,GAAA,EAAK,CAAA,CAAA,EACzBA,MAAM,EAAA,EACNC,IAAI,SAAUC,IAAAA;AACX,aAAO,OAAO,OAAOA,GAAEzC,WAAW,CAAA,EAAGkB,SAAS,EAAA,GAAKrB,MAAAA,EAAO;IAC9D,CAAA,EACC6C,KAAK,EAAA,CAAA;AAGd,WAAOC,KAAKC,MAAMN,EAAAA,KAAmB,CAAA;EACxC,SAAQO,IAAAA;EAAK;AAGlB,SAAO,CAAA;AACX;AAAA,SAUgBC,eAAeT,IAAeU,KAAsB,GAAA;AAChE,MAAIC,KAAUZ,gBAAgBC,EAAAA;AAE9B,SAAA,EACIjE,OAAO6E,KAAKD,EAAAA,EAASzD,SAAS,MAAA,CAC5ByD,GAAQE,OAAOF,GAAQE,MAAMH,KAAsB3B,KAAK+B,IAAAA,IAAQ;AAM1E;AAzEIhB,IAPgB,cAAA,OAATiB,QAAwBtB,IAOfuB,CAAAA,OAAAA;AAGZ,MAAIrE,KAAMsE,OAAOD,EAAAA,EAAOE,QAAQ,OAAO,EAAA;AACvC,MAAIvE,GAAIO,SAAS,KAAK,EAClB,OAAM,IAAI5B,MACN,mEAAA;AAIR,WAEgB6F,IAAIC,IAAZC,KAAK,GAAeC,KAAM,GAAGC,IAAS,IAEzCH,KAASzE,GAAI6E,OAAOF,IAAAA,GAAAA,CAEpBF,OACCD,KAAKE,KAAK,IAAkB,KAAbF,KAAkBC,KAASA,IAG5CC,OAAO,KACAE,KAAUN,OAAOQ,aAAa,MAAON,OAAAA,KAAaE,KAAM,EAAA,IACzD,EAGND,CAAAA,KAxBU,oEAwBKhE,QAAQgE,EAAAA;AAG3B,SAAOG;AAAM,IAlCFR;ACGnB,IAAMW,IAAmB;AAAA,IAQZC,gBARY,MAQZA;EAAb,cAAAC;AACcnG,SAASoG,YAAW,IACpBpG,KAASqG,YAAe,MAE1BrG,KAAkBsG,qBAA6B,CAAA;EAuN1D;EAlNG,IAAA,QAAI/B;AACA,WAAOvE,KAAKoG;EACf;EAKD,IAAA,SAAIG;AACA,WAAOvG,KAAKqG;EACf;EAKD,IAAA,QAAIG;AACA,WAAOxG,KAAKqG;EACf;EAKD,IAAA,UAAII;AACA,WAAA,CAAQzB,eAAehF,KAAKuE,KAAAA;EAC/B;EAOD,IAAA,cAAImC;AACA,QAAIxB,KAAUZ,gBAAgBtE,KAAKuE,KAAAA;AAEnC,WACoB,UAAhBW,GAAQyB,SACwB,iBAA/B3G,KAAKuG,QAAQK,kBAAAA,CAGR5G,KAAKuG,QAAQK,kBACa,oBAAxB1B,GAAQ2B;EAEvB;EAKD,IAAA,UAAIC;AAIA,WAHAC,QAAQC,KACJ,oIAAA,GAEGhH,KAAK0G;EACf;EAKD,IAAA,eAAIO;AAIA,WAHAF,QAAQC,KACJ,0IAAA,GAEuC,UAApC1C,gBAAgBtE,KAAKuE,KAAAA,EAAOoC,QAAAA,CAAmB3G,KAAK0G;EAC9D;EAKD,KAAKnC,IAAegC,IAAAA;AAChBvG,SAAKoG,YAAY7B,MAAS,IAC1BvE,KAAKqG,YAAYE,MAAU,MAE3BvG,KAAKkH,cAAAA;EACR;EAKD,QAAAC;AACInH,SAAKoG,YAAY,IACjBpG,KAAKqG,YAAY,MACjBrG,KAAKkH,cAAAA;EACR;EA0BD,eAAeE,IAAgBtF,KAAMmE,GAAAA;AACjC,UAAMoB,KAAUpG,YAAYmG,MAAU,EAAA,EAAItF,EAAAA,KAAQ;AAElD,QAAIrB,KAA+B,CAAA;AACnC,QAAA;AACIA,MAAAA,KAAOoE,KAAKC,MAAMuC,EAAAA,IAEE,SAAA,OAAT5G,MAAiC,YAAA,OAATA,MAAqB6G,MAAMC,QAAQ9G,EAAAA,OAClEA,KAAO,CAAA;IAEd,SAAQ0B,IAAAA;IAAK;AAEdnC,SAAKwH,KAAK/G,GAAK8D,SAAS,IAAI9D,GAAK8F,UAAU9F,GAAK+F,SAAS,IAAA;EAC5D;EAgBD,eAAerF,IAA4BW,KAAMmE,GAAAA;AAC7C,UAAMwB,KAAmC,EACrC/D,QAAAA,MACAG,UAAAA,MACAJ,UAAAA,MACAR,MAAM,IAAA,GAIJiC,KAAUZ,gBAAgBtE,KAAKuE,KAAAA;AAEjCkD,IAAAA,GAAevE,UADfgC,IAASE,MACgB,IAAI9B,KAAmB,MAAd4B,GAAQE,GAAAA,IAEjB,oBAAI9B,KAAK,YAAA,GAItCnC,KAAUb,OAAOgB,OAAO,CAAE,GAAEmG,IAAgBtG,EAAAA;AAE5C,UAAMkG,KAAU,EACZ9C,OAAOvE,KAAKuE,OACZgC,QAAQvG,KAAKuG,SAAS1B,KAAKC,MAAMD,KAAK6C,UAAU1H,KAAKuG,MAAAA,CAAAA,IAAW,KAAA;AAGpE,QAAInF,IAASgB,gBAAgBN,IAAK+C,KAAK6C,UAAUL,EAAAA,GAAUlG,EAAAA;AAE3D,UAAMwG,IACc,eAAA,OAATC,OAAuB,IAAIA,KAAK,CAACxG,CAAAA,CAAAA,EAASyG,OAAOzG,EAAOK;AAGnE,QAAI4F,GAAQd,UAAUoB,IAAe,MAAM;AACvCN,MAAAA,GAAQd,SAAS,EAAEuB,IAAIT,GAAQd,QAAQuB,IAAIC,OAAOV,GAAQd,QAAQwB,MAAAA;AAClE,YAAMC,KAAa,CAAC,gBAAgB,kBAAkB,UAAA;AACtD,iBAAWC,MAAQjI,KAAKuG,OAChByB,CAAAA,GAAWlH,SAASmH,EAAAA,MACpBZ,GAAQd,OAAO0B,EAAAA,IAAQjI,KAAKuG,OAAO0B,EAAAA;AAG3C7G,UAASgB,gBAAgBN,IAAK+C,KAAK6C,UAAUL,EAAAA,GAAUlG,EAAAA;IAC1D;AAED,WAAOC;EACV;EAUD,SAAS8G,IAA6BC,KAAAA,OAAkB;AAOpD,WANAnI,KAAKsG,mBAAmB8B,KAAKF,EAAAA,GAEzBC,MACAD,GAASlI,KAAKuE,OAAOvE,KAAKuG,MAAAA,GAGvB,MAAA;AACH,eAAS8B,KAAIrI,KAAKsG,mBAAmB7E,SAAS,GAAG4G,MAAK,GAAGA,KACrD,KAAIrI,KAAKsG,mBAAmB+B,EAAAA,KAAMH,GAG9B,QAAA,OAFOlI,KAAKsG,mBAAmB+B,EAAAA,GAAAA,KAC/BrI,KAAKsG,mBAAmBgC,OAAOD,IAAG,CAAA;IAGzC;EAER;EAES,gBAAAnB;AACN,eAAWgB,MAAYlI,KAAKsG,mBACxB4B,CAAAA,MAAYA,GAASlI,KAAKuE,OAAOvE,KAAKuG,MAAAA;EAE7C;AAAA;ACtOC,IAAOgC,iBAAP,cAA8BrC,cAAAA;EAIhC,YAAYsC,KAAa,mBAAA;AACrBzI,UAAAA,GAJIC,KAAeyI,kBAA2B,CAAA,GAM9CzI,KAAKwI,aAAaA,IAElBxI,KAAK0I,kBAAAA;EACR;EAKD,IAAA,QAAInE;AAGA,YAFavE,KAAK2I,YAAY3I,KAAKwI,UAAAA,KAAe,CAAA,GAEtCjE,SAAS;EACxB;EAKD,IAAA,SAAIgC;AACA,UAAM9F,KAAOT,KAAK2I,YAAY3I,KAAKwI,UAAAA,KAAe,CAAA;AAElD,WAAO/H,GAAK8F,UAAU9F,GAAK+F,SAAS;EACvC;EAKD,IAAA,QAAIA;AACA,WAAOxG,KAAKuG;EACf;EAKD,KAAKhC,IAAegC,IAAAA;AAChBvG,SAAK4I,YAAY5I,KAAKwI,YAAY,EAC9BjE,OAAOA,IACPgC,QAAQA,GAAAA,CAAAA,GAGZxG,MAAMyH,KAAKjD,IAAOgC,EAAAA;EACrB;EAKD,QAAAY;AACInH,SAAK6I,eAAe7I,KAAKwI,UAAAA,GAEzBzI,MAAMoH,MAAAA;EACT;EAUO,YAAYrF,IAAAA;AAChB,QAAsB,eAAA,OAAXgH,UAA0BA,QAAQC,cAAc;AACvD,YAAMC,KAAWF,OAAOC,aAAaE,QAAQnH,EAAAA,KAAQ;AACrD,UAAA;AACI,eAAO+C,KAAKC,MAAMkE,EAAAA;MACrB,SAAQjE,IAAAA;AAEL,eAAOiE;MACV;IACJ;AAGD,WAAOhJ,KAAKyI,gBAAgB3G,EAAAA;EAC/B;EAMO,YAAYA,IAAaY,IAAAA;AAC7B,QAAsB,eAAA,OAAXoG,UAA0BA,QAAQC,cAAc;AAEvD,UAAIG,KAAgBxG;AACC,kBAAA,OAAVA,OACPwG,KAAgBrE,KAAK6C,UAAUhF,EAAAA,IAEnCoG,OAAOC,aAAaI,QAAQrH,IAAKoH,EAAAA;IACpC,MAEGlJ,MAAKyI,gBAAgB3G,EAAAA,IAAOY;EAEnC;EAKO,eAAeZ,IAAAA;AAEG,mBAAA,OAAXgH,UAA0BA,QAAQC,gBACzCD,OAAOC,cAAcK,WAAWtH,EAAAA,GAAAA,OAI7B9B,KAAKyI,gBAAgB3G,EAAAA;EAC/B;EAKO,oBAAA4G;AAEkB,mBAAA,OAAXI,UACNA,QAAQC,gBACRD,OAAOO,oBAKZP,OAAOO,iBAAiB,WAAYtE,CAAAA,OAAAA;AAChC,UAAIA,GAAEjD,OAAO9B,KAAKwI,WACd;AAGJ,YAAM/H,KAAOT,KAAK2I,YAAY3I,KAAKwI,UAAAA,KAAe,CAAA;AAElDzI,YAAMyH,KAAK/G,GAAK8D,SAAS,IAAI9D,GAAK8F,UAAU9F,GAAK+F,SAAS,IAAA;IAAK,CAAA;EAEtE;AAAA;AAAA,ICtIiB8C,cDsIjB,MCtIiBA;EAGlB,YAAYC,IAAAA;AACRvJ,SAAKuJ,SAASA;EACjB;AAAA;ACHC,IAAOC,kBAAP,cAA+BF,YAAAA;EAMjC,MAAA,OAAanI,IAAAA;AAQT,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,iBAAiBvI,EAAAA;EAC5C;EAOD,MAAA,OACIwI,IACAxI,IAAAA;AAUA,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,SACRG,MAAMD,GAAAA,GAEVxI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,iBAAiBvI,EAAAA;EAC5C;EASD,MAAA,OACI0I,KAAqB,WACrB1I,IAAAA;AAYA,WAVAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EACFC,YAAYA,GAAAA,EAAAA,GAGpB1I,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,yBAAyBvI,EAAAA,EAAS2I,KAAK,MAAA,IAAM;EACxE;EAYD,MAAA,UACIC,IACAC,IACAC,IACA9I,IAAAA;AAcA,WAZAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EACF7B,OAAOiC,IACPE,UAAUD,IACVE,YAAYJ,GAAAA,EAAAA,GAGpB5I,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,4BAA4BvI,EAAAA,EAAS2I,KAAK,MAAA,IAAM;EAC3E;EAOD,MAAA,0BACIM,IACAC,IACAC,IACAC,IACAC,IACArJ,IAAAA;AAgBA,WAdAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EACFQ,UAAAA,IACAC,QAAAA,IACAC,OAAAA,IACAC,YAAAA,IACAC,UAAAA,GAAAA,EAAAA,GAGRrJ,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,8CAA8CvI,EAAAA;EACzE;AAAA;AClBL,IAAMsJ,IAAuB,CACzB,cACA,cACA,eACA,SACA,WACA,QACA,SACA,UAEA,SACA,eACA,WACA,aACA,aACA,UACA,QACA,YACA,YACA,kBACA,UACA,QAAA;AAIE,SAAUC,4BAA4BvJ,IAAAA;AACxC,MAAKA,IAAL;AAIAA,IAAAA,GAAQwJ,QAAQxJ,GAAQwJ,SAAS,CAAA;AACjC,aAAS7I,MAAOX,GACRsJ,GAAqB3J,SAASgB,EAAAA,MAIlCX,GAAQwJ,MAAM7I,EAAAA,IAAOX,GAAQW,EAAAA,GAAAA,OACtBX,GAAQW,EAAAA;EATlB;AAWL;AAEM,SAAU8I,qBAAqBC,IAAAA;AACjC,QAAMzJ,KAAwB,CAAA;AAE9B,aAAWU,MAAO+I,IAAQ;AACtB,UAAMC,KAAa/G,mBAAmBjC,EAAAA,GAChCiJ,KAAWzD,MAAMC,QAAQsD,GAAO/I,EAAAA,CAAAA,IAAQ+I,GAAO/I,EAAAA,IAAO,CAAC+I,GAAO/I,EAAAA,CAAAA;AAEpE,aAASkJ,MAAKD,GACVC,CAAAA,KAAIC,uBAAuBD,EAAAA,GACjB,SAANA,MAGJ5J,GAAOgH,KAAK0C,KAAa,MAAME,EAAAA;EAEtC;AAED,SAAO5J,GAAOwD,KAAK,GAAA;AACvB;AAGA,SAASqG,uBAAuBvI,IAAAA;AAC5B,SAAIA,QAAAA,KACO,OAGPA,cAAiBY,OACVS,mBAAmBrB,GAAMwI,YAAAA,EAAczF,QAAQ,KAAK,GAAA,CAAA,IAG1C,YAAA,OAAV/C,KACAqB,mBAAmBc,KAAK6C,UAAUhF,EAAAA,CAAAA,IAGtCqB,mBAAmBrB,EAAAA;AAC9B;AC3KM,IAAOyI,kBAAP,cAA+B7B,YAAAA;EAArC,cAAAnD;AAAAA,UAAAA,GAAAA,SAAAA,GACInG,KAAQoK,WAAW,IAEXpK,KAAWoL,cAAuB,MAClCpL,KAAaqL,gBAAkB,CAAA,GAC/BrL,KAAqBsL,wBAAkB,CAAA,GAEvCtL,KAAiBuL,oBAAW,MAE5BvL,KAAiBwL,oBAAW,GAC5BxL,KAAoByL,uBAAWC,IAAAA,GAC/B1L,KAAA2L,+BAA8C,CAClD,KAAK,KAAK,KAAK,KAAM,MAAM,MAAM,GAAA,GAE7B3L,KAAe4L,kBAA4B,CAAA;EAgetD;EA3dG,IAAA,cAAIC;AACA,WAAA,CAAA,CAAS7L,KAAKoL,eAAAA,CAAAA,CAAiBpL,KAAKoK,YAAAA,CAAapK,KAAK4L,gBAAgBnK;EACzE;EAwBD,MAAA,UACIqK,IACA5D,IACA/G,IAAAA;AAEA,QAAA,CAAK2K,GACD,OAAM,IAAIjM,MAAM,oBAAA;AAGpB,QAAIiC,KAAMgK;AAGV,QAAI3K,IAAS;AAETuJ,kCADAvJ,KAAUb,OAAOgB,OAAO,CAAE,GAAEH,EAAAA,CAAAA;AAE5B,YAAM4K,KACF,aACAhI,mBACIc,KAAK6C,UAAU,EAAEiD,OAAOxJ,GAAQwJ,OAAOqB,SAAS7K,GAAQ6K,QAAAA,CAAAA,CAAAA;AAEhElK,MAAAA,OAAQA,GAAIhB,SAAS,GAAA,IAAO,MAAM,OAAOiL;IAC5C;AAED,UAAME,WAAW,SAAUlH,IAAAA;AACvB,YAAMmH,KAAWnH;AAEjB,UAAItE;AACJ,UAAA;AACIA,QAAAA,KAAOoE,KAAKC,MAAMoH,IAAUzL,IAAAA;MAC/B,QAAC;MAAQ;AAEVyH,MAAAA,GAASzH,MAAQ,CAAA,CAAA;IACrB;AAmBA,WAhBKT,KAAKqL,cAAcvJ,EAAAA,MACpB9B,KAAKqL,cAAcvJ,EAAAA,IAAO,CAAA,IAE9B9B,KAAKqL,cAAcvJ,EAAAA,EAAKsG,KAAK6D,QAAAA,GAExBjM,KAAK6L,cAGoC,MAAnC7L,KAAKqL,cAAcvJ,EAAAA,EAAKL,SAAAA,MAEzBzB,KAAKmM,oBAAAA,IAGXnM,KAAKoL,aAAa/B,iBAAiBvH,IAAKmK,QAAAA,IAAAA,MANlCjM,KAAKoM,QAAAA,GASRC,YACIrM,KAAKsM,8BAA8BR,IAAOG,QAAAA;EAExD;EAaD,MAAA,YAAkBH,IAAAA;AACd,QAAIS,KAAAA;AAEJ,QAAKT,IAGE;AAEH,YAAMU,KAAOxM,KAAKyM,wBAAwBX,EAAAA;AAC1C,eAAShK,MAAO0K,GACZ,KAAKxM,KAAK0M,yBAAyB5K,EAAAA,GAAnC;AAIA,iBAASmK,MAAYjM,KAAKqL,cAAcvJ,EAAAA,EACpC9B,MAAKoL,aAAauB,oBAAoB7K,IAAKmK,EAAAA;AAAAA,eAExCjM,KAAKqL,cAAcvJ,EAAAA,GAGrByK,OACDA,KAAAA;MATH;IAYR,MAnBGvM,MAAKqL,gBAAgB,CAAA;AAqBpBrL,SAAK0M,yBAAAA,IAGCH,MAAAA,MACDvM,KAAKmM,oBAAAA,IAFXnM,KAAK4M,WAAAA;EAIZ;EAUD,MAAA,oBAA0BC,IAAAA;AACtB,QAAIC,KAAAA;AACJ,aAAShL,MAAO9B,KAAKqL,cAEjB,MAAMvJ,KAAM,KAAKiL,WAAWF,EAAAA,GAA5B;AAIAC,MAAAA,KAAAA;AACA,eAASb,MAAYjM,KAAKqL,cAAcvJ,EAAAA,EACpC9B,MAAKoL,aAAauB,oBAAoB7K,IAAKmK,EAAAA;AAAAA,aAExCjM,KAAKqL,cAAcvJ,EAAAA;IANzB;AASAgL,IAAAA,OAID9M,KAAK0M,yBAAAA,IAAAA,MAEC1M,KAAKmM,oBAAAA,IAGXnM,KAAK4M,WAAAA;EAEZ;EAWD,MAAA,8BACId,IACAG,IAAAA;AAEA,QAAIM,KAAAA;AAEJ,UAAMC,KAAOxM,KAAKyM,wBAAwBX,EAAAA;AAC1C,aAAShK,MAAO0K,IAAM;AAClB,UAAA,CACKlF,MAAMC,QAAQvH,KAAKqL,cAAcvJ,EAAAA,CAAAA,KAAAA,CACjC9B,KAAKqL,cAAcvJ,EAAAA,EAAKL,OAEzB;AAGJ,UAAIuL,KAAAA;AACJ,eAAS3E,KAAIrI,KAAKqL,cAAcvJ,EAAAA,EAAKL,SAAS,GAAG4G,MAAK,GAAGA,KACjDrI,MAAKqL,cAAcvJ,EAAAA,EAAKuG,EAAAA,MAAO4D,OAInCe,KAAAA,MAAQ,OACDhN,KAAKqL,cAAcvJ,EAAAA,EAAKuG,EAAAA,GAC/BrI,KAAKqL,cAAcvJ,EAAAA,EAAKwG,OAAOD,IAAG,CAAA,GAClCrI,KAAKoL,aAAauB,oBAAoB7K,IAAKmK,EAAAA;AAE1Ce,MAAAA,OAKAhN,KAAKqL,cAAcvJ,EAAAA,EAAKL,UAAAA,OAClBzB,KAAKqL,cAAcvJ,EAAAA,GAIzByK,MAAiBvM,KAAK0M,yBAAyB5K,EAAAA,MAChDyK,KAAAA;IAEP;AAEIvM,SAAK0M,yBAAAA,IAGCH,MAAAA,MACDvM,KAAKmM,oBAAAA,IAFXnM,KAAK4M,WAAAA;EAIZ;EAEO,yBAAyBK,IAAAA;AAI7B,QAHAjN,KAAKqL,gBAAgBrL,KAAKqL,iBAAiB,CAAA,GAGvC4B,GACA,QAAA,CAAA,CAASjN,KAAKqL,cAAc4B,EAAAA,GAAaxL;AAI7C,aAASK,MAAO9B,KAAKqL,cACjB,KAAMrL,KAAKqL,cAAcvJ,EAAAA,GAAML,OAC3B,QAAA;AAIR,WAAA;EACH;EAEO,MAAA,sBAAM0K;AACV,QAAKnM,KAAKoK,SASV,QAJApK,KAAKkN,4BAAAA,GAELlN,KAAKsL,wBAAwBtL,KAAKmN,4BAAAA,GAE3BnN,KAAKuJ,OACPG,KAAK,iBAAiB,EACnBD,QAAQ,QACRG,MAAM,EACFQ,UAAUpK,KAAKoK,UACfiB,eAAerL,KAAKsL,sBAAAA,GAExB8B,YAAYpN,KAAKqN,0BAAAA,EAAAA,CAAAA,EAEpBC,MAAOC,CAAAA,OAAAA;AACJ,UAAA,CAAIA,IAAKnN,QAGT,OAAMmN;IAAG,CAAA;EAEpB;EAEO,4BAAAF;AACJ,WAAO,cAAcrN,KAAKoK;EAC7B;EAEO,wBAAwB0B,IAAAA;AAC5B,UAAM1K,KAAwB,CAAA;AAG9B0K,IAAAA,KAAQA,GAAMhL,SAAS,GAAA,IAAOgL,KAAQA,KAAQ;AAE9C,aAAShK,MAAO9B,KAAKqL,cAAAA,EACZvJ,KAAM,KAAKiL,WAAWjB,EAAAA,MACvB1K,GAAOU,EAAAA,IAAO9B,KAAKqL,cAAcvJ,EAAAA;AAIzC,WAAOV;EACV;EAEO,8BAAA+L;AACJ,UAAM/L,KAAwB,CAAA;AAE9B,aAASU,MAAO9B,KAAKqL,cACbrL,MAAKqL,cAAcvJ,EAAAA,EAAKL,UACxBL,GAAOgH,KAAKtG,EAAAA;AAIpB,WAAOV;EACV;EAEO,8BAAA8L;AACJ,QAAKlN,KAAKoL,aAAV;AAIApL,WAAKwN,+BAAAA;AAEL,eAAS1L,MAAO9B,KAAKqL,cACjB,UAASY,MAAYjM,KAAKqL,cAAcvJ,EAAAA,EACpC9B,MAAKoL,YAAY/B,iBAAiBvH,IAAKmK,EAAAA;IAN9C;EASJ;EAEO,iCAAAuB;AACJ,QAAKxN,KAAKoL,YAIV,UAAStJ,MAAO9B,KAAKqL,cACjB,UAASY,MAAYjM,KAAKqL,cAAcvJ,EAAAA,EACpC9B,MAAKoL,YAAYuB,oBAAoB7K,IAAKmK,EAAAA;EAGrD;EAEO,MAAA,UAAMG;AACV,QAAA,EAAIpM,KAAKwL,oBAAoB,GAM7B,QAAO,IAAIiC,QAAQ,CAACC,IAASC,OAAAA;AACzB3N,WAAK4L,gBAAgBxD,KAAK,EAAEsF,SAAAA,IAASC,QAAAA,GAAAA,CAAAA,GAEjC3N,KAAK4L,gBAAgBnK,SAAS,KAKlCzB,KAAK4N,YAAAA;IAAa,CAAA;EAEzB;EAEO,cAAAA;AACJ5N,SAAK4M,WAAAA,IAAW,GAGhBiB,aAAa7N,KAAK8N,gBAAAA,GAClB9N,KAAK8N,mBAAmBC,WAAW,MAAA;AAC/B/N,WAAKgO,oBAAoB,IAAInO,MAAM,oCAAA,CAAA;IAAsC,GAC1EG,KAAKuL,iBAAAA,GAERvL,KAAKoL,cAAc,IAAI6C,YAAYjO,KAAKuJ,OAAO2E,SAAS,eAAA,CAAA,GAExDlO,KAAKoL,YAAY+C,UAAWhM,CAAAA,OAAAA;AACxBnC,WAAKgO,oBACD,IAAInO,MAAM,0CAAA,CAAA;IACb,GAGLG,KAAKoL,YAAY/B,iBAAiB,cAAetE,CAAAA,OAAAA;AAC7C,YAAMmH,KAAWnH;AACjB/E,WAAKoK,WAAW8B,IAAUkC,aAE1BpO,KAAKmM,oBAAAA,EACArC,KAAKuC,YAAAA;AACF,YAAIgC,KAAU;AACd,eAAOrO,KAAKsO,uBAAAA,KAA4BD,KAAU,IAC9CA,CAAAA,MAAAA,MAMMrO,KAAKmM,oBAAAA;MACd,CAAA,EAEJrC,KAAK,MAAA;AACF,iBAASyE,MAAKvO,KAAK4L,gBACf2C,CAAAA,GAAEb,QAAAA;AAIN1N,aAAK4L,kBAAkB,CAAA,GACvB5L,KAAKwL,oBAAoB,GACzBqC,aAAa7N,KAAKwO,kBAAAA,GAClBX,aAAa7N,KAAK8N,gBAAAA;AAGlB,cAAMW,KAAczO,KAAKyM,wBAAwB,YAAA;AACjD,iBAAS3K,MAAO2M,GACZ,UAASxC,MAAYwC,GAAY3M,EAAAA,EAC7BmK,CAAAA,GAASlH,EAAAA;MAEhB,CAAA,EAEJuI,MAAOC,CAAAA,OAAAA;AACJvN,aAAKoK,WAAW,IAChBpK,KAAKgO,oBAAoBT,EAAAA;MAAI,CAAA;IAC/B,CAAA;EAEb;EAEO,yBAAAe;AACJ,UAAMI,KAAe1O,KAAKmN,4BAAAA;AAC1B,QAAIuB,GAAajN,UAAUzB,KAAKsL,sBAAsB7J,OAClD,QAAA;AAGJ,eAAWkN,MAAKD,GACZ,KAAA,CAAK1O,KAAKsL,sBAAsBxK,SAAS6N,EAAAA,EACrC,QAAA;AAIR,WAAA;EACH;EAEO,oBAAoBpB,IAAAA;AAIxB,QAHAM,aAAa7N,KAAK8N,gBAAAA,GAClBD,aAAa7N,KAAKwO,kBAAAA,GAAAA,CAIZxO,KAAKoK,YAAAA,CAAapK,KAAKwL,qBAEzBxL,KAAKwL,oBAAoBxL,KAAKyL,sBAChC;AACE,eAAS8C,MAAKvO,KAAK4L,gBACf2C,CAAAA,GAAEZ,OAAO,IAAI/N,oBAAoB2N,EAAAA,CAAAA;AAIrC,aAFAvN,KAAK4L,kBAAkB,CAAA,GAAA,KACvB5L,KAAK4M,WAAAA;IAER;AAGD5M,SAAK4M,WAAAA,IAAW;AAChB,UAAMgC,KACF5O,KAAK2L,6BAA6B3L,KAAKwL,iBAAAA,KACvCxL,KAAK2L,6BACD3L,KAAK2L,6BAA6BlK,SAAS,CAAA;AAEnDzB,SAAKwL,qBACLxL,KAAKwO,qBAAqBT,WAAW,MAAA;AACjC/N,WAAK4N,YAAAA;IAAa,GACnBgB,EAAAA;EACN;EAEO,WAAWC,KAAAA,OAAgB;AAa/B,QAZI7O,KAAKoK,YAAYpK,KAAK8O,gBACtB9O,KAAK8O,aAAaxO,OAAO6E,KAAKnF,KAAKqL,aAAAA,CAAAA,GAGvCwC,aAAa7N,KAAK8N,gBAAAA,GAClBD,aAAa7N,KAAKwO,kBAAAA,GAClBxO,KAAKwN,+BAAAA,GACLxN,KAAKuJ,OAAOwF,cAAc/O,KAAKqN,0BAAAA,CAAAA,GAC/BrN,KAAKoL,aAAa4D,MAAAA,GAClBhP,KAAKoL,cAAc,MACnBpL,KAAKoK,WAAW,IAAA,CAEXyE,IAAe;AAChB7O,WAAKwL,oBAAoB;AAOzB,eAAS+C,MAAKvO,KAAK4L,gBACf2C,CAAAA,GAAEb,QAAAA;AAEN1N,WAAK4L,kBAAkB,CAAA;IAC1B;EACJ;AAAA;ACrfC,IAAgBqD,cAAhB,cAAuC3F,YAAAA;EASzC,OAAc7I,IAAAA;AACV,WAAOA;EACV;EAiBD,MAAA,YACIyO,IACA/N,IAAAA;AAEA,QAAiC,YAAA,OAAtB+N,GACP,QAAOlP,KAAKmP,aAAgBD,IAAoB/N,EAAAA;AAKpD,QAAIiO,KAAQ;AAMZ,YARAjO,KAAUb,OAAOgB,OAAO,CAAE,GAAE4N,IAAoB/N,EAAAA,GAGpCiO,UACRA,KAAQjO,GAAQiO,OAAAA,OACTjO,GAAQiO,QAGZpP,KAAKmP,aAAgBC,IAAOjO,EAAAA;EACtC;EASD,MAAA,QACIkO,KAAO,GACPC,KAAU,IACVnO,IAAAA;AAiBA,YAfAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGIwJ,QAAQrK,OAAOgB,OACnB,EACI+N,MAAMA,IACNC,SAASA,GAAAA,GAEbnO,GAAQwJ,KAAAA,GAGL3K,KAAKuJ,OAAOG,KAAK1J,KAAKuP,cAAcpO,EAAAA,EAAS2I,KAAM0F,CAAAA,QACtDA,GAAaC,QACTD,GAAaC,OAAO/K,IAAKgL,CAAAA,OACd1P,KAAKqB,OAAUqO,EAAAA,CAAAA,KACpB,CAAA,GAEHF,GAAAA;EAEd;EAeD,MAAA,iBAA8BG,IAAgBxO,IAAAA;AAgB1C,YAfAA,KAAUb,OAAOgB,OACb,EACI8L,YAAY,mBAAmBpN,KAAKuP,eAAe,MAAMI,GAAAA,GAE7DxO,EAAAA,GAGIwJ,QAAQrK,OAAOgB,OACnB,EACIqO,QAAQA,IACRC,WAAW,EAAA,GAEfzO,GAAQwJ,KAAAA,GAGL3K,KAAK6P,QAAW,GAAG,GAAG1O,EAAAA,EAAS2I,KAAM1I,CAAAA,OAAAA;AACxC,UAAA,CAAKA,IAAQqO,OAAOhO,OAChB,OAAM,IAAI7B,oBAAoB,EAC1BM,QAAQ,KACRC,UAAU,EACN2P,MAAM,KACNlP,SAAS,wCACTH,MAAM,CAAE,EAAA,EAAA,CAAA;AAKpB,aAAOW,GAAOqO,MAAM,CAAA;IAAE,CAAA;EAE7B;EAWD,MAAA,OAAoB3H,IAAY3G,IAAAA;AAC5B,QAAA,CAAK2G,GACD,OAAM,IAAIlI,oBAAoB,EAC1BK,KAAKD,KAAKuJ,OAAO2E,SAASlO,KAAKuP,eAAe,GAAA,GAC9CrP,QAAQ,KACRC,UAAU,EACN2P,MAAM,KACNlP,SAAS,+BACTH,MAAM,CAAE,EAAA,EAAA,CAAA;AAYpB,WAPAU,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK1J,KAAKuP,eAAe,MAAMxL,mBAAmB+D,EAAAA,GAAK3G,EAAAA,EACvD2I,KAAM0F,CAAAA,OAAsBxP,KAAKqB,OAAUmO,EAAAA,CAAAA;EACnD;EASD,MAAA,OACI7F,IACAxI,IAAAA;AAUA,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAMD,GAAAA,GAEVxI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK1J,KAAKuP,cAAcpO,EAAAA,EACxB2I,KAAM0F,CAAAA,OAAsBxP,KAAKqB,OAAUmO,EAAAA,CAAAA;EACnD;EASD,MAAA,OACI1H,IACA6B,IACAxI,IAAAA;AAUA,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,SACRG,MAAMD,GAAAA,GAEVxI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK1J,KAAKuP,eAAe,MAAMxL,mBAAmB+D,EAAAA,GAAK3G,EAAAA,EACvD2I,KAAM0F,CAAAA,OAAsBxP,KAAKqB,OAAUmO,EAAAA,CAAAA;EACnD;EAOD,MAAA,OAAa1H,IAAY3G,IAAAA;AAQrB,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,SAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK1J,KAAKuP,eAAe,MAAMxL,mBAAmB+D,EAAAA,GAAK3G,EAAAA,EACvD2I,KAAK,MAAA,IAAM;EACnB;EAKS,aACNiG,KAAY,KACZ5O,IAAAA;AAAAA,KAEAA,KAAUA,MAAW,CAAA,GACbwJ,QAAQrK,OAAOgB,OACnB,EACIsO,WAAW,EAAA,GAEfzO,GAAQwJ,KAAAA;AAGZ,QAAIvJ,KAAmB,CAAA,GAEnB4O,UAAU3D,OAAOgD,OACVrP,KAAK6P,QAAQR,IAAMU,MAAa,KAAK5O,EAAAA,EAAS2I,KAAMmG,CAAAA,OAAAA;AACvD,YACMR,KADaQ,GACMR;AAIzB,aAFArO,KAASA,GAAO8O,OAAOT,EAAAA,GAEnBA,GAAMhO,UAAUwO,GAAKX,UACdU,QAAQX,KAAO,CAAA,IAGnBjO;IAAM,CAAA;AAIrB,WAAO4O,QAAQ,CAAA;EAClB;AAAA;AC1QC,SAAUG,2BACZC,IACAC,IACAC,IACA3F,IAAAA;AAEA,QACM4F,KAAAA,WAAkB5F;AAExB,SAAK4F,MAAAA,WAH2BD,KAO5BC,MACAxJ,QAAQC,KAAKoJ,EAAAA,GACbC,GAAYzG,OAAOtJ,OAAOgB,OAAO,CAAE,GAAE+O,GAAYzG,MAAM0G,EAAAA,GACvDD,GAAY1F,QAAQrK,OAAOgB,OAAO,CAAE,GAAE+O,GAAY1F,OAAOA,EAAAA,GAElD0F,MAGJ/P,OAAOgB,OAAO+O,IAAaC,EAAAA,IAXvBD;AAYf;ACpBM,SAAUG,iBAAiBjH,IAAAA;AAC5BA,EAAAA,GAAekH,oBAAAA;AACpB;ACyFM,IAAOC,gBAAP,cAA8CzB,YAAAA;EAGhD,YAAY1F,IAAgBQ,IAAAA;AACxBhK,UAAMwJ,EAAAA,GAENvJ,KAAK+J,qBAAqBA;EAC7B;EAKD,IAAA,eAAIwF;AACA,WAAOvP,KAAK2Q,qBAAqB;EACpC;EAKD,IAAA,qBAAIA;AACA,WAAO,sBAAsB5M,mBAAmB/D,KAAK+J,kBAAAA;EACxD;EAKD,IAAA,eAAI6G;AACA,WAC+B,iBAA3B5Q,KAAK+J,sBACsB,qBAA3B/J,KAAK+J;EAEZ;EAmBD,MAAA,UACI+B,IACA5D,IACA/G,IAAAA;AAEA,QAAA,CAAK2K,GACD,OAAM,IAAIjM,MAAM,gBAAA;AAGpB,QAAA,CAAKqI,GACD,OAAM,IAAIrI,MAAM,gCAAA;AAGpB,WAAOG,KAAKuJ,OAAOsH,SAASC,UACxB9Q,KAAK+J,qBAAqB,MAAM+B,IAChC5D,IACA/G,EAAAA;EAEP;EASD,MAAA,YAAkB2K,IAAAA;AAEd,WAAIA,KACO9L,KAAKuJ,OAAOsH,SAASE,YACxB/Q,KAAK+J,qBAAqB,MAAM+B,EAAAA,IAKjC9L,KAAKuJ,OAAOsH,SAASG,oBAAoBhR,KAAK+J,kBAAAA;EACxD;EAqBD,MAAA,YACIkH,IACA9P,IAAAA;AAEA,QAA6B,YAAA,OAAlB8P,GACP,QAAOlR,MAAMmR,YAAeD,IAAgB9P,EAAAA;AAGhD,UAAM0J,KAASvK,OAAOgB,OAAO,CAAA,GAAI2P,IAAgB9P,EAAAA;AAEjD,WAAOpB,MAAMmR,YAAerG,EAAAA;EAC/B;EAKD,MAAA,QACIwE,KAAO,GACPC,KAAU,IACVnO,IAAAA;AAEA,WAAOpB,MAAM8P,QAAWR,IAAMC,IAASnO,EAAAA;EAC1C;EAKD,MAAA,iBACIwO,IACAxO,IAAAA;AAEA,WAAOpB,MAAMoR,iBAAoBxB,IAAQxO,EAAAA;EAC5C;EAKD,MAAA,OAAoB2G,IAAY3G,IAAAA;AAC5B,WAAOpB,MAAMqR,OAAUtJ,IAAI3G,EAAAA;EAC9B;EAKD,MAAA,OACIwI,IACAxI,IAAAA;AAEA,WAAOpB,MAAMsR,OAAU1H,IAAYxI,EAAAA;EACtC;EAQD,MAAA,OACI2G,IACA6B,IACAxI,IAAAA;AAEA,WAAOpB,MAAMuR,OAAoBxJ,IAAI6B,IAAYxI,EAAAA,EAAS2I,KAAM4F,CAAAA,OAAAA;AAC5D,UAEI1P,KAAKuJ,OAAOgI,UAAUhL,QAAQuB,OAAO4H,IAAM5H,OAC1C9H,KAAKuJ,OAAOgI,UAAUhL,QAAQM,iBAAiB7G,KAAK+J,sBACjD/J,KAAKuJ,OAAOgI,UAAUhL,QAAQK,mBAC1B5G,KAAK+J,qBACf;AACE,YAAIyH,KAAalR,OAAOgB,OAAO,CAAE,GAAEtB,KAAKuJ,OAAOgI,UAAUhL,OAAOkL,MAAAA,GAC5DC,KAAapR,OAAOgB,OAAO,CAAE,GAAEtB,KAAKuJ,OAAOgI,UAAUhL,QAAQmJ,EAAAA;AAC7D8B,QAAAA,OAEAE,GAAWD,SAASnR,OAAOgB,OAAOkQ,IAAY9B,GAAK+B,MAAAA,IAGvDzR,KAAKuJ,OAAOgI,UAAU/J,KAAKxH,KAAKuJ,OAAOgI,UAAUhN,OAAOmN,EAAAA;MAC3D;AAED,aAAOhC;IAAgB,CAAA;EAE9B;EAQD,MAAA,OAAa5H,IAAY3G,IAAAA;AACrB,WAAOpB,MAAM4R,OAAO7J,IAAI3G,EAAAA,EAAS2I,KAAM8H,CAAAA,QAAAA,CAE/BA,MAEA5R,KAAKuJ,OAAOgI,UAAUhL,QAAQuB,OAAOA,MACpC9H,KAAKuJ,OAAOgI,UAAUhL,QAAQM,iBAAiB7G,KAAK+J,sBACjD/J,KAAKuJ,OAAOgI,UAAUhL,QAAQK,mBAC1B5G,KAAK+J,sBAEb/J,KAAKuJ,OAAOgI,UAAUpK,MAAAA,GAGnByK,GAAAA;EAEd;EASS,aAAoBpC,IAAAA;AAC1B,UAAMjJ,KAASvG,KAAKqB,OAAOmO,IAAcjJ,UAAU,CAAA,CAAA;AAInD,WAFAvG,KAAKuJ,OAAOgI,UAAU/J,KAAKgI,IAAcjL,OAAOgC,EAAAA,GAEzCjG,OAAOgB,OAAO,CAAE,GAAEkO,IAAc,EAEnCjL,OAAOiL,IAAcjL,SAAS,IAC9BgC,QAAQA,GAAAA,CAAAA;EAEf;EAOD,MAAA,gBAAsBpF,IAAAA;AAUlB,WATAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,OAERoI,QAAQ,0BAAA,GAEZ1Q,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK1J,KAAK2Q,qBAAqB,iBAAiBxP,EAAAA;EACtE;EAYD,MAAA,iBACI2Q,IACAC,IACA5Q,IAAAA;AAcA,QAAI6Q;AAZJ7Q,IAAAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EACFqI,UAAUH,IACVC,UAAUA,GAAAA,EAAAA,GAGlB5Q,EAAAA,GAKAnB,KAAK4Q,iBACLoB,KAAuB7Q,GAAQ6Q,sBAAAA,OACxB7Q,GAAQ6Q,sBACV7Q,GAAQ+Q,eACT1B,iBAAiBxQ,KAAKuJ,MAAAA;AAI9B,QAAI4I,KAAAA,MAAiBnS,KAAKuJ,OAAOG,KAC7B1J,KAAK2Q,qBAAqB,uBAC1BxP,EAAAA;AAmBJ,WAhBAgR,KAAWnS,KAAKoS,aAAgBD,EAAAA,GAE5BH,MAAwBhS,KAAK4Q,gBD9XnC,SAAUyB,oBACZ9I,IACA+I,IACAC,IACAC,IAAAA;AAEAhC,uBAAiBjH,EAAAA;AAEjB,YAAMkJ,KAAgBlJ,GAAOmJ,YACvBC,KAAWpJ,GAAOgI,UAAUhL,QAI5BqM,IAAmBrJ,GAAOgI,UAAUsB,SAAS,CAACC,IAAUtM,OAAAA;AAAAA,SAAAA,CAErDsM,MACDtM,IAAOsB,MAAM6K,IAAU7K,OACrBtB,IAAOK,gBAAgB8L,IAAU9L,iBAC/BL,IAAOK,gBAAgB8L,IAAU9L,iBAErC2J,iBAAiBjH,EAAAA;MACpB,CAAA;AAIJA,MAAAA,GAAekH,oBAAoB,WAAA;AAChCmC,UAAAA,GACArJ,GAAOmJ,aAAaD,IAAAA,OACZlJ,GAAekH;MAC3B,GAEAlH,GAAOmJ,aAAarG,OAAOpM,IAAK8S,OAAAA;AAC5B,cAAMC,IAAWzJ,GAAOgI,UAAUhN;AAElC,YAAIwO,GAAYpI,OAAOuH,YACnB,QAAOO,KAAgBA,GAAcxS,IAAK8S,EAAAA,IAAe,EAAE9S,KAAAA,IAAK8S,aAAAA,GAAAA;AAGpE,YAAItM,IAAU8C,GAAOgI,UAAU9K;AAC/B,YAEIA,KAEAzB,eAAeuE,GAAOgI,UAAUhN,OAAO+N,EAAAA,EAEvC,KAAA;AAAA,gBACUC,GAAAA;QACT,SAAQpQ,IAAAA;AACLsE,cAAAA;QACH;AAIAA,aAAAA,MACK+L,GAAAA;AAIV,cAAMxG,IAAU+G,GAAY/G,WAAW,CAAA;AACvC,iBAASlK,MAAOkK,EACZ,KACyB,mBAArBlK,GAAI8B,YAAAA,KAEJoP,KAAYhH,EAAQlK,EAAAA,KACpByH,GAAOgI,UAAUhN,OACnB;AAEEyH,YAAQlK,EAAAA,IAAOyH,GAAOgI,UAAUhN;AAChC;QACH;AAIL,eAFAwO,GAAY/G,UAAUA,GAEfyG,KAAgBA,GAAcxS,IAAK8S,EAAAA,IAAe,EAAE9S,KAAAA,IAAK8S,aAAAA,GAAAA;MAAa;IAErF,ECqTgB/S,KAAKuJ,QACLyI,IACA,MAAMhS,KAAKiT,YAAY,EAAEf,aAAAA,KAAa,CAAA,GACtC,MACIlS,KAAKkT,iBACDpB,IACAC,IACAzR,OAAOgB,OAAO,EAAE4Q,aAAAA,KAAa,GAAQ/Q,EAAAA,CAAAA,CAAAA,GAK9CgR;EACV;EAsCD,MAAA,mBACIgB,IACArD,IACAsD,IACAC,IACAC,IACAhD,IACA3F,GAAAA;AAEA,QAAIxJ,IAAe,EACfsI,QAAQ,QACRG,MAAM,EACFuJ,UAAUA,IACVrD,MAAMA,IACNsD,cAAcA,IACdC,aAAaA,IACbC,YAAYA,GAAAA,EAAAA;AAWpB,WAPAnS,IAAUgP,2BACN,0OACAhP,GACAmP,IACA3F,CAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,qBAAqBxP,CAAAA,EACpD2I,KAAMrJ,CAAAA,OAAST,KAAKoS,aAAgB3R,EAAAA,CAAAA;EAC5C;EA2ED,kBAAyB8S,IAAAA;AAErB,QAAIA,GAAK9R,SAAS,KAA0B,YAAA,OAAd8R,KAAO,CAAA,EAIjC,QAHAxM,QAAQC,KACJ,0PAAA,GAEGhH,KAAKwT,mBACRD,KAAO,CAAA,KAAM,IACbA,KAAO,CAAA,KAAM,IACbA,KAAO,CAAA,KAAM,IACbA,KAAO,CAAA,KAAM,IACbA,KAAO,CAAA,KAAM,CAAA,GACbA,KAAO,CAAA,KAAM,CAAA,GACbA,KAAO,CAAA,KAAM,CAAE,CAAA;AAIvB,UAAME,KAASF,KAAO,CAAA,KAAM,CAAA;AAM5B,QAAIG,KAAmC;AAClCD,IAAAA,GAAOE,gBACRD,KAAoBE,iBAAAA,MAAiBC;AAIzC,UAAMhD,KAAW,IAAI1F,gBAAgBnL,KAAKuJ,MAAAA;AAE1C,aAASuK,UAAAA;AACLJ,MAAAA,IAAmB1E,MAAAA,GACnB6B,GAASE,YAAAA;IACZ;AAED,UAAMgD,KAAiC,CAAA,GACjC3G,KAAaqG,GAAOrG;AAK1B,WAJIA,OACA2G,GAAkB3G,aAAaA,KAG5BpN,KAAKgU,gBAAgBD,EAAAA,EACvBjK,KAAMmK,CAAAA,OAAAA;AACH,YAAMd,KAAWc,GAAYC,OAAOC,UAAUC,KACzC7F,CAAAA,OAAMA,GAAE5N,SAAS8S,GAAON,QAAAA;AAE7B,UAAA,CAAKA,GACD,OAAM,IAAIvT,oBACN,IAAIC,MAAM,gCAAgC4T,GAAON,QAAAA,IAAAA,CAAAA;AAIzD,YAAME,IAAcrT,KAAKuJ,OAAO2E,SAAS,sBAAA,GAGnCmG,IAAmBjH,KACnBpN,KAAKuJ,OAA0B,oBAAI6D,EAAAA,IAAAA;AAQzC,aANIiH,MACAA,EAAiBC,OAAOC,UAAU,MAAA;AAC9BT,gBAAAA;MAAS,IAIV,IAAIrG,QAAQpB,OAAOqB,IAASC,OAAAA;AAC/B,YAAA;AAAA,gBACUkD,GAASC,UAAU,WAAWzE,OAAOtH,OAAAA;AACvC,kBAAMyP,KAAW3D,GAASzG;AAE1B,gBAAA;AACI,kBAAA,CAAKrF,GAAE0P,SAASD,OAAazP,GAAE0P,MAC3B,OAAM,IAAI5U,MAAM,+BAAA;AAGpB,kBAAIkF,GAAE2P,SAAAA,CAAU3P,GAAE+K,KACd,OAAM,IAAIjQ,MACN,4CACIkF,GAAE2P,KAAAA;AAKd,oBAAMvT,KAAUb,OAAOgB,OAAO,CAAE,GAAEmS,EAAAA;AAAAA,qBAC3BtS,GAAQgS,UAAAA,OACRhS,GAAQwT,QAAAA,OACRxT,GAAQmS,YAAAA,OACRnS,GAAQwS,aAGXU,GAAkBC,QAAQC,YAC1BF,EAAiBC,OAAOC,UAAU;AAGtC,oBAAMpC,KAAAA,MAAiBnS,KAAKwT,mBACxBL,GAASxS,MACToE,GAAE+K,MACFqD,GAASC,cACTC,GACAI,GAAOH,YACPnS,EAAAA;AAGJuM,cAAAA,GAAQyE,EAAAA;YACX,SAAQ5E,IAAAA;AACLI,cAAAA,GAAO,IAAI/N,oBAAoB2N,EAAAA,CAAAA;YAClC;AAEDuG,oBAAAA;UAAS,CAAA;AAGb,gBAAMc,IAAuC,EACzCH,OAAO5D,GAASzG,SAAAA;AAEhBqJ,UAAAA,GAAOkB,QAAQlT,WACfmT,EAAoB,QAAInB,GAAOkB,OAAO/P,KAAK,GAAA;AAG/C,gBAAM3E,IAAMD,KAAK6U,oBACb1B,GAAS2B,UAAUzB,GACnBuB,CAAAA;AAGJ,cAAIjB,IACAF,GAAOE,eACP,SAAU1T,IAAAA;AACFyT,YAAAA,KACAA,GAAkBqB,SAASC,OAAO/U,KAIlCyT,KAAoBE,iBAAiB3T,EAAAA;UAE7C;AAAA,gBAEE0T,EAAY1T,CAAAA;QACrB,SAAQsN,IAAAA;AACLuG,kBAAAA,GACAnG,GAAO,IAAI/N,oBAAoB2N,EAAAA,CAAAA;QAClC;MAAA,CAAA;IACH,CAAA,EAELD,MAAOC,CAAAA,OAAAA;AAEJ,YADAuG,QAAAA,GACMvG;IAAG,CAAA;EAEpB;EAkBD,MAAA,YACI+C,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,OAAA;AAUZ,WAPAtI,KAAUgP,2BACN,4GACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,iBAAiBxP,EAAAA,EAChD2I,KAAMrJ,CAAAA,OAAST,KAAKoS,aAAgB3R,EAAAA,CAAAA;EAC5C;EAeD,MAAA,qBACIsH,IACAuI,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACF7B,OAAOA,GAAAA,EAAAA;AAWf,WAPA5G,KAAUgP,2BACN,4IACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,2BAA2BxP,EAAAA,EAC1D2I,KAAK,MAAA,IAAM;EACnB;EA0BD,MAAA,qBACImL,IACAlD,IACAmD,IACA5E,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACFrF,OAAO0Q,IACPlD,UAAUA,IACVmD,iBAAiBA,GAAAA,EAAAA;AAWzB,WAPA/T,KAAUgP,2BACN,kMACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,2BAA2BxP,EAAAA,EAC1D2I,KAAK,MAAA,IAAM;EACnB;EAeD,MAAA,oBACI/B,IACAuI,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACF7B,OAAOA,GAAAA,EAAAA;AAWf,WAPA5G,KAAUgP,2BACN,0IACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,yBAAyBxP,EAAAA,EACxD2I,KAAK,MAAA,IAAM;EACnB;EAyBD,MAAA,oBACIqL,IACA7E,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACFrF,OAAO4Q,GAAAA,EAAAA;AAWf,WAPAhU,KAAUgP,2BACN,0IACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,yBAAyBxP,EAAAA,EACxD2I,KAAK,MAAA;AAEF,YAAM5E,KAAUZ,gBAAgB6Q,EAAAA,GAC1B3O,KAAQxG,KAAKuJ,OAAOgI,UAAUhL;AAWpC,aATIC,MAAAA,CACCA,GAAM4O,YACP5O,GAAMsB,OAAO5C,GAAQ4C,MACrBtB,GAAMK,iBAAiB3B,GAAQ2B,iBAE/BL,GAAM4O,WAAAA,MACNpV,KAAKuJ,OAAOgI,UAAU/J,KAAKxH,KAAKuJ,OAAOgI,UAAUhN,OAAOiC,EAAAA,IAAAA;IAGjD,CAAA;EAEtB;EAeD,MAAA,mBACI6O,IACA/E,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACFyL,UAAUA,GAAAA,EAAAA;AAWlB,WAPAlU,KAAUgP,2BACN,8IACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,yBAAyBxP,EAAAA,EACxD2I,KAAK,MAAA,IAAM;EACnB;EA2BD,MAAA,mBACIwL,IACAvD,IACAzB,IACA3F,IAAAA;AAEA,QAAIxJ,KAAe,EACfsI,QAAQ,QACRG,MAAM,EACFrF,OAAO+Q,IACPvD,UAAUA,GAAAA,EAAAA;AAWlB,WAPA5Q,KAAUgP,2BACN,4JACAhP,IACAmP,IACA3F,EAAAA,GAGG3K,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,yBAAyBxP,EAAAA,EACxD2I,KAAK,MAAA;AACF,YAAM5E,KAAUZ,gBAAgBgR,EAAAA,GAC1B9O,KAAQxG,KAAKuJ,OAAOgI,UAAUhL;AASpC,aAPIC,MACAA,GAAMsB,OAAO5C,GAAQ4C,MACrBtB,GAAMK,iBAAiB3B,GAAQ2B,gBAE/B7G,KAAKuJ,OAAOgI,UAAUpK,MAAAA,GAAAA;IAGf,CAAA;EAEtB;EASD,MAAA,kBACIoO,IACApU,IAAAA;AAEA,WAAOnB,KAAKuJ,OAAOY,WAAW,gBAAA,EAAkB+G,YAC5C5Q,OAAOgB,OAAO,CAAE,GAAEH,IAAS,EACvBwO,QAAQ3P,KAAKuJ,OAAOoG,OAAO,qBAAqB,EAAE7H,IAAIyN,GAAAA,CAAAA,EAAAA,CAAAA,CAAAA;EAGjE;EASD,MAAA,mBACIA,IACApC,IACAhS,IAAAA;AAEA,UAAMqU,KAAAA,MAAWxV,KAAKuJ,OAAOY,WAAW,gBAAA,EAAkBgH,iBACtDnR,KAAKuJ,OAAOoG,OAAO,qDAAqD,EACpE4F,UAAAA,IACApC,UAAAA,GAAAA,CAAAA,CAAAA;AAIR,WAAOnT,KAAKuJ,OACPY,WAAW,gBAAA,EACXwH,OAAO6D,GAAG1N,IAAI3G,EAAAA,EACd2I,KAAK,MAAA,IAAM;EACnB;EAOD,MAAA,WAAiB/B,IAAe5G,IAAAA;AAS5B,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EAAE7B,OAAOA,GAAAA,EAAAA,GAEnB5G,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK1J,KAAK2Q,qBAAqB,gBAAgBxP,EAAAA;EACrE;EAYD,MAAA,YACIsU,IACA1D,IACA5Q,IAAAA;AAUA,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EAAE6L,OAAAA,IAAO1D,UAAAA,GAAAA,EAAAA,GAEnB5Q,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK1J,KAAK2Q,qBAAqB,kBAAkBxP,EAAAA,EACjD2I,KAAMrJ,CAAAA,OAAST,KAAKoS,aAAgB3R,EAAAA,CAAAA;EAC5C;EAaD,MAAA,YACI8U,IACA/K,IACArJ,IAAAA;AAAAA,KAEAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EAAEY,UAAUA,GAAAA,EAAAA,GAEtBrJ,EAAAA,GAEI6K,UAAU7K,GAAQ6K,WAAW,CAAA,GAChC7K,GAAQ6K,QAAQ0J,kBACjBvU,GAAQ6K,QAAQ0J,gBAAgB1V,KAAKuJ,OAAOgI,UAAUhN;AAK1D,UAAMgF,KAAS,IAAIoM,OACf3V,KAAKuJ,OAAOqM,SACZ,IAAI1P,iBACJlG,KAAKuJ,OAAOsM,IAAAA,GAGV1D,KAAAA,MAAiB5I,GAAOG,KAC1B1J,KAAK2Q,qBAAqB,kBAAkB5M,mBAAmBwR,EAAAA,GAC/DpU,EAAAA;AAMJ,WAHAoI,GAAOgI,UAAU/J,KAAK2K,IAAU5N,OAAOvE,KAAKqB,OAAO8Q,IAAU5L,UAAU,CAAA,CAAA,CAAA,GAGhEgD;EACV;EAQO,oBACJtJ,IACA2U,KAAuC,CAAA,GAAA;AAEvC,QAAIkB,KAAU7V,IACV0K,KAAQ;AAEO1K,IAAAA,GAAI0B,QAAQ,GAAA,KACb,MACdmU,KAAU7V,GAAI8V,UAAU,GAAG9V,GAAI0B,QAAQ,GAAA,CAAA,GACvCgJ,KAAQ1K,GAAI8V,UAAU9V,GAAI0B,QAAQ,GAAA,IAAO,CAAA;AAG7C,UAAMqU,KAA0C,CAAA,GAG1CC,KAAYtL,GAAMlG,MAAM,GAAA;AAC9B,eAAWyR,MAASD,IAAW;AAC3B,UAAa,MAATC,GACA;AAGJ,YAAMC,KAAOD,GAAMzR,MAAM,GAAA;AACzBuR,MAAAA,GAAalS,mBAAmBqS,GAAK,CAAA,EAAG1Q,QAAQ,OAAO,GAAA,CAAA,CAAA,IACnD3B,oBAAoBqS,GAAK,CAAA,KAAM,IAAI1Q,QAAQ,OAAO,GAAA,CAAA;IACzD;AAGD,aAAS3D,MAAO8S,GACPA,CAAAA,GAAawB,eAAetU,EAAAA,MAIR,QAArB8S,GAAa9S,EAAAA,IAAAA,OACNkU,GAAalU,EAAAA,IAEpBkU,GAAalU,EAAAA,IAAO8S,GAAa9S,EAAAA;AAKzC6I,IAAAA,KAAQ;AACR,aAAS7I,MAAOkU,GACPA,CAAAA,GAAaI,eAAetU,EAAAA,MAIpB,MAAT6I,OACAA,MAAS,MAGbA,MACI5G,mBAAmBjC,GAAI2D,QAAQ,QAAQ,GAAA,CAAA,IACvC,MACA1B,mBAAmBiS,GAAalU,EAAAA,EAAK2D,QAAQ,QAAQ,GAAA,CAAA;AAG7D,WAAgB,MAATkF,KAAcmL,KAAU,MAAMnL,KAAQmL;EAChD;AAAA;AAGL,SAASlC,iBAAiB3T,IAAAA;AACtB,MAAsB,eAAA,OAAX6I,UAAAA,CAA2BA,QAAQuN,KAC1C,OAAM,IAAIzW,oBACN,IAAIC,MACA,uEAAA,CAAA;AAKZ,MAAIyW,KAAQ,MACRC,KAAS,KAETC,KAAc1N,OAAO2N,YACrBC,KAAe5N,OAAO6N;AAG1BL,EAAAA,KAAQA,KAAQE,KAAcA,KAAcF,IAC5CC,KAASA,KAASG,KAAeA,KAAeH;AAEhD,MAAIK,KAAOJ,KAAc,IAAIF,KAAQ,GACjCO,IAAMH,KAAe,IAAIH,KAAS;AAItC,SAAOzN,OAAOuN,KACVpW,IACA,gBACA,WACIqW,KACA,aACAC,KACA,UACAM,IACA,WACAD,KACA,uBAAA;AAEZ;ACvuCM,IAAOE,oBAAP,cAAiC7H,YAAAA;EAInC,IAAA,eAAIM;AACA,WAAO;EACV;EAWD,MAAA,OACIwH,IACAC,KAAAA,OACA7V,IAAAA;AAaA,WAXAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,OACRG,MAAM,EACFmN,aAAaA,IACbC,eAAeA,GAAAA,EAAAA,GAGvB7V,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK1J,KAAKuP,eAAe,WAAWpO,EAAAA,EAAS2I,KAAK,MAAA,IAAM;EAC9E;EAQD,MAAA,aACI3I,IAAAA;AASA,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK1J,KAAKuP,eAAe,mBAAmBpO,EAAAA;EAClE;EAOD,MAAA,SAAe4I,IAA4B5I,IAAAA;AAQvC,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,SAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KACG1J,KAAKuP,eACD,MACAxL,mBAAmBgG,EAAAA,IACnB,aACJ5I,EAAAA,EAEH2I,KAAK,MAAA,IAAM;EACnB;AAAA;ACvEC,IAAOmN,aAAP,cAA0B3N,YAAAA;EAM5B,MAAA,QACI+F,KAAO,GACPC,KAAU,IACVnO,IAAAA;AAYA,YAVAA,KAAUb,OAAOgB,OAAO,EAAEmI,QAAQ,MAAA,GAAStI,EAAAA,GAEnCwJ,QAAQrK,OAAOgB,OACnB,EACI+N,MAAMA,IACNC,SAASA,GAAAA,GAEbnO,GAAQwJ,KAAAA,GAGL3K,KAAKuJ,OAAOG,KAAK,aAAavI,EAAAA;EACxC;EASD,MAAA,OAAa2G,IAAY3G,IAAAA;AACrB,QAAA,CAAK2G,GACD,OAAM,IAAIlI,oBAAoB,EAC1BK,KAAKD,KAAKuJ,OAAO2E,SAAS,YAAA,GAC1BhO,QAAQ,KACRC,UAAU,EACN2P,MAAM,KACNlP,SAAS,4BACTH,MAAM,CAAE,EAAA,EAAA,CAAA;AAYpB,WAPAU,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,eAAe3F,mBAAmB+D,EAAAA,GAAK3G,EAAAA;EAClE;EAOD,MAAA,SAAeA,IAAAA;AAQX,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,mBAAmBvI,EAAAA;EAC9C;AAAA;ACrEC,IAAO+V,gBAAP,cAA6B5N,YAAAA;EAM/B,MAAA,MAAYnI,IAAAA;AAQR,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,eAAevI,EAAAA;EAC1C;AAAA;ACrBC,IAAOgW,cAAP,cAA2B7N,YAAAA;EAI7B,OACI/C,IACA6Q,IACAC,KAA2B,CAAA,GAAA;AAG3B,WADAtQ,QAAQC,KAAK,yDAAA,GACNhH,KAAKsX,OAAO/Q,IAAQ6Q,IAAUC,EAAAA;EACxC;EAKD,OACI9Q,IACA6Q,IACAC,KAA2B,CAAA,GAAA;AAE3B,QAAA,CACKD,MAAAA,CACA7Q,IAAQuB,MAAAA,CACPvB,IAAQM,gBAAAA,CAAgBN,IAAQK,eAElC,QAAO;AAGX,UAAM2Q,KAAQ,CAAA;AACdA,IAAAA,GAAMnP,KAAK,KAAA,GACXmP,GAAMnP,KAAK,OAAA,GACXmP,GAAMnP,KAAKrE,mBAAmBwC,GAAOM,gBAAgBN,GAAOK,cAAAA,CAAAA,GAC5D2Q,GAAMnP,KAAKrE,mBAAmBwC,GAAOuB,EAAAA,CAAAA,GACrCyP,GAAMnP,KAAKrE,mBAAmBqT,EAAAA,CAAAA;AAE9B,QAAIhW,KAASpB,KAAKuJ,OAAO2E,SAASqJ,GAAM3S,KAAK,GAAA,CAAA;AAE7C,QAAItE,OAAO6E,KAAKkS,EAAAA,EAAa5V,QAAQ;AAAA,gBAE7B4V,GAAYG,YAAAA,OACLH,GAAYG;AAGvB,YAAM3M,KAAS,IAAI4M,gBAAgBJ,EAAAA;AAEnCjW,MAAAA,OAAWA,GAAON,SAAS,GAAA,IAAO,MAAM,OAAO+J;IAClD;AAED,WAAOzJ;EACV;EAOD,MAAA,SAAeD,IAAAA;AAQX,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,OAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK,oBAAoBvI,EAAAA,EACzB2I,KAAMrJ,CAAAA,OAASA,IAAM8D,SAAS,EAAA;EACtC;AAAA;AC9DC,IAAOmT,gBAAP,cAA6BpO,YAAAA;EAM/B,MAAA,YAAkBnI,IAAAA;AAQd,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,gBAAgBvI,EAAAA;EAC3C;EAOD,MAAA,OAAawW,IAAkBxW,IAAAA;AAW3B,WAVAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAM,EACFjJ,MAAMgX,GAAAA,EAAAA,GAGdxW,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,gBAAgBvI,EAAAA,EAAS2I,KAAK,MAAA,IAAM;EAC/D;EAeD,MAAA,OACIH,IACAxI,IAAAA;AAUA,WARAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAMD,GAAAA,GAEVxI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,uBAAuBvI,EAAAA,EAAS2I,KAAK,MAAA,IAAM;EACtE;EAOD,MAAA,OAAahI,IAAaX,IAAAA;AAQtB,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,SAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK,gBAAgB3F,mBAAmBjC,EAAAA,CAAAA,IAAQX,EAAAA,EAChD2I,KAAK,MAAA,IAAM;EACnB;EAOD,MAAA,QAAchI,IAAaX,IAAAA;AAQvB,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,OAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK,gBAAgB3F,mBAAmBjC,EAAAA,CAAAA,YAAgBX,EAAAA,EACxD2I,KAAK,MAAA,IAAM;EACnB;EAKD,eAAevF,IAAezC,IAAAA;AAI1B,WAHAiF,QAAQC,KACJ,6EAAA,GAEGhH,KAAK4X,eAAerT,IAAOzC,EAAAA;EACrC;EAQD,eAAeyC,IAAezC,IAAAA;AAC1B,WAAO9B,KAAKuJ,OAAO2E,SACf,gBAAgBnK,mBAAmBjC,EAAAA,CAAAA,UAAciC,mBAAmBQ,EAAAA,CAAAA,EAAAA;EAE3E;AAAA;ACzHC,IAAOsT,cAAP,cAA2BvO,YAAAA;EAM7B,MAAA,YAAkBnI,IAAAA;AAQd,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,MAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,cAAcvI,EAAAA;EACzC;EAOD,MAAA,IAAU2W,IAAe3W,IAAAA;AAQrB,WAPAA,KAAUb,OAAOgB,OACb,EACImI,QAAQ,OAAA,GAEZtI,EAAAA,GAGGnB,KAAKuJ,OACPG,KAAK,cAAc3F,mBAAmB+T,EAAAA,CAAAA,IAAU3W,EAAAA,EAChD2I,KAAK,MAAA,IAAM;EACnB;AAAA;ACtCC,SAAUiO,OAAO9V,IAAAA;AACnB,SACqB,eAAA,OAAT2F,QAAwB3F,cAAe2F,QAC9B,eAAA,OAAToQ,QAAwB/V,cAAe+V,QAGtC,SAAR/V,MACkB,YAAA,OAARA,MACPA,GAAIgW,QACmB,eAAA,OAAdhU,aAAmD,kBAAtBA,UAAUC,WACzB,eAAA,OAAXC,UAA2BA,OAAeC;AAElE;AAKM,SAAU8T,WAAWtO,IAAAA;AACvB,SACIA,OAI2B,eAA1BA,GAAKzD,YAAYxF,QAIO,eAAA,OAAbwX,YAA4BvO,cAAgBuO;AAEhE;AAKM,SAAUC,aAAaxO,IAAAA;AACzB,aAAW9H,MAAO8H,IAAM;AACpB,UAAMyO,KAAS/Q,MAAMC,QAAQqC,GAAK9H,EAAAA,CAAAA,IAAQ8H,GAAK9H,EAAAA,IAAO,CAAC8H,GAAK9H,EAAAA,CAAAA;AAC5D,eAAWkJ,MAAKqN,GACZ,KAAIN,OAAO/M,EAAAA,EACP,QAAA;EAGX;AAED,SAAA;AACJ;AAoFA,IAAMsN,IAAwB;AAE9B,SAASC,mBAAmB7V,IAAAA;AACxB,MAAoB,YAAA,OAATA,GACP,QAAOA;AAGX,MAAa,UAATA,GACA,QAAA;AAGJ,MAAa,WAATA,GACA,QAAA;AAIJ,OACkB,QAAbA,GAAM,CAAA,KAAeA,GAAM,CAAA,KAAM,OAAOA,GAAM,CAAA,KAAM,QACrD4V,EAAsB9V,KAAKE,EAAAA,GAC7B;AACE,QAAI8V,KAAAA,CAAO9V;AACX,QAAI,KAAK8V,OAAQ9V,GACb,QAAO8V;EAEd;AAED,SAAO9V;AACX;ACzIM,IAAO+V,eAAP,cAA4BnP,YAAAA;EAAlC,cAAAnD;AAAAA,UAAAA,GAAAA,SAAAA,GACYnG,KAAQ0Y,WAAwB,CAAA,GAChC1Y,KAAIwM,OAAuC,CAAA;EA0DtD;EArDG,WAAWzC,IAAAA;AAQP,WAPK/J,KAAKwM,KAAKzC,EAAAA,MACX/J,KAAKwM,KAAKzC,EAAAA,IAAsB,IAAI4O,gBAChC3Y,KAAK0Y,UACL3O,EAAAA,IAID/J,KAAKwM,KAAKzC,EAAAA;EACpB;EAOD,MAAA,KAAW5I,IAAAA;AACP,UAAMyX,KAAW,IAAIT,YAEfU,KAAW,CAAA;AAEjB,aAASxQ,KAAI,GAAGA,KAAIrI,KAAK0Y,SAASjX,QAAQ4G,MAAK;AAC3C,YAAMyQ,KAAM9Y,KAAK0Y,SAASrQ,EAAAA;AAS1B,UAPAwQ,GAASzQ,KAAK,EACVqB,QAAQqP,GAAIrP,QACZxJ,KAAK6Y,GAAI7Y,KACT+L,SAAS8M,GAAI9M,SACbpC,MAAMkP,GAAIC,KAAAA,CAAAA,GAGVD,GAAIE,MACJ,UAASlX,MAAOgX,GAAIE,OAAO;AACvB,cAAMA,KAAQF,GAAIE,MAAMlX,EAAAA,KAAQ,CAAA;AAChC,iBAASmX,MAAQD,GACbJ,CAAAA,GAASM,OAAO,cAAc7Q,KAAI,MAAMvG,IAAKmX,EAAAA;MAEpD;IAER;AAYD,WAVAL,GAASM,OAAO,gBAAgBrU,KAAK6C,UAAU,EAAEgR,UAAUG,GAAAA,CAAAA,CAAAA,GAE3D1X,KAAUb,OAAOgB,OACb,EACImI,QAAQ,QACRG,MAAMgP,GAAAA,GAEVzX,EAAAA,GAGGnB,KAAKuJ,OAAOG,KAAK,cAAcvI,EAAAA;EACzC;AAAA;AAAA,IAGQwX,kBAHR,MAGQA;EAIT,YAAYD,IAA+B3O,IAAAA;AAHnC/J,SAAQ0Y,WAAwB,CAAA,GAIpC1Y,KAAK0Y,WAAWA,IAChB1Y,KAAK+J,qBAAqBA;EAC7B;EAOD,OACIJ,IACAxI,IAAAA;AAEAA,IAAAA,KAAUb,OAAOgB,OACb,EACIsI,MAAMD,MAAc,CAAE,EAAA,GAE1BxI,EAAAA;AAGJ,UAAM6O,KAAwB,EAC1BvG,QAAQ,OACRxJ,KACI,sBACA8D,mBAAmB/D,KAAK+J,kBAAAA,IACxB,WAAA;AAGR/J,SAAKmZ,eAAenJ,IAAS7O,EAAAA,GAE7BnB,KAAK0Y,SAAStQ,KAAK4H,EAAAA;EACtB;EAKD,OACIrG,IACAxI,IAAAA;AAEAA,IAAAA,KAAUb,OAAOgB,OACb,EACIsI,MAAMD,MAAc,CAAE,EAAA,GAE1BxI,EAAAA;AAGJ,UAAM6O,KAAwB,EAC1BvG,QAAQ,QACRxJ,KACI,sBACA8D,mBAAmB/D,KAAK+J,kBAAAA,IACxB,WAAA;AAGR/J,SAAKmZ,eAAenJ,IAAS7O,EAAAA,GAE7BnB,KAAK0Y,SAAStQ,KAAK4H,EAAAA;EACtB;EAKD,OACIlI,IACA6B,IACAxI,IAAAA;AAEAA,IAAAA,KAAUb,OAAOgB,OACb,EACIsI,MAAMD,MAAc,CAAE,EAAA,GAE1BxI,EAAAA;AAGJ,UAAM6O,KAAwB,EAC1BvG,QAAQ,SACRxJ,KACI,sBACA8D,mBAAmB/D,KAAK+J,kBAAAA,IACxB,cACAhG,mBAAmB+D,EAAAA,EAAAA;AAG3B9H,SAAKmZ,eAAenJ,IAAS7O,EAAAA,GAE7BnB,KAAK0Y,SAAStQ,KAAK4H,EAAAA;EACtB;EAKD,OAAOlI,IAAY3G,IAAAA;AACfA,IAAAA,KAAUb,OAAOgB,OAAO,CAAE,GAAEH,EAAAA;AAE5B,UAAM6O,KAAwB,EAC1BvG,QAAQ,UACRxJ,KACI,sBACA8D,mBAAmB/D,KAAK+J,kBAAAA,IACxB,cACAhG,mBAAmB+D,EAAAA,EAAAA;AAG3B9H,SAAKmZ,eAAenJ,IAAS7O,EAAAA,GAE7BnB,KAAK0Y,SAAStQ,KAAK4H,EAAAA;EACtB;EAEO,eAAeA,IAAuB7O,IAAAA;AAS1C,QARAuJ,4BAA4BvJ,EAAAA,GAE5B6O,GAAQhE,UAAU7K,GAAQ6K,SAC1BgE,GAAQ+I,OAAO,CAAA,GACf/I,GAAQgJ,QAAQ,CAAA,GAAA,WAIL7X,GAAQwJ,OAAuB;AACtC,YAAMA,KAAQC,qBAAqBzJ,GAAQwJ,KAAAA;AACvCA,MAAAA,OACAqF,GAAQ/P,QAAQ+P,GAAQ/P,IAAIa,SAAS,GAAA,IAAO,MAAM,OAAO6J;IAEhE;AAID,QAAIf,KAAOzI,GAAQyI;AACfsO,eAAWtO,EAAAA,MACXA,KDhHN,SAAUwP,wBAAwBR,IAAAA;AACpC,UAAIxX,KAAiC,CAAA;AAsBrC,aApBAwX,GAASS,QAAQ,CAACrO,IAAGsO,OAAAA;AACjB,YAAU,mBAANA,MAAoC,YAAA,OAALtO,GAC/B,KAAA;AACI,cAAIuO,KAAS1U,KAAKC,MAAMkG,EAAAA;AACxB1K,iBAAOgB,OAAOF,IAAQmY,EAAAA;QACzB,SAAQhM,IAAAA;AACLxG,kBAAQC,KAAK,uBAAuBuG,EAAAA;QACvC;YAAA,YAEUnM,GAAOkY,EAAAA,KACThS,MAAMC,QAAQnG,GAAOkY,EAAAA,CAAAA,MACtBlY,GAAOkY,EAAAA,IAAK,CAAClY,GAAOkY,EAAAA,CAAAA,IAExBlY,GAAOkY,EAAAA,EAAGlR,KAAKmQ,mBAAmBvN,EAAAA,CAAAA,KAElC5J,GAAOkY,EAAAA,IAAKf,mBAAmBvN,EAAAA;MAEtC,CAAA,GAGE5J;IACX,ECwF2CwI,EAAAA;AAGnC,eAAW9H,MAAO8H,IAAM;AACpB,YAAM3H,KAAM2H,GAAK9H,EAAAA;AAEjB,UAAIiW,OAAO9V,EAAAA,EACP+N,CAAAA,GAAQgJ,MAAMlX,EAAAA,IAAOkO,GAAQgJ,MAAMlX,EAAAA,KAAQ,CAAA,GAC3CkO,GAAQgJ,MAAMlX,EAAAA,EAAKsG,KAAKnG,EAAAA;eACjBqF,MAAMC,QAAQtF,EAAAA,GAAM;AAC3B,cAAMuX,KAAa,CAAA,GACbC,KAAe,CAAA;AACrB,mBAAWzO,MAAK/I,GACR8V,QAAO/M,EAAAA,IACPwO,GAAWpR,KAAK4C,EAAAA,IAEhByO,GAAarR,KAAK4C,EAAAA;AAI1B,YAAIwO,GAAW/X,SAAS,KAAK+X,GAAW/X,UAAUQ,GAAIR,QAAQ;AAG1DuO,UAAAA,GAAQgJ,MAAMlX,EAAAA,IAAOkO,GAAQgJ,MAAMlX,EAAAA,KAAQ,CAAA;AAC3C,mBAASmX,MAAQO,GACbxJ,CAAAA,GAAQgJ,MAAMlX,EAAAA,EAAKsG,KAAK6Q,EAAAA;QAE/B,WAGGjJ,GAAQ+I,KAAKjX,EAAAA,IAAO2X,IAEhBD,GAAW/X,SAAS,GAAG;AAIvB,cAAIiY,KAAU5X;AACTA,UAAAA,GAAIiL,WAAW,GAAA,KAASjL,GAAI6X,SAAS,GAAA,MACtCD,MAAW,MAGf1J,GAAQgJ,MAAMU,EAAAA,IAAW1J,GAAQgJ,MAAMU,EAAAA,KAAY,CAAA;AACnD,mBAAST,MAAQO,GACbxJ,CAAAA,GAAQgJ,MAAMU,EAAAA,EAAStR,KAAK6Q,EAAAA;QAEnC;MAER,MACGjJ,CAAAA,GAAQ+I,KAAKjX,EAAAA,IAAOG;IAE3B;EACJ;AAAA;AC9OS,IAAO0T,SAAP,MAAOA;EAUjB,IAAA,UAAIiE;AACA,WAAO5Z,KAAK4V;EACf;EAMD,IAAA,QAAY5K,IAAAA;AACRhL,SAAK4V,UAAU5K;EAClB;EAiHD,YAAY4K,KAAU,KAAKrE,IAAkCsE,KAAO,SAAA;AAJ5D7V,SAAiB6Z,oBAAuC,CAAA,GACxD7Z,KAAc8Z,iBAAqC,CAAA,GACnD9Z,KAAsB+Z,yBAAAA,MAG1B/Z,KAAK4V,UAAUA,IACf5V,KAAK6V,OAAOA,IAERtE,KACAvR,KAAKuR,YAAYA,KACO,eAAA,OAAVzI,UAA4BA,OAAekR,OAEzDha,KAAKuR,YAAY,IAAIrL,kBAErBlG,KAAKuR,YAAY,IAAIhJ,kBAIzBvI,KAAK+W,cAAc,IAAID,kBAAkB9W,IAAAA,GACzCA,KAAKgZ,QAAQ,IAAI7B,YAAYnX,IAAAA,GAC7BA,KAAKia,OAAO,IAAIhD,WAAWjX,IAAAA,GAC3BA,KAAKka,WAAW,IAAI1Q,gBAAgBxJ,IAAAA,GACpCA,KAAK6Q,WAAW,IAAI1F,gBAAgBnL,IAAAA,GACpCA,KAAKma,SAAS,IAAIjD,cAAclX,IAAAA,GAChCA,KAAKoa,UAAU,IAAI1C,cAAc1X,IAAAA,GACjCA,KAAKqa,QAAQ,IAAIxC,YAAY7X,IAAAA;EAChC;EAOD,IAAA,SAAIsa;AACA,WAAOta,KAAKmK,WAAW,aAAA;EAC1B;EAkBD,cAAAoQ;AACI,WAAO,IAAI9B,aAAazY,IAAAA;EAC3B;EAKD,WAA4Bwa,IAAAA;AAKxB,WAJKxa,KAAK8Z,eAAeU,EAAAA,MACrBxa,KAAK8Z,eAAeU,EAAAA,IAAY,IAAI9J,cAAc1Q,MAAMwa,EAAAA,IAGrDxa,KAAK8Z,eAAeU,EAAAA;EAC9B;EAKD,iBAAiBC,IAAAA;AAGb,WAFAza,KAAK+Z,yBAAAA,CAAAA,CAA2BU,IAEzBza;EACV;EAKD,cAAcoN,IAAAA;AAMV,WALIpN,KAAK6Z,kBAAkBzM,EAAAA,MACvBpN,KAAK6Z,kBAAkBzM,EAAAA,EAAYsN,MAAAA,GAAAA,OAC5B1a,KAAK6Z,kBAAkBzM,EAAAA,IAG3BpN;EACV;EAKD,oBAAA2a;AACI,aAASrB,MAAKtZ,KAAK6Z,kBACf7Z,MAAK6Z,kBAAkBP,EAAAA,EAAGoB,MAAAA;AAK9B,WAFA1a,KAAK6Z,oBAAoB,CAAA,GAElB7Z;EACV;EAyBD,OAAO4a,IAAa/P,IAAAA;AAChB,QAAA,CAAKA,GACD,QAAO+P;AAGX,aAAS9Y,MAAO+I,IAAQ;AACpB,UAAI5I,KAAM4I,GAAO/I,EAAAA;AACjB,cAAA,OAAeG,IAAAA;QACX,KAAK;QACL,KAAK;AACDA,UAAAA,KAAM,KAAKA;AACX;QACJ,KAAK;AACDA,UAAAA,KAAM,MAAMA,GAAIwD,QAAQ,MAAM,KAAA,IAAS;AACvC;QACJ;AAEQxD,UAAAA,KADQ,SAARA,KACM,SACCA,cAAeqB,OAChB,MAAMrB,GAAIiJ,YAAAA,EAAczF,QAAQ,KAAK,GAAA,IAAO,MAE5C,MAAMZ,KAAK6C,UAAUzF,EAAAA,EAAKwD,QAAQ,MAAM,KAAA,IAAS;MAAA;AAGnEmV,MAAAA,KAAMA,GAAIC,WAAW,OAAO/Y,KAAM,KAAKG,EAAAA;IAC1C;AAED,WAAO2Y;EACV;EAKD,WACIrU,IACA6Q,IACAC,KAA2B,CAAA,GAAA;AAG3B,WADAtQ,QAAQC,KAAK,uDAAA,GACNhH,KAAKgZ,MAAM1B,OAAO/Q,IAAQ6Q,IAAUC,EAAAA;EAC9C;EAKD,SAASpU,IAAAA;AAEL,WADA8D,QAAQC,KAAK,iDAAA,GACNhH,KAAKkO,SAASjL,EAAAA;EACxB;EAKD,SAASA,IAAAA;AACL,QAAIhD,KAAMD,KAAK4V;AA2Bf,WAvBsB,eAAA,OAAX9M,UAAAA,CACLA,OAAOiM,YACR9U,GAAI8M,WAAW,UAAA,KACf9M,GAAI8M,WAAW,SAAA,MAEhB9M,KAAM6I,OAAOiM,SAAS+F,QAAQnB,SAAS,GAAA,IACjC7Q,OAAOiM,SAAS+F,OAAO/E,UAAU,GAAGjN,OAAOiM,SAAS+F,OAAOrZ,SAAS,CAAA,IACpEqH,OAAOiM,SAAS+F,UAAU,IAE3B9a,KAAK4V,QAAQ7I,WAAW,GAAA,MACzB9M,MAAO6I,OAAOiM,SAASgG,YAAY,KACnC9a,MAAOA,GAAI0Z,SAAS,GAAA,IAAO,KAAK,MAGpC1Z,MAAOD,KAAK4V,UAIZ3S,OACAhD,MAAOA,GAAI0Z,SAAS,GAAA,IAAO,KAAK,KAChC1Z,MAAOgD,GAAK8J,WAAW,GAAA,IAAO9J,GAAK8S,UAAU,CAAA,IAAK9S,KAG/ChD;EACV;EAOD,MAAA,KAAoBgD,IAAc9B,IAAAA;AAC9BA,IAAAA,KAAUnB,KAAKgb,gBAAgB/X,IAAM9B,EAAAA;AAGrC,QAAIlB,KAAMD,KAAKkO,SAASjL,EAAAA;AAExB,QAAIjD,KAAK0S,YAAY;AACjB,YAAMtR,KAASd,OAAOgB,OAAO,CAAE,GAAA,MAAQtB,KAAK0S,WAAWzS,IAAKkB,EAAAA,CAAAA;AAAAA,iBAEjDC,GAAOnB,OAAAA,WACPmB,GAAOD,WAEdlB,KAAMmB,GAAOnB,OAAOA,IACpBkB,KAAUC,GAAOD,WAAWA,MACrBb,OAAO6E,KAAK/D,EAAAA,EAAQK,WAE3BN,KAAUC,IACV2F,SAASC,QACLD,QAAQC,KACJ,4GAAA;IAGf;AAGD,QAAA,WAAW7F,GAAQwJ,OAAuB;AACtC,YAAMA,KAAQC,qBAAqBzJ,GAAQwJ,KAAAA;AACvCA,MAAAA,OACA1K,OAAQA,GAAIa,SAAS,GAAA,IAAO,MAAM,OAAO6J,KAAAA,OAEtCxJ,GAAQwJ;IAClB;AAIsD,0BAAnD3K,KAAKib,UAAU9Z,GAAQ6K,SAAS,cAAA,KAChC7K,GAAQyI,QACgB,YAAA,OAAjBzI,GAAQyI,SAEfzI,GAAQyI,OAAO/E,KAAK6C,UAAUvG,GAAQyI,IAAAA;AAM1C,YAHkBzI,GAAQ+Z,SAASA,OAGlBjb,IAAKkB,EAAAA,EACjB2I,KAAKuC,OAAOlM,OAAAA;AACT,UAAIM,KAAY,CAAA;AAEhB,UAAA;AACIA,QAAAA,KAAAA,MAAaN,GAAS4Y,KAAAA;MACzB,SAAQ5W,IAAAA;MAGR;AAMD,UAJInC,KAAKmb,cACL1a,KAAAA,MAAaT,KAAKmb,UAAUhb,IAAUM,IAAMU,EAAAA,IAG5ChB,GAASD,UAAU,IACnB,OAAM,IAAIN,oBAAoB,EAC1BK,KAAKE,GAASF,KACdC,QAAQC,GAASD,QACjBO,MAAMA,GAAAA,CAAAA;AAId,aAAOA;IAAS,CAAA,EAEnB6M,MAAOC,CAAAA,OAAAA;AAEJ,YAAM,IAAI3N,oBAAoB2N,EAAAA;IAAI,CAAA;EAE7C;EASO,gBAAgBtK,IAAc9B,IAAAA;AAyDlC,SAxDAA,KAAUb,OAAOgB,OAAO,EAAEmI,QAAQ,MAAA,GAAwBtI,EAAAA,GAGlDyI,OFvZV,SAAUwR,0BAA0BxR,IAAAA;AACtC,UACwB,eAAA,OAAbuO,YAAAA,WACAvO,MACS,YAAA,OAATA,MACE,SAATA,MACAsO,WAAWtO,EAAAA,KAAAA,CACVwO,aAAaxO,EAAAA,EAEd,QAAOA;AAGX,YAAMyR,KAAO,IAAIlD;AAEjB,iBAAWrW,MAAO8H,IAAM;AACpB,cAAM3H,KAAM2H,GAAK9H,EAAAA;AAIjB,YAAA,WAAWG,GAIX,KAAmB,YAAA,OAARA,MAAqBmW,aAAa,EAAE3X,MAAMwB,GAAAA,CAAAA,GAK9C;AAEH,gBAAMiH,KAAgB5B,MAAMC,QAAQtF,EAAAA,IAAOA,KAAM,CAACA,EAAAA;AAClD,mBAAS+I,MAAK9B,GACVmS,CAAAA,GAAKnC,OAAOpX,IAAKkJ,EAAAA;QAExB,OAX4D;AAEzD,cAAI9F,KAAkC,CAAA;AACtCA,UAAAA,GAAQpD,EAAAA,IAAOG,IACfoZ,GAAKnC,OAAO,gBAAgBrU,KAAK6C,UAAUxC,EAAAA,CAAAA;QAC9C;MAOJ;AAED,aAAOmW;IACX,EEiXiDla,GAAQyI,IAAAA,GAGjDc,4BAA4BvJ,EAAAA,GAI5BA,GAAQwJ,QAAQrK,OAAOgB,OAAO,CAAA,GAAIH,GAAQ0J,QAAQ1J,GAAQwJ,KAAAA,GAAAA,WAC/CxJ,GAAQiM,eAAAA,UACXjM,GAAQma,eAAAA,UAAyBna,GAAQwJ,MAAM2Q,cAC/Cna,GAAQiM,aAAa,QACdjM,GAAQoa,cAAcpa,GAAQwJ,MAAM4Q,gBAC3Cpa,GAAQiM,aAAajM,GAAQoa,cAAcpa,GAAQwJ,MAAM4Q,cAAAA,OAI1Dpa,GAAQma,aAAAA,OACRna,GAAQwJ,MAAM2Q,aAAAA,OACdna,GAAQoa,YAAAA,OACRpa,GAAQwJ,MAAM4Q,YAMmC,SAApDvb,KAAKib,UAAU9Z,GAAQ6K,SAAS,cAAA,KAC/BkM,WAAW/W,GAAQyI,IAAAA,MAEpBzI,GAAQ6K,UAAU1L,OAAOgB,OAAO,CAAE,GAAEH,GAAQ6K,SAAS,EACjD,gBAAgB,mBAAA,CAAA,IAKmC,SAAvDhM,KAAKib,UAAU9Z,GAAQ6K,SAAS,iBAAA,MAChC7K,GAAQ6K,UAAU1L,OAAOgB,OAAO,CAAE,GAAEH,GAAQ6K,SAAS,EACjD,mBAAmBhM,KAAK6V,KAAAA,CAAAA,IAO5B7V,KAAKuR,UAAUhN,SAEsC,SAArDvE,KAAKib,UAAU9Z,GAAQ6K,SAAS,eAAA,MAEhC7K,GAAQ6K,UAAU1L,OAAOgB,OAAO,CAAE,GAAEH,GAAQ6K,SAAS,EACjD0J,eAAe1V,KAAKuR,UAAUhN,MAAAA,CAAAA,IAKlCvE,KAAK+Z,0BAAiD,SAAvB5Y,GAAQiM,YAAqB;AAC5D,YAAMA,KAAajM,GAAQiM,eAAejM,GAAQsI,UAAU,SAASxG;AAAAA,aAE9D9B,GAAQiM,YAGfpN,KAAK+O,cAAc3B,EAAAA;AAEnB,YAAMoO,KAAa,IAAIC;AACvBzb,WAAK6Z,kBAAkBzM,EAAAA,IAAcoO,IACrCra,GAAQmT,SAASkH,GAAWlH;IAC/B;AAED,WAAOnT;EACV;EAMO,UACJ6K,IACArL,IAAAA;AAEAqL,IAAAA,KAAUA,MAAW,CAAA,GACrBrL,KAAOA,GAAKiD,YAAAA;AAEZ,aAAS9B,MAAOkK,GACZ,KAAIlK,GAAI8B,YAAAA,KAAiBjD,GACrB,QAAOqL,GAAQlK,EAAAA;AAIvB,WAAO;EACV;AAAA;ACzgBC,IAAO4Z,iBAAP,cAA8BxV,cAAAA;EAKhC,YAAYuN,IAAAA;AAcR1T,UAAAA,GAhBIC,KAAK2b,QAAqB,CAAA,GAkB9B3b,KAAK4b,WAAWnI,GAAOjM,MACvBxH,KAAK6b,YAAYpI,GAAOtM,OAExBnH,KAAK8b,SAAS,MAAM9b,KAAK+b,aAAatI,GAAOuI,OAAAA,CAAAA;EAChD;EAKD,KAAKzX,IAAegC,IAAAA;AAChBxG,UAAMyH,KAAKjD,IAAOgC,EAAAA;AAElB,QAAI7D,KAAQ;AACZ,QAAA;AACIA,MAAAA,KAAQmC,KAAK6C,UAAU,EAAEnD,OAAAA,IAAOgC,QAAAA,GAAAA,CAAAA;IACnC,SAAQgH,IAAAA;AACLxG,cAAQC,KAAK,mDAAA;IAChB;AAEDhH,SAAK8b,SAAS,MAAM9b,KAAK4b,SAASlZ,EAAAA,CAAAA;EACrC;EAKD,QAAAyE;AACIpH,UAAMoH,MAAAA,GAEFnH,KAAK6b,YACL7b,KAAK8b,SAAS,MAAM9b,KAAK6b,UAAAA,CAAAA,IAEzB7b,KAAK8b,SAAS,MAAM9b,KAAK4b,SAAS,EAAA,CAAA;EAEzC;EAKO,MAAA,aAAmB1W,IAAAA;AACvB,QAAA;AAGI,UAFAA,KAAAA,MAAgBA,IAEH;AACT,YAAIqU;AACmB,oBAAA,OAAZrU,KACPqU,KAAS1U,KAAKC,MAAMI,EAAAA,KAAY,CAAA,IACN,YAAA,OAAZA,OACdqU,KAASrU,KAGblF,KAAKwH,KAAK+R,GAAOhV,SAAS,IAAIgV,GAAOhT,UAAUgT,GAAO/S,SAAS,IAAA;MAClE;IACJ,SAAQrE,IAAAA;IAAK;EACjB;EAKO,SAAS8Z,IAAAA;AACbjc,SAAK2b,MAAMvT,KAAK6T,EAAAA,GAES,KAArBjc,KAAK2b,MAAMla,UACXzB,KAAKkc,SAAAA;EAEZ;EAKO,WAAAA;AACClc,SAAK2b,MAAMla,UAIhBzB,KAAK2b,MAAM,CAAA,EAAA,EAAKQ,QAAQ,MAAA;AACpBnc,WAAK2b,MAAMS,MAAAA,GAENpc,KAAK2b,MAAMla,UAIhBzB,KAAKkc,SAAAA;IAAU,CAAA;EAEtB;AAAA;", "names": ["ClientResponseError", "Error", "errData", "super", "this", "url", "status", "response", "isAbort", "originalError", "Object", "setPrototypeOf", "prototype", "data", "DOMException", "name", "message", "cause", "includes", "toJSON", "fieldContentRegExp", "cookieParse", "str", "options", "result", "decode", "assign", "defaultDecode", "index", "length", "eqIdx", "indexOf", "endIdx", "lastIndexOf", "key", "slice", "trim", "val", "charCodeAt", "_", "cookieSerialize", "opt", "encode", "defaultEncode", "test", "TypeError", "value", "maxAge", "isNaN", "isFinite", "Math", "floor", "domain", "path", "expires", "isDate", "toString", "call", "Date", "valueOf", "toUTCString", "httpOnly", "secure", "priority", "toLowerCase", "sameSite", "decodeURIComponent", "encodeURIComponent", "isReactNative", "navigator", "product", "global", "HermesInternal", "atobPolyfill", "getTokenPayload", "token", "encodedPayload", "split", "map", "c", "join", "JSON", "parse", "e", "isTokenExpired", "expirationThreshold", "payload", "keys", "exp", "now", "atob", "input", "String", "replace", "bs", "buffer", "bc", "idx", "output", "char<PERSON>t", "fromCharCode", "defaultCookieKey", "BaseAuthStore", "constructor", "baseToken", "baseModel", "_onChangeCallbacks", "record", "model", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "collectionName", "collectionId", "isAdmin", "console", "warn", "isAuthRecord", "trigger<PERSON>hange", "clear", "cookie", "rawData", "Array", "isArray", "save", "defaultOptions", "stringify", "result<PERSON><PERSON><PERSON>", "Blob", "size", "id", "email", "extraProps", "prop", "callback", "fireImmediately", "push", "i", "splice", "LocalAuthStore", "storageKey", "storageFallback", "_bindStorageEvent", "_storageGet", "_storageSet", "_storageRemove", "window", "localStorage", "rawValue", "getItem", "normalizedVal", "setItem", "removeItem", "addEventListener", "BaseService", "client", "SettingsService", "method", "send", "bodyParams", "body", "filesystem", "then", "collectionIdOrName", "toEmail", "emailTemplate", "template", "collection", "clientId", "teamId", "keyId", "privateKey", "duration", "knownSendOptionsKeys", "normalizeUnknownQueryParams", "query", "serializeQueryParams", "params", "<PERSON><PERSON><PERSON>", "arrV<PERSON>ue", "v", "prepareQueryParamValue", "toISOString", "RealtimeService", "eventSource", "subscriptions", "lastSentSubscriptions", "maxConnectTimeout", "reconnectAttempts", "maxReconnectAttempts", "Infinity", "predefinedReconnectIntervals", "pendingConnects", "isConnected", "topic", "serialized", "headers", "listener", "msgEvent", "submitSubscriptions", "connect", "async", "unsubscribeByTopicAndListener", "needToSubmit", "subs", "getSubscriptionsByTopic", "hasSubscriptionListeners", "removeEventListener", "disconnect", "keyPrefix", "hasAtleastOneTopic", "startsWith", "exist", "keyTo<PERSON>heck", "addAllSubscriptionListeners", "getNonEmptySubscriptionKeys", "request<PERSON>ey", "getSubscriptionsCancelKey", "catch", "err", "removeAllSubscriptionListeners", "Promise", "resolve", "reject", "initConnect", "clearTimeout", "connectTimeoutId", "setTimeout", "connectErrorHandler", "EventSource", "buildURL", "onerror", "lastEventId", "retries", "hasUnsentSubscriptions", "p", "reconnectTimeoutId", "connectSubs", "latestTopics", "t", "timeout", "fromReconnect", "onDisconnect", "cancelRequest", "close", "CrudService", "batchOrqueryParams", "_getFullList", "batch", "page", "perPage", "baseCrud<PERSON><PERSON>", "responseData", "items", "item", "filter", "skipTotal", "getList", "code", "batchSize", "request", "list", "concat", "normalizeLegacyOptionsArgs", "legacyWarn", "baseOptions", "bodyOrOptions", "<PERSON><PERSON><PERSON><PERSON>", "resetAutoRefresh", "_resetAutoRefresh", "RecordService", "baseCollectionPath", "isSuperusers", "realtime", "subscribe", "unsubscribe", "unsubscribeByPrefix", "batchOrOptions", "getFullList", "getFirstListItem", "getOne", "create", "update", "authStore", "authExpand", "expand", "authRecord", "delete", "success", "fields", "usernameOrEmail", "password", "autoRefreshThreshold", "identity", "autoRefresh", "authData", "authResponse", "registerAutoRefresh", "threshold", "refreshFunc", "reauthenticateFunc", "oldBeforeSend", "beforeSend", "oldModel", "unsubStoreChange", "onChange", "newToken", "sendOptions", "oldToken", "authRefresh", "authWithPassword", "provider", "codeVerifier", "redirectURL", "createData", "args", "authWithOAuth2Code", "config", "eagerDefaultPopup", "url<PERSON><PERSON><PERSON>", "openBrowserPopup", "undefined", "cleanup", "requestKeyOptions", "listAuthMethods", "authMethods", "oauth2", "providers", "find", "cancelController", "signal", "<PERSON>ab<PERSON>", "oldState", "state", "error", "scopes", "replacements", "_replaceQueryParams", "authURL", "location", "href", "passwordResetToken", "passwordConfirm", "verificationToken", "verified", "newEmail", "emailChangeToken", "recordId", "ea", "otpId", "Authorization", "Client", "baseURL", "lang", "url<PERSON><PERSON>", "substring", "parsedParams", "rawParams", "param", "pair", "hasOwnProperty", "open", "width", "height", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "left", "top", "CollectionService", "collections", "deleteMissing", "LogService", "HealthService", "FileService", "filename", "queryParams", "getURL", "parts", "download", "URLSearchParams", "BackupService", "basename", "getDownloadURL", "CronService", "jobId", "isFile", "File", "uri", "isFormData", "FormData", "hasFileField", "values", "inferNumberCharsRegex", "inferFormDataValue", "num", "BatchService", "requests", "SubBatchService", "formData", "jsonData", "req", "json", "files", "file", "append", "prepareRequest", "convertFormDataToObject", "for<PERSON>ach", "k", "parsed", "foundFiles", "foundRegular", "fileKey", "endsWith", "baseUrl", "cancelControllers", "recordServices", "enableAutoCancellation", "<PERSON><PERSON>", "logs", "settings", "health", "backups", "crons", "admins", "createBatch", "idOrName", "enable", "abort", "cancelAllRequests", "raw", "replaceAll", "origin", "pathname", "initSendOptions", "<PERSON><PERSON><PERSON><PERSON>", "fetch", "afterSend", "convertToFormDataIfNeeded", "form", "$autoCancel", "$cancelKey", "controller", "AbortController", "AsyncAuthStore", "queue", "saveFunc", "clearFunc", "_enqueue", "_loadInitial", "initial", "as<PERSON><PERSON><PERSON><PERSON>", "_dequeue", "finally", "shift"]}