import{a as getWindow,g as getDocument}from"./ssr-window.esm.min.mjs";function deleteProps(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}function nextTick(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function now(){return Date.now()}function getComputedStyle(e){const t=getWindow();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function getTranslate(e,t){void 0===t&&(t="x");const n=getWindow();let r,o,l;const s=getComputedStyle(e);return n.WebKitCSSMatrix?(o=s.transform||s.webkitTransform,o.split(",").length>6&&(o=o.split(", ").map((e=>e.replace(",","."))).join(", ")),l=new n.WebKitCSSMatrix("none"===o?"":o)):(l=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=l.toString().split(",")),"x"===t&&(o=n.WebKitCSSMatrix?l.m41:16===r.length?parseFloat(r[12]):parseFloat(r[4])),"y"===t&&(o=n.WebKitCSSMatrix?l.m42:16===r.length?parseFloat(r[13]):parseFloat(r[5])),o||0}function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function isNode(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function extend(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(null!=r&&!isNode(r)){const n=Object.keys(Object(r)).filter((e=>t.indexOf(e)<0));for(let t=0,o=n.length;t<o;t+=1){const o=n[t],l=Object.getOwnPropertyDescriptor(r,o);void 0!==l&&l.enumerable&&(isObject(e[o])&&isObject(r[o])?r[o].__swiper__?e[o]=r[o]:extend(e[o],r[o]):!isObject(e[o])&&isObject(r[o])?(e[o]={},r[o].__swiper__?e[o]=r[o]:extend(e[o],r[o])):e[o]=r[o])}}}return e}function setCSSProperty(e,t,n){e.style.setProperty(t,n)}function animateCSSModeScroll(e){let{swiper:t,targetPosition:n,side:r}=e;const o=getWindow(),l=-t.translate;let s,i=null;const a=t.params.speed;t.wrapperEl.style.scrollSnapType="none",o.cancelAnimationFrame(t.cssModeFrameID);const c=n>l?"next":"prev",m=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,u=()=>{s=(new Date).getTime(),null===i&&(i=s);const e=Math.max(Math.min((s-i)/a,1),0),c=.5-Math.cos(e*Math.PI)/2;let p=l+c*(n-l);if(m(p,n)&&(p=n),t.wrapperEl.scrollTo({[r]:p}),m(p,n))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:p})})),void o.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=o.requestAnimationFrame(u)};u()}function getSlideTransformEl(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function elementChildren(e,t){return void 0===t&&(t=""),[...e.children].filter((e=>e.matches(t)))}function createElement(e,t){void 0===t&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:[t]),n}function elementOffset(e){const t=getWindow(),n=getDocument(),r=e.getBoundingClientRect(),o=n.body,l=e.clientTop||o.clientTop||0,s=e.clientLeft||o.clientLeft||0,i=e===t?t.scrollY:e.scrollTop,a=e===t?t.scrollX:e.scrollLeft;return{top:r.top+i-l,left:r.left+a-s}}function elementPrevAll(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function elementNextAll(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function elementStyle(e,t){return getWindow().getComputedStyle(e,null).getPropertyValue(t)}function elementIndex(e){let t,n=e;if(n){for(t=0;null!==(n=n.previousSibling);)1===n.nodeType&&(t+=1);return t}}function elementParents(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function elementTransitionEnd(e,t){t&&e.addEventListener("transitionend",(function n(r){r.target===e&&(t.call(e,r),e.removeEventListener("transitionend",n))}))}function elementOuterSize(e,t,n){const r=getWindow();return n?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}export{elementParents as a,elementOffset as b,createElement as c,now as d,elementChildren as e,elementOuterSize as f,elementIndex as g,getTranslate as h,elementTransitionEnd as i,isObject as j,getSlideTransformEl as k,elementStyle as l,elementNextAll as m,nextTick as n,elementPrevAll as o,animateCSSModeScroll as p,extend as q,deleteProps as r,setCSSProperty as s};
//# sourceMappingURL=utils.min.mjs.map