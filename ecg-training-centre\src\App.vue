<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuth } from './composables/useAuth'
import AppHeader from './components/AppHeader.vue'
import AppFooter from './components/AppFooter.vue'

const { isAuthenticated, user } = useAuth()

onMounted(() => {
  console.log('ECG Training Centre - Vue.js app loaded successfully!')
})
</script>

<template>
  <div id="app" class="min-h-screen bg-gray-50 flex flex-col">
    <!-- Header -->
    <AppHeader />

    <!-- Main Content -->
    <main class="flex-1">
      <RouterView />
    </main>

    <!-- Footer -->
    <AppFooter />
  </div>
</template>

<style>
/* Global styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* HTMX Loading States */
.htmx-indicator {
  @apply opacity-0 transition-opacity duration-200 ease-in;
}

.htmx-request .htmx-indicator {
  @apply opacity-100;
}

.htmx-loading {
  @apply opacity-70 pointer-events-none;
}

.htmx-error-message {
  @apply mt-2;
}

.error-alert {
  @apply flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm;
}

/* Custom animations */
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  @apply animate-slide-up;
}

.bounce-in {
  @apply animate-bounce-in;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style>
