<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('ECG Training Centre website loaded successfully!')
})
</script>

<template>
  <div id="app">
    <h1>ECG Training Centre</h1>
    <p>Welcome to ECG Training Centre - Professional Training for the Power Sector</p>
    <nav>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#courses">Courses</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
    </nav>

    <div class="content">
      <h2>Our Training Programs</h2>
      <div class="programs">
        <div class="program-card">
          <h3>Power Systems Programs</h3>
          <p>Comprehensive training in power distribution, protection systems, and electrical safety.</p>
        </div>
        <div class="program-card">
          <h3>Renewable Energy Programs</h3>
          <p>Training in solar, wind, and sustainable energy technologies for the future.</p>
        </div>
        <div class="program-card">
          <h3>IT Proficiency</h3>
          <p>Digital skills, data analytics, and modern technology training for professionals.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #1e40af;
  text-align: center;
  margin-bottom: 10px;
  font-size: 2.5rem;
}

p {
  text-align: center;
  margin-bottom: 30px;
  color: #666;
}

nav ul {
  list-style: none;
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
}

nav a {
  text-decoration: none;
  color: #1e40af;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 5px;
  transition: background-color 0.3s;
}

nav a:hover {
  background-color: #f0f9ff;
}

.content h2 {
  color: #1e40af;
  text-align: center;
  margin-bottom: 30px;
}

.programs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.program-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: transform 0.3s, box-shadow 0.3s;
}

.program-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.program-card h3 {
  color: #059669;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.program-card p {
  text-align: left;
  margin: 0;
  color: #666;
}

@media (max-width: 768px) {
  nav ul {
    flex-direction: column;
    gap: 10px;
  }

  h1 {
    font-size: 2rem;
  }

  .programs {
    grid-template-columns: 1fr;
  }
}
</style>
