<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { f7ready, f7 } from 'framework7-vue'

// Framework7 parameters
const f7params = {
  name: 'ECG Training Centre',
  theme: 'auto',
  id: 'com.ecgtc.app',
  routes: [
    {
      path: '/',
      component: () => import('./views/HomePage.vue'),
    },
    {
      path: '/about/',
      component: () => import('./views/AboutPage.vue'),
    },
    {
      path: '/courses/',
      component: () => import('./views/CoursesPage.vue'),
    },
    {
      path: '/contact/',
      component: () => import('./views/ContactPage.vue'),
    },
    {
      path: '/payment/:courseId/:category',
      component: () => import('./views/PaymentPage.vue'),
    },
    {
      path: '/login/',
      component: () => import('./views/LoginPage.vue'),
    },
    {
      path: '/register/',
      component: () => import('./views/RegisterPage.vue'),
    },
    {
      path: '/profile/',
      component: () => import('./views/ProfilePage.vue'),
    },
  ],
}

onMounted(() => {
  f7ready(() => {
    // Framework7 is ready
    console.log('Framework7 is ready')
  })
})
</script>

<template>
  <f7-app :params="f7params">
    <!-- Status bar overlay for fullscreen mode-->
    <f7-statusbar></f7-statusbar>

    <!-- Main Framework7 App component where all pages will be loaded -->
    <f7-view main url="/"></f7-view>
  </f7-app>
</template>

<style>
/* Global styles */
:root {
  --f7-theme-color: #007aff;
  --f7-theme-color-rgb: 0, 122, 255;
}

/* Custom ECG branding colors */
.ecg-primary {
  --f7-theme-color: #1e40af;
  --f7-theme-color-rgb: 30, 64, 175;
}

.ecg-secondary {
  --f7-theme-color: #059669;
  --f7-theme-color-rgb: 5, 150, 105;
}
</style>
