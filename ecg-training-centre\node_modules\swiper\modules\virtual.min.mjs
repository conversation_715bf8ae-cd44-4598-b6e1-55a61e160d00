import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{s as setCSSProperty,e as elementChildren,c as createElement}from"../shared/utils.min.mjs";function Virtual(e){let s,{swiper:t,extendParams:r,on:i,emit:a}=e;r({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});const l=getDocument();t.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const d=l.createElement("div");function n(e,s){const r=t.params.virtual;if(r.cache&&t.virtual.cache[s])return t.virtual.cache[s];let i;return r.renderSlide?(i=r.renderSlide.call(t,e,s),"string"==typeof i&&(d.innerHTML=i,i=d.children[0])):i=t.isElement?createElement("swiper-slide"):createElement("div",t.params.slideClass),i.setAttribute("data-swiper-slide-index",s),r.renderSlide||(i.innerHTML=e),r.cache&&(t.virtual.cache[s]=i),i}function c(e){const{slidesPerView:s,slidesPerGroup:r,centeredSlides:i,loop:l}=t.params,{addSlidesBefore:d,addSlidesAfter:c}=t.params.virtual,{from:o,to:u,slides:p,slidesGrid:h,offset:f}=t.virtual;t.params.cssMode||t.updateActiveIndex();const v=t.activeIndex||0;let m,g,E;m=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",i?(g=Math.floor(s/2)+r+c,E=Math.floor(s/2)+r+d):(g=s+(r-1)+c,E=(l?s:r)+d);let x=v-E,w=v+g;l||(x=Math.max(x,0),w=Math.min(w,p.length-1));let S=(t.slidesGrid[x]||0)-(t.slidesGrid[0]||0);function b(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),a("virtualUpdate")}if(l&&v>=E?(x-=E,i||(S+=t.slidesGrid[0])):l&&v<E&&(x=-E,i&&(S+=t.slidesGrid[0])),Object.assign(t.virtual,{from:x,to:w,offset:S,slidesGrid:t.slidesGrid,slidesBefore:E,slidesAfter:g}),o===x&&u===w&&!e)return t.slidesGrid!==h&&S!==f&&t.slides.forEach((e=>{e.style[m]=S-Math.abs(t.cssOverflowAdjustment())+"px"})),t.updateProgress(),void a("virtualUpdate");if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:S,from:x,to:w,slides:function(){const e=[];for(let s=x;s<=w;s+=1)e.push(p[s]);return e}()}),void(t.params.virtual.renderExternalUpdate?b():a("virtualUpdate"));const A=[],M=[],y=e=>{let s=e;return e<0?s=p.length+e:s>=p.length&&(s-=p.length),s};if(e)t.slides.filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`))).forEach((e=>{e.remove()}));else for(let e=o;e<=u;e+=1)if(e<x||e>w){const s=y(e);t.slides.filter((e=>e.matches(`.${t.params.slideClass}[data-swiper-slide-index="${s}"], swiper-slide[data-swiper-slide-index="${s}"]`))).forEach((e=>{e.remove()}))}const P=l?-p.length:0,j=l?2*p.length:p.length;for(let s=P;s<j;s+=1)if(s>=x&&s<=w){const t=y(s);void 0===u||e?M.push(t):(s>u&&M.push(t),s<o&&A.push(t))}if(M.forEach((e=>{t.slidesEl.append(n(p[e],e))})),l)for(let e=A.length-1;e>=0;e-=1){const s=A[e];t.slidesEl.prepend(n(p[s],s))}else A.sort(((e,s)=>s-e)),A.forEach((e=>{t.slidesEl.prepend(n(p[e],e))}));elementChildren(t.slidesEl,".swiper-slide, swiper-slide").forEach((e=>{e.style[m]=S-Math.abs(t.cssOverflowAdjustment())+"px"})),b()}i("beforeInit",(()=>{if(!t.params.virtual.enabled)return;let e;if(void 0===t.passedParams.virtual.slides){const s=[...t.slidesEl.children].filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`)));s&&s.length&&(t.virtual.slides=[...s],e=!0,s.forEach(((e,s)=>{e.setAttribute("data-swiper-slide-index",s),t.virtual.cache[s]=e,e.remove()})))}e||(t.virtual.slides=t.params.virtual.slides),t.classNames.push(`${t.params.containerModifierClass}virtual`),t.params.watchSlidesProgress=!0,t.originalParams.watchSlidesProgress=!0,c()})),i("setTranslate",(()=>{t.params.virtual.enabled&&(t.params.cssMode&&!t._immediateVirtual?(clearTimeout(s),s=setTimeout((()=>{c()}),100)):c())})),i("init update resize",(()=>{t.params.virtual.enabled&&t.params.cssMode&&setCSSProperty(t.wrapperEl,"--swiper-virtual-size",`${t.virtualSize}px`)})),Object.assign(t.virtual,{appendSlide:function(e){if("object"==typeof e&&"length"in e)for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.push(e[s]);else t.virtual.slides.push(e);c(!0)},prependSlide:function(e){const s=t.activeIndex;let r=s+1,i=1;if(Array.isArray(e)){for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.unshift(e[s]);r=s+e.length,i=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){const e=t.virtual.cache,s={};Object.keys(e).forEach((t=>{const r=e[t],a=r.getAttribute("data-swiper-slide-index");a&&r.setAttribute("data-swiper-slide-index",parseInt(a,10)+i),s[parseInt(t,10)+i]=r})),t.virtual.cache=s}c(!0),t.slideTo(r,0)},removeSlide:function(e){if(null==e)return;let s=t.activeIndex;if(Array.isArray(e))for(let r=e.length-1;r>=0;r-=1)t.params.virtual.cache&&(delete t.virtual.cache[e[r]],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e[r],1),e[r]<s&&(s-=1),s=Math.max(s,0);else t.params.virtual.cache&&(delete t.virtual.cache[e],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e,1),e<s&&(s-=1),s=Math.max(s,0);c(!0),t.slideTo(s,0)},removeAllSlides:function(){t.virtual.slides=[],t.params.virtual.cache&&(t.virtual.cache={}),c(!0),t.slideTo(0,0)},update:c})}export{Virtual as default};
//# sourceMappingURL=virtual.min.mjs.map