"use strict";function n(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,a=Array(e);t<e;t++)a[t]=n[t];return a}function e(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function t(n,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(n,u(a.key),a)}}function a(n,e,a){return e&&t(n.prototype,e),a&&t(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n}function i(n,e,t){return(e=u(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function o(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,a)}return t}function r(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?o(Object(t),!0).forEach((function(e){i(n,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))}))}return n}function c(n,e){if(null==n)return{};var t,a,i=function(n,e){if(null==n)return{};var t={};for(var a in n)if({}.hasOwnProperty.call(n,a)){if(-1!==e.indexOf(a))continue;t[a]=n[a]}return t}(n,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(a=0;a<o.length;a++)t=o[a],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}function s(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var a,i,o,r,c=[],s=!0,l=!1;try{if(o=(t=t.call(n)).next,0===e){if(Object(t)!==t)return;s=!1}else for(;!(s=(a=o.call(t)).done)&&(c.push(a.value),c.length!==e);s=!0);}catch(n){l=!0,i=n}finally{try{if(!s&&null!=t.return&&(r=t.return(),Object(r)!==r))return}finally{if(l)throw i}}return c}}(n,e)||d(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(e)||d(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(n){var e=function(n,e){if("object"!=typeof n||!n)return n;var t=n[Symbol.toPrimitive];if(void 0!==t){var a=t.call(n,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(n)}(n,"string");return"symbol"==typeof e?e:e+""}function p(n){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},p(n)}function d(e,t){if(e){if("string"==typeof e)return n(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?n(e,t):void 0}}var h={cookieTestUrl:"https://legacy-staging.paystack.co/test-iframe/start.html",publishableKey:"uFmz/uE/SDT6GupOrSEXIZXGByjQ0zFkPyc9LqKHFqnTI0WPN3JS5kQPo/j9or0TOXlqMQj2lzHn/UGsQT4XeQ==",publicKey:"-----BEGIN PUBLIC KEY-----\r\nMFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALhZs/7hP0g0+hrqTq0hFyGVxgco0NMx\r\nZD8nPS6ihxap0yNFjzdyUuZED6P4/aK9Ezl5ajEI9pcx5/1BrEE+F3kCAwEAAQ==\r\n-----END PUBLIC KEY-----",applePayVersion:6,applePayValidateSessionPath:"applepay/validate-session/",applePayChargePath:"applepay/charge"},C=r(r({},h),{},{checkoutUrl:"http://localhost:8081/",paymentBaseUrl:"https://legacy-staging.paystack.co/",paystackApiUrl:"https://studio-api.paystack.co/",siteUrl:"https://paystack.com",pusherKey:"1c7b262ee18455815893",pusherUrl:"http://localhost:8081/static/vendor/pusher.min.js"}),m=r(r({},h),{},{checkoutUrl:"https://checkout-studio.paystack.com/",paymentBaseUrl:"https://legacy-staging.paystack.co/",paystackApiUrl:"https://studio-api.paystack.co/",siteUrl:"https://beta.paystack.com",pusherKey:"1c7b262ee18455815893",pusherUrl:"https://checkout-studio.paystack.com/static/vendor/pusher.min.js"}),y={dev:C,staging:m,production:r(r({},h),{},{checkoutUrl:"https://checkout.paystack.com/",paymentBaseUrl:"https://standard.paystack.co/",paystackApiUrl:"https://api.paystack.co/",siteUrl:"https://paystack.com",pusherKey:"8e4b9b7ca3418bd5cdc8",pusherUrl:"https://checkout.paystack.com/static/vendor/pusher.min.js"})}.production||m;function f(n,e){var t=[];return Object.keys(n).forEach((function(a){var i=e?"".concat(e,"[").concat(a,"]"):a,o=n[i];t.push(null!==o&&"object"===("undefined"==typeof v?"undefined":p(v))?f(o,i):"".concat(encodeURIComponent(a),"=").concat(encodeURIComponent(o)))})),t.join("&")}function g(){return document.currentScript||(n=document.getElementsByTagName("script"))[n.length-1];var n}function b(){var n=[],e=g();if(e){var t=Array.prototype.slice.call(e.attributes);n=Object.keys(t).filter((function(n){var e=t[n].nodeName;return e&&e.indexOf("data")>-1})).map((function(n){return t[n].nodeName}))}return n}var k='\n  <svg id="inline-button-wordmark--white" width="137" height="13" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path d="M.037 5.095l1.075-.135c-.011-.774-.025-1.944-.013-2.149C1.19 1.364 2.38.134 3.81.013 3.9.006 3.99.002 4.077 0a2.947 2.947 0 0 1 2.046.76c.574.509.95 1.26 1.008 *************.01 1.491.01 2.257l1.096.163L8.2 11.44 4.093 12 0 11.346l.037-6.251zm4.106-.514l1.724.256c-.007-.933-.05-2.295-.26-2.654-.319-.545-.846-.867-1.443-.88h-.063c-.607.008-1.138.322-1.458.864-.222.378-.266 1.66-.265 2.637l1.765-.223zM18.228 10.108c-.576 0-1.064-.072-1.464-.216a2.864 2.864 0 0 1-.972-.6 2.552 2.552 0 0 1-.588-.864 4.067 4.067 0 0 1-.252-1.044h1.008c.032.256.088.5.168.732.08.224.204.424.372.6.168.168.388.304.66.408.28.096.636.144 1.068.144.28 0 .536-.036.768-.108.24-.08.448-.192.624-.336.176-.144.312-.316.408-.516.104-.2.156-.42.156-.66 0-.24-.032-.448-.096-.624a1.02 1.02 0 0 0-.336-.468 1.885 1.885 0 0 0-.636-.324 6.4 6.4 0 0 0-1.008-.228 8.79 8.79 0 0 1-1.212-.276 3.246 3.246 0 0 1-.9-.432 1.982 1.982 0 0 1-.564-.672c-.128-.272-.192-.6-.192-.984 0-.328.068-.632.204-.912.136-.288.324-.536.564-.744.248-.208.54-.372.876-.492.336-.12.708-.18 1.116-.18.864 0 1.548.204 2.052.612.512.4.812.984.9 1.752h-.936c-.104-.544-.316-.932-.636-1.164-.32-.24-.78-.36-1.38-.36-.592 0-1.04.132-1.344.396a1.255 1.255 0 0 0-.444.996c0 .208.024.396.072.564.056.16.156.3.3.42.152.12.36.228.624.324a6.72 6.72 0 0 0 1.068.228c.48.072.9.168 1.26.288.36.12.664.276.912.468s.432.428.552.708c.128.28.192.624.192 1.032 0 .36-.076.696-.228 1.008a2.472 2.472 0 0 1-.612.804c-.264.224-.58.4-.948.528-.36.128-.752.192-1.176.192zM25.355 10.108c-.44 0-.848-.076-1.224-.228a2.916 2.916 0 0 1-.96-.636 2.966 2.966 0 0 1-.636-1.008 3.77 3.77 0 0 1-.216-1.308v-.096c0-.472.072-.904.216-1.296.144-.4.344-.74.6-1.02.264-.288.576-.508.936-.66.36-.16.756-.24 1.188-.24.36 0 .708.06 1.044.18.344.112.648.292.912.54.264.248.472.572.624.972.16.392.24.868.24 1.428v.324h-4.728c.024.72.204 1.272.54 1.656.336.376.828.564 1.476.564.984 0 1.54-.364 1.668-1.092h.996c-.112.632-.408 1.112-.888 1.44-.48.32-1.076.48-1.788.48zm1.704-3.852c-.048-.648-.232-1.112-.552-1.392-.312-.28-.728-.42-1.248-.42-.512 0-.932.164-1.26.492-.32.32-.524.76-.612 1.32h3.672zM32.091 10.108c-.44 0-.848-.072-1.224-.216a3.054 3.054 0 0 1-.972-.636 3.12 3.12 0 0 1-.648-1.008 3.626 3.626 0 0 1-.228-1.32v-.096c0-.48.08-.916.24-1.308.16-.4.376-.74.648-1.02.28-.28.604-.496.972-.648.376-.16.772-.24 1.188-.24.328 0 .644.04.948.12.312.08.588.208.828.384.248.168.456.392.624.672.168.28.276.62.324 1.02h-.984c-.08-.496-.284-.848-.612-1.056-.32-.208-.696-.312-1.128-.312a1.93 1.93 0 0 0-.804.168c-.24.112-.452.272-.636.48a2.23 2.23 0 0 0-.42.744 2.991 2.991 0 0 0-.156.996v.096c0 .776.188 1.364.564 1.764.384.392.88.588 1.488.588.224 0 .436-.032.636-.096a1.651 1.651 0 0 0 .96-.768c.112-.192.18-.416.204-.672h.924a2.595 2.595 0 0 1-.276.948 2.386 2.386 0 0 1-.576.744c-.24.208-.52.372-.84.492-.32.12-.668.18-1.044.18zM38.335 10.108a2.83 2.83 0 0 1-.876-.132 1.724 1.724 0 0 1-.684-.42 2.145 2.145 0 0 1-.456-.756c-.112-.304-.168-.672-.168-1.104V3.724h.996v3.924c0 .552.116.956.348 1.212.24.256.608.384 1.104.384.224 0 .44-.036.648-.108.208-.072.392-.18.552-.324.16-.144.288-.324.384-.54.096-.216.144-.464.144-.744V3.724h.996V10h-.996v-.996c-.144.296-.388.556-.732.78-.336.216-.756.324-1.26.324zM43.216 3.724h.996v1.128c.2-.352.452-.64.756-.864.312-.232.748-.356 1.308-.372v.936a4.461 4.461 0 0 0-.852.12 1.647 1.647 0 0 0-.66.324 1.472 1.472 0 0 0-.408.612c-.096.248-.144.564-.144.948V10h-.996V3.724zM50 10.108c-.44 0-.848-.076-1.224-.228a2.916 2.916 0 0 1-.96-.636 2.966 2.966 0 0 1-.636-1.008 3.77 3.77 0 0 1-.216-1.308v-.096c0-.472.072-.904.216-1.296.144-.4.344-.74.6-1.02.264-.288.576-.508.936-.66.36-.16.756-.24 1.188-.24.36 0 .708.06 1.044.18.344.112.648.292.912.54.264.248.472.572.624.972.16.392.24.868.24 1.428v.324h-4.728c.024.72.204 1.272.54 1.656.336.376.828.564 1.476.564.984 0 1.54-.364 1.668-1.092h.996c-.112.632-.408 1.112-.888 1.44-.48.32-1.076.48-1.788.48zm1.704-3.852c-.048-.648-.232-1.112-.552-1.392-.312-.28-.728-.42-1.248-.42-.512 0-.932.164-1.26.492-.32.32-.524.76-.612 1.32h3.672zM56.496 10.108c-.408 0-.788-.068-1.14-.204a2.683 2.683 0 0 1-.9-.612 3.01 3.01 0 0 1-.588-.984 4.01 4.01 0 0 1-.204-1.32v-.096c0-.48.072-.92.216-1.32.144-.4.344-.744.6-1.032.256-.296.564-.524.924-.684.36-.16.756-.24 1.188-.24.528 0 .956.112 1.284.336.328.216.584.476.768.78V.724h.996V10h-.996V8.92c-.088.152-.208.3-.36.444a2.792 2.792 0 0 1-.516.384 2.874 2.874 0 0 1-.6.252c-.216.072-.44.108-.672.108zm.108-.828c.288 0 .56-.048.816-.144.256-.096.476-.24.66-.432.184-.2.328-.448.432-.744.112-.304.168-.656.168-1.056v-.096c0-.808-.18-1.404-.54-1.788-.352-.384-.836-.576-1.452-.576-.624 0-1.112.208-1.464.624-.352.416-.528 1.008-.528 1.776v.096c0 .392.048.736.144 1.032.104.296.24.54.408.732.176.192.38.336.612.432.232.096.48.144.744.144zM67.712 10.108c-.512 0-.948-.112-1.308-.336a2.38 2.38 0 0 1-.816-.804V10h-.996V.724h.996V4.78a1.92 1.92 0 0 1 .348-.432c.152-.144.32-.268.504-.372.192-.112.396-.2.612-.264.216-.064.436-.096.66-.096.408 0 .788.072 1.14.216.352.144.652.352.9.624.256.272.456.604.6.996.144.392.216.832.216 1.32v.096c0 .48-.068.92-.204 1.32a3.103 3.103 0 0 1-.576 1.02 2.583 2.583 0 0 1-.9.672 2.937 2.937 0 0 1-1.176.228zm-.096-.828c.624 0 1.1-.2 1.428-.6.328-.408.492-.996.492-1.764V6.82c0-.4-.052-.748-.156-1.044a2.095 2.095 0 0 0-.42-.732 1.53 1.53 0 0 0-.612-.444 1.798 1.798 0 0 0-.744-.156c-.288 0-.56.048-.816.144a1.71 1.71 0 0 0-.648.444c-.184.192-.328.44-.432.744a3.152 3.152 0 0 0-.156 1.044v.096c0 .8.192 1.396.576 1.788.384.384.88.576 1.488.576zM73.63 9.352l-2.46-5.628h1.068l1.92 4.5 1.74-4.5h1.02l-3.468 8.46h-1.008l1.188-2.832zM87.127 3.669A3.138 3.138 0 0 0 86.1 2.95a3.09 3.09 0 0 0-1.228-.25c-.448 0-.848.086-1.187.26a2.199 2.199 0 0 0-.662.497v-.191a.387.387 0 0 0-.214-.348.323.323 0 0 0-.14-.03h-1.315a.314.314 0 0 0-.254.116.377.377 0 0 0-.1.262v8.97c0 .1.034.188.1.258a.34.34 0 0 0 .254.103h1.341a.342.342 0 0 0 .244-.103.336.336 0 0 0 .11-.259v-3.06c.178.202.417.357.702.464.35.134.72.203 1.093.203.43 0 .848-.082 1.242-.248a3.124 3.124 0 0 0 1.04-.724c.305-.326.545-.709.707-1.128a3.93 3.93 0 0 0 .263-1.477c0-.54-.086-1.037-.263-1.477a3.387 3.387 0 0 0-.706-1.12zm-1.204 3.24c-.073.19-.18.362-.315.51a1.415 1.415 0 0 1-1.065.466c-.2.001-.4-.04-.584-.12a1.484 1.484 0 0 1-.49-.346 1.593 1.593 0 0 1-.32-.51 1.738 1.738 0 0 1-.115-.63c0-.224.04-.435.115-.631a1.532 1.532 0 0 1 .804-.846c.185-.086.386-.13.59-.129.215 0 .414.044.593.13.177.083.338.199.474.341a1.622 1.622 0 0 1 .425 1.135c0 .225-.037.436-.112.63zM95.298 2.89h-1.33a.339.339 0 0 0-.246.11.384.384 0 0 0-.108.266v.166a1.856 1.856 0 0 0-.602-.472 2.525 2.525 0 0 0-1.166-.258 3.227 3.227 0 0 0-2.284.964 3.554 3.554 0 0 0-.734 1.123 3.827 3.827 0 0 0-.275 1.477c0 .54.092 1.037.275 1.477.184.434.427.817.728 1.128a3.146 3.146 0 0 0 2.277.973c.437 0 .834-.088 1.173-.259.25-.13.456-.287.608-.471v.177a.34.34 0 0 0 .11.259.341.341 0 0 0 .244.104h1.33a.324.324 0 0 0 .25-.105.349.349 0 0 0 .102-.258V3.267a.377.377 0 0 0-.1-.262.325.325 0 0 0-.252-.115zM93.502 6.9a1.55 1.55 0 0 1-.312.511c-.136.143-.296.26-.473.344-.178.085-.38.129-.596.129-.207 0-.407-.044-.59-.13a1.501 1.501 0 0 1-.791-.855 1.766 1.766 0 0 1-.112-.62c0-.225.038-.436.112-.632.075-.193.181-.364.314-.504.137-.143.3-.26.478-.342.182-.085.382-.129.59-.129.215 0 .417.044.595.13.178.085.338.2.473.341a1.623 1.623 0 0 1 .424 1.135c0 .215-.037.424-.112.622zM108.567 6.094a2.265 2.265 0 0 0-.654-.402c-.247-.101-.509-.181-.785-.235l-1.014-.204c-.26-.05-.441-.117-.543-.203a.328.328 0 0 1-.136-.264c0-.11.063-.2.189-.282.137-.086.329-.13.566-.13.26 0 .518.053.757.157.243.106.471.226.67.36.295.187.546.162.727-.053l.487-.57a.543.543 0 0 0 .152-.357c0-.128-.064-.245-.185-.351-.207-.184-.533-.378-.971-.568-.437-.192-.987-.29-1.637-.29-.427 0-.82.058-1.168.172-.35.116-.65.276-.893.474-.245.204-.438.44-.57.713a2 2 0 0 0-.198.875c0 .56.167 1.017.496 1.358.328.333.766.56 1.304.67l1.054.232c.3.062.528.132.675.21.129.067.19.163.19.297 0 .12-.061.227-.188.324-.133.104-.342.155-.622.155a1.83 1.83 0 0 1-.831-.19 3.056 3.056 0 0 1-.678-.458.995.995 0 0 0-.307-.17c-.126-.037-.268.003-.431.13l-.583.461c-.169.145-.24.32-.209.522.029.194.19.394.491.62.269.193.614.368 1.029.518.415.151.901.229 1.453.229.444 0 .854-.058 1.215-.172.362-.119.681-.278.941-.48a2.056 2.056 0 0 0 .819-1.663c0-.319-.053-.6-.165-.836a1.843 1.843 0 0 0-.447-.6zM114.383 7.73a.363.363 0 0 0-.295-.192.55.55 0 0 0-.343.113c-.095.062-.198.11-.306.141a.75.75 0 0 1-.426.013.43.43 0 0 1-.181-.093.554.554 0 0 1-.143-.204.92.92 0 0 1-.059-.362v-2.46h1.731c.099 0 .188-.04.266-.117a.368.368 0 0 0 .112-.26V3.268a.369.369 0 0 0-.115-.268.38.38 0 0 0-.263-.109h-1.732V1.216a.354.354 0 0 0-.108-.27.347.347 0 0 0-.243-.104h-1.344a.36.36 0 0 0-.34.226.371.371 0 0 0-.027.148V2.89h-.767a.324.324 0 0 0-.255.115.385.385 0 0 0-.098.262V4.31a.4.4 0 0 0 .212.346c.044.021.092.032.14.03h.768v2.925c0 .39.069.726.2 1.003.132.274.305.504.514.676.217.178.465.31.731.388.27.084.551.126.833.126.385 0 .75-.061 1.094-.18a2.13 2.13 0 0 0 .861-.552c.152-.181.17-.381.046-.581l-.463-.76zM121.672 2.89h-1.329a.339.339 0 0 0-.244.11.39.39 0 0 0-.08.122.394.394 0 0 0-.027.144v.166a1.906 1.906 0 0 0-.605-.472c-.335-.173-.726-.258-1.168-.258-.42 0-.834.083-1.226.249a3.24 3.24 0 0 0-1.055.715 3.528 3.528 0 0 0-.734 1.123 3.79 3.79 0 0 0-.276 1.477c0 .54.092 1.037.275 1.477.184.434.428.817.729 1.128a3.138 3.138 0 0 0 2.273.973 2.59 2.59 0 0 0 1.175-.259c.255-.13.457-.287.612-.471v.177a.34.34 0 0 0 .108.259.343.343 0 0 0 .243.104h1.329a.335.335 0 0 0 .252-.105.364.364 0 0 0 .102-.258V3.267a.38.38 0 0 0-.1-.262.332.332 0 0 0-.115-.087.311.311 0 0 0-.139-.028zM119.876 6.9a1.534 1.534 0 0 1-.786.855 1.362 1.362 0 0 1-.594.129c-.207 0-.405-.044-.588-.13a1.516 1.516 0 0 1-.792-.855 1.757 1.757 0 0 1-.113-.62c0-.225.037-.436.112-.632.073-.187.179-.358.314-.504.138-.143.3-.26.479-.342.184-.086.385-.13.588-.129.217 0 .415.044.594.13.181.085.34.2.472.341.134.143.24.313.314.504a1.73 1.73 0 0 1 0 1.253zM128.978 7.64l-.763-.593c-.146-.118-.284-.15-.404-.1a.742.742 0 0 0-.279.205 2.527 2.527 0 0 1-.583.535c-.192.122-.444.183-.742.183-.219 0-.42-.04-.6-.122a1.423 1.423 0 0 1-.469-.342 1.575 1.575 0 0 1-.308-.51 1.751 1.751 0 0 1-.106-.617c0-.228.034-.438.106-.632.07-.192.173-.363.308-.503.135-.144.295-.26.472-.342.187-.088.391-.132.597-.13.298 0 .547.064.742.187.198.126.396.306.584.534.078.092.17.16.278.206.122.048.259.016.401-.101l.762-.594a.53.53 0 0 0 .201-.269.437.437 0 0 0-.034-.365 3.329 3.329 0 0 0-1.18-1.127c-.504-.291-1.108-.441-1.784-.441a3.519 3.519 0 0 0-2.51 1.033c-.322.322-.576.71-.747 1.137a3.68 3.68 0 0 0-.273 1.407c0 .495.093.968.273 1.402.173.424.427.808.747 1.128a3.527 3.527 0 0 0 2.51 1.034c.676 0 1.28-.149 1.784-.444a3.286 3.286 0 0 0 1.182-1.13.411.411 0 0 0 .055-.173.415.415 0 0 0-.023-.182.624.624 0 0 0-.197-.273zM136.06 9.045l-2.104-3.143 1.801-2.415c.094-.139.119-.272.075-.397-.031-.09-.116-.2-.334-.2h-1.425a.52.52 0 0 0-.234.058.482.482 0 0 0-.209.205L132.191 5.2h-.349V.363a.37.37 0 0 0-.099-.26.352.352 0 0 0-.253-.103h-1.332a.37.37 0 0 0-.337.22.346.346 0 0 0-.027.143V9.29c0 .103.038.193.11.259a.353.353 0 0 0 .254.104h1.333a.328.328 0 0 0 .251-.105.346.346 0 0 0 .075-.119.333.333 0 0 0 .024-.14V6.927h.386l1.571 2.446c.112.187.267.281.46.281h1.491c.226 0 .32-.11.358-.202.054-.13.038-.262-.047-.406zM102.863 2.89h-1.489a.389.389 0 0 0-.298.122.544.544 0 0 0-.13.249l-1.099 4.167h-.268l-1.182-4.167a.66.66 0 0 0-.113-.247.329.329 0 0 0-.264-.124h-1.544c-.199 0-.325.066-.372.193a.588.588 0 0 0-.002.37l1.887 5.865c.03.093.08.17.145.232a.388.388 0 0 0 .281.104h.798l-.066.19-.19.547a.872.872 0 0 1-.29.426.7.7 0 0 1-.442.148.956.956 0 0 1-.4-.09 1.842 1.842 0 0 1-.35-.209.62.62 0 0 0-.335-.115h-.016c-.13 0-.243.074-.334.216l-.474.708c-.193.304-.086.504.039.615.234.224.528.399.875.524.344.125.723.186 1.126.186.682 0 1.252-.187 1.689-.565.435-.376.756-.887.952-1.524l2.188-7.258c.05-.155.05-.284.005-.389-.037-.08-.125-.174-.327-.174z" fill="#ffffff"/>\n  </svg>\n',w='\n<svg id="inline-button-wordmark--grey" width="166" height="16" viewBox="0 0 166 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path\n  d="M0.564068 6.26985L1.86515 6.10375C1.85184 5.15143 1.83489 3.71187 1.84942 3.45964C1.95955 1.67927 3.39982 0.16589 5.13056 0.0170127C5.23949 0.00839996 5.34842 0.0034784 5.45371 0.00101762C6.36645 -0.0209585 7.25272 0.313716 7.93 0.936113C8.62472 1.56238 9.07979 2.4864 9.14999 3.4055C9.16815 3.64174 9.1621 5.24002 9.1621 6.18249L10.4886 6.38305L10.4438 14.0767L5.47308 14.7657L0.519287 13.961L0.564068 6.26985ZM5.53359 5.63743L7.62016 5.95241C7.61169 4.80446 7.55965 3.12867 7.30548 2.68696C6.91939 2.0164 6.28156 1.62021 5.55901 1.60421H5.48276C4.7481 1.61406 4.10543 2.0004 3.71813 2.66727C3.44944 3.13236 3.39619 4.70972 3.3974 5.91181L5.53359 5.63743ZM22.5808 12.4378C21.8836 12.4378 21.293 12.3492 20.8089 12.172C20.372 12.0088 19.9719 11.7577 19.6325 11.4338C19.3256 11.1331 19.0833 10.7712 18.9208 10.3707C18.7637 9.95815 18.6612 9.52621 18.6158 9.08621H19.8358C19.8745 9.40119 19.9423 9.70141 20.0391 9.98686C20.136 10.2625 20.286 10.5085 20.4894 10.7251C20.6927 10.9318 20.959 11.0991 21.2882 11.2271C21.6271 11.3452 22.0579 11.4043 22.5808 11.4043C22.9197 11.4043 23.2295 11.36 23.5103 11.2714C23.8008 11.173 24.0525 11.0351 24.2655 10.858C24.4785 10.6808 24.6431 10.4692 24.7593 10.2231C24.8852 9.97701 24.9481 9.70633 24.9481 9.41103C24.9481 9.11574 24.9094 8.85982 24.8319 8.64327C24.7536 8.41559 24.6125 8.21568 24.4253 8.06745C24.196 7.88594 23.9347 7.75064 23.6555 7.6688C23.257 7.54201 22.849 7.4482 22.4355 7.38828C21.9393 7.31041 21.4491 7.19693 20.9686 7.04869C20.5808 6.92967 20.2133 6.75038 19.8794 6.51716C19.5939 6.29685 19.3607 6.01432 19.1968 5.69034C19.0418 5.35567 18.9644 4.9521 18.9644 4.47963C18.9644 4.07607 19.0467 3.70203 19.2113 3.35752C19.3759 3.00317 19.6034 2.69803 19.8939 2.44211C20.194 2.18619 20.5475 1.98441 20.9541 1.83676C21.3608 1.68911 21.811 1.61529 22.3048 1.61529C23.3505 1.61529 24.1784 1.86629 24.7884 2.36829C25.4081 2.86044 25.7711 3.57899 25.8777 4.52393H24.7448C24.6189 3.8546 24.3624 3.37721 23.9751 3.09176C23.5878 2.79646 23.031 2.64882 22.3048 2.64882C21.5883 2.64882 21.0461 2.81123 20.6782 3.13605C20.5037 3.28606 20.3648 3.47417 20.2717 3.68635C20.1787 3.89853 20.1339 4.12931 20.1408 4.36152C20.1408 4.61744 20.1698 4.84875 20.2279 5.05546C20.2957 5.25232 20.4167 5.42457 20.591 5.57222C20.775 5.71987 21.0267 5.85275 21.3463 5.97087C21.7689 6.09987 22.2012 6.19369 22.6389 6.25139C23.2198 6.33998 23.7281 6.4581 24.1639 6.60575C24.5996 6.75339 24.9675 6.94533 25.2677 7.18157C25.5678 7.4178 25.7905 7.70818 25.9358 8.05268C26.0907 8.39719 26.1681 8.82045 26.1681 9.32245C26.1681 9.76539 26.0761 10.1788 25.8922 10.5627C25.7149 10.9408 25.4627 11.2775 25.1515 11.5519C24.8319 11.8275 24.4495 12.0441 24.0041 12.2016C23.5684 12.359 23.094 12.4378 22.5808 12.4378ZM31.2066 12.4378C30.6741 12.4378 30.1803 12.3443 29.7252 12.1573C29.2906 11.9775 28.8956 11.7115 28.5633 11.3747C28.2268 11.0185 27.965 10.5966 27.7936 10.1345C27.6136 9.61796 27.5251 9.07309 27.5321 8.52515V8.40704C27.5321 7.82629 27.6193 7.29476 27.7936 6.81245C27.9679 6.3203 28.2099 5.90196 28.5198 5.55746C28.8393 5.2031 29.2169 4.93242 29.6526 4.7454C30.0883 4.54854 30.5676 4.45011 31.0905 4.45011C31.5262 4.45011 31.9473 4.52393 32.354 4.67158C32.7704 4.80938 33.1383 5.03085 33.4578 5.33599C33.7773 5.64112 34.0291 6.03977 34.213 6.53192C34.4067 7.01424 34.5035 7.5999 34.5035 8.28892V8.68756H28.7812C28.8102 9.57345 29.0281 10.2526 29.4348 10.7251C29.8414 11.1877 30.4369 11.419 31.2212 11.419C32.4121 11.419 33.085 10.9712 33.24 10.0754H34.4454C34.3099 10.8531 33.9516 11.4436 33.3707 11.8472C32.7897 12.2409 32.0684 12.4378 31.2066 12.4378ZM33.269 7.69833C33.2109 6.90104 32.9882 6.33014 32.6009 5.98563C32.2233 5.64112 31.7198 5.46887 31.0905 5.46887C30.4708 5.46887 29.9624 5.67065 29.5655 6.07422C29.1782 6.46794 28.9313 7.00932 28.8248 7.69833H33.269ZM39.3593 12.4378C38.8267 12.4378 38.3329 12.3492 37.8779 12.172C37.4401 11.9901 37.0407 11.7245 36.7014 11.3895C36.3636 11.0315 36.0973 10.6103 35.9172 10.1493C35.7268 9.63002 35.6332 9.07925 35.6412 8.52515V8.40704C35.6412 7.81645 35.738 7.28 35.9317 6.79769C36.1253 6.30553 36.3868 5.8872 36.716 5.54269C37.0548 5.19818 37.447 4.93242 37.8924 4.7454C38.3475 4.54854 38.8267 4.45011 39.3302 4.45011C39.7272 4.45011 40.1097 4.49932 40.4776 4.59775C40.8552 4.69618 41.1893 4.85367 41.4797 5.07022C41.7799 5.27693 42.0316 5.55253 42.235 5.89704C42.4383 6.24155 42.569 6.65988 42.6271 7.15204H41.4362C41.3393 6.54177 41.0924 6.10867 40.6955 5.85275C40.3082 5.59683 39.8531 5.46887 39.3302 5.46887C38.995 5.46599 38.6632 5.53649 38.3571 5.67557C38.0667 5.81338 37.8101 6.01024 37.5874 6.26616C37.3615 6.53514 37.1889 6.84598 37.0791 7.18157C36.9484 7.57626 36.8845 7.99063 36.8902 8.40704V8.52515C36.8902 9.47994 37.1178 10.2034 37.5729 10.6956C38.0376 11.1779 38.6379 11.419 39.3738 11.419C39.6449 11.419 39.9015 11.3797 40.1436 11.3009C40.6361 11.1497 41.0523 10.8113 41.3055 10.356C41.441 10.1197 41.5233 9.84413 41.5524 9.52915H42.6707C42.6338 9.9361 42.5204 10.3321 42.3366 10.6956C42.1663 11.0447 41.9293 11.3559 41.6395 11.611C41.349 11.8669 41.0101 12.0687 40.6228 12.2163C40.2355 12.364 39.8144 12.4378 39.3593 12.4378ZM46.9164 12.4378C46.5568 12.4406 46.199 12.3858 45.8562 12.2754C45.5441 12.1717 45.2605 11.9947 45.0284 11.7586C44.7829 11.4908 44.595 11.1741 44.4765 10.8284C44.3409 10.4544 44.2731 10.0016 44.2731 9.47009V4.58299H45.4786V9.41103C45.4786 10.0902 45.619 10.5873 45.8998 10.9023C46.1903 11.2172 46.6356 11.3747 47.236 11.3747C47.5071 11.3747 47.7685 11.3304 48.0202 11.2419C48.272 11.1533 48.4947 11.0204 48.6883 10.8432C48.882 10.666 49.0369 10.4446 49.1531 10.1788C49.2693 9.91303 49.3274 9.6079 49.3274 9.26339V4.58299H50.5328V12.3049H49.3274V11.0794C49.1531 11.4436 48.8578 11.7635 48.4414 12.0391C48.0348 12.3049 47.5264 12.4378 46.9164 12.4378ZM52.8239 4.58299H54.0294V5.97087C54.2715 5.53777 54.5765 5.18342 54.9444 4.90781C55.322 4.62236 55.8497 4.46979 56.5275 4.45011V5.60175C56.1799 5.61707 55.8346 5.66652 55.4963 5.7494C55.2039 5.81939 54.9308 5.95567 54.6975 6.14804C54.4729 6.35252 54.303 6.6116 54.2037 6.90104C54.0875 7.20618 54.0294 7.59498 54.0294 8.06745V12.3049H52.8239V4.58299ZM61.0347 12.4378C60.5021 12.4378 60.0083 12.3443 59.5533 12.1573C59.1186 11.9775 58.7236 11.7115 58.3914 11.3747C58.0549 11.0185 57.793 10.5966 57.6216 10.1345C57.4416 9.61796 57.3531 9.07309 57.3602 8.52515V8.40704C57.3602 7.82629 57.4473 7.29476 57.6216 6.81245C57.7959 6.3203 58.038 5.90196 58.3478 5.55746C58.6673 5.2031 59.0449 4.93242 59.4806 4.7454C59.9164 4.54854 60.3956 4.45011 60.9185 4.45011C61.3542 4.45011 61.7754 4.52393 62.1821 4.67158C62.5984 4.80938 62.9663 5.03085 63.2859 5.33599C63.6054 5.64112 63.8571 6.03977 64.0411 6.53192C64.2347 7.01424 64.3316 7.5999 64.3316 8.28892V8.68756H58.6092C58.6383 9.57345 58.8561 10.2526 59.2628 10.7251C59.6695 11.1877 60.2649 11.419 61.0492 11.419C62.2401 11.419 62.9131 10.9712 63.068 10.0754H64.2735C64.1379 10.8531 63.7797 11.4436 63.1987 11.8472C62.6178 12.2409 61.8964 12.4378 61.0347 12.4378ZM63.097 7.69833C63.0389 6.90104 62.8162 6.33014 62.429 5.98563C62.0513 5.64112 61.5478 5.46887 60.9185 5.46887C60.2988 5.46887 59.7905 5.67065 59.3935 6.07422C59.0062 6.46794 58.7593 7.00932 58.6528 7.69833H63.097ZM68.8968 12.4378C68.403 12.4378 67.9431 12.3541 67.5171 12.1868C67.1072 12.0141 66.7365 11.7578 66.4278 11.4338C66.1165 11.0803 65.8749 10.6693 65.7161 10.2231C65.5451 9.69956 65.4617 9.15057 65.4692 8.59898V8.48086C65.4692 7.89027 65.5564 7.3489 65.7307 6.85675C65.905 6.36459 66.147 5.94134 66.4569 5.58698C66.7667 5.22279 67.1395 4.94226 67.5752 4.7454C68.0109 4.54854 68.4902 4.45011 69.013 4.45011C69.6521 4.45011 70.1701 4.58791 70.5671 4.86352C70.964 5.12928 71.2739 5.44918 71.4966 5.82322V0.891819H72.702V12.3049H71.4966V10.9761C71.3901 11.1631 71.2448 11.3452 71.0609 11.5224C70.8713 11.7038 70.6617 11.8623 70.4363 11.9949C70.2066 12.1258 69.963 12.2298 69.7102 12.3049C69.4487 12.3935 69.1776 12.4378 68.8968 12.4378ZM69.0275 11.419C69.3761 11.419 69.7053 11.36 70.0152 11.2419C70.325 11.1237 70.5913 10.9466 70.814 10.7103C71.0367 10.4642 71.2109 10.1591 71.3368 9.79492C71.4724 9.42088 71.5401 8.98778 71.5401 8.49562V8.37751C71.5401 7.38335 71.3223 6.65004 70.8866 6.17757C70.4606 5.7051 69.8748 5.46887 69.1292 5.46887C68.374 5.46887 67.7834 5.72479 67.3573 6.23663C66.9313 6.74847 66.7183 7.47686 66.7183 8.4218V8.53992C66.7183 9.02223 66.7764 9.44549 66.8926 9.80968C67.0184 10.1739 67.183 10.4741 67.3864 10.7103C67.5994 10.9466 67.8463 11.1237 68.1271 11.2419C68.4079 11.36 68.708 11.419 69.0275 11.419ZM82.4716 12.4378C81.852 12.4378 81.3243 12.3 80.8886 12.0244C80.485 11.7813 80.146 11.4417 79.901 11.0351V12.3049H78.6955V0.891819H79.901V5.88228C80.0153 5.68531 80.1572 5.50626 80.3221 5.35075C80.5061 5.17357 80.7094 5.02101 80.9321 4.89305C81.1645 4.75524 81.4114 4.64697 81.6729 4.56822C81.9343 4.48948 82.2005 4.45011 82.4716 4.45011C82.9655 4.45011 83.4254 4.53869 83.8514 4.71587C84.2774 4.89305 84.6405 5.14897 84.9407 5.48363C85.2505 5.8183 85.4926 6.22679 85.6669 6.7091C85.8411 7.19141 85.9283 7.73278 85.9283 8.33321V8.45133C85.9283 9.04192 85.846 9.58329 85.6814 10.0754C85.5295 10.535 85.2929 10.9609 84.9843 11.3304C84.6852 11.6839 84.3133 11.9662 83.895 12.1573C83.4445 12.3492 82.96 12.4447 82.4716 12.4378ZM82.3555 11.419C83.1107 11.419 83.6868 11.173 84.0838 10.6808C84.4808 10.1788 84.6793 9.45533 84.6793 8.51039V8.39227C84.6793 7.90012 84.6163 7.47194 84.4904 7.10775C84.3829 6.77559 84.2099 6.46915 83.9821 6.2071C83.7841 5.96487 83.5294 5.77704 83.2414 5.66081C82.9581 5.53232 82.6511 5.46687 82.3409 5.46887C81.9924 5.46887 81.6632 5.52793 81.3533 5.64604C81.0537 5.7638 80.7852 5.95084 80.569 6.19234C80.3464 6.42857 80.1721 6.73371 80.0462 7.10775C79.9127 7.52209 79.8488 7.95635 79.8574 8.39227V8.51039C79.8574 9.4947 80.0898 10.228 80.5545 10.7103C81.0193 11.1828 81.6196 11.419 82.3555 11.419ZM89.6342 11.5076L86.6569 4.58299H87.9495L90.2733 10.1197L92.3792 4.58299H93.6137L89.4164 14.9921H88.1964L89.6342 11.5076ZM105.97 4.51532C105.618 4.13844 105.195 3.83755 104.727 3.63067C104.257 3.42601 103.751 3.32132 103.241 3.32307C102.698 3.32307 102.214 3.42888 101.804 3.64297C101.501 3.7934 101.229 4.00091 101.003 4.25447V4.01947C101.003 3.93064 100.979 3.84347 100.933 3.76781C100.888 3.69214 100.822 3.631 100.744 3.59129C100.691 3.56626 100.633 3.55364 100.574 3.55438H98.9827C98.9241 3.55245 98.8658 3.56433 98.8125 3.58909C98.7592 3.61385 98.7122 3.65082 98.6753 3.69711C98.5956 3.78474 98.5523 3.90019 98.5542 4.01947V15.0561C98.5542 15.1791 98.5954 15.2874 98.6753 15.3735C98.715 15.4152 98.7629 15.4479 98.8158 15.4698C98.8688 15.4916 98.9256 15.502 98.9827 15.5002H100.606C100.661 15.5001 100.715 15.4889 100.766 15.4671C100.817 15.4453 100.863 15.4135 100.901 15.3735C100.945 15.3333 100.979 15.284 101.002 15.229C101.025 15.174 101.036 15.1146 101.034 15.0548V11.2898C101.25 11.5384 101.539 11.7291 101.884 11.8607C102.307 12.0256 102.755 12.1105 103.207 12.1105C103.727 12.1105 104.233 12.0096 104.71 11.8054C105.185 11.599 105.613 11.2958 105.969 10.9146C106.338 10.5135 106.628 10.0422 106.824 9.52669C107.044 8.94733 107.152 8.33033 107.143 7.70941C107.143 7.045 107.038 6.43349 106.824 5.89212C106.631 5.38011 106.341 4.91182 105.97 4.51409V4.51532ZM104.513 8.50178C104.424 8.73555 104.295 8.94718 104.131 9.12928C103.969 9.31219 103.77 9.45789 103.547 9.55674C103.325 9.65558 103.085 9.70531 102.842 9.70264C102.6 9.70387 102.358 9.65342 102.136 9.55499C101.911 9.4556 101.71 9.31074 101.542 9.12928C101.375 8.94704 101.244 8.73407 101.155 8.50178C101.062 8.25439 101.015 7.99155 101.016 7.72663C101.016 7.45102 101.064 7.19141 101.155 6.95026C101.332 6.48635 101.682 6.1122 102.128 5.90935C102.352 5.80353 102.595 5.7494 102.842 5.75063C103.103 5.75063 103.343 5.80476 103.56 5.91058C103.774 6.0127 103.969 6.15542 104.134 6.33014C104.476 6.71058 104.661 7.21105 104.648 7.72663C104.648 8.00347 104.603 8.26308 104.513 8.50178ZM115.859 3.55684H114.249C114.193 3.55751 114.138 3.56987 114.087 3.59315C114.036 3.61643 113.99 3.65013 113.952 3.69219C113.868 3.78 113.821 3.8973 113.821 4.01947V4.22371C113.62 3.9808 113.372 3.78302 113.092 3.64297C112.654 3.42397 112.17 3.31511 111.681 3.32553C111.166 3.32821 110.657 3.43439 110.183 3.63795C109.708 3.84151 109.278 4.13843 108.917 4.51162C108.537 4.91069 108.236 5.37962 108.029 5.89335C107.801 6.47081 107.687 7.08847 107.696 7.71064C107.696 8.37505 107.807 8.98655 108.029 9.52792C108.251 10.0619 108.545 10.5331 108.91 10.9158C109.265 11.2945 109.693 11.5958 110.167 11.8016C110.641 12.0074 111.15 12.1133 111.665 12.113C112.194 12.113 112.675 12.0047 113.085 11.7943C113.388 11.6343 113.637 11.4412 113.821 11.2148V11.4326C113.82 11.4923 113.831 11.5516 113.854 11.6066C113.877 11.6615 113.911 11.7109 113.954 11.7512C113.993 11.7915 114.038 11.8236 114.089 11.8456C114.14 11.8676 114.194 11.879 114.249 11.8792H115.859C115.916 11.8812 115.972 11.8706 116.024 11.8483C116.077 11.826 116.124 11.7925 116.162 11.75C116.203 11.7085 116.235 11.6589 116.256 11.6043C116.277 11.5497 116.287 11.4912 116.285 11.4326V4.0207C116.287 3.90142 116.244 3.78597 116.164 3.69834C116.127 3.65337 116.08 3.61736 116.027 3.5929C115.975 3.56844 115.917 3.55613 115.859 3.55684ZM113.685 8.4907C113.601 8.72324 113.473 8.9368 113.308 9.11943C113.143 9.29538 112.95 9.43933 112.735 9.54269C112.52 9.64727 112.275 9.70141 112.014 9.70141C111.764 9.70141 111.521 9.64727 111.3 9.54146C111.079 9.4398 110.881 9.29437 110.717 9.11372C110.552 8.93306 110.425 8.72082 110.343 8.48947C110.253 8.24551 110.207 7.98712 110.207 7.72663C110.207 7.44979 110.253 7.19018 110.343 6.94903C110.433 6.71156 110.562 6.50116 110.723 6.32891C110.888 6.15296 111.086 6.00901 111.301 5.90812C111.521 5.80353 111.764 5.7494 112.015 5.7494C112.275 5.7494 112.52 5.80353 112.735 5.90935C112.951 6.01393 113.144 6.15542 113.308 6.32891C113.65 6.70961 113.834 7.21001 113.821 7.7254C113.821 7.98993 113.776 8.24709 113.685 8.4907ZM131.919 7.49901C131.685 7.28955 131.417 7.12228 131.127 7.00439C130.828 6.88012 130.511 6.78169 130.177 6.71525L128.95 6.46425C128.635 6.40273 128.416 6.3203 128.293 6.21448C128.242 6.17732 128.201 6.12853 128.172 6.07209C128.144 6.01565 128.128 5.95315 128.128 5.88966C128.128 5.75432 128.204 5.64358 128.357 5.54269C128.523 5.43688 128.755 5.38274 129.042 5.38274C129.357 5.38274 129.669 5.44795 129.958 5.57591C130.252 5.70633 130.528 5.85398 130.769 6.01885C131.126 6.24893 131.43 6.21817 131.649 5.95364L132.238 5.25232C132.351 5.13393 132.416 4.97752 132.422 4.81307C132.422 4.65558 132.345 4.51162 132.198 4.3812C131.948 4.15481 131.553 3.91612 131.023 3.68234C130.494 3.44611 129.829 3.32553 129.042 3.32553C128.525 3.32553 128.049 3.39689 127.628 3.53716C127.205 3.67988 126.842 3.87674 126.547 4.12036C126.251 4.37136 126.017 4.66173 125.858 4.99763C125.699 5.33341 125.617 5.70154 125.618 6.07422C125.618 6.76324 125.82 7.32552 126.218 7.74509C126.615 8.15481 127.145 8.43411 127.796 8.56945L129.072 8.8549C129.435 8.93118 129.711 9.01731 129.889 9.11328C130.045 9.19572 130.119 9.31383 130.119 9.47871C130.119 9.62635 130.045 9.758 129.892 9.87735C129.731 10.0053 129.478 10.0681 129.139 10.0681C128.79 10.0717 128.445 9.99161 128.133 9.83429C127.836 9.68469 127.56 9.49515 127.312 9.27077C127.202 9.17922 127.076 9.1084 126.941 9.0616C126.788 9.01608 126.616 9.0653 126.419 9.22155L125.714 9.78876C125.509 9.96717 125.423 10.1825 125.461 10.431C125.496 10.6697 125.691 10.9158 126.055 11.1939C126.38 11.4313 126.798 11.6467 127.3 11.8312C127.803 12.017 128.391 12.113 129.059 12.113C129.596 12.113 130.092 12.0416 130.529 11.9013C130.967 11.7549 131.354 11.5593 131.668 11.3108C131.98 11.0724 132.231 10.7631 132.404 10.4077C132.576 10.0523 132.663 9.66076 132.659 9.26462C132.659 8.87212 132.595 8.52638 132.46 8.23601C132.331 7.95492 132.147 7.70366 131.919 7.49778V7.49901ZM138.958 9.51193C138.923 9.445 138.872 9.3882 138.809 9.34687C138.747 9.30555 138.675 9.28105 138.601 9.27569C138.451 9.27322 138.305 9.3222 138.186 9.41473C138.071 9.49101 137.946 9.55007 137.815 9.58821C137.649 9.64412 137.47 9.64967 137.3 9.60421C137.219 9.58443 137.144 9.54519 137.081 9.48978C137.005 9.42056 136.946 9.33472 136.908 9.23878C136.855 9.09669 136.83 8.94521 136.836 8.79338V5.76662H138.931C139.051 5.76662 139.159 5.71741 139.253 5.62267C139.295 5.58108 139.329 5.5314 139.352 5.4765C139.376 5.42159 139.388 5.36254 139.389 5.30276V4.02193C139.389 3.96024 139.377 3.89918 139.353 3.84249C139.329 3.78579 139.294 3.73465 139.25 3.69219C139.165 3.60724 139.05 3.55916 138.931 3.55807H136.835V1.49717C136.838 1.43555 136.827 1.37406 136.805 1.31679C136.782 1.25952 136.748 1.20777 136.704 1.16497C136.627 1.08454 136.521 1.03854 136.41 1.03701H134.784C134.695 1.03555 134.609 1.06135 134.535 1.11101C134.462 1.16066 134.405 1.23182 134.372 1.31507C134.349 1.37287 134.338 1.43484 134.339 1.49717V3.55684H133.411C133.353 3.55549 133.294 3.5675 133.241 3.59199C133.188 3.61647 133.14 3.65281 133.102 3.69834C133.025 3.78687 132.982 3.90193 132.984 4.0207V5.304C132.985 5.39177 133.01 5.47753 133.055 5.55238C133.1 5.62724 133.164 5.68846 133.24 5.72971C133.294 5.75555 133.352 5.76908 133.41 5.76662H134.339V9.36551C134.339 9.84536 134.423 10.2588 134.581 10.5996C134.741 10.9367 134.951 11.2197 135.204 11.4313C135.466 11.6503 135.766 11.8128 136.088 11.9087C136.415 12.0121 136.755 12.0638 137.096 12.0638C137.562 12.0638 138.004 11.9887 138.421 11.8423C138.817 11.7053 139.175 11.4722 139.463 11.1631C139.647 10.9404 139.668 10.6943 139.518 10.4483L138.958 9.51316V9.51193ZM147.78 3.55684H146.171C146.116 3.55785 146.061 3.57036 146.01 3.59363C145.959 3.6169 145.914 3.65043 145.876 3.69219C145.835 3.73558 145.802 3.78651 145.779 3.84229C145.758 3.89876 145.746 3.95885 145.747 4.01947V4.22371C145.544 3.98167 145.295 3.78409 145.014 3.64297C144.609 3.43011 144.136 3.32553 143.601 3.32553C143.092 3.32553 142.591 3.42765 142.117 3.6319C141.638 3.83631 141.204 4.13534 140.84 4.51162C140.46 4.9102 140.158 5.37925 139.952 5.89335C139.722 6.47038 139.608 7.08828 139.618 7.71064C139.618 8.37505 139.729 8.98655 139.95 9.52792C140.173 10.0619 140.468 10.5331 140.833 10.9158C141.188 11.2943 141.614 11.5956 142.087 11.8014C142.56 12.0072 143.069 12.1132 143.584 12.113C144.076 12.1217 144.563 12.0125 145.006 11.7943C145.314 11.6343 145.559 11.4412 145.747 11.2148V11.4326C145.745 11.4921 145.756 11.5513 145.778 11.6062C145.801 11.6612 145.834 11.7106 145.877 11.7512C145.916 11.7913 145.961 11.8232 146.012 11.8452C146.062 11.8671 146.116 11.8787 146.171 11.8792H147.78C147.837 11.8806 147.893 11.8699 147.946 11.8476C147.998 11.8254 148.046 11.7921 148.085 11.75C148.166 11.6649 148.21 11.5508 148.208 11.4326V4.0207C148.21 3.90149 148.167 3.78617 148.087 3.69834C148.049 3.65272 148.002 3.61618 147.948 3.59129C147.895 3.56679 147.838 3.555 147.78 3.55684ZM145.606 8.4907C145.437 8.95456 145.095 9.33232 144.655 9.54269C144.43 9.64997 144.184 9.70423 143.936 9.70141C143.685 9.70141 143.446 9.64727 143.224 9.54146C143.004 9.43906 142.806 9.2934 142.642 9.11285C142.477 8.9323 142.349 8.72044 142.266 8.48947C142.175 8.24565 142.129 7.98721 142.129 7.72663C142.129 7.44979 142.174 7.19018 142.264 6.94903C142.353 6.71894 142.481 6.50855 142.645 6.32891C142.812 6.15296 143.008 6.00901 143.224 5.90812C143.447 5.8023 143.69 5.74817 143.936 5.7494C144.199 5.7494 144.438 5.80353 144.655 5.90935C144.874 6.01393 145.066 6.15542 145.226 6.32891C145.388 6.50486 145.517 6.71402 145.606 6.94903C145.796 7.44486 145.796 7.99486 145.606 8.4907ZM156.622 9.40119L155.699 8.67157C155.522 8.52638 155.355 8.48701 155.21 8.54853C155.079 8.60436 154.964 8.69079 154.872 8.80076C154.673 9.05649 154.434 9.27863 154.167 9.45902C153.934 9.60913 153.629 9.68418 153.269 9.68418C153.004 9.68418 152.76 9.63496 152.542 9.53407C152.327 9.43495 152.134 9.2917 151.975 9.11328C151.812 8.93013 151.686 8.71715 151.602 8.48578C151.515 8.24262 151.471 7.98546 151.474 7.72663C151.474 7.4461 151.515 7.18772 151.602 6.94903C151.687 6.71279 151.811 6.50239 151.975 6.33014C152.138 6.15296 152.332 6.01024 152.546 5.90935C152.772 5.80107 153.019 5.74694 153.269 5.7494C153.629 5.7494 153.931 5.82814 154.167 5.97948C154.406 6.13451 154.646 6.35598 154.873 6.63651C154.968 6.7497 155.079 6.83337 155.21 6.88997C155.358 6.94903 155.523 6.90965 155.695 6.7657L156.618 6.03485C156.732 5.95424 156.817 5.83809 156.861 5.70387C156.886 5.63045 156.896 5.55227 156.889 5.47473C156.882 5.39719 156.858 5.32214 156.82 5.25478C156.464 4.67928 155.973 4.20275 155.391 3.86813C154.781 3.51009 154.05 3.32553 153.232 3.32553C152.668 3.32238 152.109 3.43311 151.588 3.65129C151.066 3.86947 150.593 4.19076 150.194 4.59652C149.805 4.99271 149.497 5.4701 149.29 5.99547C149.07 6.54494 148.957 7.13314 148.96 7.72663C148.96 8.33567 149.072 8.91765 149.29 9.45164C149.5 9.97332 149.807 10.4458 150.194 10.8395C150.593 11.2451 151.067 11.5663 151.588 11.7846C152.11 12.003 152.668 12.1142 153.232 12.1117C154.05 12.1117 154.781 11.9284 155.391 11.5654C155.976 11.232 156.468 10.7537 156.822 10.1751C156.86 10.1101 156.882 10.0374 156.889 9.96225C156.896 9.88643 156.886 9.80992 156.861 9.73832C156.813 9.60626 156.731 9.49007 156.622 9.40242V9.40119ZM165.194 11.1299L162.647 7.26277L164.827 4.29138C164.941 4.12036 164.971 3.95672 164.918 3.80292C164.88 3.69219 164.777 3.55684 164.514 3.55684H162.789C162.69 3.55775 162.593 3.58219 162.506 3.62821C162.398 3.68359 162.309 3.77173 162.253 3.88043L160.511 6.39904H160.089V0.447649C160.091 0.329229 160.048 0.214475 159.969 0.127748C159.929 0.0869473 159.881 0.0547186 159.828 0.0329554C159.776 0.0111921 159.719 0.000333517 159.663 0.00101762H158.051C157.964 0.00131009 157.88 0.0270233 157.807 0.0750545C157.735 0.123086 157.678 0.191382 157.643 0.271703C157.62 0.327334 157.608 0.387308 157.61 0.447649V11.4313C157.61 11.5581 157.656 11.6688 157.743 11.75C157.783 11.7911 157.831 11.8236 157.884 11.8456C157.937 11.8676 157.993 11.8786 158.051 11.878H159.664C159.721 11.8798 159.777 11.8692 159.83 11.8469C159.882 11.8246 159.929 11.7912 159.968 11.7488C160.007 11.7068 160.038 11.657 160.058 11.6024C160.08 11.5477 160.09 11.489 160.087 11.4301V8.52392H160.555L162.456 11.5335C162.592 11.7635 162.779 11.8792 163.013 11.8792H164.817C165.091 11.8792 165.205 11.7439 165.251 11.6307C165.316 11.4707 165.297 11.3083 165.194 11.1311V11.1299ZM125.015 3.55684H123.213C123.146 3.55424 123.079 3.56628 123.017 3.59218C122.954 3.61807 122.898 3.6572 122.852 3.70695C122.774 3.79331 122.72 3.89895 122.695 4.01332L121.365 9.14035H121.041L119.61 4.01332C119.586 3.90347 119.539 3.79998 119.473 3.70941C119.435 3.66072 119.386 3.62162 119.331 3.59516C119.276 3.5687 119.215 3.55559 119.154 3.55684H117.285C117.044 3.55684 116.892 3.63805 116.835 3.79431C116.786 3.94184 116.785 4.10149 116.832 4.24955L119.116 11.4658C119.152 11.5802 119.213 11.675 119.292 11.7512C119.337 11.7944 119.391 11.828 119.449 11.8499C119.507 11.8719 119.57 11.8818 119.632 11.8792H120.598L120.518 12.113L120.288 12.786C120.225 12.9927 120.103 13.1754 119.937 13.3101C119.784 13.4312 119.595 13.4954 119.402 13.4922C119.234 13.4914 119.069 13.4536 118.918 13.3815C118.768 13.312 118.625 13.2257 118.494 13.1243C118.375 13.0381 118.234 12.9889 118.089 12.9829H118.069C117.912 12.9829 117.775 13.0739 117.665 13.2486L117.091 14.1197C116.858 14.4938 116.987 14.7399 117.139 14.8764C117.422 15.152 117.778 15.3673 118.198 15.5211C118.614 15.6749 119.073 15.75 119.56 15.75C120.386 15.75 121.076 15.5199 121.605 15.0548C122.131 14.5922 122.52 13.9635 122.757 13.1797L125.405 4.24955C125.465 4.05884 125.465 3.90012 125.411 3.77093C125.366 3.6725 125.26 3.55684 125.015 3.55684Z"\n  fill="#838383"\n/>\n</svg>\n',x='\n  <button type="button" id="apple-pay-close-button">\n    <svg width="10" height="9" fill="none" xmlns="http://www.w3.org/2000/svg">\n      <path\n        d="M5.572 4.033L8.89.71a.4.4 0 0 0-.566-.566L5.003 3.459 1.681.145a.4.4 0 0 0-.566.566L4.44 4.033\n      1.115 7.354a.398.398 0 0 0 0 .566.4.4 0 0 0 .566 0l3.322-3.33 3.322 3.33a.4.4 0 0 0 .566-.566L5.57 4.033z"\n        fill="white"\n      />\n    </svg>\n  </button>\n',M='\n<svg width="74" height="19" viewBox="0 0 74 19" fill="none" xmlns="http://www.w3.org/2000/svg" id="vault-logo">\n  <g clip-path="url(#clip0_9910_9664)">\n    <path\n      d="M32.1273 15.8163H28.9432C28.6448 15.8163 28.4481 15.6622 28.3497 15.3507L25.1886 6.20188C25.1165 6.01825 25.1296 5.85101 25.2214 5.70345C25.3132 5.55589 25.451 5.48047 25.6346 5.48047H27.9693C28.2513 5.48047 28.435 5.63787 28.5202 5.94611L30.6648 12.9077L32.5536 5.94611C32.6388 5.63459 32.829 5.48047 33.1274 5.48047H35.4195C35.6031 5.48047 35.7441 5.55589 35.8425 5.70345C35.9409 5.85101 35.954 6.01825 35.8851 6.20188L32.7241 15.3507C32.6257 15.6622 32.4257 15.8163 32.1305 15.8163H32.1273Z"\n      fill="#343C43" />\n    <path\n      d="M37.6361 14.5842C36.6097 13.5644 36.0981 12.2495 36.0981 10.6362C36.0981 9.02283 36.6097 7.71118 37.6361 6.69792C38.6624 5.68795 39.8757 5.17969 41.2759 5.17969C41.9416 5.17969 42.5384 5.31085 43.0696 5.57318C43.6008 5.83551 43.9943 6.16998 44.2468 6.57988V5.94373C44.2468 5.81584 44.2927 5.70763 44.3845 5.61581C44.4763 5.524 44.578 5.47809 44.6927 5.47809H46.8373C46.9652 5.47809 47.0701 5.524 47.1554 5.61581C47.2406 5.70763 47.2833 5.81584 47.2833 5.94373V15.3679C47.2833 15.4958 47.2406 15.6008 47.1554 15.686C47.0701 15.7713 46.9652 15.8139 46.8373 15.8139H44.6927C44.578 15.8139 44.4763 15.7713 44.3845 15.686C44.2927 15.6008 44.2468 15.4958 44.2468 15.3679V14.7088C43.991 15.1187 43.6008 15.4565 43.0696 15.7155C42.5384 15.9779 41.935 16.109 41.2562 16.109C39.8691 16.109 38.6624 15.6008 37.6361 14.581V14.5842ZM39.9151 8.79985C39.433 9.31795 39.1936 9.93443 39.1936 10.6558C39.1936 11.3772 39.433 11.997 39.9151 12.5118C40.3971 13.0299 40.9972 13.2857 41.7186 13.2857C42.44 13.2857 43.0401 13.0266 43.5221 12.5118C44.0041 11.997 44.2435 11.3772 44.2435 10.6558C44.2435 9.93443 44.0041 9.31467 43.5221 8.79985C43.0401 8.28502 42.44 8.02597 41.7186 8.02597C40.9972 8.02597 40.3938 8.28502 39.9151 8.79985Z"\n      fill="#343C43" />\n    <path\n      d="M51.6315 5.9016V11.2302C51.6315 11.9385 51.8086 12.4959 52.1627 12.9058C52.5169 13.3157 53.0415 13.5223 53.7334 13.5223C54.4253 13.5223 54.9401 13.319 55.3139 12.9058C55.6878 12.4959 55.878 11.9516 55.878 11.2728V5.9016C55.878 5.78683 55.9239 5.68845 56.0157 5.60319C56.1075 5.51794 56.2092 5.47531 56.3239 5.47531H58.4685C58.6095 5.47531 58.7242 5.52122 58.8095 5.61303C58.8948 5.70485 58.9374 5.81306 58.9374 5.94094V15.3652C58.9374 15.4931 58.8948 15.598 58.8095 15.6832C58.7242 15.7685 58.6128 15.8111 58.4685 15.8111H56.3239C56.196 15.8111 56.0911 15.7718 56.0058 15.6931C55.9206 15.6144 55.878 15.5127 55.878 15.3848V14.7257C55.1139 15.6472 54.081 16.1062 52.7792 16.1062C51.4774 16.1062 50.4707 15.7062 49.7132 14.9061C48.9557 14.106 48.5786 13.0501 48.5786 11.7319V5.89504C48.5786 5.78027 48.6278 5.68189 48.7262 5.59664C48.8246 5.51138 48.9295 5.46875 49.0442 5.46875H51.1658C51.2937 5.46875 51.4019 5.51138 51.4938 5.59664C51.5856 5.68189 51.6315 5.78027 51.6315 5.89504V5.9016Z"\n      fill="#343C43" />\n    <path\n      d="M62.9872 15.8148C62.0658 15.8148 61.387 15.582 60.9509 15.113C60.5115 14.6474 60.2917 14.0178 60.2917 13.2243V1.42267C60.2917 1.29478 60.3344 1.18657 60.4196 1.09475C60.5049 1.00294 60.6164 0.957031 60.7607 0.957031H62.8823C63.0233 0.957031 63.138 1.00294 63.2233 1.09475C63.3085 1.18657 63.3512 1.29478 63.3512 1.42267V12.6078C63.3512 12.8045 63.4069 12.9652 63.5217 13.0865C63.6332 13.2079 63.7906 13.2669 63.9873 13.2669H64.6464C64.9448 13.2669 65.0924 13.4144 65.0924 13.7129V15.2409C65.0924 15.6246 64.8956 15.8148 64.4989 15.8148H62.9905H62.9872Z"\n      fill="#343C43" />\n    <path\n      d="M66.722 12.7378V8.04861H65.3644C65.2365 8.04861 65.1316 8.00271 65.0463 7.91089C64.9611 7.81907 64.9185 7.71086 64.9185 7.58298V5.94997C64.9185 5.82208 64.9611 5.71387 65.0463 5.62206C65.1316 5.53024 65.2365 5.48433 65.3644 5.48433H66.722V2.74626C66.722 2.60526 66.7679 2.49049 66.8597 2.40523C66.9515 2.31997 67.0597 2.27734 67.1876 2.27734H69.3518C69.4666 2.27734 69.5683 2.31997 69.6601 2.40523C69.7519 2.49049 69.7978 2.60198 69.7978 2.74626V5.48433H72.7064C72.8343 5.48433 72.9458 5.53024 73.0474 5.62206C73.1458 5.71387 73.195 5.82208 73.195 5.94997V7.58298C73.195 7.69775 73.1458 7.80268 73.0474 7.90105C72.949 7.99943 72.8343 8.04861 72.7064 8.04861H69.7978V12.0164C69.7978 12.4689 69.9027 12.7804 70.1159 12.9509C70.329 13.1214 70.5684 13.2067 70.8373 13.2067C71.1193 13.2067 71.4308 13.1083 71.7718 12.9083C72.1555 12.6689 72.4441 12.6951 72.6408 12.9935L73.3852 14.1806C73.5557 14.4495 73.5327 14.7052 73.3229 14.9446C72.6146 15.6792 71.5948 16.0497 70.2667 16.0497C69.2764 16.0497 68.437 15.7742 67.7516 15.2234C67.0663 14.6725 66.722 13.8428 66.722 12.741V12.7378Z"\n      fill="#343C43" />\n    <path\n      d="M15.2266 0H4.43496C2.26089 0 0.5 1.76089 0.5 3.93496V14.7266C0.5 16.9006 2.26089 18.6615 4.43496 18.6615H15.2266C17.4006 18.6615 19.1615 16.9006 19.1615 14.7266V3.93496C19.1615 1.76089 17.4006 0 15.2266 0ZM15.079 12.2312L12.1442 14.6118C11.9114 14.8086 11.5507 14.6807 11.4949 14.379L10.8161 11.8934C10.7702 11.6934 10.8489 11.4868 11.0096 11.3589C11.4425 11.0113 11.7179 10.4801 11.7179 9.8833C11.7179 8.44048 10.098 7.37476 8.5765 8.40113C8.48468 8.46344 8.40598 8.54214 8.34368 8.63723C7.63539 9.68983 7.92723 10.7883 8.65192 11.3655C8.8126 11.4934 8.88474 11.6967 8.84211 11.8967L8.22891 14.3823C8.17317 14.6839 7.81246 14.8118 7.57964 14.6151L4.57924 12.2344C4.47431 12.1262 4.41856 11.9787 4.4284 11.8278L4.77271 6.69271C4.78582 6.49268 4.91371 6.31561 5.09734 6.23691L9.41924 4.03661C9.68157 3.9284 9.97341 3.9284 10.2357 4.03661L14.5576 6.23691C14.7445 6.31561 14.8691 6.49268 14.8855 6.69271L15.2299 11.8278C15.2397 11.9787 15.1839 12.1262 15.079 12.2344V12.2312Z"\n      fill="#343C43" />\n  </g>\n  <defs>\n    <clipPath id="clip0_9910_9664">\n      <rect width="73" height="18.6615" fill="white" transform="translate(0.5)" />\n    </clipPath>\n  </defs>\n</svg>\n';var P={height:"50px",width:"auto",borderRadius:"3px",padding:"10px",locale:"en",type:"pay"},V=function(n){return n&&"object"===p(n)?Object.keys(P).reduce((function(e,t){return r(r({},e),{},i({},t,n[t]||P[t]))}),{}):P},L='\n  .pre-checkout-modal {\n    display: none;\n    position: fixed;\n    z-index: 1;\n    left: 0;\n    top: 0;\n    width: 100vw;\n    height: 100%;\n    overflow: auto;\n    background-color: rgba(0, 0, 0, 0.75);\n    transition: all 0.2s ease;\n  }\n\n  .pre-checkout-modal.show {\n    display: block;\n  }\n\n  .pre-checkout-modal__content {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    margin-left: auto;\n    margin-right: auto;\n    background-color: #fefefe;\n    padding: 20px;\n    padding-bottom: max(30px, env(safe-area-inset-bottom));\n    width: 100%;\n    border-radius: 6px 6px 0 0;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    max-width: 350px;\n\n    box-sizing: border-box;\n    transform: translateY(238px);\n    transition: transform 0.3s cubic-bezier(.16,.81,.32,1);\n  }\n\n  .modal-wrapper {\n    width: 100%;\n  }\n\n  .payment-info {\n    position: relative;\n    padding-bottom: 15px;\n    border-bottom: solid 1px whitesmoke;\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n    width: 100%;\n    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",\n      "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;\n  }\n\n  .customer-email {\n    color: #737373;\n    font-size: 13px;\n    line-height: 16px;\n  }\n\n  .customer-info {\n    flex: 1;\n    text-align: right;\n  }\n\n  .merchant-logo {\n    display: flex;\n    align-items: center;\n    height: 30px;\n  }\n  .transaction-amount {\n    margin-top: 5px;\n    font-size: 13px;\n    line-height: 16px;\n    color: #737373;\n  }\n\n  .amount {\n    color: #29b263;\n    font-weight: bold;\n  }\n\n  @media only screen and (min-width: 500px) {\n    .pre-checkout-modal__content {\n      bottom: 0;\n      top: 0;\n      margin: auto;\n      border-radius: 6px;\n      height: fit-content;\n    }\n  }\n\n  .pre-checkout-modal__content.show {\n    transform: translateY(0);\n    margin: 0 auto;\n    margin-top: 100px;\n  }\n\n  .pre-checkout-modal__content > * {\n    margin-top: 0;\n    margin-bottom: 40px;\n  }\n  .pre-checkout-modal__content > *:last-child {\n    margin-bottom: 0;\n  }\n\n  .pre-checkout-modal__content svg {\n    margin: auto;\n    width: 100%;\n  }\n\n  #inline-button-wordmark--white {\n    position: absolute;\n    bottom: -50px;\n    margin: auto;\n    left: 0;\n    right: 0;\n    width: fit-content;\n  }\n\n  #inline-button-wordmark--grey {\n    display: none;\n  }\n\n  .pre-checkout-modal__content #apple-pay-mark--light {\n    margin-bottom: 16px;\n  }\n\n  .pre-checkout-modal p {\n    -webkit-text-size-adjust: 100%;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    text-rendering: optimizeLegibility;\n    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",\n      "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;\n    color: #4E4E4E;\n    line-height: 140%;\n    font-size: 14px;\n    font-weight: 500;\n    margin: 0;\n    padding: 0 20px;\n    text-align: center;\n    letter-spacing: -0.3px;\n  }\n\n  .pre-checkout-modal button {\n    height: 42px;\n    width: 100%;\n    \n    box-sizing: border-box;\n    border-radius: 3px;\n    font-size: 14px;\n    line-height: 24px;\n    cursor: pointer;\n    -webkit-text-size-adjust: 100%;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    text-rendering: optimizeLegibility;\n    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",\n      "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;\n  }\n\n  .pre-checkout-modal .open-paystack-pop-button {\n    background: #FAFAFA;\n    border: 1px solid #F2F3F3;\n    color: #4E4E4E;\n    font-weight: 500;\n  }\n\n  .pre-checkout-modal .open-paystack-pop-button:hover, \n  .pre-checkout-modal .open-paystack-pop-button:active, \n  .pre-checkout-modal .open-paystack-pop-button:focus {\n    background: #F2F3F3;\n  }\n\n  .pre-checkout-modal .pay-with-vault-button {\n    font-weight: 700;\n    background: #44b669;\n    background: linear-gradient(to bottom, #44b669 0%, #40ad57 100%);\n    border: solid 1px #49a861;\n    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);\n    outline: none;\n    color: white;\n    transition: all 300ms;\n  }\n\n  .pre-checkout-modal .vault-instruction {\n    color: #2f3d4d;\n    font-size: 14px;\n    letter-spacing: normal;\n    line-height: 1.4;\n    margin: 0 auto 24px;\n    padding: 0;\n  }\n  .vault-logo-container {\n    width: 74px;\n    height: 20px;\n    margin: 0 auto 24px\n  }\n  .vault-logo-container img {\n    height: 100%;\n    width: 100%;\n    border-radius: 8px;\n  }\n  .vault-divider {\n    margin-bottom: 16px;\n    margin-top: 24px;\n    position: relative;\n  }\n  .vault-divider__container {\n    align-items: center;\n    bottom: 0;\n    display: flex;\n    left: 0;\n    position: absolute;\n    right: 0;\n    top: 0;\n  }\n  .vault-divider__line {\n    border: 1px dashed #ccced0;\n    width: 100%;\n  }\n  .vault-divider__text-container {\n    display: flex;\n    justify-content: center;\n    position: relative;\n  }\n  .vault-divider__text {\n    background-color: #fff;\n    color: #999da1;\n    font-size: 14px;\n    font-weight: 500;\n    letter-spacing: -.3px;\n    line-height: 19.6px;\n    margin-bottom: 2px;\n    padding: 0 8px;\n  }\n\n  #payment-request-button {\n    width: 100%;\n    height: fit-content;\n    margin: 24px 0 16px 0;\n  }\n\n  #paystackpop-button {\n    padding: 0 16px;\n  }\n\n  #apple-pay-close-button {\n    position: absolute;\n    text-align: center;\n    top: 0;\n    right: -26px;\n    height: 16px;\n    width: 16px;\n    padding: 0;\n    display: inline-block;\n    z-index: 3;\n    border-radius: 50%;\n    background: transparent;\n    transition: all 300ms;\n    outline: none;\n    cursor: pointer;\n    border: none;\n  }\n\n  #apple-pay-close-button svg {\n    width: initial;\n  }\n  \n  #apple-pay-close-button:hover {\n    background-color: #e22b28;\n  }\n\n  @media only screen and (max-width: 500px) {\n    .pre-checkout-modal__content {\n      max-width: 500px;\n      border-radius: 0;\n      padding-bottom: 0;\n    }\n\n    .modal-wrapper {\n      padding: 0;\n    }\n\n    .vault-logo-container {\n      width: 74px;\n      height: 20px;\n    }\n\n    #inline-button-wordmark--white {\n      display: none\n    }\n    \n    #inline-button-wordmark--grey {\n      display: block;\n      width: 100%;\n      margin: 16px 0;\n      height: 13px;\n    }\n\n    #apple-pay-close-button {\n      display: none;\n    }\n  }\n',_=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return Number(parseFloat(n/100).toFixed(2))},S={headers:{accept:"application/json, text/plain, */*","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","content-type":"application/x-www-form-urlencoded","sec-ch-ua-mobile":"?0","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"cross-site"},referrerPolicy:"no-referrer-when-downgrade",method:"POST",mode:"cors",credentials:"omit"};function E(n){return Object.keys(n).reduce((function(e,t){var a=encodeURIComponent(t),i=encodeURIComponent(n[t]),o="".concat(a,"=").concat(i);return[].concat(l(e),[o])}),[]).join("&")}var A=function(n){return{biannually:"BIANNUAL PLAN",annually:"ANNUAL PLAN"}[n]||"".concat(n.toUpperCase()," PLAN")},T=function(){try{return window.location&&"https:"===window.location.protocol&&window.ApplePaySession&&window.ApplePaySession.supportsVersion(y.applePayVersion)}catch(n){return!1}},q=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return T()&&n.includes("apple_pay")};function H(){var n=0;return Array.from(document.querySelectorAll("body *")).forEach((function(e){var t=window.getComputedStyle(e),a=parseFloat(t.zIndex);!Number.isNaN(a)&&a>n&&(n=a)})),n}function I(n){var e=document.createElement("iframe");return e.setAttribute("frameBorder","0"),e.setAttribute("allowtransparency","true"),e.id=n,e.style.display="none",e}function z(n){return n.querySelector("apple-pay-button")||n.querySelector("#apple-pay-button")}function O(n){return document.querySelector("#".concat(n))}function j(n,e,t){var a=e.channels,i=void 0===a?[]:a,o=e.styles,r=void 0===o?{}:o,c={applePay:!1};return new Promise((function(e,a){if(n)if(q(i)){if(z(n))return c.applePay=!0,void e(c);(function(n,e){var t=e.container,a=e.integrity;return new Promise((function(e,i){n||i("No script url");var o=document.createElement("script");o.src=n,o.crossOrigin="anonymous",a&&(o.integrity=a),o.addEventListener("load",(function(){e(!0)})),o.addEventListener("error",(function(){o.remove(),i(!1)})),t?t.appendChild(o):document.head.appendChild(o)}))})("https://applepay.cdn-apple.com/jsapi/v1.1.0/apple-pay-sdk.js",{container:n,integrity:"sha384-z/6BVHCcSypLSykOVpaT1PQWHOOgU45uOOlMkgi/bElX4yFqmChNMb7qiv80wFav"}).then((function(){if(t&&1077497!==t&&window&&!Array.isArray(window.webpackJsonp))throw new Error("Incorrect data type for 'webpackJsonp', expected array, got ".concat(p(window.webpackJsonp),". Switching to fallback apple pay button"));!function(n,e){var t,a,i,o,r,c=e.styles,s=e.theme,l=document.createElement("style"),u=(a=(t=c).height,i=t.width,o=t.borderRadius,r=t.padding,"\n  apple-pay-button {\n    --apple-pay-button-width: ".concat(i,";\n    --apple-pay-button-height: ").concat(a,";\n    --apple-pay-button-border-radius: ").concat(o,";\n    --apple-pay-button-padding: ").concat(r,";\n    --apple-pay-button-box-sizing: border-box;\n    width: ").concat(i,";\n  }\n"));l.type="text/css",l.styleSheet?l.styleSheet.cssText=u:l.appendChild(document.createTextNode(u)),n.appendChild(l);var p=document.createElement("apple-pay-button");p.setAttribute("buttonstyle","light"===s?"white":"black"),p.setAttribute("type",c.type),p.setAttribute("locale",c.locale),n.appendChild(p)}(n,{styles:V(r.applePay),theme:r.theme}),c.applePay=!0,e(c)})).catch((function(){!function(n,e){var t,a,i,o,r,c,s,l=e.styles,u=e.theme,p=document.createElement("style"),d=(a=(t=l).height,i=t.width,o=t.borderRadius,r=t.padding,c=t.type,s=t.locale,"\n  @supports (-webkit-appearance: -apple-pay-button) { \n    .apple-pay-button {\n        display: inline-block;\n        -webkit-appearance: -apple-pay-button;\n        width: ".concat(i,";\n        height: ").concat(a,";\n        border-radius: ").concat(o,";\n        padding: ").concat(r,";\n        -apple-pay-button-type: ").concat(c,";\n        -webkit-locale: ").concat(s,";\n    }\n    .apple-pay-button-black {\n        -apple-pay-button-style: black;\n    }\n    .apple-pay-button-white {\n        -apple-pay-button-style: white;\n    }\n    .apple-pay-button-white-with-line {\n        -apple-pay-button-style: white-outline;\n    }\n  }\n\n  @supports not (-webkit-appearance: -apple-pay-button) {\n    .apple-pay-button {\n        display: inline-block;\n        background-size: 100% 60%;\n        background-repeat: no-repeat;\n        background-position: 50% 50%;\n        border-radius: 5px;\n        padding: 0px;\n        box-sizing: border-box;\n        min-width: 200px;\n        min-height: 32px;\n        max-height: 64px;\n    }\n    .apple-pay-button-black {\n        background-image: -webkit-named-image(apple-pay-logo-white);\n        background-color: black;\n    }\n    .apple-pay-button-white {\n        background-image: -webkit-named-image(apple-pay-logo-black);\n        background-color: white;\n    }\n    .apple-pay-button-white-with-line {\n        background-image: -webkit-named-image(apple-pay-logo-black);\n        background-color: white;\n        border: .5px solid black;\n    }\n  }\n"));p.type="text/css",p.styleSheet?p.styleSheet.cssText=d:p.appendChild(document.createTextNode(d)),n.appendChild(p);var h=document.createElement("button");h.classList.add("apple-pay-button","light"===u?"apple-pay-button-white":"apple-pay-button-black"),h.id="apple-pay-button";var C=document.createElement("span");C.classList.add("logo"),h.appendChild(C),n.appendChild(h)}(n,{styles:V(r.applePay),theme:r.theme}),c.applePay=!0,e(c)}))}else a("No wallet payment method is available on this device");else a("Container to mount elements was not provided")}))}function N(n){for(;n.firstChild;)n.removeChild(n.firstChild)}var U="payment-request-button",F="paystackpop-button",Z="pay-with-vault-button";function B(n){var e=document.createElement("button");return e.id=F,e.className="open-paystack-pop-button",e.innerText=n,e}function R(n){return n.querySelector("#".concat(F))}function D(){var n=document.createElement("div");return n.id=U,n}function W(n){return n.querySelector("#".concat(U))}function J(){var n=document.createElement("button");return n.className="pay-with-vault-button",n.id=Z,n.innerText="Pay with Vault",n}function K(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=document.createElement("div");t.className="vault-logo-container",t.innerHTML=M,n.appendChild(t);var a=document.createElement("p");a.id="instruction",a.className="vault-instruction",a.innerHTML="Access your saved cards and bank details for faster, more secure payments",n.appendChild(a);var i=J();n.appendChild(i);var o=document.createElement("div");if(o.className="vault-divider",o.innerHTML='<div id="vault-divider" class="vault-divider__container"><div class="vault-divider__line"></div></div><div class="vault-divider__text-container"><span class="vault-divider__text">or</span></div>',n.appendChild(o),e.canPayWithApplePay){var r=D();n.appendChild(r)}var c=B("Use other payment methods");n.appendChild(c)}function Q(n){var e=document.createElement("div");e.innerHTML='\n  <svg width="51" height="32" viewBox="0 0 51 32" fill="none" xmlns="http://www.w3.org/2000/svg" id="apple-pay-mark--light">\n    <g>\n    <path d="M46.0162 0H4.98386C4.81297 0 4.64177 0 4.47118 0.000996555C4.32698 0.******** 4.18311 0.******** 4.03925 0.00754966C3.72548 0.0160355 3.40903 0.0345472 3.09919 0.0902335C2.7844 0.146886 2.49148 0.239294 2.20571 0.384791C1.92477 0.52766 1.66757 0.71453 1.44468 0.937516C1.22169 1.1605 1.03482 1.41728 0.891977 1.69852C0.74645 1.98429 0.653982 2.27731 0.597722 2.59234C0.541737 2.90227 0.523101 3.21866 0.514645 3.53209C0.51078 3.67596 0.509122 3.81982 0.508183 3.96366C0.507186 4.13461 0.507519 4.30545 0.507519 4.4767V27.5236C0.507519 27.6949 0.507186 27.8654 0.508183 28.0367C0.509122 28.1805 0.51078 28.3244 0.514645 28.4683C0.523101 28.7814 0.541737 29.0978 0.597722 29.4077C0.653982 29.7228 0.74645 30.0157 0.891977 30.3015C1.03482 30.5827 1.22169 30.8399 1.44468 31.0625C1.66757 31.2859 1.92477 31.4727 2.20571 31.6152C2.49148 31.7611 2.7844 31.8535 3.09919 31.9102C3.40903 31.9655 3.72548 31.9843 4.03925 31.9928C4.18311 31.9961 4.32698 31.998 4.47118 31.9987C4.64177 32 4.81297 32 4.98386 32H46.0162C46.1868 32 46.358 32 46.5286 31.9987C46.6724 31.998 46.8163 31.9961 46.9608 31.9928C47.2739 31.9843 47.5903 31.9655 47.9009 31.9102C48.2153 31.8535 48.5083 31.7611 48.7941 31.6152C49.0753 31.4727 49.3317 31.2859 49.5551 31.0625C49.7777 30.8399 49.9646 30.5827 50.1078 30.3015C50.2537 30.0157 50.346 29.7228 50.402 29.4077C50.458 29.0978 50.4762 28.7814 50.4847 28.4683C50.4886 28.3244 50.4906 28.1805 50.4912 28.0367C50.4925 27.8654 50.4926 27.6949 50.4926 27.5236V4.4767C50.4926 4.30545 50.4925 4.13461 50.4912 3.96366C50.4906 3.81982 50.4886 3.67596 50.4847 3.53209C50.4762 3.21866 50.458 2.90227 50.402 2.59234C50.346 2.27731 50.2537 1.98429 50.1078 1.69852C49.9646 1.41728 49.7777 1.1605 49.5551 0.937516C49.3317 0.71453 49.0753 0.52766 48.7941 0.384791C48.5083 0.239294 48.2153 0.146886 47.9009 0.0902335C47.5903 0.0345472 47.2739 0.0160355 46.9608 0.00754966C46.8163 0.******** 46.6724 0.******** 46.5286 0.000996555C46.358 0 46.1868 0 46.0162 0Z" fill="black"/>\n    <path d="M46.0162 1.06662L46.521 1.06759C46.6577 1.06855 46.7945 1.07003 46.932 1.07378C47.1711 1.08024 47.4509 1.09319 47.7117 1.13994C47.9384 1.18077 48.1285 1.24286 48.311 1.33575C48.4911 1.42728 48.6562 1.54723 48.8003 1.69113C48.9449 1.83599 49.065 2.0013 49.1578 2.18343C49.2501 2.36447 49.3118 2.55369 49.3524 2.78205C49.3991 3.04001 49.412 3.32055 49.4185 3.56121C49.4222 3.69704 49.424 3.83287 49.4247 3.97194C49.426 4.14012 49.4259 4.3082 49.4259 4.47671V27.5236C49.4259 27.6921 49.426 27.8599 49.4246 28.0317C49.424 28.1675 49.4222 28.3033 49.4185 28.4394C49.4119 28.6797 49.3991 28.9601 49.3519 29.2211C49.3118 29.4463 49.2502 29.6356 49.1573 29.8175C49.0648 29.9992 48.9449 30.1643 48.8009 30.3083C48.656 30.4532 48.4915 30.5728 48.3092 30.6652C48.1281 30.7576 47.9383 30.8197 47.7138 30.8601C47.4477 30.9075 47.1562 30.9205 46.9367 30.9265C46.7986 30.9296 46.6611 30.9315 46.5203 30.9321C46.3525 30.9334 46.1841 30.9334 46.0162 30.9334H4.98386C4.98162 30.9334 4.97945 30.9334 4.97718 30.9334C4.81127 30.9334 4.64503 30.9334 4.4761 30.9321C4.33836 30.9315 4.20093 30.9296 4.06805 30.9266C3.8435 30.9205 3.55181 30.9075 3.2879 30.8604C3.06151 30.8197 2.87171 30.7576 2.68822 30.664C2.50766 30.5724 2.34329 30.453 2.19831 30.3077C2.05444 30.1641 1.93488 29.9995 1.84245 29.8176C1.74992 29.6358 1.68801 29.446 1.64731 29.218C1.60025 28.9576 1.58733 28.6783 1.58087 28.4396C1.57718 28.303 1.57564 28.1664 1.57476 28.0305L1.5741 27.6295L1.57413 27.5236V4.47671L1.5741 4.37083L1.57473 3.97067C1.57564 3.83402 1.57718 3.6974 1.58087 3.56088C1.58733 3.32197 1.60025 3.04258 1.64769 2.77991C1.68804 2.55405 1.74992 2.36422 1.84293 2.18155C1.93464 2.001 2.05441 1.83617 2.19903 1.69158C2.34308 1.54747 2.50799 1.42767 2.6897 1.33527C2.87122 1.24283 3.06138 1.18077 3.28778 1.14003C3.54864 1.09316 3.82861 1.08024 4.06839 1.07375C4.20507 1.07003 4.34174 1.06855 4.4774 1.06762L4.98386 1.06662H46.0162Z" fill="white"/>\n    <path d="M14.1531 10.7629C14.5811 10.2276 14.8715 9.50886 14.7949 8.77435C14.1684 8.80551 13.4038 9.18768 12.9612 9.72342C12.5638 10.1822 12.212 10.9311 12.3037 11.6348C13.007 11.6958 13.7097 11.2832 14.1531 10.7629Z" fill="black"/>\n    <path d="M14.7869 11.7722C13.7655 11.7114 12.8972 12.3519 12.4094 12.3519C11.9214 12.3519 11.1745 11.8029 10.3667 11.8177C9.31521 11.8331 8.33959 12.4276 7.80602 13.3731C6.70857 15.2646 7.51641 18.0704 8.58362 19.611C9.10188 20.3731 9.72648 21.2123 10.5495 21.1822C11.3271 21.1517 11.6319 20.6787 12.5771 20.6787C13.5216 20.6787 13.7961 21.1822 14.6192 21.1669C15.4729 21.1516 16.0065 20.4044 16.5248 19.6415C17.1193 18.7727 17.3627 17.9338 17.378 17.8877C17.3627 17.8725 15.732 17.2469 15.7169 15.3711C15.7015 13.8004 16.9972 13.0534 17.0581 13.007C16.3265 11.9249 15.1832 11.8029 14.7869 11.7722Z" fill="black"/>\n    <path d="M23.68 9.64661C25.8999 9.64661 27.4457 11.1768 27.4457 13.4046C27.4457 15.6404 25.8681 17.1786 23.6244 17.1786H21.1665V21.0872H19.3907V9.64661H23.68V9.64661ZM21.1665 15.688H23.2041C24.7502 15.688 25.6302 14.8556 25.6302 13.4126C25.6302 11.9697 24.7502 11.1451 23.2121 11.1451H21.1665V15.688Z" fill="black"/>\n    <path d="M27.9097 18.7167C27.9097 17.2578 29.0276 16.3619 31.0098 16.2509L33.293 16.1162V15.474C33.293 14.5464 32.6666 13.9914 31.6203 13.9914C30.629 13.9914 30.0106 14.467 29.8601 15.2124H28.2428C28.3379 13.7059 29.6222 12.5959 31.6836 12.5959C33.7053 12.5959 34.9976 13.6663 34.9976 15.3392V21.0872H33.3563V19.7156H33.3169C32.8333 20.6433 31.7787 21.2299 30.6847 21.2299C29.0514 21.2299 27.9097 20.2151 27.9097 18.7167ZM33.293 17.9635V17.3055L31.2395 17.4323C30.2167 17.5037 29.6381 17.9556 29.6381 18.6691C29.6381 19.3985 30.2406 19.8742 31.1603 19.8742C32.3574 19.8742 33.293 19.0496 33.293 17.9635Z" fill="black"/>\n    <path d="M36.547 24.1556V22.768C36.6736 22.7997 36.959 22.7997 37.1018 22.7997C37.8946 22.7997 38.3228 22.4668 38.5843 21.6105C38.5843 21.5946 38.7351 21.1031 38.7351 21.0952L35.7224 12.7466H37.5774L39.6866 19.5333H39.7181L41.8273 12.7466H43.6349L40.5109 21.5232C39.7976 23.5451 38.973 24.1952 37.2447 24.1952C37.1018 24.1952 36.6736 24.1793 36.547 24.1556Z" fill="black"/>\n    </g>\n    <defs>\n    <clipPath id="clip0">\n    <rect width="49.9851" height="32" fill="white" transform="translate(0.507462)"/>\n    </clipPath>\n    </defs>\n  </svg>\n',n.appendChild(e);var t=document.createElement("p");t.id="apple-pay-description",t.innerHTML="Pay with Apple Pay to complete your purchase without filling a form",n.appendChild(t);var a=D();n.appendChild(a);var i=B("More payment options");n.appendChild(i)}var G=[{value:"key",required:!0,types:["string"]},{value:"amount",required:!0,or:["plan","planCode"],types:["string","number"]},{value:"currency",required:!1,types:["string"]},{value:"email",required:!0,or:["customerCode"],types:["string"]},{value:"label",required:!1,types:["string"]},{value:"firstName",required:!1,types:["string"]},{value:"lastName",required:!1,types:["string"]},{value:"reference",required:!1,types:["string"]},{value:"phone",required:!1,types:["string"]},{value:"customerCode",required:!1,override:"email",types:["string"]},{value:"channels",required:!1,types:["array"]},{value:"paymentRequest",required:!1,types:["string","number"]},{value:"paymentPage",required:!1,types:["string"]},{value:"hash",required:!1,types:["string"]},{value:"container",required:!1,types:["string"]},{value:"metadata",required:!1,types:["object"]},{value:"subaccountCode",required:!1,types:["string"]},{value:"bearer",required:!1,types:["string"]},{value:"transactionCharge",required:!1,types:["string","number"]},{value:"planCode",required:!1,override:"amount",types:["string"]},{value:"subscriptionCount",required:!1,types:["number"]},{value:"planInterval",required:!1,types:["string"]},{value:"subscriptionLimit",required:!1,types:["number"]},{value:"subscriptionStartDate",required:!1,types:["string"]},{value:"accessCode",required:!1,types:["string"]},{value:"onError",required:!1,types:["function"]},{value:"onLoad",required:!1,types:["function"]},{value:"onSuccess",required:!1,types:["function"]},{value:"onCancel",required:!1,types:["function"]},{value:"callback",required:!1,types:["function"]},{value:"onClose",required:!1,types:["function"]},{value:"onBankTransferConfirmationPending",required:!1,types:["function"]},{value:"firstname",required:!1,types:["string"]},{value:"lastname",required:!1,types:["string"]},{value:"customer_code",required:!1,types:["string"]},{value:"payment_request",required:!1,types:["string","number"]},{value:"subaccount",required:!1,types:["string"]},{value:"transaction_charge",required:!1,types:["number","string"]},{value:"plan",required:!1,types:["string"]},{value:"quantity",required:!1,types:["number"]},{value:"interval",required:!1,types:["string"]},{value:"invoice_limit",required:!1,types:["number","string"]},{value:"start_date",required:!1,types:["string"]},{value:"payment_page",required:!1,types:["number","string"]},{value:"order_id",required:!1,types:["number"]},{value:"ref",required:!1,types:["string"]},{value:"card",required:!1,types:["string"]},{value:"bank",required:!1,types:["string"]},{value:"split",required:!1,types:["object"]},{value:"split_code",required:!1,types:["string"]},{value:"transaction_type",required:!1,types:["string"]},{value:"subscription",required:!1,types:["number"]},{value:"language",required:!1,types:["string"]},{value:"connect_account",required:!1,types:["string"]},{value:"connect_split",required:!1,types:["array"]}];function Y(n){return(null==n?void 0:n.length)>500?n.split("?")[0]:n}function X(n){var e,t,a,i,o=r({},n);o.metadata=n.metadata||{},o.metadata.referrer=(e=window.location,t=e.href,a=void 0===t?"":t,i=e.ancestorOrigins,[a].concat(l(void 0===i?[]:i)).map(Y).join(",")),o.metadata=JSON.stringify(o.metadata),o.mode="popup",n.split&&"string"!=typeof n.split&&(o.split=JSON.stringify(o.split));return void 0!==o.card&&["false",!1].indexOf(o.card)>-1&&(o.channels=["bank"],delete o.card),void 0!==o.bank&&["false",!1].indexOf(o.bank)>-1&&(o.channels=["card"],delete o.bank),[{to:"firstname",from:"firstName"},{to:"lastname",from:"lastName"},{to:"customer_code",from:"customerCode"},{to:"payment_request",from:"paymentRequest"},{to:"subaccount",from:"subaccountCode"},{to:"transaction_charge",from:"transactionCharge"},{to:"plan",from:"planCode"},{to:"quantity",from:"subscriptionCount"},{to:"interval",from:"planInterval"},{to:"invoice_limit",from:"subscriptionLimit"},{to:"start_date",from:"subscriptionStartDate"},{to:"ref",from:"reference"}].forEach((function(n){o[n.from]&&(o[n.to]=o[n.from],delete o[n.from])})),Object.values(n).forEach((function(e,t){if("function"==typeof e){var a=Object.keys(n)[t];delete o[a]}})),o}var $=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"],nn=window&&window.navigator&&(window.navigator.platform||window.navigator.userAgentData&&window.navigator.userAgentData.platform),en=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=n.platform,t=n.userAgent,a=void 0===t?window&&window.navigator&&window.navigator.userAgent:t,i=e||nn;return $.includes(i)||a.includes("Mac")&&"ontouchend"in document},tn=function(n,e,t){var a="".concat(y.paystackApiUrl,"transaction/update_log/").concat(n),i={Authorization:"Bearer ".concat(e)};return fetch(a,{method:"POST",body:JSON.stringify({payload:JSON.stringify(t)}),headers:i})},an=function(n,e){var t="".concat(y.paystackApiUrl,"transaction/set_ip/").concat(n),a={Authorization:"Bearer ".concat(e)};return fetch(t,{method:"POST",headers:a})},on={initializeLog:function(n){var e=n||{},t=e.attempts,a=e.authentication,i=e.errors,o=e.history;this.log={start_time:Math.round(Date.now()/1e3),time_spent:0,attempts:t||0,authentication:a,errors:i||0,success:!1,mobile:en(),input:[],history:o||[]}},getTimeSpent:function(){var n=Math.round(Date.now()/1e3);return this.log.time_spent=n-this.log.start_time,this.log.time_spent},logAPIResponse:function(n,e){switch(n.status){case"success":return this.logApiSuccess(e);case"failed":return this.logApiError(n.message);default:return!1}},logValidationResponse:function(n){return this.log.history.push({type:"action",message:n,time:this.getTimeSpent()}),this.saveLog()},logAttempt:function(n){var e="Attempted to pay";return n&&(e+=" with ".concat(n)),this.log.attempts+=1,this.log.history.push({type:"action",message:e,time:this.getTimeSpent()}),this.saveLog()},logApiError:function(n){var e="Error";return n&&(e+=": ".concat(n)),this.log.errors+=1,this.log.history.push({type:"error",message:e,time:this.getTimeSpent()}),this.saveLog()},logApiSuccess:function(n){var e="Successfully paid";return n&&(e+=" with ".concat(n)),this.log.success=!0,this.log.history.push({type:"success",message:e,time:this.getTimeSpent()}),this.saveLog()},saveLog:function(){try{if(this.response)return tn(this.id,this.response.merchant_key,this.log)}catch(n){}},saveIpAddress:function(){try{if(this.response)return an(this.id,this.response.merchant_key)}catch(n){}}},rn=["language","connect_account"],cn={requestInline:function(){var n=this,e=this.urlParameters,t=e.language,a=e.connect_account,i=c(e,rn),o=r({"Content-Type":"application/json"},t&&{"Accept-Language":t});return(this.accessCode?fetch(new URL("transaction/verify_access_code/".concat(this.accessCode),y.paystackApiUrl).toString(),{headers:o}):fetch(new URL("/checkout/request_inline",y.paystackApiUrl).toString(),{method:"POST",body:JSON.stringify(i),headers:r(r({},o),a&&{"x-connect-account":a})})).then((function(n){return n.json()})).then((function(e){if(!1===e.status)throw new Error(e.message);return n.response=e.data,n.id=e.data.id,n.status=e.data.transaction_status,n.accessCode=e.data.access_code,n.log=null,Object.assign(n,on),n.initializeLog(e.data.log),n.saveIpAddress(),e.data}))}},sn=function(){function n(t){e(this,n),function(n){function e(n,e){this.message=n,this.issues=e||[]}if(!n||"object"!==p(n))throw new e("Transaction parameters should be a non-empty object");var t=n;if("accessCode"in t)return{accessCode:t.accessCode};Object.keys(t).forEach((function(n){void 0!==G.find((function(e){return e.value===n}))||delete t[n]}));var a=Object.keys(t),i=[];if(G.filter((function(n){return n.required})).forEach((function(n){var e=!t[n.value],a=n.or?n.or.some((function(n){return t[n]})):null;e&&!a&&i.push({message:"Required parameter missing: ".concat(n.value)})})),a.forEach((function(n){var e=t[n],a=G.find((function(e){return e.value===n})),o=p(e);"object"===o&&Array.isArray(e)&&(o="array"),a.types.indexOf(o)<=-1&&i.push({message:"Invalid parameter type: ".concat(n),validTypes:a.types})})),a.forEach((function(n){var e=G.find((function(e){return e.value===n}));e.override&&delete t[e.override]})),i.length)throw new e("Invalid transaction parameters",i)}(t),this.parameters=t,this.urlParameters=X(t),this.id=null,this.status=null,this.accessCode=t.accessCode||null,this.authorizationUrl=null,this.errors=[],this.response=null,this.isActive=!0;var a=t.onError,i=t.onLoad,o=t.onSuccess,r=t.onCancel,c=t.callback,s=t.onClose,l=t.onBankTransferConfirmationPending;this.callbacks={onError:a,onLoad:i,onSuccess:o,onCancel:r,onBankTransferConfirmationPending:l},this.deprecatedCallbacks={callback:c,onClose:s},Object.assign(this,cn)}return a(n,[{key:"onSetupError",value:function(n){this.logError(n),this.callbacks.onError&&this.callbacks.onError(n)}},{key:"onLoad",value:function(n){var e=n.id,t=n.customer,a=n.accessCode;Object.assign(this,{id:e,customer:t,accessCode:a}),this.authorizationUrl="".concat(y.checkoutUrl).concat(a),this.callbacks.onLoad&&this.callbacks.onLoad({id:e,customer:t,accessCode:a})}},{key:"onSuccess",value:function(n){this.isActive=!1,this.response=n,this.status=n.status,this.callbacks.onSuccess&&this.callbacks.onSuccess(n),this.deprecatedCallbacks.callback&&this.deprecatedCallbacks.callback(n)}},{key:"setStatus",value:function(n){this.status=n}},{key:"onCancel",value:function(){this.callbacks.onCancel&&this.callbacks.onCancel(),this.deprecatedCallbacks.onClose&&this.deprecatedCallbacks.onClose()}},{key:"cancel",value:function(){this.isActive=!1,this.onCancel()}},{key:"onBankTransferConfirmationPending",value:function(){this.cancel(),this.callbacks.onBankTransferConfirmationPending&&this.callbacks.onBankTransferConfirmationPending()}},{key:"logError",value:function(n){this.errors.push(n)}}]),n}(),ln=console?console.warn||console.log:function(){};function un(n,e,t){ln('"'.concat(n,'" has been deprecated, please use "').concat(e,'". ').concat(t||""))}var pn,dn=["preload","inlineTransaction","transactionData"],hn=["container","styles","onElementsMount"];function Cn(n,e){if(!n.length)return null;var t=n.filter((function(n){var t,a,i,o,r=!n.status||"abandoned"===n.status,c=(t=n.parameters,a=e,i=Object.keys(t).sort().join("")===Object.keys(a).sort().join(""),o=Object.values(t).sort().join("")===Object.values(a).sort().join(""),i&&o);return r&&c}));return t.length?t[t.length-1]:null}function mn(n){var e=n.checkoutIframe,t=n.urlParameters;e&&t&&e.contentWindow.postMessage({type:"inline:url",path:"newTransaction",params:t},"*")}var yn="trackCheckoutClosed",vn="trackPaymentError",fn="trackPaymentAttempt",gn="trackPaymentCompletion";function bn(n){throw ln(n),new Error(n)}var kn,wn,xn=function(){function n(t){var a,i;e(this,n),this.id=function(){for(var n="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t=0;t<5;t+=1)n+=e.charAt(Math.floor(Math.random()*e.length));return n}(),this.transactions=[],this.isOpen=!1,this.isLoaded=!1,this.isDeprecatedApi=t&&t.isDeprecatedApi,t&&t.isEmbed?this.isEmbed=!0:t&&t.isPaymentRequest&&(t.container&&O(t.container)||bn("A container is required to mount the payment request button"),this.paymentRequestContainer=O(t.container),this.paymentRequestTransaction=null),this.preCheckoutModal=null,this.backgroundIframe=function(n){var e=I("inline-background-".concat(n));e.style.cssText="\n  z-index: 999999999999999;\n  background: transparent;\n  background: rgba(0, 0, 0, 0.75);    \n  border: 0px none transparent;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  margin: 0;\n  padding: 0;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n  position: fixed;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  transition: opacity 0.3s;\n  -webkit-transition: opacity 0.3s;\n  visibility: hidden;\n  display: none;\n",document.body.appendChild(e);var t=e.contentWindow.document;return t.open(),t.write('\n  <!DOCTYPE html>\n  <html lang="en">\n\n  <head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <meta http-equiv="X-UA-Compatible" content="ie=edge">\n    <title>Paystack Popup Loader</title>\n    <style>\n      .app-loader {\n        margin: 200px 0;\n        text-align: center;\n        color: white;\n      }      \n      @keyframes app-loader__spinner {\n        0% {\n          opacity: 1;\n        }\n        100% {\n          opacity: 0;\n        }\n      }\n      @-webkit-keyframes app-loader__spinner {\n        0% {\n          opacity: 1;\n        }\n        100% {\n          opacity: 0;\n        }\n      }\n      .app-loader__spinner {\n        position: relative;\n        display: inline-block;\n      }\n      .app-loader__spinner div {\n        left: 95px;\n        top: 35px;\n        position: absolute;\n        -webkit-animation: app-loader__spinner linear 1s infinite;\n        animation: app-loader__spinner linear 1s infinite;\n        background: white;\n        width: 10px;\n        height: 30px;\n        border-radius: 40%;\n        -webkit-transform-origin: 5px 65px;\n        transform-origin: 5px 65px;\n      }\n      .app-loader__spinner div:nth-child(1) {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n        -webkit-animation-delay: -0.916666666666667s;\n        animation-delay: -0.916666666666667s;\n      }\n      .app-loader__spinner div:nth-child(2) {\n        -webkit-transform: rotate(30deg);\n        transform: rotate(30deg);\n        -webkit-animation-delay: -0.833333333333333s;\n        animation-delay: -0.833333333333333s;\n      }\n      .app-loader__spinner div:nth-child(3) {\n        -webkit-transform: rotate(60deg);\n        transform: rotate(60deg);\n        -webkit-animation-delay: -0.75s;\n        animation-delay: -0.75s;\n      }\n      .app-loader__spinner div:nth-child(4) {\n        -webkit-transform: rotate(90deg);\n        transform: rotate(90deg);\n        -webkit-animation-delay: -0.666666666666667s;\n        animation-delay: -0.666666666666667s;\n      }\n      .app-loader__spinner div:nth-child(5) {\n        -webkit-transform: rotate(120deg);\n        transform: rotate(120deg);\n        -webkit-animation-delay: -0.583333333333333s;\n        animation-delay: -0.583333333333333s;\n      }\n      .app-loader__spinner div:nth-child(6) {\n        -webkit-transform: rotate(150deg);\n        transform: rotate(150deg);\n        -webkit-animation-delay: -0.5s;\n        animation-delay: -0.5s;\n      }\n      .app-loader__spinner div:nth-child(7) {\n        -webkit-transform: rotate(180deg);\n        transform: rotate(180deg);\n        -webkit-animation-delay: -0.416666666666667s;\n        animation-delay: -0.416666666666667s;\n      }\n      .app-loader__spinner div:nth-child(8) {\n        -webkit-transform: rotate(210deg);\n        transform: rotate(210deg);\n        -webkit-animation-delay: -0.333333333333333s;\n        animation-delay: -0.333333333333333s;\n      }\n      .app-loader__spinner div:nth-child(9) {\n        -webkit-transform: rotate(240deg);\n        transform: rotate(240deg);\n        -webkit-animation-delay: -0.25s;\n        animation-delay: -0.25s;\n      }\n      .app-loader__spinner div:nth-child(10) {\n        -webkit-transform: rotate(270deg);\n        transform: rotate(270deg);\n        -webkit-animation-delay: -0.166666666666667s;\n        animation-delay: -0.166666666666667s;\n      }\n      .app-loader__spinner div:nth-child(11) {\n        -webkit-transform: rotate(300deg);\n        transform: rotate(300deg);\n        -webkit-animation-delay: -0.083333333333333s;\n        animation-delay: -0.083333333333333s;\n      }\n      .app-loader__spinner div:nth-child(12) {\n        -webkit-transform: rotate(330deg);\n        transform: rotate(330deg);\n        -webkit-animation-delay: 0s;\n        animation-delay: 0s;\n      }\n      .app-loader__spinner {\n        width: 40px;\n        height: 40px;\n        -webkit-transform: translate(-20px, -20px) scale(0.2) translate(20px, 20px);\n        transform: translate(-20px, -20px) scale(0.2) translate(20px, 20px);\n      }\n    </style>\n  </head>\n\n  <body>\n    <div id="app-loader" class="app-loader">\n      <div id="spinner" class="app-loader__spinner">\n        <div></div><div></div><div></div><div></div><div></div><div></div><div>\n        </div><div></div><div></div><div></div><div></div><div></div>\n      </div>\n    </div>\n  </body>\n\n  </html>\n'),t.close(),e}(this.id),this.checkoutIframe=(a=this.id,(i=I("inline-checkout-".concat(a))).src="".concat(y.checkoutUrl,"popup"),i.style.cssText="\n  z-index: 999999999999999;\n  background: transparent;\n  border: 0px none transparent;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  margin: 0;\n  padding: 0;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n  position: fixed;\n  left: 0;\n  top: 0;\n  width: 100%;\n  visibility: hidden;\n  display: none;\n  height: 100%;\n",i.setAttribute("allowpaymentrequest","true"),i.setAttribute("allow","payment; clipboard-read; clipboard-write"),document.body.appendChild(i),i),this.registerListeners()}return a(n,[{key:"registerListeners",value:function(){var n=this;window.addEventListener("message",(function(e){var t="".concat(e.origin,"/")===y.checkoutUrl,a=n.checkoutIframe&&n.checkoutIframe.contentWindow===e.source,i=n.isEmbed;t||a?n.respondToEvent(e):i&&n.respondToEmbedEvents(e)}))}},{key:"sendAnalyticsEventToCheckout",value:function(n,e){this.checkoutIframe.contentWindow.postMessage({type:"analytics",action:n,params:e},"*")}},{key:"checkout",value:function(n){this.activeTransaction()&&this.activeTransaction().cancel(),pn=this;var e=Cn(this.transactions,n)||new sn(n);return new Promise((function(n,t){e.requestInline().then((function(t){var a=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=n.platform,t=n.userAgent,a=void 0===t?window&&window.navigator&&window.navigator.userAgent:t,i=e||nn,o=a&&!!a.match(/Version\/[\d.]+.*Safari/),r=i&&/(Mac)/i.test(i);return en()||r&&o}()&&q(t.channels),i=function(){var n,e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return!(null===(n=t.link_config)||void 0===n||!n.enabled||null===(e=t.link_config)||void 0===e||!e.has_payment_instruments)}(t);i||a?(pn.preloadTransaction({inlineTransaction:e,transactionData:t}),pn.preCheckoutModal=function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=document.querySelector("#pre-checkout-modal-".concat(n));if(a){if(R(a)&&W(a))return a;a.remove()}var i=document.createElement("div");i.classList.add("pre-checkout-modal"),i.id="pre-checkout-modal-".concat(n),i.style.zIndex=H()+1;var o=document.createElement("div");o.classList.add("pre-checkout-modal__content"),i.appendChild(o);var r=e||{},c=r.merchant_logo,s=r.merchant_name,l=r.email,u=r.amount,p=r.currency,d=r.label,h=new Intl.NumberFormat("en",{style:"currency",currency:p,currencyDisplay:"code",maximumFractionDigits:2,minimumFractionDigits:0}).format(u/100),C=document.createElement("div");C.classList.add("payment-info"),C.innerHTML='<img class="merchant-logo" src="'.concat(c,'" alt="').concat(s,' Logo">\n    <div class="customer-info">\n      <div class="customer-email">').concat(d||l,'</div>\n      <div class="transaction-amount">Pay <span class="amount">').concat(h,"</span></div>\n    </div>"),o.appendChild(C),o.innerHTML+=x;var m=document.createElement("div");m.classList.add("modal-wrapper"),t.canPayWithVault?K(m,{canPayWithApplePay:t.canPayWithApplePay}):Q(m),m.innerHTML=m.innerHTML+k+w,o.appendChild(m);var y=document.createElement("style");return y.textContent=L,document.body.appendChild(y),document.body.appendChild(i),i}(pn.id,t,{canPayWithVault:i,canPayWithApplePay:a}),a?(pn.paymentRequestContainer=W(pn.preCheckoutModal),j(pn.paymentRequestContainer,{channels:t.channels,styles:{applePay:{width:"100%",type:"pay",height:"42px",padding:"15px",borderRadius:"5px"}}},t.merchant_id).then((function(){pn.registerPaymentRequestEventListeners()})).catch((function(){i?W(pn.preCheckoutModal).remove():(pn.closePreCheckoutModal(),pn.animateCheckoutIn())})).finally((function(){n(e)}))):n(e)):(pn.newTransaction({inlineTransaction:e,transactionData:t}),n(e))})).catch((function(n){e.onSetupError({status:!1,message:n.message}),t(n)}))}))}},{key:"openPreCheckoutModal",value:function(){var n;this.registerPreCheckoutModalEventListeners(),n=this.preCheckoutModal,new Promise((function(e,t){try{var a=n.querySelector(".pre-checkout-modal__content");n.classList.add("show"),setTimeout((function(){a.classList.add("show"),e(!0)}),50)}catch(n){t(n)}}))}},{key:"registerPreCheckoutModalEventListeners",value:function(){var n,e=this,t=!1,a=this.activeTransaction();document.addEventListener("touchstart",(function(e){e.preventDefault(),t||(t=!0,n=setTimeout((function(){t=!1}),125))}),!0),document.addEventListener("touchend",(function(e){e.target&&e.target.isSameNode(pn.preCheckoutModal)&&t&&(clearTimeout(n),pn.closePreCheckoutModal(),a&&a.cancel()),t=!1}),!0);var i=R(this.preCheckoutModal),o=this.preCheckoutModal.querySelector("#".concat(Z));i.onclick=function(){e.closePreCheckoutModal(),e.animateCheckoutIn()},o&&(o.onclick=function(){e.closePreCheckoutModal(),e.animateCheckoutIn(),e.checkoutIframe.contentWindow.postMessage({type:"inline:pay-with-vault"},"*")});var r=function(n){return n.querySelector("#apple-pay-close-button")}(this.preCheckoutModal);r.onclick=function(){e.sendAnalyticsEventToCheckout(yn),e.closePreCheckoutModalAndCancelTransaction()}}},{key:"closePreCheckoutModal",value:function(n){var e;this.preCheckoutModal&&("failed"===n?(e=this.preCheckoutModal)&&(e.querySelector("#apple-pay-mark--light").innerHTML='<svg width="50" height="30" viewBox="0 0 21 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="error-icon">\n    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="error" fill-rule="nonzero">\n            <path d="M9.14672,0.47855 L0.14829,15.47855 C-0.0403320234,15.7872042 -0.0475647902,16.1736607 0.129375884,16.4891566 C0.306316558,16.8046526 0.639843999,16.9999993 1.00157,17 L19.43546,17 C19.797186,16.9999993 20.1307134,16.8046526 20.3076541,16.4891566 C20.4845948,16.1736607 20.477362,15.7872042 20.28874,15.47855 L10.85328,0.47855 C10.671624,0.181297031 10.3483651,3.00996351e-06 10,3.00996351e-06 C9.6516349,3.00996351e-06 9.32837603,0.181297031 9.14672,0.47855 Z" id="Shape" fill="#FFAA22"></path>\n            <rect id="Rectangle-path" fill="#FFFFFF" x="9" y="6" width="2" height="5"></rect>\n            <rect id="Rectangle-path" fill="#FFFFFF" x="9" y="12" width="2" height="2"></rect>\n        </g>\n    </g>\n</svg>',e.querySelector("#apple-pay-description").textContent="An error occurred while paying with Apple Pay. Please try again or use another payment method."):(!function(n){n&&(n.querySelector(".pre-checkout-modal__content").classList.remove("show"),n.classList.remove("show"))}(this.preCheckoutModal),this.preCheckoutModal.remove(),this.preCheckoutModal=null))}},{key:"closePreCheckoutModalAndCancelTransaction",value:function(){this.preCheckoutModal&&(this.cancelTransaction(),this.checkoutIframe&&this.checkoutIframe.contentWindow&&this.checkoutIframe.contentWindow.postMessage("close","*"),this.closePreCheckoutModal())}},{key:"newTransaction",value:function(n){var e=n.preload,t=n.inlineTransaction,a=n.transactionData,i=c(n,dn),o=this.paymentRequestContainer&&z(this.paymentRequestContainer);if(this.activeTransaction()&&!o&&this.activeTransaction().cancel(),t&&a)return this.transactions.push(t),this.isDeprecatedApi||this.open({accessCode:a.access_code,language:t.urlParameters.language},e),t;var r=Cn(this.transactions,t?t.parameters:i);if(r)return r.isActive=!0,this.isDeprecatedApi||this.open({accessCode:r.accessCode,language:r.urlParameters.language},e),r;var s=t||new sn(i),l=s.accessCode?{accessCode:s.accessCode,language:s.urlParameters.language}:s.urlParameters;return this.transactions.push(s),this.isDeprecatedApi||this.open(l,e),s}},{key:"preloadTransaction",value:function(n){var e=this;this.newTransaction(r(r({},n),{},{preload:!0}));return function(){return e.animateCheckoutIn()}}},{key:"paymentRequest",value:function(n){var e=n.container,t=n.styles,a=n.onElementsMount,i=c(n,hn);return pn=this,new Promise((function(o,r){var c=document.querySelector("#".concat(n.loadPaystackCheckoutButton));if(T()){pn.activeTransaction()&&pn.activeTransaction().cancel(),e&&O(e)||bn("A container is required to mount the payment request button"),pn.paymentRequestContainer=O(e);var s=Cn(pn.transactions,i),l=s||new sn(i);l.requestInline().then((function(n){j(pn.paymentRequestContainer,{channels:n.channels,styles:t},n.merchant_id).then((function(n){a&&a(n)})).catch((function(){a&&a(null)})).finally((function(){if(s?l.isActive=!0:pn.transactions.push(l),pn.registerPaymentRequestEventListeners(),c){var e=pn.preloadTransaction({inlineTransaction:l,transactionData:n});c.onclick=e}o(l)}))})).catch((function(n){l.onSetupError({status:!1,message:n.message}),r(n)}))}else{if(n&&n.loadPaystackCheckoutButton)if(c){var u=pn.preloadTransaction(i);c.onclick=u}else ln("This device does not support any payment request wallet options. Please consult our documentation at https://developers.paystack.co/docs/paystack-inline to see how to load alternative payment options using 'loadPaystackCheckoutButton'");a&&a(null);var p=pn.activeTransaction();o(p)}}))}},{key:"registerApplePayEventListener",value:function(){var n=this;z(this.paymentRequestContainer).onclick=function(){return n.startApplePay()}}},{key:"registerPaymentRequestEventListeners",value:function(){var n=this.activeTransaction();n&&q(n.response.channels)?this.registerApplePayEventListener():N(this.paymentRequestContainer)}},{key:"startApplePay",value:function(){var n,e,t,a,i,o=this,c="apple pay",s=this.activeTransaction();if(s){var l={channel:"apple_pay",paymentMethod:c,currency:s.currency,amount:s.amount},u={channel:"apple_pay",currency:s.currency,amount:s.amount,timeSpent:s.getTimeSpent()};try{s.logAttempt(c),this.sendAnalyticsEventToCheckout(fn,l);var p=(n={currency:s.response.currency,amount:s.response.amount,merchantName:s.response.merchant_name,interval:s.response.plan_details&&s.response.plan_details.interval},e=n.currency,t=n.amount,a=n.merchantName,i=n.interval,r({countryCode:"NG",currencyCode:e,merchantCapabilities:["supports3DS","supportsCredit","supportsDebit"],supportedNetworks:["visa","masterCard"],requiredBillingContactFields:["postalAddress","name","phone","email"],total:{label:"".concat(a," - Paystack"),type:"final",amount:String(_(t))}},"string"==typeof i&&""!==i.trim()&&{lineItems:[{label:A(i),amount:String(_(t))}]})),d=new window.ApplePaySession(y.applePayVersion,p);d.onvalidatemerchant=function(n){var e=function(n){var e=n.transactionId,t=n.validationURL,a=n.merchantName,i=n.domainName,o=void 0===i?window&&window.location&&window.location.hostname:i,c="".concat(y.paymentBaseUrl).concat(y.applePayValidateSessionPath),s=E({transaction:e,sessionUrl:t,displayName:a,domainName:o});return fetch(c,r(r({},S),{},{body:s})).then((function(n){return n.json()}))}({validationURL:n.validationURL,transactionId:s.id,merchantName:s.response.merchant_name});e.then((function(n){"success"!==n.status?s.onSetupError(n):d.completeMerchantValidation(n.data),s.logValidationResponse(n.message)})).catch((function(n){s.onSetupError(n)}))},d.oncancel=function(){pn.preCheckoutModal||s.onCancel()},d.onpaymentauthorized=function(n){var e=n.payment,t=function(n){var e=n.transactionId,t=n.payment,a="".concat(y.paymentBaseUrl).concat(y.applePayChargePath),i=E({transaction:e,paymentObject:JSON.stringify(t)});return fetch(a,r(r({},S),{},{body:i})).then((function(n){return n.json()}))}({transactionId:s.id,payment:e});t.then((function(n){s.logAPIResponse(n,c),"success"===n.status?(d.completePayment(d.STATUS_SUCCESS),s.onSuccess(n),o.sendAnalyticsEventToCheckout(gn,u)):(d.completePayment(d.STATUS_FAILURE),s.onSetupError(n),o.sendAnalyticsEventToCheckout(vn,{channel:"apple_pay",message:n&&n.message||"Transaction attempt failed"})),pn.closePreCheckoutModal(n.status)})).catch((function(n){d.completePayment(d.STATUS_FAILURE),s.onSetupError(n),o.sendAnalyticsEventToCheckout(vn,{channel:"apple_pay",message:n&&n.message||"Error occurred"}),pn.closePreCheckoutModal("failed")}))},d.begin()}catch(n){s.onSetupError(n)}}else bn("Could not initiate apple pay transaction")}},{key:"resumeTransaction",value:function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.onSuccess,a=e.onCancel,i=e.onLoad,o=e.onError;return this.newTransaction({accessCode:n,onSuccess:t,onCancel:a,onLoad:i,onError:o})}},{key:"activeTransaction",value:function(){var n=this.transactions.filter((function(n){return n.isActive})),e=n.length?n[n.length-1]:null;return e}},{key:"cancelTransaction",value:function(n){var e=this.transactions.find((function(e){return e.id===n}))||this.activeTransaction();e&&(e.cancel(),this.close())}},{key:"respondToEvent",value:function(n){if(n){var e,t,a=this.activeTransaction();try{var i=n.data||n.message,o=i.event,r=i.data;if(o)switch(o){case"loaded:checkout":if(this.isLoaded=!0,a){var c=this.checkoutIframe,s=a.urlParameters,l=a.response;mn({checkoutIframe:c,urlParameters:l?{accessCode:l.access_code,language:null==s?void 0:s.language}:s})}break;case"loaded:transaction":e=this.backgroundIframe,(t=e.contentWindow.document)&&(t.getElementById("app-loader").style.display="none"),this.preCheckoutModal&&this.openPreCheckoutModal(),a.onLoad(r);break;case"error":"setup"===r.type?a.onSetupError(r):a.logError(r);break;case"cancel":case"close":this.close();var u=r&&r.status;u&&a.setStatus(u),!(this.paymentRequestContainer&&z(this.paymentRequestContainer)&&!this.preCheckoutModal)&&(a.isActive=!1),a.onCancel();break;case"transfer:pending":this.close();var p=r&&r.status;p&&a.setStatus(p),a.onBankTransferConfirmationPending();break;case"success":this.close(),a.onSuccess(r)}}catch(n){}}}},{key:"respondToEmbedEvents",value:function(n){var e,t,a=this.activeTransaction(),i=n.data||n.message;if(i&&("string"==typeof i||i instanceof String)){var o={action:t=(e=i)&&"string"==typeof e?e.split(" ")[0]:null,data:t?e.split(" ").slice(2).join(" "):null};if(o&&"PaystackClose"===o.action)o.data&&a.onSuccess(i);"PaystackTLSClose"===o.action&&a.cancel()}}},{key:"animateCheckoutIn",value:function(){var n,e=this;if(!this.isOpen){var t=this.checkoutIframe,a=this.backgroundIframe;(n={checkoutIframe:t,backgroundIframe:a},new Promise((function(e,t){n||t("No dom element provided");var a=n.checkoutIframe,i=n.backgroundIframe;a&&i||t("No dom element provided"),a.style.display="",a.style.visibility="visible",i.style.display="",i.style.visibility="visible",e()}))).then((function(){e.checkoutIframe.contentWindow.postMessage("render","*")})),this.isOpen=!0}}},{key:"open",value:function(n,e){n&&(mn({checkoutIframe:this.checkoutIframe,urlParameters:n}),e||this.animateCheckoutIn())}},{key:"close",value:function(){var n=this;if(this.isOpen){var e,t=this.checkoutIframe,a=this.backgroundIframe;(e={checkoutIframe:t,backgroundIframe:a},new Promise((function(n,t){e||t("No dom element provided");var a=e.checkoutIframe,i=e.backgroundIframe;a&&i||t("No dom element provided"),i.style.opacity=0,a.style.display="none",a.style.visibility="hidden",setTimeout((function(){i.style.display="none",i.style.visibility="hidden",i.style.opacity=1,n()}),300)}))).then((function(){n.checkoutIframe.contentWindow.postMessage("close","*")})),this.isOpen=!1}}},{key:"isLoaded",value:function(){return this.isLoaded}}],[{key:"setup",value:function(e){var t=e&&e.container;pn||(pn=new n({isDeprecatedApi:!0,isEmbed:t})),un("PaystackPop.setup()","new PaystackPop()","Please consult our documentation at https://developers.paystack.co/docs/paystack-inline");var a=pn.newTransaction(e,"deprecated"),i=a.urlParameters;if(t){var o="".concat(y.siteUrl,"/assets/payment/production/inline.html?").concat(f(i)),r=function(n,e){var t=I("embed-checkout-".concat(n));return t.style.cssText="\n  background: transparent;\n  background: rgba(0,0,0,0);\n  border: 0px none transparent;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  nmargin: 0;\n  padding: 0;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  visibility: hidden;\n  display: none;\n",t.src=e,t.id=n,t.name=n,t}(pn.id,o);!function(n,e){var t=document.getElementById(n);t.innerHTML="",t.removeAttribute("style"),t.className="paystack-embed-container",t.style.position="relative",t.style.width="100%",t.appendChild(e)}(e.container,r),r.onload=function(){var n;r.contentWindow.postMessage("PaystackOpen ".concat(pn.id),"*"),n=r,new Promise((function(e,t){n||t("No dom element provided"),n.style.display="",n.style.visibility="visible",e()}))}}else a.openIframe=function(){un("openIframe","open","Please consult our documentation at https://developers.paystack.co/docs/paystack-inline"),pn.open(i)};return a}}]),n}();if(kn=b().length>0,wn=g()&&"FORM"===g().parentElement.tagName,kn&&wn){var Mn,Pn=function(){var n={},e=g();return b().forEach((function(t){var a=e.getAttribute(t),i=t.split("data-")[1].replace(/-([a-z])/g,(function(n){return n[1].toUpperCase()}));n[i]=a})),function(n){if(n.buttonId&&!document.getElementById(n.buttonId))throw new Error("Please make sure the buttonId is an element available in the DOM");var e=r({},n);e.buttonText=n.buttonText||"Pay",e.buttonVariant="normal",e.buttonWordmarkVariant="normal";var t=["normal","light"];return n.buttonVariant&&t.indexOf(n.buttonVariant)>-1&&(e.buttonVariant=n.buttonVariant),n.buttonWordmarkVariant&&t.indexOf(n.buttonWordmarkVariant)>-1&&(e.buttonWordmarkVariant=n.buttonWordmarkVariant),e}(n)}(),Vn=g().parentElement;pn||(pn=new xn),function(n){var e;if(n.id)(e=document.getElementById(n.id)).setAttribute("data-inline-id",n.id);else{var t=document.createElement("div");t.id="inline-button-".concat(n.inlineId),t.innerHTML=function(n){var e,t,a={normal:'\n  <svg id="inline-button-wordmark" width="137" height="13" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M.037 5.095l1.075-.135c-.011-.774-.025-1.944-.013-2.149C1.19 1.364 2.38.134 3.81.013 3.9.006 3.99.002 4.077 0a2.947 2.947 0 0 1 2.046.76c.574.509.95 1.26 1.008 *************.01 1.491.01 2.257l1.096.163L8.2 11.44 4.093 12 0 11.346l.037-6.251zm4.106-.514l1.724.256c-.007-.933-.05-2.295-.26-2.654-.319-.545-.846-.867-1.443-.88h-.063c-.607.008-1.138.322-1.458.864-.222.378-.266 1.66-.265 2.637l1.765-.223zM18.228 10.108c-.576 0-1.064-.072-1.464-.216a2.864 2.864 0 0 1-.972-.6 2.552 2.552 0 0 1-.588-.864 4.067 4.067 0 0 1-.252-1.044h1.008c.032.256.088.5.168.732.08.224.204.424.372.6.168.168.388.304.66.408.28.096.636.144 1.068.144.28 0 .536-.036.768-.108.24-.08.448-.192.624-.336.176-.144.312-.316.408-.516.104-.2.156-.42.156-.66 0-.24-.032-.448-.096-.624a1.02 1.02 0 0 0-.336-.468 1.885 1.885 0 0 0-.636-.324 6.4 6.4 0 0 0-1.008-.228 8.79 8.79 0 0 1-1.212-.276 3.246 3.246 0 0 1-.9-.432 1.982 1.982 0 0 1-.564-.672c-.128-.272-.192-.6-.192-.984 0-.328.068-.632.204-.912.136-.288.324-.536.564-.744.248-.208.54-.372.876-.492.336-.12.708-.18 1.116-.18.864 0 1.548.204 2.052.612.512.4.812.984.9 1.752h-.936c-.104-.544-.316-.932-.636-1.164-.32-.24-.78-.36-1.38-.36-.592 0-1.04.132-1.344.396a1.255 1.255 0 0 0-.444.996c0 .208.024.396.072.564.056.16.156.3.3.42.152.12.36.228.624.324a6.72 6.72 0 0 0 1.068.228c.48.072.9.168 1.26.288.36.12.664.276.912.468s.432.428.552.708c.128.28.192.624.192 1.032 0 .36-.076.696-.228 1.008a2.472 2.472 0 0 1-.612.804c-.264.224-.58.4-.948.528-.36.128-.752.192-1.176.192zM25.355 10.108c-.44 0-.848-.076-1.224-.228a2.916 2.916 0 0 1-.96-.636 2.966 2.966 0 0 1-.636-1.008 3.77 3.77 0 0 1-.216-1.308v-.096c0-.472.072-.904.216-1.296.144-.4.344-.74.6-1.02.264-.288.576-.508.936-.66.36-.16.756-.24 1.188-.24.36 0 .708.06 1.044.18.344.112.648.292.912.54.264.248.472.572.624.972.16.392.24.868.24 1.428v.324h-4.728c.024.72.204 1.272.54 1.656.336.376.828.564 1.476.564.984 0 1.54-.364 1.668-1.092h.996c-.112.632-.408 1.112-.888 1.44-.48.32-1.076.48-1.788.48zm1.704-3.852c-.048-.648-.232-1.112-.552-1.392-.312-.28-.728-.42-1.248-.42-.512 0-.932.164-1.26.492-.32.32-.524.76-.612 1.32h3.672zM32.091 10.108c-.44 0-.848-.072-1.224-.216a3.054 3.054 0 0 1-.972-.636 3.12 3.12 0 0 1-.648-1.008 3.626 3.626 0 0 1-.228-1.32v-.096c0-.48.08-.916.24-1.308.16-.4.376-.74.648-1.02.28-.28.604-.496.972-.648.376-.16.772-.24 1.188-.24.328 0 .644.04.948.12.312.08.588.208.828.384.248.168.456.392.624.672.168.28.276.62.324 1.02h-.984c-.08-.496-.284-.848-.612-1.056-.32-.208-.696-.312-1.128-.312a1.93 1.93 0 0 0-.804.168c-.24.112-.452.272-.636.48a2.23 2.23 0 0 0-.42.744 2.991 2.991 0 0 0-.156.996v.096c0 .776.188 1.364.564 1.764.384.392.88.588 1.488.588.224 0 .436-.032.636-.096a1.651 1.651 0 0 0 .96-.768c.112-.192.18-.416.204-.672h.924a2.595 2.595 0 0 1-.276.948 2.386 2.386 0 0 1-.576.744c-.24.208-.52.372-.84.492-.32.12-.668.18-1.044.18zM38.335 10.108a2.83 2.83 0 0 1-.876-.132 1.724 1.724 0 0 1-.684-.42 2.145 2.145 0 0 1-.456-.756c-.112-.304-.168-.672-.168-1.104V3.724h.996v3.924c0 .552.116.956.348 1.212.24.256.608.384 1.104.384.224 0 .44-.036.648-.108.208-.072.392-.18.552-.324.16-.144.288-.324.384-.54.096-.216.144-.464.144-.744V3.724h.996V10h-.996v-.996c-.144.296-.388.556-.732.78-.336.216-.756.324-1.26.324zM43.216 3.724h.996v1.128c.2-.352.452-.64.756-.864.312-.232.748-.356 1.308-.372v.936a4.461 4.461 0 0 0-.852.12 1.647 1.647 0 0 0-.66.324 1.472 1.472 0 0 0-.408.612c-.096.248-.144.564-.144.948V10h-.996V3.724zM50 10.108c-.44 0-.848-.076-1.224-.228a2.916 2.916 0 0 1-.96-.636 2.966 2.966 0 0 1-.636-1.008 3.77 3.77 0 0 1-.216-1.308v-.096c0-.472.072-.904.216-1.296.144-.4.344-.74.6-1.02.264-.288.576-.508.936-.66.36-.16.756-.24 1.188-.24.36 0 .708.06 1.044.18.344.112.648.292.912.54.264.248.472.572.624.972.16.392.24.868.24 1.428v.324h-4.728c.024.72.204 1.272.54 1.656.336.376.828.564 1.476.564.984 0 1.54-.364 1.668-1.092h.996c-.112.632-.408 1.112-.888 1.44-.48.32-1.076.48-1.788.48zm1.704-3.852c-.048-.648-.232-1.112-.552-1.392-.312-.28-.728-.42-1.248-.42-.512 0-.932.164-1.26.492-.32.32-.524.76-.612 1.32h3.672zM56.496 10.108c-.408 0-.788-.068-1.14-.204a2.683 2.683 0 0 1-.9-.612 3.01 3.01 0 0 1-.588-.984 4.01 4.01 0 0 1-.204-1.32v-.096c0-.48.072-.92.216-1.32.144-.4.344-.744.6-1.032.256-.296.564-.524.924-.684.36-.16.756-.24 1.188-.24.528 0 .956.112 1.284.336.328.216.584.476.768.78V.724h.996V10h-.996V8.92c-.088.152-.208.3-.36.444a2.792 2.792 0 0 1-.516.384 2.874 2.874 0 0 1-.6.252c-.216.072-.44.108-.672.108zm.108-.828c.288 0 .56-.048.816-.144.256-.096.476-.24.66-.432.184-.2.328-.448.432-.744.112-.304.168-.656.168-1.056v-.096c0-.808-.18-1.404-.54-1.788-.352-.384-.836-.576-1.452-.576-.624 0-1.112.208-1.464.624-.352.416-.528 1.008-.528 1.776v.096c0 .392.048.736.144 1.032.104.296.24.54.408.732.176.192.38.336.612.432.232.096.48.144.744.144zM67.712 10.108c-.512 0-.948-.112-1.308-.336a2.38 2.38 0 0 1-.816-.804V10h-.996V.724h.996V4.78a1.92 1.92 0 0 1 .348-.432c.152-.144.32-.268.504-.372.192-.112.396-.2.612-.264.216-.064.436-.096.66-.096.408 0 .788.072 1.14.216.352.144.652.352.9.624.256.272.456.604.6.996.144.392.216.832.216 1.32v.096c0 .48-.068.92-.204 1.32a3.103 3.103 0 0 1-.576 1.02 2.583 2.583 0 0 1-.9.672 2.937 2.937 0 0 1-1.176.228zm-.096-.828c.624 0 1.1-.2 1.428-.6.328-.408.492-.996.492-1.764V6.82c0-.4-.052-.748-.156-1.044a2.095 2.095 0 0 0-.42-.732 1.53 1.53 0 0 0-.612-.444 1.798 1.798 0 0 0-.744-.156c-.288 0-.56.048-.816.144a1.71 1.71 0 0 0-.648.444c-.184.192-.328.44-.432.744a3.152 3.152 0 0 0-.156 1.044v.096c0 .8.192 1.396.576 1.788.384.384.88.576 1.488.576zM73.63 9.352l-2.46-5.628h1.068l1.92 4.5 1.74-4.5h1.02l-3.468 8.46h-1.008l1.188-2.832zM87.127 3.669A3.138 3.138 0 0 0 86.1 2.95a3.09 3.09 0 0 0-1.228-.25c-.448 0-.848.086-1.187.26a2.199 2.199 0 0 0-.662.497v-.191a.387.387 0 0 0-.214-.348.323.323 0 0 0-.14-.03h-1.315a.314.314 0 0 0-.254.116.377.377 0 0 0-.1.262v8.97c0 .1.034.188.1.258a.34.34 0 0 0 .254.103h1.341a.342.342 0 0 0 .244-.103.336.336 0 0 0 .11-.259v-3.06c.178.202.417.357.702.464.35.134.72.203 1.093.203.43 0 .848-.082 1.242-.248a3.124 3.124 0 0 0 1.04-.724c.305-.326.545-.709.707-1.128a3.93 3.93 0 0 0 .263-1.477c0-.54-.086-1.037-.263-1.477a3.387 3.387 0 0 0-.706-1.12zm-1.204 3.24c-.073.19-.18.362-.315.51a1.415 1.415 0 0 1-1.065.466c-.2.001-.4-.04-.584-.12a1.484 1.484 0 0 1-.49-.346 1.593 1.593 0 0 1-.32-.51 1.738 1.738 0 0 1-.115-.63c0-.224.04-.435.115-.631a1.532 1.532 0 0 1 .804-.846c.185-.086.386-.13.59-.129.215 0 .414.044.593.13.177.083.338.199.474.341a1.622 1.622 0 0 1 .425 1.135c0 .225-.037.436-.112.63zM95.298 2.89h-1.33a.339.339 0 0 0-.246.11.384.384 0 0 0-.108.266v.166a1.856 1.856 0 0 0-.602-.472 2.525 2.525 0 0 0-1.166-.258 3.227 3.227 0 0 0-2.284.964 3.554 3.554 0 0 0-.734 1.123 3.827 3.827 0 0 0-.275 1.477c0 .54.092 1.037.275 1.477.184.434.427.817.728 1.128a3.146 3.146 0 0 0 2.277.973c.437 0 .834-.088 1.173-.259.25-.13.456-.287.608-.471v.177a.34.34 0 0 0 .11.259.341.341 0 0 0 .244.104h1.33a.324.324 0 0 0 .25-.105.349.349 0 0 0 .102-.258V3.267a.377.377 0 0 0-.1-.262.325.325 0 0 0-.252-.115zM93.502 6.9a1.55 1.55 0 0 1-.312.511c-.136.143-.296.26-.473.344-.178.085-.38.129-.596.129-.207 0-.407-.044-.59-.13a1.501 1.501 0 0 1-.791-.855 1.766 1.766 0 0 1-.112-.62c0-.225.038-.436.112-.632.075-.193.181-.364.314-.504.137-.143.3-.26.478-.342.182-.085.382-.129.59-.129.215 0 .417.044.595.13.178.085.338.2.473.341a1.623 1.623 0 0 1 .424 1.135c0 .215-.037.424-.112.622zM108.567 6.094a2.265 2.265 0 0 0-.654-.402c-.247-.101-.509-.181-.785-.235l-1.014-.204c-.26-.05-.441-.117-.543-.203a.328.328 0 0 1-.136-.264c0-.11.063-.2.189-.282.137-.086.329-.13.566-.13.26 0 .518.053.757.157.243.106.471.226.67.36.295.187.546.162.727-.053l.487-.57a.543.543 0 0 0 .152-.357c0-.128-.064-.245-.185-.351-.207-.184-.533-.378-.971-.568-.437-.192-.987-.29-1.637-.29-.427 0-.82.058-1.168.172-.35.116-.65.276-.893.474-.245.204-.438.44-.57.713a2 2 0 0 0-.198.875c0 .56.167 1.017.496 1.358.328.333.766.56 1.304.67l1.054.232c.3.062.528.132.675.21.129.067.19.163.19.297 0 .12-.061.227-.188.324-.133.104-.342.155-.622.155a1.83 1.83 0 0 1-.831-.19 3.056 3.056 0 0 1-.678-.458.995.995 0 0 0-.307-.17c-.126-.037-.268.003-.431.13l-.583.461c-.169.145-.24.32-.209.522.029.194.19.394.491.62.269.193.614.368 1.029.518.415.151.901.229 1.453.229.444 0 .854-.058 1.215-.172.362-.119.681-.278.941-.48a2.056 2.056 0 0 0 .819-1.663c0-.319-.053-.6-.165-.836a1.843 1.843 0 0 0-.447-.6zM114.383 7.73a.363.363 0 0 0-.295-.192.55.55 0 0 0-.343.113c-.095.062-.198.11-.306.141a.75.75 0 0 1-.426.013.43.43 0 0 1-.181-.093.554.554 0 0 1-.143-.204.92.92 0 0 1-.059-.362v-2.46h1.731c.099 0 .188-.04.266-.117a.368.368 0 0 0 .112-.26V3.268a.369.369 0 0 0-.115-.268.38.38 0 0 0-.263-.109h-1.732V1.216a.354.354 0 0 0-.108-.27.347.347 0 0 0-.243-.104h-1.344a.36.36 0 0 0-.34.226.371.371 0 0 0-.027.148V2.89h-.767a.324.324 0 0 0-.255.115.385.385 0 0 0-.098.262V4.31a.4.4 0 0 0 .212.346c.044.021.092.032.14.03h.768v2.925c0 .39.069.726.2 1.003.132.274.305.504.514.676.217.178.465.31.731.388.27.084.551.126.833.126.385 0 .75-.061 1.094-.18a2.13 2.13 0 0 0 .861-.552c.152-.181.17-.381.046-.581l-.463-.76zM121.672 2.89h-1.329a.339.339 0 0 0-.244.11.39.39 0 0 0-.08.122.394.394 0 0 0-.027.144v.166a1.906 1.906 0 0 0-.605-.472c-.335-.173-.726-.258-1.168-.258-.42 0-.834.083-1.226.249a3.24 3.24 0 0 0-1.055.715 3.528 3.528 0 0 0-.734 1.123 3.79 3.79 0 0 0-.276 1.477c0 .54.092 1.037.275 1.477.184.434.428.817.729 1.128a3.138 3.138 0 0 0 2.273.973 2.59 2.59 0 0 0 1.175-.259c.255-.13.457-.287.612-.471v.177a.34.34 0 0 0 .108.259.343.343 0 0 0 .243.104h1.329a.335.335 0 0 0 .252-.105.364.364 0 0 0 .102-.258V3.267a.38.38 0 0 0-.1-.262.332.332 0 0 0-.115-.087.311.311 0 0 0-.139-.028zM119.876 6.9a1.534 1.534 0 0 1-.786.855 1.362 1.362 0 0 1-.594.129c-.207 0-.405-.044-.588-.13a1.516 1.516 0 0 1-.792-.855 1.757 1.757 0 0 1-.113-.62c0-.225.037-.436.112-.632.073-.187.179-.358.314-.504.138-.143.3-.26.479-.342.184-.086.385-.13.588-.129.217 0 .415.044.594.13.181.085.34.2.472.341.134.143.24.313.314.504a1.73 1.73 0 0 1 0 1.253zM128.978 7.64l-.763-.593c-.146-.118-.284-.15-.404-.1a.742.742 0 0 0-.279.205 2.527 2.527 0 0 1-.583.535c-.192.122-.444.183-.742.183-.219 0-.42-.04-.6-.122a1.423 1.423 0 0 1-.469-.342 1.575 1.575 0 0 1-.308-.51 1.751 1.751 0 0 1-.106-.617c0-.228.034-.438.106-.632.07-.192.173-.363.308-.503.135-.144.295-.26.472-.342.187-.088.391-.132.597-.13.298 0 .547.064.742.187.198.126.396.306.584.534.078.092.17.16.278.206.122.048.259.016.401-.101l.762-.594a.53.53 0 0 0 .201-.269.437.437 0 0 0-.034-.365 3.329 3.329 0 0 0-1.18-1.127c-.504-.291-1.108-.441-1.784-.441a3.519 3.519 0 0 0-2.51 1.033c-.322.322-.576.71-.747 1.137a3.68 3.68 0 0 0-.273 1.407c0 .495.093.968.273 1.402.173.424.427.808.747 1.128a3.527 3.527 0 0 0 2.51 1.034c.676 0 1.28-.149 1.784-.444a3.286 3.286 0 0 0 1.182-1.13.411.411 0 0 0 .055-.173.415.415 0 0 0-.023-.182.624.624 0 0 0-.197-.273zM136.06 9.045l-2.104-3.143 1.801-2.415c.094-.139.119-.272.075-.397-.031-.09-.116-.2-.334-.2h-1.425a.52.52 0 0 0-.234.058.482.482 0 0 0-.209.205L132.191 5.2h-.349V.363a.37.37 0 0 0-.099-.26.352.352 0 0 0-.253-.103h-1.332a.37.37 0 0 0-.337.22.346.346 0 0 0-.027.143V9.29c0 .103.038.193.11.259a.353.353 0 0 0 .254.104h1.333a.328.328 0 0 0 .251-.105.346.346 0 0 0 .075-.119.333.333 0 0 0 .024-.14V6.927h.386l1.571 2.446c.112.187.267.281.46.281h1.491c.226 0 .32-.11.358-.202.054-.13.038-.262-.047-.406zM102.863 2.89h-1.489a.389.389 0 0 0-.298.122.544.544 0 0 0-.13.249l-1.099 4.167h-.268l-1.182-4.167a.66.66 0 0 0-.113-.247.329.329 0 0 0-.264-.124h-1.544c-.199 0-.325.066-.372.193a.588.588 0 0 0-.002.37l1.887 5.865c.03.093.08.17.145.232a.388.388 0 0 0 .281.104h.798l-.066.19-.19.547a.872.872 0 0 1-.29.426.7.7 0 0 1-.442.148.956.956 0 0 1-.4-.09 1.842 1.842 0 0 1-.35-.209.62.62 0 0 0-.335-.115h-.016c-.13 0-.243.074-.334.216l-.474.708c-.193.304-.086.504.039.615.234.224.528.399.875.524.344.125.723.186 1.126.186.682 0 1.252-.187 1.689-.565.435-.376.756-.887.952-1.524l2.188-7.258c.05-.155.05-.284.005-.389-.037-.08-.125-.174-.327-.174z" fill="#011B33"/>\n    </svg>',light:k};return"\n    <style>\n      #inline-button-".concat(n.inlineId," {\n        position: relative;\n        text-align: center;\n        display: inline-block;\n      }\n      #inline-button-").concat(n.inlineId,"__trigger {\n        ").concat((e=n.variant||"normal",t={normal:"\n    background: linear-gradient(180deg,#44b669 0,#40ad57);\n    text-shadow: 1px 1px 1px rgba(0,0,0,.1);\n    color: #ffffff;\n  ",light:"\n    background: white;\n    text-shadow: none;\n    color: #011b33;\n  "},"".concat("\n    box-sizing: border-box;\n    display: inline-block;\n    line-height: 1;\n    white-space: nowrap;\n    margin: 0 0 10px;\n    text-align: center;\n    -webkit-appearance: none;\n    outline: none;\n    font-size: 14px;\n    font-weight: 600;\n    border-radius: 6px;\n    cursor: pointer;\n    padding: 16px 24px;\n    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.15);\n    transition: all .3s ease;\n    border: none;\n    min-width: 190px;\n  ").concat(t[e])),"\n      }\n      #inline-button-").concat(n.inlineId,"__trigger:hover {\n        box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);\n      }\n      #inline-button-").concat(n.inlineId,'__trigger:active {\n        transform: translateY(3px);\n      }\n    </style>\n    <button id="inline-button-').concat(n.inlineId,'__trigger" data-inline-id="').concat(n.inlineId,'">').concat(n.text||"Pay"," ").concat(n.currency||"NGN"," ").concat(n.amount,'</button>\n    <div id="inline-button-').concat(n.inlineId,'__wordmark">\n      ').concat(a[n.wordmarkVariant||"normal"],"\n    </div>\n  ")}(n),n.parent.parentNode.insertBefore(t,n.parent.nextSibling),e=s(t.getElementsByTagName("button"),1)[0]}return e}({inlineId:pn.id,amount:Pn.amount/100,currency:Pn.currency,id:Pn.buttonId,text:Pn.buttonText,variant:Pn.buttonVariant,wordmarkVariant:Pn.buttonWordmarkVariant,parent:g()}).addEventListener("click",(function(n){n.preventDefault(),Mn?pn.resumeTransaction(Mn.accessCode):Mn=pn.newTransaction(r(r({},Pn),{},{onSuccess:function(n){var e,t,a,i,o,r;e={type:"hidden",name:"reference",value:n.reference,parent:Vn},t=e.type,a=e.value,i=e.name,o=e.parent,(r=document.createElement("input")).type=t,r.value=a,r.name=i,o.appendChild(r),Vn.submit()}}))}))}module.exports=xn;
