/* === Content Block === */
@import './block-vars.less';

.block {
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  color: var(--f7-block-text-color);
  margin: var(--f7-block-margin-vertical) 0;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
  font-size: var(--f7-block-font-size);
  > h1,
  > h2,
  > h3,
  > h4,
  > p {
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.block-strong,
.ios .block-strong-ios,
.md .block-strong-md {
  color: var(--f7-block-strong-text-color);
  padding-top: var(--f7-block-padding-vertical);
  padding-bottom: var(--f7-block-padding-vertical);
  background-color: var(--f7-block-strong-bg-color);
}
.block-outline:not(.inset),
.ios .block-outline-ios:not(.inset):not(.inset-ios),
.md .block-outline-md:not(.inset):not(.inset-md) {
  .hairline(top, var(--f7-block-outline-border-color));
  .hairline(bottom, var(--f7-block-outline-border-color));
}
.block-outline.inset,
.ios .block-outline-ios.inset,
.ios .block-outline-ios.inset-ios,
.md .block-outline-md.inset,
.md .block-outline-md.inset-md {
  border: 1px solid var(--f7-block-outline-border-color);
}

.block-title {
  position: relative;
  overflow: hidden;
  margin: 0;
  white-space: var(--f7-block-title-white-space);
  text-overflow: ellipsis;
  text-transform: var(--f7-block-title-text-transform);
  color: var(--f7-block-title-text-color);
  font-size: var(--f7-block-title-font-size, inherit);
  font-weight: var(--f7-block-title-font-weight);
  line-height: var(--f7-block-title-line-height);
  margin-top: var(--f7-block-margin-vertical);
  margin-bottom: var(--f7-block-title-margin-bottom);
  margin-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  margin-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
  + .list,
  + .block,
  + .card,
  + .timeline,
  + .block-header {
    margin-top: 0px;
  }
}
.block-title-medium {
  font-size: var(--f7-block-title-medium-font-size);
  text-transform: var(--f7-block-title-medium-text-transform);
  color: var(--f7-block-title-medium-text-color);
  font-weight: var(--f7-block-title-medium-font-weight);
  line-height: var(--f7-block-title-medium-line-height);
}
.block-title-large {
  font-size: var(--f7-block-title-large-font-size);
  text-transform: var(--f7-block-title-large-text-transform);
  color: var(--f7-block-title-large-text-color);
  font-weight: var(--f7-block-title-large-font-weight);
  line-height: var(--f7-block-title-large-line-height);
}
.block,
.list {
  > .block-title:first-child {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }
}
// Header/Footer
.block-header {
  color: var(--f7-block-header-text-color);
  font-size: var(--f7-block-header-font-size);
  margin-bottom: var(--f7-block-header-margin);
  margin-top: var(--f7-block-margin-vertical);
  + .list,
  + .block,
  + .card,
  + .timeline {
    margin-top: var(--f7-block-header-margin);
  }
}
.block-footer {
  color: var(--f7-block-footer-text-color);
  font-size: var(--f7-block-footer-font-size);
  margin-top: var(--f7-block-footer-margin);
  margin-bottom: var(--f7-block-margin-vertical);
}
.block-footer,
.block-header {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
  ul,
  p,
  h1,
  h2,
  h3,
  h4 {
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
    &:first-child:last-child {
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}
.list,
.block,
.card,
.timeline {
  .block-header {
    margin-top: 0;
  }
  .block-footer {
    margin-bottom: 0;
  }
  + .block-footer {
    margin-top: calc(-1 * (var(--f7-block-margin-vertical) - var(--f7-block-footer-margin)));
  }
}
.block + .block-footer {
  margin-top: calc(-1 * (var(--f7-block-margin-vertical) - var(--f7-block-footer-margin)));
  margin-bottom: var(--f7-block-margin-vertical);
}
.block {
  .block-header,
  .block-footer {
    padding: 0;
  }
}
// Inset
.block.inset,
.ios .block.inset-ios,
.md .block.inset-md {
  border-radius: var(--f7-block-inset-border-radius);
  margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
  margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
  --f7-safe-area-left: 0px;
  --f7-safe-area-right: 0px;
}
.block-strong.inset,
.ios .block-strong-ios.inset,
.md .block-strong-md.inset,
.ios .block-strong.inset-ios,
.md .block-strong.inset-md,
.ios .block-strong-ios.inset-ios,
.md .block-strong-md.inset-md {
  .hairline-remove-top-bottom();
}

each(@breakpoints, {
  @media (min-width:@value) {
    .block.@{key}-inset,
    .ios .block.@{key}-inset-ios,
    .md .block.@{key}-inset-md {
      .hairline-remove-top-bottom();
      border-radius: var(--f7-block-inset-border-radius);
      margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
      margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
      --f7-safe-area-left: 0px;
      --f7-safe-area-right: 0px;
    }
    .block.@{key}-inset.block-outline,
    .ios .block.@{key}-inset-ios.block-outline,
    .ios .block.@{key}-inset-ios.block-outline-ios,
    .md .block.@{key}-inset-md.block-outline,
    .md .block.@{key}-inset-md.block-outline-md {
        border: 1px solid var(--f7-block-outline-border-color);
    }
  }
});

.if-ios-theme({
  @import './block-ios.less';
});
.if-md-theme({
  @import './block-md.less';
});
