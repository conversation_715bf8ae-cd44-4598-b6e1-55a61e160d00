<template>
  <f7-page name="payment">
    <f7-navbar title="Course Payment" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Payment Header -->
      <div class="payment-header">
        <div class="container">
          <h1>Complete Your Registration</h1>
          <p>Secure payment with Mobile Money and Card support</p>
        </div>
      </div>

      <!-- Course Details -->
      <div class="course-details-section" v-if="selectedCourse">
        <div class="container">
          <f7-card>
            <f7-card-header>Course Details</f7-card-header>
            <f7-card-content>
              <div class="course-info">
                <h3>{{ selectedCourse.title }}</h3>
                <p>{{ selectedCourse.description }}</p>
                <div class="course-meta">
                  <span class="duration">Duration: {{ selectedCourse.duration }}</span>
                  <span class="category">Category: {{ selectedCourse.category }}</span>
                </div>
              </div>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- Payment Summary -->
      <div class="payment-summary-section">
        <div class="container">
          <f7-card>
            <f7-card-header>Payment Summary</f7-card-header>
            <f7-card-content>
              <div class="payment-breakdown">
                <div class="line-item">
                  <span>Course Fee</span>
                  <span>{{ getFormattedCoursePrice(courseCategory) }}</span>
                </div>
                <div class="line-item">
                  <span>Processing Fee</span>
                  <span>Free</span>
                </div>
                <div class="line-item total">
                  <span>Total Amount</span>
                  <span>{{ getFormattedCoursePrice(courseCategory) }}</span>
                </div>
              </div>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- Payment Methods -->
      <div class="payment-methods-section">
        <div class="container">
          <h2>Choose Payment Method</h2>
          
          <!-- Mobile Money -->
          <f7-card class="payment-method-card">
            <f7-card-header>
              <f7-icon ios="f7:device_phone_portrait" md="material:phone_android" size="24" color="green"></f7-icon>
              Mobile Money
            </f7-card-header>
            <f7-card-content>
              <p>Pay with MTN Mobile Money, Vodafone Cash, or AirtelTigo Money</p>
              <div class="momo-providers">
                <div v-for="provider in getMobileMoneyProviders()" :key="provider.code" class="provider-item">
                  <span>{{ provider.name }}</span>
                </div>
              </div>
            </f7-card-content>
            <f7-card-footer>
              <f7-button fill color="green" @click="initiatePayment" :loading="loading">
                Pay with Mobile Money
              </f7-button>
            </f7-card-footer>
          </f7-card>

          <!-- Card Payment -->
          <f7-card class="payment-method-card">
            <f7-card-header>
              <f7-icon ios="f7:creditcard" md="material:credit_card" size="24" color="blue"></f7-icon>
              Card Payment
            </f7-card-header>
            <f7-card-content>
              <p>Pay securely with your Visa, Mastercard, or Verve card</p>
            </f7-card-content>
            <f7-card-footer>
              <f7-button fill color="blue" @click="initiatePayment" :loading="loading">
                Pay with Card
              </f7-button>
            </f7-card-footer>
          </f7-card>

          <!-- Bank Transfer -->
          <f7-card class="payment-method-card">
            <f7-card-header>
              <f7-icon ios="f7:building_2" md="material:account_balance" size="24" color="orange"></f7-icon>
              Bank Transfer
            </f7-card-header>
            <f7-card-content>
              <p>Transfer directly from your bank account</p>
            </f7-card-content>
            <f7-card-footer>
              <f7-button fill color="orange" @click="initiatePayment" :loading="loading">
                Pay via Bank Transfer
              </f7-button>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="security-section">
        <div class="container">
          <f7-card>
            <f7-card-content>
              <div class="security-notice">
                <f7-icon ios="f7:lock_shield" md="material:security" size="30" color="green"></f7-icon>
                <div class="security-text">
                  <h4>Secure Payment</h4>
                  <p>Your payment is secured with 256-bit SSL encryption. We never store your payment details.</p>
                </div>
              </div>
            </f7-card-content>
          </f7-card>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="error-section">
        <div class="container">
          <f7-card>
            <f7-card-content>
              <div class="error-message">
                <f7-icon ios="f7:exclamationmark_triangle" md="material:error" size="24" color="red"></f7-icon>
                <span>{{ error }}</span>
              </div>
            </f7-card-content>
            <f7-card-footer>
              <f7-button color="red" @click="clearError">Dismiss</f7-button>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { f7 } from 'framework7-vue'
import { usePayment } from '@/composables/usePayment'
import { useAuth } from '@/composables/useAuth'
import { useCourses } from '@/composables/useCourses'

const { 
  loading, 
  error, 
  initiateCoursePayment, 
  getFormattedCoursePrice, 
  getMobileMoneyProviders,
  clearError 
} = usePayment()

const { isAuthenticated } = useAuth()
const { getCourse } = useCourses()

const selectedCourse = ref<any>(null)
const courseId = ref('')
const courseCategory = ref('')

onMounted(async () => {
  // Check if user is authenticated
  if (!isAuthenticated.value) {
    f7.dialog.alert('Please login to continue with payment', 'Authentication Required', () => {
      f7.views.main.router.navigate('/login/')
    })
    return
  }

  // Get course details from route params
  const route = f7.views.main.router.currentRoute
  courseId.value = route.params?.courseId || ''
  courseCategory.value = route.params?.category || 'default'

  if (courseId.value) {
    try {
      selectedCourse.value = await getCourse(courseId.value)
    } catch (err) {
      console.error('Failed to load course details:', err)
    }
  }
})

const initiatePayment = async () => {
  if (!courseId.value) {
    f7.dialog.alert('Course information is missing', 'Error')
    return
  }

  try {
    const result = await initiateCoursePayment(courseId.value, courseCategory.value)
    
    if (result.success) {
      f7.dialog.alert(
        'Payment successful! You have been enrolled in the course.',
        'Payment Successful',
        () => {
          f7.views.main.router.navigate('/courses/')
        }
      )
    }
  } catch (err: any) {
    console.error('Payment failed:', err)
    f7.dialog.alert(err.message || 'Payment failed. Please try again.', 'Payment Failed')
  }
}
</script>

<style scoped>
.payment-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.payment-header h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.course-details-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.course-info h3 {
  color: #1e40af;
  margin-bottom: 1rem;
}

.course-meta {
  display: flex;
  gap: 20px;
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.payment-summary-section {
  padding: 30px 20px;
}

.payment-breakdown {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.line-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.line-item.total {
  font-weight: bold;
  font-size: 1.1rem;
  color: #1e40af;
  border-bottom: 2px solid #1e40af;
}

.payment-methods-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.payment-methods-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 2rem;
}

.payment-method-card {
  margin-bottom: 20px;
}

.payment-method-card .card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
}

.momo-providers {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.provider-item {
  padding: 8px 12px;
  background: #e8f5e8;
  border-radius: 5px;
  font-size: 0.9rem;
  color: #059669;
}

.security-section {
  padding: 30px 20px;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 15px;
}

.security-text h4 {
  margin: 0 0 5px 0;
  color: #059669;
}

.security-text p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.error-section {
  padding: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #dc3545;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
