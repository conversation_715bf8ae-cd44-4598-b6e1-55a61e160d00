import Framework7, { Framework7Plugin } from 'framework7/types';
import { Store } from 'framework7/types';

import f7AccordionContent from './components/accordion-content.js';
import f7AccordionItem from './components/accordion-item.js';
import f7AccordionToggle from './components/accordion-toggle.js';
import f7Accordion from './components/accordion.js';
import f7ActionsButton from './components/actions-button.js';
import f7ActionsGroup from './components/actions-group.js';
import f7ActionsLabel from './components/actions-label.js';
import f7Actions from './components/actions.js';
import f7App from './components/app.js';
import f7AreaChart from './components/area-chart.js';
import f7Badge from './components/badge.js';
import f7BlockFooter from './components/block-footer.js';
import f7BlockHeader from './components/block-header.js';
import f7BlockTitle from './components/block-title.js';
import f7Block from './components/block.js';
import f7BreadcrumbsCollapsed from './components/breadcrumbs-collapsed.js';
import f7BreadcrumbsItem from './components/breadcrumbs-item.js';
import f7BreadcrumbsSeparator from './components/breadcrumbs-separator.js';
import f7Breadcrumbs from './components/breadcrumbs.js';
import f7Button from './components/button.js';
import f7CardContent from './components/card-content.js';
import f7CardFooter from './components/card-footer.js';
import f7CardHeader from './components/card-header.js';
import f7Card from './components/card.js';
import f7Checkbox from './components/checkbox.js';
import f7Chip from './components/chip.js';
import f7FabBackdrop from './components/fab-backdrop.js';
import f7FabButton from './components/fab-button.js';
import f7FabButtons from './components/fab-buttons.js';
import f7Fab from './components/fab.js';
import f7Gauge from './components/gauge.js';
import f7Icon from './components/icon.js';
import f7Input from './components/input.js';
import f7Link from './components/link.js';
import f7ListButton from './components/list-button.js';
import f7ListGroup from './components/list-group.js';
import f7ListIndex from './components/list-index.js';
import f7ListInput from './components/list-input.js';
import f7ListItem from './components/list-item.js';
import f7List from './components/list.js';
import f7LoginScreenTitle from './components/login-screen-title.js';
import f7LoginScreen from './components/login-screen.js';
import f7Message from './components/message.js';
import f7MessagebarAttachment from './components/messagebar-attachment.js';
import f7MessagebarAttachments from './components/messagebar-attachments.js';
import f7MessagebarSheetImage from './components/messagebar-sheet-image.js';
import f7MessagebarSheetItem from './components/messagebar-sheet-item.js';
import f7MessagebarSheet from './components/messagebar-sheet.js';
import f7Messagebar from './components/messagebar.js';
import f7MessagesTitle from './components/messages-title.js';
import f7Messages from './components/messages.js';
import f7NavLeft from './components/nav-left.js';
import f7NavRight from './components/nav-right.js';
import f7NavTitleLarge from './components/nav-title-large.js';
import f7NavTitle from './components/nav-title.js';
import f7Navbar from './components/navbar.js';
import f7PageContent from './components/page-content.js';
import f7Page from './components/page.js';
import f7Panel from './components/panel.js';
import f7PhotoBrowser from './components/photo-browser.js';
import f7PieChart from './components/pie-chart.js';
import f7Popover from './components/popover.js';
import f7Popup from './components/popup.js';
import f7Preloader from './components/preloader.js';
import f7Progressbar from './components/progressbar.js';
import f7Radio from './components/radio.js';
import f7Range from './components/range.js';
import f7RoutableModals from './components/routable-modals.js';
import f7Searchbar from './components/searchbar.js';
import f7Segmented from './components/segmented.js';
import f7Sheet from './components/sheet.js';
import f7SkeletonAvatar from './components/skeleton-avatar.js';
import f7SkeletonBlock from './components/skeleton-block.js';
import f7SkeletonImage from './components/skeleton-image.js';
import f7SkeletonText from './components/skeleton-text.js';
import f7Stepper from './components/stepper.js';
import f7Subnavbar from './components/subnavbar.js';
import f7SwipeoutActions from './components/swipeout-actions.js';
import f7SwipeoutButton from './components/swipeout-button.js';
import f7Tab from './components/tab.js';
import f7Tabs from './components/tabs.js';
import f7TextEditor from './components/text-editor.js';
import f7Toggle from './components/toggle.js';
import f7Toolbar from './components/toolbar.js';
import f7TreeviewItem from './components/treeview-item.js';
import f7Treeview from './components/treeview.js';
import f7UseIcon from './components/use-icon.js';
import f7View from './components/view.js';
import f7Views from './components/views.js';

export interface Framework7Theme {
  ios: boolean;
  md: boolean;
}

/** Object with boolean properties with information about currently used theme (`ios` and `md`) */
declare const theme: Framework7Theme;

/** Main Framework7's initialized instance. It allows you to use any of Framework7 APIs */
declare const f7: Framework7;

/** Callback function that will be executed when Framework7 fully intialized. Useful to use in components when you need to access Framework7 API and to be sure it is ready. So it is safe to put all Framework7 related logic into this callback. As an argument it receives initialized Framework7 instance */
declare const f7ready: (callback: (f7: Framework7) => void) => void;

declare const Framework7Vue: Framework7Plugin;

interface useStore {
  (store: Store, getter: string): any;
  (getter: string): any;
}

declare const useStore: useStore;

export { f7AccordionContent, f7AccordionItem, f7AccordionToggle, f7Accordion, f7ActionsButton, f7ActionsGroup, f7ActionsLabel, f7Actions, f7App, f7AreaChart, f7Badge, f7BlockFooter, f7BlockHeader, f7BlockTitle, f7Block, f7BreadcrumbsCollapsed, f7BreadcrumbsItem, f7BreadcrumbsSeparator, f7Breadcrumbs, f7Button, f7CardContent, f7CardFooter, f7CardHeader, f7Card, f7Checkbox, f7Chip, f7FabBackdrop, f7FabButton, f7FabButtons, f7Fab, f7Gauge, f7Icon, f7Input, f7Link, f7ListButton, f7ListGroup, f7ListIndex, f7ListInput, f7ListItem, f7List, f7LoginScreenTitle, f7LoginScreen, f7Message, f7MessagebarAttachment, f7MessagebarAttachments, f7MessagebarSheetImage, f7MessagebarSheetItem, f7MessagebarSheet, f7Messagebar, f7MessagesTitle, f7Messages, f7NavLeft, f7NavRight, f7NavTitleLarge, f7NavTitle, f7Navbar, f7PageContent, f7Page, f7Panel, f7PhotoBrowser, f7PieChart, f7Popover, f7Popup, f7Preloader, f7Progressbar, f7Radio, f7Range, f7RoutableModals, f7Searchbar, f7Segmented, f7Sheet, f7SkeletonAvatar, f7SkeletonBlock, f7SkeletonImage, f7SkeletonText, f7Stepper, f7Subnavbar, f7SwipeoutActions, f7SwipeoutButton, f7Tab, f7Tabs, f7TextEditor, f7Toggle, f7Toolbar, f7TreeviewItem, f7Treeview, f7UseIcon, f7View, f7Views }
export { f7, f7ready, theme, useStore };
export default Framework7Vue;
