.ios-vars({
  --f7-toggle-width: 52px;
  --f7-toggle-height: 32px;
  /*
  --f7-toggle-active-bg-color: var(--f7-theme-color);
  */
  --f7-toggle-inactive-knob-bg-color: #fff;
  --f7-toggle-active-knob-bg-color: #fff;

  .light-vars({
    --f7-toggle-inactive-border-color: #e5e5e5;
    --f7-toggle-inactive-bg-color: #fff;
  });
  .dark-vars({
    --f7-toggle-inactive-border-color: #555;
    --f7-toggle-inactive-bg-color: #555;
  });
});
.md-vars({
  --f7-toggle-width: 52px;
  --f7-toggle-height: 32px;
});
.md-color-vars({
  --f7-toggle-inactive-bg-color: var(--f7-md-surface-variant);
  --f7-toggle-active-bg-color: var(--f7-theme-color);
  --f7-toggle-inactive-knob-bg-color: var(--f7-md-outline);
  --f7-toggle-active-knob-bg-color: var(--f7-md-on-primary);
  --f7-toggle-inactive-border-color: var(--f7-md-outline);
  --f7-toggle-active-border-color: var(--f7-theme-color);
});
