import { ref, onMounted, onUnmounted } from 'vue'
import { htmxService } from '@/services/htmx'

export function useHTMX() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Load content dynamically
  const loadContent = async (url: string, target: string, method = 'GET') => {
    loading.value = true
    error.value = null

    try {
      await htmxService.loadContent(url, target, method)
    } catch (err: any) {
      error.value = err.message || 'Failed to load content'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Submit form with HTMX
  const submitForm = async (formElement: HTMLFormElement, target?: string) => {
    loading.value = true
    error.value = null

    try {
      await htmxService.submitForm(formElement, target)
    } catch (err: any) {
      error.value = err.message || 'Failed to submit form'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Check if HTMX is loaded
  const isHTMXReady = () => {
    return htmxService.isHTMXLoaded()
  }

  const clearError = () => {
    error.value = null
  }

  return {
    loading,
    error,
    loadContent,
    submitForm,
    isHTMXReady,
    clearError
  }
}

// Composable for HTMX event handling
export function useHTMXEvents() {
  const eventListeners = new Map<string, EventListener>()

  const addEventListener = (eventName: string, handler: EventListener, element?: HTMLElement) => {
    const target = element || document.body
    target.addEventListener(eventName, handler)
    eventListeners.set(eventName, handler)
  }

  const removeEventListener = (eventName: string, element?: HTMLElement) => {
    const target = element || document.body
    const handler = eventListeners.get(eventName)
    if (handler) {
      target.removeEventListener(eventName, handler)
      eventListeners.delete(eventName)
    }
  }

  const removeAllEventListeners = (element?: HTMLElement) => {
    const target = element || document.body
    eventListeners.forEach((handler, eventName) => {
      target.removeEventListener(eventName, handler)
    })
    eventListeners.clear()
  }

  // Common HTMX event handlers
  const onBeforeRequest = (handler: (event: CustomEvent) => void, element?: HTMLElement) => {
    addEventListener('htmx:beforeRequest', handler as EventListener, element)
  }

  const onAfterRequest = (handler: (event: CustomEvent) => void, element?: HTMLElement) => {
    addEventListener('htmx:afterRequest', handler as EventListener, element)
  }

  const onBeforeSwap = (handler: (event: CustomEvent) => void, element?: HTMLElement) => {
    addEventListener('htmx:beforeSwap', handler as EventListener, element)
  }

  const onAfterSwap = (handler: (event: CustomEvent) => void, element?: HTMLElement) => {
    addEventListener('htmx:afterSwap', handler as EventListener, element)
  }

  const onResponseError = (handler: (event: CustomEvent) => void, element?: HTMLElement) => {
    addEventListener('htmx:responseError', handler as EventListener, element)
  }

  onUnmounted(() => {
    removeAllEventListeners()
  })

  return {
    addEventListener,
    removeEventListener,
    removeAllEventListeners,
    onBeforeRequest,
    onAfterRequest,
    onBeforeSwap,
    onAfterSwap,
    onResponseError
  }
}

// Composable for dynamic content loading with HTMX
export function useHTMXContent(initialUrl?: string) {
  const content = ref<string>('')
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadContent = async (url: string, target?: string) => {
    loading.value = true
    error.value = null

    try {
      if (target) {
        await htmxService.loadContent(url, target)
      } else {
        // If no target specified, we'll fetch the content manually
        const response = await fetch(url)
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        content.value = await response.text()
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to load content'
      throw err
    } finally {
      loading.value = false
    }
  }

  const refreshContent = async (url?: string) => {
    if (url || initialUrl) {
      await loadContent(url || initialUrl!)
    }
  }

  const clearContent = () => {
    content.value = ''
    error.value = null
  }

  onMounted(() => {
    if (initialUrl) {
      loadContent(initialUrl)
    }
  })

  return {
    content,
    loading,
    error,
    loadContent,
    refreshContent,
    clearContent
  }
}

// Utility function to add HTMX attributes to elements
export function addHTMXAttributes(element: HTMLElement, attributes: Record<string, string>) {
  Object.entries(attributes).forEach(([key, value]) => {
    if (key.startsWith('hx-')) {
      element.setAttribute(key, value)
    } else {
      element.setAttribute(`hx-${key}`, value)
    }
  })
}

// Utility function to create HTMX-enabled forms
export function createHTMXForm(config: {
  action: string
  method?: string
  target?: string
  swap?: string
  trigger?: string
  indicator?: string
}) {
  const form = document.createElement('form')
  
  form.setAttribute('hx-post', config.action)
  
  if (config.method && config.method !== 'POST') {
    form.setAttribute('hx-' + config.method.toLowerCase(), config.action)
  }
  
  if (config.target) {
    form.setAttribute('hx-target', config.target)
  }
  
  if (config.swap) {
    form.setAttribute('hx-swap', config.swap)
  }
  
  if (config.trigger) {
    form.setAttribute('hx-trigger', config.trigger)
  }
  
  if (config.indicator) {
    form.setAttribute('hx-indicator', config.indicator)
  }

  return form
}
