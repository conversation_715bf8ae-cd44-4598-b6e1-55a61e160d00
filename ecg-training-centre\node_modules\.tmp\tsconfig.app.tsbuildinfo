{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/appfooter.vue", "../../src/components/helloworld.vue", "../../src/components/newslettersubscription.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/composables/useauth.ts", "../../src/composables/usecourses.ts", "../../src/composables/usehtmx.ts", "../../src/composables/usenewsletter.ts", "../../src/composables/usepayment.ts", "../../src/router/index.ts", "../../src/services/htmx.ts", "../../src/services/payment.ts", "../../src/services/pocketbase.ts", "../../src/stores/counter.ts", "../../src/views/aboutpage.vue", "../../src/views/aboutview.vue", "../../src/views/contactpage.vue", "../../src/views/coursespage.vue", "../../src/views/homepage.vue", "../../src/views/homeview.vue", "../../src/views/loginpage.vue", "../../src/views/paymentpage.vue", "../../src/views/profilepage.vue", "../../src/views/registerpage.vue"], "version": "5.8.3"}