<template>
  <footer class="bg-gradient-ecg text-white mt-auto">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="space-y-4">
          <div>
            <h3 class="text-xl font-bold mb-2">ECG Training Centre</h3>
            <p class="text-blue-100 leading-relaxed">Building capacity for the power sector in West Africa</p>
          </div>

          <div class="space-y-3">
            <div class="flex items-start space-x-2">
              <svg class="w-4 h-4 mt-1 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-blue-100 text-sm">GPS: GT-049-4121, Community 10 Junction, Tema, Ghana</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              <span class="text-blue-100 text-sm">+233 302 676 756 / +233 576 870 291</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-blue-100 text-sm"><EMAIL></span>
            </div>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold mb-4 border-b border-blue-400 pb-2">Quick Links</h4>
          <ul class="space-y-2">
            <li><router-link to="/" class="footer-link">Home</router-link></li>
            <li><router-link to="/about" class="footer-link">About Us</router-link></li>
            <li><router-link to="/courses" class="footer-link">Courses</router-link></li>
            <li><router-link to="/contact" class="footer-link">Contact</router-link></li>
            <li><router-link to="/profile" class="footer-link">My Profile</router-link></li>
          </ul>
        </div>

        <!-- Course Categories -->
        <div>
          <h4 class="text-lg font-semibold mb-4 border-b border-blue-400 pb-2">Course Categories</h4>
          <ul class="space-y-2">
            <li><router-link to="/courses" class="footer-link">Power Systems Programs</router-link></li>
            <li><router-link to="/courses" class="footer-link">Renewable Energy Programs</router-link></li>
            <li><router-link to="/courses" class="footer-link">IT Proficiency Courses</router-link></li>
            <li><router-link to="/courses" class="footer-link">Contractors Training</router-link></li>
          </ul>
        </div>

        <!-- Resources -->
        <div>
          <h4 class="text-lg font-semibold mb-4 border-b border-blue-400 pb-2">Resources</h4>
          <ul class="space-y-2">
            <li><a href="#" class="footer-link">Forms & Admission</a></li>
            <li><a href="#" class="footer-link">Professional Consultancy</a></li>
            <li><a href="#" class="footer-link">Achievements</a></li>
            <li><a href="#" class="footer-link">FAQs</a></li>
            <li><a href="#" class="footer-link">Downloads</a></li>
          </ul>
        </div>
      </div>

      <!-- Social Media & Newsletter -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8 pt-8 border-t border-blue-400">
        <div>
          <h4 class="text-lg font-semibold mb-4">Follow Us</h4>
          <div class="flex space-x-4">
            <a href="https://facebook.com/ECGTrainingHub" target="_blank" class="social-link">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            <a href="https://twitter.com/ECGTrainingHub" target="_blank" class="social-link">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
            <a href="https://linkedin.com/ECGTrainingHub" target="_blank" class="social-link">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
          </div>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Stay Updated</h4>
          <p class="text-blue-100 mb-4">Subscribe to our newsletter for the latest updates</p>
          <div class="flex flex-col sm:flex-row gap-2">
            <input
              type="email"
              placeholder="Your email"
              v-model="newsletterEmail"
              class="flex-1 px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50"
            />
            <button
              @click="subscribeNewsletter"
              :disabled="newsletterLoading"
              class="px-6 py-2 bg-white text-ecg-blue rounded-lg font-medium hover:bg-blue-50 transition-colors disabled:opacity-50"
            >
              {{ newsletterLoading ? 'Subscribing...' : 'Subscribe' }}
            </button>
          </div>
        </div>
      </div>
      </div>
    </div>

    </div>

    <!-- Copyright -->
    <div class="border-t border-blue-400 mt-8 pt-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-blue-100 text-sm">&copy; {{ currentYear }} ECG Training Centre. All rights reserved.</p>
        <div class="flex items-center space-x-4 mt-4 md:mt-0">
          <a href="#" class="text-blue-200 hover:text-white text-sm transition-colors">Privacy Policy</a>
          <span class="text-blue-300">|</span>
          <a href="#" class="text-blue-200 hover:text-white text-sm transition-colors">Terms of Service</a>
          <span class="text-blue-300">|</span>
          <a href="#" class="text-blue-200 hover:text-white text-sm transition-colors">Cookie Policy</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useNewsletter } from '@/composables/useNewsletter'

const { subscribe, loading: newsletterLoading } = useNewsletter()

const newsletterEmail = ref('')
const currentYear = computed(() => new Date().getFullYear())

const subscribeNewsletter = async () => {
  if (!newsletterEmail.value) {
    alert('Please enter your email address')
    return
  }

  try {
    const success = await subscribe(newsletterEmail.value)
    if (success) {
      alert('Successfully subscribed to newsletter!')
      newsletterEmail.value = ''
    }
  } catch (error: any) {
    alert(error.message || 'Failed to subscribe')
  }
}
</script>

<style scoped>
.footer-link {
  @apply text-blue-200 hover:text-white transition-colors text-sm;
}

.social-link {
  @apply flex items-center justify-center w-10 h-10 bg-white/10 rounded-full text-white hover:bg-white/20 transition-all duration-300 hover:-translate-y-1;
}
</style>
