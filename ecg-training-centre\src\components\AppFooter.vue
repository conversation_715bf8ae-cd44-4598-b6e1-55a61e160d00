<template>
  <footer class="app-footer">
    <div class="footer-content">
      <div class="container">
        <div class="footer-grid">
          <!-- Company Info -->
          <div class="footer-section">
            <div class="footer-logo">
              <h3>ECG Training Centre</h3>
              <p>Building capacity for the power sector in West Africa</p>
            </div>
            
            <div class="contact-info">
              <div class="contact-item">
                <f7-icon ios="f7:location" md="material:location_on" size="16" color="white"></f7-icon>
                <span>GPS: GT-049-4121, Community 10 Junction, Tema, Ghana</span>
              </div>
              <div class="contact-item">
                <f7-icon ios="f7:phone" md="material:phone" size="16" color="white"></f7-icon>
                <span>+*********** 756 / +*********** 291</span>
              </div>
              <div class="contact-item">
                <f7-icon ios="f7:envelope" md="material:email" size="16" color="white"></f7-icon>
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          <!-- Quick Links -->
          <div class="footer-section">
            <h4>Quick Links</h4>
            <ul class="footer-links">
              <li><f7-link href="/" color="white">Home</f7-link></li>
              <li><f7-link href="/about/" color="white">About Us</f7-link></li>
              <li><f7-link href="/courses/" color="white">Courses</f7-link></li>
              <li><f7-link href="/contact/" color="white">Contact</f7-link></li>
              <li><f7-link href="/profile/" color="white">My Profile</f7-link></li>
            </ul>
          </div>

          <!-- Course Categories -->
          <div class="footer-section">
            <h4>Course Categories</h4>
            <ul class="footer-links">
              <li><f7-link href="/courses/" color="white">Power Systems Programs</f7-link></li>
              <li><f7-link href="/courses/" color="white">Renewable Energy Programs</f7-link></li>
              <li><f7-link href="/courses/" color="white">IT Proficiency Courses</f7-link></li>
              <li><f7-link href="/courses/" color="white">Contractors Training</f7-link></li>
            </ul>
          </div>

          <!-- Resources -->
          <div class="footer-section">
            <h4>Resources</h4>
            <ul class="footer-links">
              <li><f7-link href="#" color="white">Forms & Admission</f7-link></li>
              <li><f7-link href="#" color="white">Professional Consultancy</f7-link></li>
              <li><f7-link href="#" color="white">Achievements</f7-link></li>
              <li><f7-link href="#" color="white">FAQs</f7-link></li>
              <li><f7-link href="#" color="white">Downloads</f7-link></li>
            </ul>
          </div>
        </div>

        <!-- Social Media & Newsletter -->
        <div class="footer-bottom">
          <div class="social-section">
            <h4>Follow Us</h4>
            <div class="social-links">
              <f7-link external href="https://facebook.com/ECGTrainingHub" class="social-link">
                <f7-icon ios="f7:logo_facebook" md="material:facebook" size="24"></f7-icon>
              </f7-link>
              <f7-link external href="https://twitter.com/ECGTrainingHub" class="social-link">
                <f7-icon ios="f7:logo_twitter" md="material:twitter" size="24"></f7-icon>
              </f7-link>
              <f7-link external href="https://linkedin.com/ECGTrainingHub" class="social-link">
                <f7-icon ios="f7:logo_linkedin" md="material:linkedin" size="24"></f7-icon>
              </f7-link>
            </div>
          </div>

          <div class="newsletter-section">
            <h4>Stay Updated</h4>
            <p>Subscribe to our newsletter for the latest updates</p>
            <div class="newsletter-form">
              <f7-input
                type="email"
                placeholder="Your email"
                v-model:value="newsletterEmail"
                class="newsletter-input"
              ></f7-input>
              <f7-button 
                fill 
                color="blue" 
                @click="subscribeNewsletter"
                :loading="newsletterLoading"
                class="newsletter-btn"
              >
                Subscribe
              </f7-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="footer-copyright">
      <div class="container">
        <div class="copyright-content">
          <p>&copy; {{ currentYear }} ECG Training Centre. All rights reserved.</p>
          <div class="legal-links">
            <f7-link href="#" color="white">Privacy Policy</f7-link>
            <span>|</span>
            <f7-link href="#" color="white">Terms of Service</f7-link>
            <span>|</span>
            <f7-link href="#" color="white">Cookie Policy</f7-link>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { f7 } from 'framework7-vue'
import { useNewsletter } from '@/composables/useNewsletter'

const { subscribe, loading: newsletterLoading } = useNewsletter()

const newsletterEmail = ref('')
const currentYear = computed(() => new Date().getFullYear())

const subscribeNewsletter = async () => {
  if (!newsletterEmail.value) {
    f7.dialog.alert('Please enter your email address', 'Email Required')
    return
  }

  try {
    const success = await subscribe(newsletterEmail.value)
    if (success) {
      f7.toast.create({
        text: 'Successfully subscribed to newsletter!',
        position: 'center',
        closeTimeout: 3000
      }).open()
      newsletterEmail.value = ''
    }
  } catch (error: any) {
    f7.dialog.alert(error.message || 'Failed to subscribe', 'Error')
  }
}
</script>

<style scoped>
.app-footer {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  margin-top: auto;
}

.footer-content {
  padding: 60px 20px 40px;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 20px;
  color: white;
}

.footer-section h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.footer-section h4 {
  font-size: 1.2rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.footer-logo p {
  opacity: 0.9;
  margin-bottom: 20px;
  line-height: 1.6;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.9;
}

.contact-item span {
  font-size: 0.9rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  opacity: 0.9;
  transition: opacity 0.3s ease;
  text-decoration: none;
}

.footer-links a:hover {
  opacity: 1;
}

.footer-bottom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  padding-top: 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.newsletter-section p {
  opacity: 0.9;
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.newsletter-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  color: white;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-btn {
  white-space: nowrap;
}

.footer-copyright {
  background: rgba(0, 0, 0, 0.2);
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright-content p {
  margin: 0;
  opacity: 0.8;
}

.legal-links {
  display: flex;
  align-items: center;
  gap: 15px;
  opacity: 0.8;
}

.legal-links a {
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.legal-links a:hover {
  opacity: 1;
}

.legal-links span {
  opacity: 0.5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-bottom {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .copyright-content {
    flex-direction: column;
    text-align: center;
  }
  
  .legal-links {
    justify-content: center;
  }
}
</style>
