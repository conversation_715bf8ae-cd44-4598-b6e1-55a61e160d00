<template>
  <f7-page name="home">
    <!-- Top Navbar -->
    <f7-navbar title="ECG Training Centre" large transparent>
      <f7-nav-right>
        <f7-link icon-ios="f7:menu" icon-md="material:menu" panel-open="right"></f7-link>
      </f7-nav-right>
    </f7-navbar>

    <!-- Page content -->
    <div class="page-content">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="hero-content">
          <h1>ECG Training Centre</h1>
          <p>Professional Training for the Power Sector</p>
          <f7-button large fill color="blue" href="/courses/">Explore Courses</f7-button>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="stats-section">
        <div class="container">
          <div class="row">
            <div class="col-25">
              <div class="stat-item">
                <h3>1000+</h3>
                <p>Graduates</p>
              </div>
            </div>
            <div class="col-25">
              <div class="stat-item">
                <h3>50+</h3>
                <p>Courses</p>
              </div>
            </div>
            <div class="col-25">
              <div class="stat-item">
                <h3>10+</h3>
                <p>Labs</p>
              </div>
            </div>
            <div class="col-25">
              <div class="stat-item">
                <h3>500+</h3>
                <p>Students</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Featured Programs -->
      <div class="programs-section">
        <div class="container">
          <h2>Available Programs</h2>
          <div class="row">
            <div class="col-100 medium-33">
              <f7-card>
                <f7-card-header>Power Systems Programs</f7-card-header>
                <f7-card-content>
                  <p>Comprehensive training in power distribution, protection systems, and electrical safety.</p>
                </f7-card-content>
                <f7-card-footer>
                  <f7-button fill color="blue">View Courses</f7-button>
                </f7-card-footer>
              </f7-card>
            </div>
            <div class="col-100 medium-33">
              <f7-card>
                <f7-card-header>Renewable Energy Programs</f7-card-header>
                <f7-card-content>
                  <p>Training in solar, wind, and sustainable energy technologies for the future.</p>
                </f7-card-content>
                <f7-card-footer>
                  <f7-button fill color="green">View Courses</f7-button>
                </f7-card-footer>
              </f7-card>
            </div>
            <div class="col-100 medium-33">
              <f7-card>
                <f7-card-header>IT Proficiency</f7-card-header>
                <f7-card-content>
                  <p>Digital skills, data analytics, and modern technology training for professionals.</p>
                </f7-card-content>
                <f7-card-footer>
                  <f7-button fill color="orange">View Courses</f7-button>
                </f7-card-footer>
              </f7-card>
            </div>
          </div>
        </div>
      </div>

      <!-- Newsletter Section -->
      <div class="newsletter-section">
        <div class="container">
          <h2>Stay Updated</h2>
          <p>Subscribe to our newsletter for the latest courses and updates</p>
          <form hx-post="/api/newsletter" hx-target="#newsletter-result">
            <f7-list>
              <f7-list-input
                type="email"
                placeholder="Enter your email"
                name="email"
                required
              ></f7-list-input>
              <f7-list-button type="submit" color="blue">Subscribe</f7-list-button>
            </f7-list>
          </form>
          <div id="newsletter-result"></div>
        </div>
      </div>
    </div>

    <!-- Right Panel -->
    <f7-panel right cover>
      <f7-view>
        <f7-page>
          <f7-navbar title="Menu">
            <f7-nav-right>
              <f7-link icon-ios="f7:xmark" icon-md="material:close" panel-close></f7-link>
            </f7-nav-right>
          </f7-navbar>
          <f7-list>
            <f7-list-item link="/" title="Home" panel-close></f7-list-item>
            <f7-list-item link="/about/" title="About Us" panel-close></f7-list-item>
            <f7-list-item link="/courses/" title="Courses" panel-close></f7-list-item>
            <f7-list-item link="/contact/" title="Contact" panel-close></f7-list-item>
          </f7-list>
        </f7-page>
      </f7-view>
    </f7-panel>
  </f7-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  // Initialize any page-specific functionality
})
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.stats-section {
  padding: 40px 20px;
  background: #f8f9fa;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-item h3 {
  font-size: 2rem;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.programs-section {
  padding: 60px 20px;
}

.programs-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  color: #1e40af;
}

.newsletter-section {
  background: #1e40af;
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.newsletter-section h2 {
  margin-bottom: 1rem;
}

.newsletter-section p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
