<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="bg-gradient-ecg text-white py-20 lg:py-32">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in">
          <h1 class="text-4xl md:text-6xl font-bold mb-6">ECG Training Centre</h1>
          <p class="text-xl md:text-2xl mb-8 text-blue-100">Professional Training for the Power Sector</p>
          <router-link
            to="/courses"
            class="inline-block bg-white text-ecg-blue px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors transform hover:scale-105"
          >
            Explore Courses
          </router-link>
        </div>
      </div>
    </section>

    <!-- Quick Stats -->
    <section class="bg-gray-100 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div class="text-center animate-slide-up">
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <h3 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-2">1000+</h3>
              <p class="text-gray-600 font-medium">Graduates</p>
            </div>
          </div>
          <div class="text-center animate-slide-up" style="animation-delay: 0.1s">
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <h3 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-2">50+</h3>
              <p class="text-gray-600 font-medium">Courses</p>
            </div>
          </div>
          <div class="text-center animate-slide-up" style="animation-delay: 0.2s">
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <h3 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-2">10+</h3>
              <p class="text-gray-600 font-medium">Labs</p>
            </div>
          </div>
          <div class="text-center animate-slide-up" style="animation-delay: 0.3s">
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <h3 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-2">500+</h3>
              <p class="text-gray-600 font-medium">Students</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Programs -->
    <section class="py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-4">Available Programs</h2>
          <p class="text-xl text-gray-600">Professional training courses designed for the power sector</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 animate-bounce-in">
            <div class="p-8">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-ecg-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">Power Systems Programs</h3>
              <p class="text-gray-600 mb-6">Comprehensive training in power distribution, protection systems, and electrical safety.</p>
              <router-link
                to="/courses"
                class="inline-block bg-ecg-blue text-white px-6 py-3 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
              >
                View Courses
              </router-link>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 animate-bounce-in" style="animation-delay: 0.1s">
            <div class="p-8">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-ecg-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">Renewable Energy Programs</h3>
              <p class="text-gray-600 mb-6">Training in solar, wind, and sustainable energy technologies for the future.</p>
              <router-link
                to="/courses"
                class="inline-block bg-ecg-green text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                View Courses
              </router-link>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 animate-bounce-in" style="animation-delay: 0.2s">
            <div class="p-8">
              <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">IT Proficiency</h3>
              <p class="text-gray-600 mb-6">Digital skills, data analytics, and modern technology training for professionals.</p>
              <router-link
                to="/courses"
                class="inline-block bg-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-700 transition-colors"
              >
                View Courses
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <NewsletterSubscription />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import NewsletterSubscription from '@/components/NewsletterSubscription.vue'

onMounted(() => {
  console.log('ECG Training Centre - Homepage loaded successfully!')
})
</script>

<style scoped>
/* Custom animations for staggered effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-bounce-in {
  animation: fadeInUp 0.8s ease-out;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}
</style>
