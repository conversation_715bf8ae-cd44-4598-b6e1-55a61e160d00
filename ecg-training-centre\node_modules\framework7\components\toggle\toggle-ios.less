.ios {
  .toggle {
    background: var(--f7-toggle-inactive-border-color);
    input[type='checkbox']:checked + .toggle-icon {
      background: var(--f7-toggle-active-color, var(--f7-theme-color));
      &:before {
        background: var(--f7-toggle-active-bg-color, var(--f7-theme-color));
        transform: scale(0);
      }
      &:after {
        background: var(--f7-toggle-active-knob-bg-color);
        .ltr({
          transform: translateX(calc(var(--f7-toggle-width) - var(--f7-toggle-height)));
        });
        .rtl({
          transform: translateX(calc(-1 * (var(--f7-toggle-width) - var(--f7-toggle-height))));
        });
      }
    }
  }
  .toggle-icon {
    background: var(--f7-toggle-border-color);
    &:before {
      position: absolute;
      .ltr({ left: 2px; });
      .rtl({ right: 2px; });
      top: 2px;
      width: calc(var(--f7-toggle-width) - 4px);
      height: calc(var(--f7-toggle-height) - 4px);
      border-radius: var(--f7-toggle-height);
      box-sizing: border-box;
      background: var(--f7-toggle-inactive-bg-color);
      z-index: 1;
      transition-duration: 300ms;
      transform: scale(1);
    }
    &:after {
      background: var(--f7-toggle-inactive-knob-bg-color);
      height: calc(var(--f7-toggle-height) - 4px);
      width: calc(var(--f7-toggle-height) - 4px);
      top: 2px;
      .ltr({ left: 2px; });
      .rtl({ right: 2px; });
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      border-radius: calc(var(--f7-toggle-height) - 4px);
    }
  }
  .toggle-active-state {
    input[type='checkbox']:not(:checked) + .toggle-icon {
      &:before {
        transform: scale(0);
      }
    }
    input[type='checkbox'] + .toggle-icon {
      &:after {
        width: calc(var(--f7-toggle-height) + 4px);
      }
    }
    input[type='checkbox']:checked + .toggle-icon {
      &:after {
        .ltr({
          transform: translateX(calc(var(--f7-toggle-width) - var(--f7-toggle-height) - 8px));
        });
        .rtl({
          transform: translateX(calc(-1 * (var(--f7-toggle-width) - var(--f7-toggle-height) - 8px)));
        });
      }
    }
  }
}
