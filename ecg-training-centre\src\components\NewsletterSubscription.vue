<template>
  <div class="newsletter-subscription">
    <div class="newsletter-content">
      <div class="newsletter-icon">
        <f7-icon ios="f7:envelope" md="material:email" size="40" color="white"></f7-icon>
      </div>
      
      <div class="newsletter-text">
        <h2>Stay Updated</h2>
        <p>Subscribe to our newsletter for the latest courses, industry insights, and training opportunities</p>
        
        <div class="newsletter-benefits">
          <div class="benefit-item">
            <f7-icon ios="f7:checkmark" md="material:check" size="16" color="white"></f7-icon>
            <span>Course announcements</span>
          </div>
          <div class="benefit-item">
            <f7-icon ios="f7:checkmark" md="material:check" size="16" color="white"></f7-icon>
            <span>Industry news & insights</span>
          </div>
          <div class="benefit-item">
            <f7-icon ios="f7:checkmark" md="material:check" size="16" color="white"></f7-icon>
            <span>Special offers & discounts</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Subscription Form -->
    <div class="newsletter-form" v-if="!success">
      <form @submit.prevent="handleSubscribe" hx-boost="true">
        <div class="form-group">
          <f7-input
            type="email"
            placeholder="Enter your email address"
            v-model:value="email"
            :disabled="loading"
            class="newsletter-input"
            :class="{ 'error': error && !loading }"
            @input="clearError"
          ></f7-input>
          
          <f7-button
            type="submit"
            fill
            color="white"
            class="newsletter-button"
            :loading="loading"
            :disabled="!email || loading"
          >
            <span v-if="!loading">Subscribe</span>
          </f7-button>
        </div>
        
        <!-- Real-time validation -->
        <div class="validation-message" v-if="email && !loading">
          <div v-if="isValidEmail(email)" class="valid-message">
            <f7-icon ios="f7:checkmark_circle" md="material:check_circle" size="16" color="green"></f7-icon>
            <span>Valid email address</span>
          </div>
          <div v-else class="invalid-message">
            <f7-icon ios="f7:xmark_circle" md="material:cancel" size="16" color="red"></f7-icon>
            <span>Please enter a valid email address</span>
          </div>
        </div>
      </form>
      
      <!-- Error Message -->
      <div v-if="error" class="error-message">
        <f7-icon ios="f7:exclamationmark_triangle" md="material:error" size="16" color="red"></f7-icon>
        <span>{{ error }}</span>
      </div>
      
      <!-- Privacy Notice -->
      <div class="privacy-notice">
        <f7-icon ios="f7:lock" md="material:lock" size="14" color="rgba(255,255,255,0.7)"></f7-icon>
        <span>We respect your privacy. Unsubscribe at any time.</span>
      </div>
    </div>

    <!-- Success Message -->
    <div class="newsletter-success" v-if="success">
      <div class="success-icon">
        <f7-icon ios="f7:checkmark_circle_fill" md="material:check_circle" size="60" color="green"></f7-icon>
      </div>
      
      <div class="success-content">
        <h3>Thank You!</h3>
        <p>{{ subscriptionMessage }}</p>
        
        <div class="success-actions">
          <f7-button color="white" @click="resetForm">Subscribe Another Email</f7-button>
        </div>
      </div>
    </div>

    <!-- Social Media Links -->
    <div class="social-links">
      <span>Follow us on:</span>
      <div class="social-icons">
        <f7-link external href="https://facebook.com/ECGTrainingHub" class="social-link">
          <f7-icon ios="f7:logo_facebook" md="material:facebook" size="20"></f7-icon>
        </f7-link>
        <f7-link external href="https://twitter.com/ECGTrainingHub" class="social-link">
          <f7-icon ios="f7:logo_twitter" md="material:twitter" size="20"></f7-icon>
        </f7-link>
        <f7-link external href="https://linkedin.com/ECGTrainingHub" class="social-link">
          <f7-icon ios="f7:logo_linkedin" md="material:linkedin" size="20"></f7-icon>
        </f7-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useNewsletter } from '@/composables/useNewsletter'

const { 
  loading, 
  error, 
  success, 
  subscriptionMessage, 
  subscribe, 
  isValidEmail, 
  clearError,
  clearMessages 
} = useNewsletter()

const email = ref('')

const handleSubscribe = async () => {
  const result = await subscribe(email.value)
  if (result) {
    email.value = '' // Clear the form on success
  }
}

const resetForm = () => {
  clearMessages()
  email.value = ''
}
</script>

<style scoped>
.newsletter-subscription {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.newsletter-subscription::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.newsletter-content {
  position: relative;
  z-index: 1;
  max-width: 600px;
  margin: 0 auto 40px;
}

.newsletter-icon {
  margin-bottom: 20px;
}

.newsletter-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.newsletter-text p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.newsletter-benefits {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 2rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.benefit-item span {
  font-size: 0.95rem;
}

.newsletter-form {
  position: relative;
  z-index: 1;
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.newsletter-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 15px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.newsletter-input:focus {
  background: white;
  border-color: #059669;
  outline: none;
}

.newsletter-input.error {
  border-color: #dc3545;
  background: rgba(255, 255, 255, 0.95);
}

.newsletter-button {
  padding: 15px 30px;
  border-radius: 8px;
  font-weight: bold;
  color: #1e40af !important;
  background: white !important;
  transition: all 0.3s ease;
}

.newsletter-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.validation-message {
  text-align: left;
  margin-bottom: 10px;
}

.valid-message, .invalid-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.valid-message {
  color: #28a745;
}

.invalid-message {
  color: #dc3545;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
  color: #dc3545;
}

.privacy-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.85rem;
  opacity: 0.8;
}

.newsletter-success {
  position: relative;
  z-index: 1;
  max-width: 500px;
  margin: 0 auto;
}

.success-icon {
  margin-bottom: 20px;
}

.success-content h3 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.success-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.success-actions {
  margin-top: 20px;
}

.social-links {
  position: relative;
  z-index: 1;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.social-links span {
  display: block;
  margin-bottom: 15px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .newsletter-text h2 {
    font-size: 2rem;
  }
  
  .form-group {
    flex-direction: column;
  }
  
  .newsletter-benefits {
    align-items: flex-start;
  }
  
  .benefit-item {
    justify-content: flex-start;
  }
}
</style>
