.ios {
  .preloader {
    color: var(--f7-preloader-color);
  }
  .preloader-inner {
    animation: ios-preloader-spin 1s steps(8, end) infinite;
  }
  .preloader-inner-line {
    display: block;
    width: 11.6%;
    height: 33.33%;
    border-radius: 100px;
    background: currentColor;
    position: absolute;
    left: 50%;
    top: 50%;
    transform-origin: center 150%;
  }
  .preloader-multi-color .preloader-inner-line,
  .preloader.color-multi .preloader-inner-line {
    animation: ios-preloader-multicolor 3s linear infinite;
  }
  @indexes: 0, 1, 2, 3, 4, 5, 6, 7;

  each(@indexes, {
    @index_plus_one: @value + 1;
    @degrees: (360deg / 8) * @value;
    @opacity: (0.27 + (0.85 - 0.27) * @value / 7);
    .preloader-inner-line:nth-child(@{index_plus_one}) {
      transform: translate(-50%, -150%) rotate(@degrees);
      opacity: @opacity;
    }
  });
}
@keyframes ios-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes ios-preloader-multicolor {
  0%,
  100% {
    background-color: #2196f3;
  }
  25% {
    background-color: #ff3b30;
  }
  50% {
    background-color: #4cd964;
  }
  75% {
    background-color: #ff9500;
  }
}
