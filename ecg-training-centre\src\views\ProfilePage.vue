<template>
  <div class="profile-page min-h-screen bg-gray-50">
    <!-- Profile Header -->
    <section class="bg-gradient-ecg text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h1 class="text-4xl font-bold mb-2">{{ user?.name || 'User' }}</h1>
          <p class="text-blue-100 text-lg">{{ user?.email }}</p>
          <button
            @click="handleLogout"
            class="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"
          >
            Logout
          </button>
        </div>
      </div>
    </section>

    <!-- Profile Information -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-xl shadow-lg p-8">
          <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-ecg-blue">Profile Information</h2>
            <button
              @click="toggleEditMode"
              class="bg-ecg-blue text-white px-4 py-2 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
            >
              {{ editMode ? 'Cancel' : 'Edit Profile' }}
            </button>
          </div>

          <form @submit.prevent="saveProfile" class="space-y-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
              <input
                id="name"
                type="text"
                v-model="profileData.name"
                :disabled="!editMode"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
              />
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                id="email"
                type="email"
                v-model="profileData.email"
                disabled
                class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
              />
              <p class="text-sm text-gray-500 mt-1">Email cannot be changed</p>
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input
                id="phone"
                type="tel"
                v-model="profileData.phone"
                :disabled="!editMode"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ecg-blue focus:border-ecg-blue transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
              />
            </div>
              </f7-list>
            </f7-card-content>
            <f7-card-footer>
              <f7-button 
                v-if="!editMode" 
                fill 
                color="blue" 
                @click="enableEditMode"
              >
                Edit Profile
              </f7-button>
              <div v-else class="edit-actions">
                <f7-button 
                  fill 
                  color="green" 
                  @click="saveProfile"
                  :loading="loading"
                >
                  Save Changes
                </f7-button>
                <f7-button 
                  color="gray" 
                  @click="cancelEdit"
                >
                  Cancel
                </f7-button>
              </div>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>

      <!-- My Enrollments -->
      <div class="enrollments-section">
        <div class="container">
          <h2>My Enrollments</h2>
          
          <div v-if="enrollments.length === 0" class="no-enrollments">
            <f7-card>
              <f7-card-content>
                <div class="empty-state">
                  <f7-icon ios="f7:book" md="material:school" size="60" color="gray"></f7-icon>
                  <h3>No Enrollments Yet</h3>
                  <p>You haven't enrolled in any courses yet. Browse our courses to get started!</p>
                  <f7-button fill color="blue" href="/courses/">Browse Courses</f7-button>
                </div>
              </f7-card-content>
            </f7-card>
          </div>

          <div v-else class="enrollments-list">
            <f7-card v-for="enrollment in enrollments" :key="enrollment.id" class="enrollment-card">
              <f7-card-content>
                <div class="enrollment-info">
                  <h3>{{ enrollment.expand?.course?.title || 'Course' }}</h3>
                  <p>{{ enrollment.expand?.course?.description }}</p>
                  
                  <div class="enrollment-meta">
                    <div class="status-badge" :class="enrollment.status">
                      {{ formatStatus(enrollment.status) }}
                    </div>
                    <div class="payment-status" :class="enrollment.payment_status">
                      Payment: {{ formatPaymentStatus(enrollment.payment_status) }}
                    </div>
                  </div>
                  
                  <div class="enrollment-dates">
                    <small>Enrolled: {{ formatDate(enrollment.enrolled_date) }}</small>
                    <small v-if="enrollment.completion_date">
                      Completed: {{ formatDate(enrollment.completion_date) }}
                    </small>
                  </div>
                </div>
              </f7-card-content>
              <f7-card-footer>
                <f7-button 
                  v-if="enrollment.status === 'enrolled'" 
                  fill 
                  color="blue"
                  @click="viewCourse(enrollment.course)"
                >
                  Continue Learning
                </f7-button>
                <f7-button 
                  v-else-if="enrollment.payment_status === 'pending'" 
                  fill 
                  color="orange"
                  @click="completePayment(enrollment)"
                >
                  Complete Payment
                </f7-button>
              </f7-card-footer>
            </f7-card>
          </div>
        </div>
      </div>

      <!-- Account Settings -->
      <div class="settings-section">
        <div class="container">
          <h2>Account Settings</h2>
          
          <f7-list>
            <f7-list-item 
              link 
              title="Change Password" 
              @click="changePassword"
            >
              <f7-icon ios="f7:lock" md="material:lock" slot="media" color="blue"></f7-icon>
            </f7-list-item>
            
            <f7-list-item 
              link 
              title="Notification Preferences" 
              @click="notificationSettings"
            >
              <f7-icon ios="f7:bell" md="material:notifications" slot="media" color="green"></f7-icon>
            </f7-list-item>
            
            <f7-list-item 
              link 
              title="Download Certificates" 
              @click="downloadCertificates"
            >
              <f7-icon ios="f7:doc" md="material:file_download" slot="media" color="orange"></f7-icon>
            </f7-list-item>
          </f7-list>
        </div>
      </div>

      <!-- Danger Zone -->
      <div class="danger-section">
        <div class="container">
          <f7-card>
            <f7-card-header>Danger Zone</f7-card-header>
            <f7-card-content>
              <p>Once you delete your account, there is no going back. Please be certain.</p>
            </f7-card-content>
            <f7-card-footer>
              <f7-button color="red" @click="deleteAccount">Delete Account</f7-button>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { f7 } from 'framework7-vue'
import { useAuth } from '@/composables/useAuth'
import { enrollmentService } from '@/services/pocketbase'

const { user, logout } = useAuth()

const loading = ref(false)
const editMode = ref(false)
const enrollments = ref<any[]>([])

const profileData = ref({
  name: '',
  email: '',
  phone: ''
})

const originalProfileData = ref({
  name: '',
  email: '',
  phone: ''
})

onMounted(async () => {
  if (user.value) {
    profileData.value = {
      name: user.value.name || '',
      email: user.value.email || '',
      phone: user.value.phone || ''
    }
    originalProfileData.value = { ...profileData.value }
    
    // Load user enrollments
    await loadEnrollments()
  }
})

const loadEnrollments = async () => {
  try {
    const userEnrollments = await enrollmentService.getUserEnrollments()
    enrollments.value = userEnrollments
  } catch (error) {
    console.error('Failed to load enrollments:', error)
  }
}

const enableEditMode = () => {
  editMode.value = true
  originalProfileData.value = { ...profileData.value }
}

const cancelEdit = () => {
  editMode.value = false
  profileData.value = { ...originalProfileData.value }
}

const saveProfile = async () => {
  loading.value = true
  try {
    // Here you would update the user profile via PocketBase
    // await pb.collection('users').update(user.value.id, profileData.value)
    
    f7.dialog.alert('Profile updated successfully!', 'Success')
    editMode.value = false
  } catch (error: any) {
    f7.dialog.alert(error.message || 'Failed to update profile', 'Error')
  } finally {
    loading.value = false
  }
}

const handleLogout = () => {
  f7.dialog.confirm(
    'Are you sure you want to logout?',
    'Logout',
    () => {
      logout()
      f7.views.main.router.navigate('/')
    }
  )
}

const formatStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const formatPaymentStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const viewCourse = (courseId: string) => {
  f7.views.main.router.navigate(`/course/${courseId}`)
}

const completePayment = (enrollment: any) => {
  const course = enrollment.expand?.course
  if (course) {
    f7.views.main.router.navigate(`/payment/${course.id}/default`)
  }
}

const changePassword = () => {
  f7.dialog.alert('Password change functionality coming soon!', 'Feature')
}

const notificationSettings = () => {
  f7.dialog.alert('Notification settings coming soon!', 'Feature')
}

const downloadCertificates = () => {
  f7.dialog.alert('Certificate download coming soon!', 'Feature')
}

const deleteAccount = () => {
  f7.dialog.confirm(
    'This action cannot be undone. Are you absolutely sure?',
    'Delete Account',
    () => {
      f7.dialog.alert('Account deletion functionality coming soon!', 'Feature')
    }
  )
}
</script>

<style scoped>
.profile-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.profile-avatar {
  margin-bottom: 20px;
}

.profile-header h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.profile-header p {
  opacity: 0.9;
}

.profile-info-section {
  padding: 30px 20px;
}

.edit-actions {
  display: flex;
  gap: 10px;
}

.enrollments-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.enrollments-section h2 {
  color: #1e40af;
  margin-bottom: 2rem;
}

.no-enrollments .empty-state {
  text-align: center;
  padding: 40px 20px;
}

.no-enrollments h3 {
  color: #666;
  margin: 1rem 0;
}

.no-enrollments p {
  color: #888;
  margin-bottom: 2rem;
}

.enrollment-card {
  margin-bottom: 20px;
}

.enrollment-info h3 {
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.enrollment-meta {
  display: flex;
  gap: 15px;
  margin: 15px 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.enrolled {
  background: #d4edda;
  color: #155724;
}

.status-badge.completed {
  background: #cce5ff;
  color: #004085;
}

.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.payment-status.pending {
  background: #f8d7da;
  color: #721c24;
}

.payment-status.paid {
  background: #d4edda;
  color: #155724;
}

.enrollment-dates {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 15px;
}

.enrollment-dates small {
  color: #666;
}

.settings-section {
  padding: 30px 20px;
}

.settings-section h2 {
  color: #1e40af;
  margin-bottom: 2rem;
}

.danger-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
