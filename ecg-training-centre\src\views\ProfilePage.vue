<template>
  <f7-page name="profile">
    <f7-navbar title="My Profile" back-link="Back">
      <f7-nav-right>
        <f7-link @click="handleLogout" color="red">Logout</f7-link>
      </f7-nav-right>
    </f7-navbar>
    
    <div class="page-content">
      <!-- Profile Header -->
      <div class="profile-header">
        <div class="container">
          <div class="profile-avatar">
            <f7-icon ios="f7:person_circle" md="material:account_circle" size="80" color="blue"></f7-icon>
          </div>
          <h1>{{ user?.name || 'User' }}</h1>
          <p>{{ user?.email }}</p>
        </div>
      </div>

      <!-- Profile Information -->
      <div class="profile-info-section">
        <div class="container">
          <f7-card>
            <f7-card-header>Profile Information</f7-card-header>
            <f7-card-content>
              <f7-list>
                <f7-list-input
                  label="Full Name"
                  type="text"
                  v-model:value="profileData.name"
                  :disabled="!editMode"
                ></f7-list-input>
                
                <f7-list-input
                  label="Email"
                  type="email"
                  v-model:value="profileData.email"
                  :disabled="true"
                ></f7-list-input>
                
                <f7-list-input
                  label="Phone"
                  type="tel"
                  v-model:value="profileData.phone"
                  :disabled="!editMode"
                ></f7-list-input>
              </f7-list>
            </f7-card-content>
            <f7-card-footer>
              <f7-button 
                v-if="!editMode" 
                fill 
                color="blue" 
                @click="enableEditMode"
              >
                Edit Profile
              </f7-button>
              <div v-else class="edit-actions">
                <f7-button 
                  fill 
                  color="green" 
                  @click="saveProfile"
                  :loading="loading"
                >
                  Save Changes
                </f7-button>
                <f7-button 
                  color="gray" 
                  @click="cancelEdit"
                >
                  Cancel
                </f7-button>
              </div>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>

      <!-- My Enrollments -->
      <div class="enrollments-section">
        <div class="container">
          <h2>My Enrollments</h2>
          
          <div v-if="enrollments.length === 0" class="no-enrollments">
            <f7-card>
              <f7-card-content>
                <div class="empty-state">
                  <f7-icon ios="f7:book" md="material:school" size="60" color="gray"></f7-icon>
                  <h3>No Enrollments Yet</h3>
                  <p>You haven't enrolled in any courses yet. Browse our courses to get started!</p>
                  <f7-button fill color="blue" href="/courses/">Browse Courses</f7-button>
                </div>
              </f7-card-content>
            </f7-card>
          </div>

          <div v-else class="enrollments-list">
            <f7-card v-for="enrollment in enrollments" :key="enrollment.id" class="enrollment-card">
              <f7-card-content>
                <div class="enrollment-info">
                  <h3>{{ enrollment.expand?.course?.title || 'Course' }}</h3>
                  <p>{{ enrollment.expand?.course?.description }}</p>
                  
                  <div class="enrollment-meta">
                    <div class="status-badge" :class="enrollment.status">
                      {{ formatStatus(enrollment.status) }}
                    </div>
                    <div class="payment-status" :class="enrollment.payment_status">
                      Payment: {{ formatPaymentStatus(enrollment.payment_status) }}
                    </div>
                  </div>
                  
                  <div class="enrollment-dates">
                    <small>Enrolled: {{ formatDate(enrollment.enrolled_date) }}</small>
                    <small v-if="enrollment.completion_date">
                      Completed: {{ formatDate(enrollment.completion_date) }}
                    </small>
                  </div>
                </div>
              </f7-card-content>
              <f7-card-footer>
                <f7-button 
                  v-if="enrollment.status === 'enrolled'" 
                  fill 
                  color="blue"
                  @click="viewCourse(enrollment.course)"
                >
                  Continue Learning
                </f7-button>
                <f7-button 
                  v-else-if="enrollment.payment_status === 'pending'" 
                  fill 
                  color="orange"
                  @click="completePayment(enrollment)"
                >
                  Complete Payment
                </f7-button>
              </f7-card-footer>
            </f7-card>
          </div>
        </div>
      </div>

      <!-- Account Settings -->
      <div class="settings-section">
        <div class="container">
          <h2>Account Settings</h2>
          
          <f7-list>
            <f7-list-item 
              link 
              title="Change Password" 
              @click="changePassword"
            >
              <f7-icon ios="f7:lock" md="material:lock" slot="media" color="blue"></f7-icon>
            </f7-list-item>
            
            <f7-list-item 
              link 
              title="Notification Preferences" 
              @click="notificationSettings"
            >
              <f7-icon ios="f7:bell" md="material:notifications" slot="media" color="green"></f7-icon>
            </f7-list-item>
            
            <f7-list-item 
              link 
              title="Download Certificates" 
              @click="downloadCertificates"
            >
              <f7-icon ios="f7:doc" md="material:file_download" slot="media" color="orange"></f7-icon>
            </f7-list-item>
          </f7-list>
        </div>
      </div>

      <!-- Danger Zone -->
      <div class="danger-section">
        <div class="container">
          <f7-card>
            <f7-card-header>Danger Zone</f7-card-header>
            <f7-card-content>
              <p>Once you delete your account, there is no going back. Please be certain.</p>
            </f7-card-content>
            <f7-card-footer>
              <f7-button color="red" @click="deleteAccount">Delete Account</f7-button>
            </f7-card-footer>
          </f7-card>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { f7 } from 'framework7-vue'
import { useAuth } from '@/composables/useAuth'
import { enrollmentService } from '@/services/pocketbase'

const { user, logout } = useAuth()

const loading = ref(false)
const editMode = ref(false)
const enrollments = ref<any[]>([])

const profileData = ref({
  name: '',
  email: '',
  phone: ''
})

const originalProfileData = ref({
  name: '',
  email: '',
  phone: ''
})

onMounted(async () => {
  if (user.value) {
    profileData.value = {
      name: user.value.name || '',
      email: user.value.email || '',
      phone: user.value.phone || ''
    }
    originalProfileData.value = { ...profileData.value }
    
    // Load user enrollments
    await loadEnrollments()
  }
})

const loadEnrollments = async () => {
  try {
    const userEnrollments = await enrollmentService.getUserEnrollments()
    enrollments.value = userEnrollments
  } catch (error) {
    console.error('Failed to load enrollments:', error)
  }
}

const enableEditMode = () => {
  editMode.value = true
  originalProfileData.value = { ...profileData.value }
}

const cancelEdit = () => {
  editMode.value = false
  profileData.value = { ...originalProfileData.value }
}

const saveProfile = async () => {
  loading.value = true
  try {
    // Here you would update the user profile via PocketBase
    // await pb.collection('users').update(user.value.id, profileData.value)
    
    f7.dialog.alert('Profile updated successfully!', 'Success')
    editMode.value = false
  } catch (error: any) {
    f7.dialog.alert(error.message || 'Failed to update profile', 'Error')
  } finally {
    loading.value = false
  }
}

const handleLogout = () => {
  f7.dialog.confirm(
    'Are you sure you want to logout?',
    'Logout',
    () => {
      logout()
      f7.views.main.router.navigate('/')
    }
  )
}

const formatStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const formatPaymentStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const viewCourse = (courseId: string) => {
  f7.views.main.router.navigate(`/course/${courseId}`)
}

const completePayment = (enrollment: any) => {
  const course = enrollment.expand?.course
  if (course) {
    f7.views.main.router.navigate(`/payment/${course.id}/default`)
  }
}

const changePassword = () => {
  f7.dialog.alert('Password change functionality coming soon!', 'Feature')
}

const notificationSettings = () => {
  f7.dialog.alert('Notification settings coming soon!', 'Feature')
}

const downloadCertificates = () => {
  f7.dialog.alert('Certificate download coming soon!', 'Feature')
}

const deleteAccount = () => {
  f7.dialog.confirm(
    'This action cannot be undone. Are you absolutely sure?',
    'Delete Account',
    () => {
      f7.dialog.alert('Account deletion functionality coming soon!', 'Feature')
    }
  )
}
</script>

<style scoped>
.profile-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.profile-avatar {
  margin-bottom: 20px;
}

.profile-header h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.profile-header p {
  opacity: 0.9;
}

.profile-info-section {
  padding: 30px 20px;
}

.edit-actions {
  display: flex;
  gap: 10px;
}

.enrollments-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.enrollments-section h2 {
  color: #1e40af;
  margin-bottom: 2rem;
}

.no-enrollments .empty-state {
  text-align: center;
  padding: 40px 20px;
}

.no-enrollments h3 {
  color: #666;
  margin: 1rem 0;
}

.no-enrollments p {
  color: #888;
  margin-bottom: 2rem;
}

.enrollment-card {
  margin-bottom: 20px;
}

.enrollment-info h3 {
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.enrollment-meta {
  display: flex;
  gap: 15px;
  margin: 15px 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.enrolled {
  background: #d4edda;
  color: #155724;
}

.status-badge.completed {
  background: #cce5ff;
  color: #004085;
}

.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.payment-status.pending {
  background: #f8d7da;
  color: #721c24;
}

.payment-status.paid {
  background: #d4edda;
  color: #155724;
}

.enrollment-dates {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 15px;
}

.enrollment-dates small {
  color: #666;
}

.settings-section {
  padding: 30px 20px;
}

.settings-section h2 {
  color: #1e40af;
  margin-bottom: 2rem;
}

.danger-section {
  padding: 30px 20px;
  background: #f8f9fa;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
