<template>
  <div class="courses-page">
    <!-- Header -->
    <section class="bg-gradient-ecg text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">Our Courses</h1>
          <p class="text-xl md:text-2xl text-blue-100">Professional training programs for the power sector</p>
        </div>
      </div>
    </section>

    <!-- Course Categories -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-4">Course Categories</h2>
          <p class="text-xl text-gray-600">Choose from our comprehensive training programs</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-8">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <svg class="w-6 h-6 text-ecg-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-4">Power Systems Programs</h3>
              <p class="text-gray-600 mb-6">Comprehensive training in power distribution, protection systems, and electrical safety.</p>
              <ul class="space-y-2 mb-8">
                <li class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Power Distribution Network Design
                </li>
                <li class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Substation Access and Awareness
                </li>
                <li class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  ECG Construction Standards
                </li>
                <li class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Protection Systems
                </li>
              </ul>
              <button
                @click="showCourses('power-systems')"
                class="w-full bg-ecg-blue text-white px-6 py-3 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
              >
                View Courses
              </button>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-5 h-5 text-ecg-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-3">Renewable Energy Programs</h3>
              <p class="text-gray-600 text-sm mb-4">Training in solar, wind, and sustainable energy technologies.</p>
              <ul class="space-y-1 mb-6">
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Solar Energy Systems
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Wind Power Technology
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Energy Storage Systems
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Grid Integration
                </li>
              </ul>
              <button
                @click="showCourses('renewable-energy')"
                class="w-full bg-ecg-green text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
              >
                View Courses
              </button>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-3">IT Proficiency</h3>
              <p class="text-gray-600 text-sm mb-4">Digital skills and modern technology training for professionals.</p>
              <ul class="space-y-1 mb-6">
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Data Analytics
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Digital Tools
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Financial Modeling
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Corporate Ethics
                </li>
              </ul>
              <button
                @click="showCourses('it-proficiency')"
                class="w-full bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-700 transition-colors"
              >
                View Courses
              </button>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-3">Contractors Training</h3>
              <p class="text-gray-600 text-sm mb-4">Specialized training for third-party contractors working with ECG.</p>
              <ul class="space-y-1 mb-6">
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Safety Protocols
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  ECG Standards
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Project Management
                </li>
                <li class="flex items-center text-xs text-gray-600">
                  <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Assurance
                </li>
              </ul>
              <button
                @click="showCourses('contractors')"
                class="w-full bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
              >
                View Courses
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Courses -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-ecg-blue mb-4">Featured Courses</h2>
          <p class="text-xl text-gray-600">Popular courses starting soon</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="course in featuredCourses" :key="course.id" class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">{{ course.title }}</h3>
              <p class="text-gray-600 mb-4">{{ course.description }}</p>
              <div class="flex justify-between items-center mb-6">
                <span class="text-sm text-gray-500">{{ course.duration }}</span>
                <span class="text-lg font-bold text-ecg-blue">{{ course.price }}</span>
              </div>
              <button
                @click="registerCourse(course.id)"
                class="w-full bg-ecg-blue text-white px-6 py-3 rounded-lg font-medium hover:bg-ecg-dark-blue transition-colors"
              >
                Register Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Registration CTA -->
    <section class="py-20 bg-ecg-blue text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Learning?</h2>
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">Join thousands of professionals who have advanced their careers with our training programs.</p>
        <button
          @click="openRegistration"
          class="bg-white text-ecg-blue px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors transform hover:scale-105"
        >
          Register for Courses
        </button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const featuredCourses = ref([
  {
    id: 1,
    title: 'Power Distribution Network Design',
    description: 'Comprehensive understanding of designing efficient and reliable power distribution networks.',
    duration: '5 days',
    price: 'GHS 2,500'
  },
  {
    id: 2,
    title: 'Substation Access and Awareness',
    description: 'Essential knowledge on navigating high-voltage environments safely.',
    duration: '3 days',
    price: 'GHS 1,800'
  },
  {
    id: 3,
    title: 'Solar Energy Systems',
    description: 'Complete training on solar panel installation and maintenance.',
    duration: '4 days',
    price: 'GHS 2,200'
  }
])

const showCourses = (category: string) => {
  alert(`Showing courses for: ${category}`)
}

const registerCourse = (courseId: number) => {
  if (confirm('Would you like to register for this course?')) {
    // Redirect to payment page with course details
    const course = featuredCourses.value.find(c => c.id === courseId)
    const category = course ? getCategoryFromTitle(course.title) : 'default'
    router.push(`/payment/${courseId}/${category}`)
  }
}

const getCategoryFromTitle = (title: string): string => {
  if (title.toLowerCase().includes('power') || title.toLowerCase().includes('distribution')) {
    return 'power-systems'
  } else if (title.toLowerCase().includes('solar') || title.toLowerCase().includes('renewable')) {
    return 'renewable-energy'
  } else if (title.toLowerCase().includes('substation') || title.toLowerCase().includes('contractor')) {
    return 'contractors'
  }
  return 'default'
}

const openRegistration = () => {
  router.push('/register')
}
</script>

<style scoped>
.courses-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.courses-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.categories-section {
  padding: 60px 20px;
}

.categories-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.category-card ul {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.category-card li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.featured-section {
  background: #f8f9fa;
  padding: 60px 20px;
}

.featured-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  font-weight: bold;
}

.duration {
  color: #059669;
}

.price {
  color: #1e40af;
}

.cta-section {
  background: #1e40af;
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.cta-section h2 {
  margin-bottom: 1rem;
}

.cta-section p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Tailwind CSS handles all styling */
</style>
