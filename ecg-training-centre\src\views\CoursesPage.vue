<template>
  <f7-page name="courses">
    <f7-navbar title="Courses" back-link="Back"></f7-navbar>
    
    <div class="page-content">
      <!-- Header -->
      <div class="courses-header">
        <div class="container">
          <h1>Our Courses</h1>
          <p>Professional training programs for the power sector</p>
        </div>
      </div>

      <!-- Course Categories -->
      <div class="categories-section">
        <div class="container">
          <h2>Course Categories</h2>
          <div class="category-grid">
            <f7-card class="category-card">
              <f7-card-header>Power Systems Programs</f7-card-header>
              <f7-card-content>
                <p>Comprehensive training in power distribution, protection systems, and electrical safety.</p>
                <ul>
                  <li>Power Distribution Network Design</li>
                  <li>Substation Access and Awareness</li>
                  <li>ECG Construction Standards</li>
                  <li>Protection Systems</li>
                </ul>
              </f7-card-content>
              <f7-card-footer>
                <f7-button fill color="blue" @click="showCourses('power-systems')">View Courses</f7-button>
              </f7-card-footer>
            </f7-card>

            <f7-card class="category-card">
              <f7-card-header>Renewable Energy Programs</f7-card-header>
              <f7-card-content>
                <p>Training in solar, wind, and sustainable energy technologies.</p>
                <ul>
                  <li>Solar Energy Systems</li>
                  <li>Wind Power Technology</li>
                  <li>Energy Storage Systems</li>
                  <li>Grid Integration</li>
                </ul>
              </f7-card-content>
              <f7-card-footer>
                <f7-button fill color="green" @click="showCourses('renewable-energy')">View Courses</f7-button>
              </f7-card-footer>
            </f7-card>

            <f7-card class="category-card">
              <f7-card-header>IT Proficiency</f7-card-header>
              <f7-card-content>
                <p>Digital skills and modern technology training for professionals.</p>
                <ul>
                  <li>Data Analytics</li>
                  <li>Digital Tools</li>
                  <li>Financial Modeling</li>
                  <li>Corporate Ethics</li>
                </ul>
              </f7-card-content>
              <f7-card-footer>
                <f7-button fill color="orange" @click="showCourses('it-proficiency')">View Courses</f7-button>
              </f7-card-footer>
            </f7-card>

            <f7-card class="category-card">
              <f7-card-header>Contractors Training</f7-card-header>
              <f7-card-content>
                <p>Specialized training for third-party contractors working with ECG.</p>
                <ul>
                  <li>Safety Protocols</li>
                  <li>ECG Standards</li>
                  <li>Project Management</li>
                  <li>Quality Assurance</li>
                </ul>
              </f7-card-content>
              <f7-card-footer>
                <f7-button fill color="red" @click="showCourses('contractors')">View Courses</f7-button>
              </f7-card-footer>
            </f7-card>
          </div>
        </div>
      </div>

      <!-- Featured Courses -->
      <div class="featured-section">
        <div class="container">
          <h2>Featured Courses</h2>
          <div class="course-list">
            <f7-card v-for="course in featuredCourses" :key="course.id" class="course-card">
              <f7-card-header>{{ course.title }}</f7-card-header>
              <f7-card-content>
                <p>{{ course.description }}</p>
                <div class="course-meta">
                  <span class="duration">{{ course.duration }}</span>
                  <span class="price">{{ course.price }}</span>
                </div>
              </f7-card-content>
              <f7-card-footer>
                <f7-button fill color="blue" @click="registerCourse(course.id)">Register Now</f7-button>
              </f7-card-footer>
            </f7-card>
          </div>
        </div>
      </div>

      <!-- Registration CTA -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Start Learning?</h2>
          <p>Join thousands of professionals who have advanced their careers with our training programs.</p>
          <f7-button large fill color="blue" @click="openRegistration">Register for Courses</f7-button>
        </div>
      </div>
    </div>
  </f7-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { f7 } from 'framework7-vue'

const featuredCourses = ref([
  {
    id: 1,
    title: 'Power Distribution Network Design',
    description: 'Comprehensive understanding of designing efficient and reliable power distribution networks.',
    duration: '5 days',
    price: 'GHS 2,500'
  },
  {
    id: 2,
    title: 'Substation Access and Awareness',
    description: 'Essential knowledge on navigating high-voltage environments safely.',
    duration: '3 days',
    price: 'GHS 1,800'
  },
  {
    id: 3,
    title: 'Solar Energy Systems',
    description: 'Complete training on solar panel installation and maintenance.',
    duration: '4 days',
    price: 'GHS 2,200'
  }
])

const showCourses = (category: string) => {
  f7.dialog.alert(`Showing courses for: ${category}`, 'Course Category')
}

const registerCourse = (courseId: number) => {
  f7.dialog.confirm(
    'Would you like to register for this course?',
    'Course Registration',
    () => {
      // Redirect to payment page
      f7.views.main.router.navigate('/payment/')
    }
  )
}

const openRegistration = () => {
  f7.views.main.router.navigate('/registration/')
}
</script>

<style scoped>
.courses-header {
  background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.courses-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.categories-section {
  padding: 60px 20px;
}

.categories-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.category-card ul {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.category-card li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.featured-section {
  background: #f8f9fa;
  padding: 60px 20px;
}

.featured-section h2 {
  text-align: center;
  color: #1e40af;
  margin-bottom: 3rem;
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  font-weight: bold;
}

.duration {
  color: #059669;
}

.price {
  color: #1e40af;
}

.cta-section {
  background: #1e40af;
  color: white;
  padding: 60px 20px;
  text-align: center;
}

.cta-section h2 {
  margin-bottom: 1rem;
}

.cta-section p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
