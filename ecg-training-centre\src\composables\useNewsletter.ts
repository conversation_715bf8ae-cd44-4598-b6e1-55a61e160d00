import { ref, computed } from 'vue'
import { newsletterService } from '@/services/pocketbase'

export function useNewsletter() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const success = ref(false)
  const subscriptionMessage = ref('')

  const subscribe = async (email: string) => {
    loading.value = true
    error.value = null
    success.value = false
    subscriptionMessage.value = ''

    // Basic email validation
    if (!isValidEmail(email)) {
      error.value = 'Please enter a valid email address'
      loading.value = false
      return false
    }

    try {
      await newsletterService.subscribe(email)
      success.value = true
      subscriptionMessage.value = 'Thank you for subscribing! You will receive our latest updates and course announcements.'
      
      // Send confirmation email (this would be handled by the backend)
      await sendConfirmationEmail(email)
      
      return true
    } catch (err: any) {
      if (err.message.includes('duplicate') || err.message.includes('already exists')) {
        error.value = 'This email is already subscribed to our newsletter'
      } else {
        error.value = err.message || 'Failed to subscribe. Please try again.'
      }
      return false
    } finally {
      loading.value = false
    }
  }

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const sendConfirmationEmail = async (email: string) => {
    // This would typically be handled by the backend
    // For now, we'll just log it
    console.log(`Confirmation email would be sent to: ${email}`)
    
    // In a real implementation, you might call an API endpoint like:
    // await fetch('/api/newsletter/confirm', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ email })
    // })
  }

  const clearMessages = () => {
    error.value = null
    success.value = false
    subscriptionMessage.value = ''
  }

  const clearError = () => {
    error.value = null
  }

  const clearSuccess = () => {
    success.value = false
    subscriptionMessage.value = ''
  }

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    success: computed(() => success.value),
    subscriptionMessage: computed(() => subscriptionMessage.value),
    
    // Actions
    subscribe,
    isValidEmail,
    clearMessages,
    clearError,
    clearSuccess
  }
}
