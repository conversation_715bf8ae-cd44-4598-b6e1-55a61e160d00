{"name": "htmx.org", "description": "high power tools for html", "keywords": ["AJAX", "HTML"], "version": "2.0.6", "homepage": "https://htmx.org/", "bugs": {"url": "https://github.com/bigskysoftware/htmx/issues"}, "license": "0BSD", "files": ["LICENSE", "README.md", "dist/htmx.esm.d.ts", "dist/*.js", "dist/ext/*.js", "dist/*.js.gz", "editors/jetbrains/htmx.web-types.json"], "main": "dist/htmx.esm.js", "types": "dist/htmx.esm.d.ts", "jsdelivr": "dist/htmx.min.js", "unpkg": "dist/htmx.min.js", "web-types": "editors/jetbrains/htmx.web-types.json", "scripts": {"dist": "./scripts/dist.sh && npm run types-generate && npm run web-types-generate", "lint": "eslint src/htmx.js test/attributes/ test/core/ test/util/ scripts/*.mjs", "format": "eslint --fix src/htmx.js test/attributes/ test/core/ test/util/ scripts/*.mjs", "types-check": "tsc src/htmx.js --noEmit --checkJs --target es6 --lib dom,dom.iterable", "types-generate": "tsc dist/htmx.esm.js --declaration --emitDeclarationOnly --allowJs --outDir dist", "test": "npm run lint && npm run types-check && npm run test:chrome", "test:debug": "web-test-runner --manual --open", "test:chrome": "playwright install chromium && web-test-runner --browsers chromium --playwright", "test:firefox": "playwright install firefox && web-test-runner --concurrency 1 --browsers firefox --playwright", "test:webkit": "playwright install webkit && web-test-runner --browsers webkit --playwright", "test:all": "playwright install chromium firefox webkit && web-test-runner --concurrency 1 --browsers chromium firefox webkit --playwright", "test:ci": "npm run lint && npm run types-check && npm run test:all", "ws-tests": "cd ./test/ws-sse && node ./server.js", "web-types-generate": "node ./scripts/generate-web-types.mjs", "www": "bash ./scripts/www.sh", "sha": "bash ./scripts/sha.sh"}, "repository": {"type": "git", "url": "git+https://github.com/bigskysoftware/htmx.git"}, "eslintConfig": {"extends": ["standard", "plugin:mocha/recommended"], "rules": {"mocha/consistent-spacing-between-blocks": 0, "mocha/no-setup-in-describe": 0, "mocha/no-skipped-tests": 0, "camelcase": 0, "no-var": 0, "no-undef": 0, "eqeqeq": 0, "no-multi-str": 0, "no-prototype-builtins": 0, "no-cond-assign": 0, "no-empty": 0, "no-eval": 0, "no-new-func": 0, "no-redeclare": 0, "no-return-assign": 0, "no-unused-vars": 0, "no-useless-call": 0, "no-useless-escape": 0, "no-unused-expressions": 0, "no-restricted-properties": ["error", {"property": "substr", "message": "Use .slice or .substring instead of .substr"}], "space-before-function-paren": ["error", "never"]}}, "devDependencies": {"@types/node": "20.0.0", "@types/parse5": "^7.0.0", "@web/test-runner": "^0.20.2", "@web/test-runner-playwright": "^0.11.0", "chai": "^4.5.0", "chai-dom": "^1.12.1", "eslint": "^8.57.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-mocha": "^10.5.0", "fs-extra": "^9.1.0", "mocha": "^11.1.0", "mock-socket": "^9.3.1", "sinon": "^10.0.1", "typescript": "^5.5.4", "uglify-js": "^3.19.3", "ws": "^8.18.1"}}