import { ref, computed } from 'vue'
import { courseService, categoryService, enrollmentService } from '@/services/pocketbase'
import type { Course, CourseCategory } from '@/services/pocketbase'

export function useCourses() {
  const courses = ref<Course[]>([])
  const categories = ref<CourseCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const getCourses = async (page = 1, perPage = 20, filter = '') => {
    loading.value = true
    error.value = null
    
    try {
      const result = await courseService.getCourses(page, perPage, filter)
      courses.value = result.items
      return result
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch courses'
      throw err
    } finally {
      loading.value = false
    }
  }

  const getCourse = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      const course = await courseService.getCourse(id)
      return course
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch course'
      throw err
    } finally {
      loading.value = false
    }
  }

  const getCoursesByCategory = async (categoryId: string) => {
    loading.value = true
    error.value = null
    
    try {
      const result = await courseService.getCoursesByCategory(categoryId)
      return result
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch courses by category'
      throw err
    } finally {
      loading.value = false
    }
  }

  const getCategories = async () => {
    loading.value = true
    error.value = null
    
    try {
      const result = await categoryService.getCategories()
      categories.value = result
      return result
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch categories'
      throw err
    } finally {
      loading.value = false
    }
  }

  const enrollInCourse = async (courseId: string) => {
    loading.value = true
    error.value = null
    
    try {
      const enrollment = await enrollmentService.enrollInCourse(courseId)
      return enrollment
    } catch (err: any) {
      error.value = err.message || 'Failed to enroll in course'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    courses: computed(() => courses.value),
    categories: computed(() => categories.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    
    // Actions
    getCourses,
    getCourse,
    getCoursesByCategory,
    getCategories,
    enrollInCourse,
    clearError
  }
}
