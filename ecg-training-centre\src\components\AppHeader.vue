<template>
  <header class="bg-white shadow-lg sticky top-0 z-50">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <router-link to="/" class="flex items-center">
            <div class="w-10 h-10 bg-gradient-ecg rounded-lg flex items-center justify-center mr-3">
              <span class="text-white font-bold text-lg">ECG</span>
            </div>
            <span class="text-xl font-bold text-ecg-blue">Training Centre</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <router-link
              to="/"
              class="nav-link"
              :class="{ 'nav-link-active': $route.path === '/' }"
            >
              Home
            </router-link>
            <router-link
              to="/about"
              class="nav-link"
              :class="{ 'nav-link-active': $route.path === '/about' }"
            >
              About
            </router-link>
            <router-link
              to="/courses"
              class="nav-link"
              :class="{ 'nav-link-active': $route.path === '/courses' }"
            >
              Courses
            </router-link>
            <router-link
              to="/contact"
              class="nav-link"
              :class="{ 'nav-link-active': $route.path === '/contact' }"
            >
              Contact
            </router-link>
          </div>
        </div>

        <!-- Auth Buttons -->
        <div class="hidden md:flex items-center space-x-4">
          <div v-if="!isAuthenticated">
            <router-link
              to="/login"
              class="text-ecg-blue hover:text-ecg-dark-blue px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Login
            </router-link>
            <router-link
              to="/register"
              class="bg-ecg-blue hover:bg-ecg-dark-blue text-white px-4 py-2 rounded-md text-sm font-medium transition-colors ml-2"
            >
              Register
            </router-link>
          </div>
          <div v-else class="flex items-center space-x-4">
            <router-link
              to="/profile"
              class="flex items-center text-gray-700 hover:text-ecg-blue transition-colors"
            >
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              {{ user?.name || 'Profile' }}
            </router-link>
            <button
              @click="handleLogout"
              class="text-red-600 hover:text-red-800 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Logout
            </button>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            class="bg-gray-200 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ecg-blue"
          >
            <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path
                :class="{ 'hidden': mobileMenuOpen, 'inline-flex': !mobileMenuOpen }"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                :class="{ 'hidden': !mobileMenuOpen, 'inline-flex': mobileMenuOpen }"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div :class="{ 'block': mobileMenuOpen, 'hidden': !mobileMenuOpen }" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
          <router-link
            to="/"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.path === '/' }"
            @click="closeMobileMenu"
          >
            Home
          </router-link>
          <router-link
            to="/about"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.path === '/about' }"
            @click="closeMobileMenu"
          >
            About
          </router-link>
          <router-link
            to="/courses"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.path === '/courses' }"
            @click="closeMobileMenu"
          >
            Courses
          </router-link>
          <router-link
            to="/contact"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.path === '/contact' }"
            @click="closeMobileMenu"
          >
            Contact
          </router-link>
          
          <!-- Mobile Auth -->
          <div class="border-t pt-4 mt-4">
            <div v-if="!isAuthenticated" class="space-y-2">
              <router-link
                to="/login"
                class="mobile-nav-link"
                @click="closeMobileMenu"
              >
                Login
              </router-link>
              <router-link
                to="/register"
                class="mobile-nav-link bg-ecg-blue text-white"
                @click="closeMobileMenu"
              >
                Register
              </router-link>
            </div>
            <div v-else class="space-y-2">
              <router-link
                to="/profile"
                class="mobile-nav-link"
                @click="closeMobileMenu"
              >
                Profile ({{ user?.name || 'User' }})
              </router-link>
              <button
                @click="handleLogout"
                class="mobile-nav-link text-red-600 w-full text-left"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const router = useRouter()
const { isAuthenticated, user, logout } = useAuth()

const mobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleLogout = () => {
  logout()
  router.push('/')
  closeMobileMenu()
}
</script>

<style scoped>
.nav-link {
  @apply text-gray-700 hover:text-ecg-blue px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-link-active {
  @apply text-ecg-blue bg-blue-50;
}

.mobile-nav-link {
  @apply text-gray-700 hover:text-ecg-blue hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium transition-colors;
}

.mobile-nav-link-active {
  @apply text-ecg-blue bg-blue-50;
}
</style>
