import { <PERSON>ala<PERSON> } from '../nodes/Scalar';
import type { BlockScalar, FlowScalar, SourceToken } from '../parse/cst';
import type { ComposeContext } from './compose-node';
import type { ComposeErrorHandler } from './composer';
export declare function composeScalar(ctx: ComposeContext, token: FlowScalar | BlockScalar, tagToken: SourceToken | null, onError: ComposeErrorHandler): Scalar.Parsed;
