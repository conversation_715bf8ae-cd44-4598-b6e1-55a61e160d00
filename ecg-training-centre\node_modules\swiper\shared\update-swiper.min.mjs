const paramsList=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function extend(e,t){const i=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>i.indexOf(e)<0)).forEach((i=>{void 0===e[i]?e[i]=t[i]:isObject(t[i])&&isObject(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:extend(e[i],t[i]):e[i]=t[i]}))}function needsNavigation(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function needsPagination(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function needsScrollbar(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function uniqueClasses(e){void 0===e&&(e="");const t=e.split(" ").map((e=>e.trim())).filter((e=>!!e)),i=[];return t.forEach((e=>{i.indexOf(e)<0&&i.push(e)})),i.join(" ")}function attrToProp(e){return void 0===e&&(e=""),e.replace(/-[a-z]/g,(e=>e.toUpperCase().replace("-","")))}function wrapperClass(e){return void 0===e&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function updateSwiper(e){let{swiper:t,slides:i,passedParams:n,changedParams:o,nextEl:l,prevEl:r,scrollbarEl:a,paginationEl:s}=e;const c=o.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:d,pagination:p,navigation:u,scrollbar:v,virtual:g,thumbs:f}=t;let b,_,w,h,S,E,m,x;o.includes("thumbs")&&n.thumbs&&n.thumbs.swiper&&d.thumbs&&!d.thumbs.swiper&&(b=!0),o.includes("controller")&&n.controller&&n.controller.control&&d.controller&&!d.controller.control&&(_=!0),o.includes("pagination")&&n.pagination&&(n.pagination.el||s)&&(d.pagination||!1===d.pagination)&&p&&!p.el&&(w=!0),o.includes("scrollbar")&&n.scrollbar&&(n.scrollbar.el||a)&&(d.scrollbar||!1===d.scrollbar)&&v&&!v.el&&(h=!0),o.includes("navigation")&&n.navigation&&(n.navigation.prevEl||r)&&(n.navigation.nextEl||l)&&(d.navigation||!1===d.navigation)&&u&&!u.prevEl&&!u.nextEl&&(S=!0);const C=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),d[e].prevEl=void 0,d[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),d[e].el=void 0,t[e].el=void 0))};if(o.includes("loop")&&t.isElement&&(d.loop&&!n.loop?E=!0:!d.loop&&n.loop?m=!0:x=!0),c.forEach((e=>{if(isObject(d[e])&&isObject(n[e]))extend(d[e],n[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in n[e])||n[e].enabled||C(e);else{const t=n[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?d[e]=n[e]:!1===t&&C(e)}})),c.includes("controller")&&!_&&t.controller&&t.controller.control&&d.controller&&d.controller.control&&(t.controller.control=d.controller.control),o.includes("children")&&i&&g&&d.virtual.enabled&&(g.slides=i,g.update(!0)),o.includes("children")&&i&&d.loop&&(x=!0),b){f.init()&&f.update(!0)}_&&(t.controller.control=d.controller.control),w&&(!t.isElement||s&&"string"!=typeof s||(s=document.createElement("div"),s.classList.add("swiper-pagination"),s.part.add("pagination"),t.el.appendChild(s)),s&&(d.pagination.el=s),p.init(),p.render(),p.update()),h&&(!t.isElement||a&&"string"!=typeof a||(a=document.createElement("div"),a.classList.add("swiper-scrollbar"),a.part.add("scrollbar"),t.el.appendChild(a)),a&&(d.scrollbar.el=a),v.init(),v.updateSize(),v.setTranslate()),S&&(t.isElement&&(l&&"string"!=typeof l||(l=document.createElement("div"),l.classList.add("swiper-button-next"),l.innerHTML=t.hostEl.constructor.nextButtonSvg,l.part.add("button-next"),t.el.appendChild(l)),r&&"string"!=typeof r||(r=document.createElement("div"),r.classList.add("swiper-button-prev"),r.innerHTML=t.hostEl.constructor.prevButtonSvg,r.part.add("button-prev"),t.el.appendChild(r))),l&&(d.navigation.nextEl=l),r&&(d.navigation.prevEl=r),u.init(),u.update()),o.includes("allowSlideNext")&&(t.allowSlideNext=n.allowSlideNext),o.includes("allowSlidePrev")&&(t.allowSlidePrev=n.allowSlidePrev),o.includes("direction")&&t.changeDirection(n.direction,!1),(E||x)&&t.loopDestroy(),(m||x)&&t.loopCreate(),t.update()}export{needsPagination as a,needsScrollbar as b,attrToProp as c,uniqueClasses as d,extend as e,isObject as i,needsNavigation as n,paramsList as p,updateSwiper as u,wrapperClass as w};
//# sourceMappingURL=update-swiper.min.mjs.map