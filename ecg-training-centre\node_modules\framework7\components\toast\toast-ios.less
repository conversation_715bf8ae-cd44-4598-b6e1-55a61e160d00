.ios {
  .toast {
    transition-duration: 300ms;
    width: 100%;
    left: 0;
    &.toast-top {
      top: 0;
      transform: translate3d(0, -100%, 0);
      &.modal-in {
        transform: translate3d(0, 0%, 0);
      }
    }
    &.toast-center {
      width: auto;
      left: 50%;
      border-radius: var(--f7-toast-border-radius);
      transform: translate3d(-50%, -50%, 0);
      &.modal-in {
        transform: translate3d(-50%, -50%, 0);
      }
    }
    &.toast-bottom {
      bottom: 0;
      transform: translate3d(0, 100%, 0);
      &.modal-in {
        transform: translate3d(0, 0%, 0);
      }
    }
    @media (max-width: 568px) {
      &.toast-top {
        margin-top: 0;
      }
      &.toast-top .toast-content {
        padding-top: calc(var(--f7-toast-padding-vertical) + var(--f7-safe-area-top));
      }
      &.toast-bottom .toast-content {
        padding-bottom: calc(var(--f7-toast-padding-vertical) + var(--f7-safe-area-bottom));
      }
    }
    @media (min-width: 569px) {
      left: 50%;
      margin-left: calc(-1 * var(--f7-toast-max-width) / 2);
      border-radius: var(--f7-toast-border-radius);
      &.toast-top {
        top: 16px;
      }
      &.toast-center {
        margin-left: 0;
      }
      &.toast-bottom {
        margin-bottom: calc(16px + var(--f7-safe-area-bottom));
      }
    }
    @media (min-width: 1024px) {
      margin-left: 0;
      width: auto;
      &.toast-horizontal-left {
        left: 16px;
      }
      &.toast-horizontal-right {
        left: auto;
        right: 16px;
      }
      &.toast-horizontal-center {
        left: 50%;
        width: var(--f7-toast-max-width);
        margin-left: calc(-1 * var(--f7-toast-max-width) / 2);
      }
    }
  }
  .toast-button {
    .ltr({
      margin-left: 16px;
      margin-right: calc(-1 * var(--f7-button-padding-horizontal));
    });
    .rtl({
      margin-right: 16px;
      margin-left: calc(-1 * var(--f7-button-padding-horizontal));
    });
  }
}
