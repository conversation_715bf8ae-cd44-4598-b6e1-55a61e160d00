:root {
  --f7-popup-border-radius: 0px;
  --f7-popup-tablet-width: 630px;
  --f7-popup-tablet-height: 630px;
  --f7-popup-push-offset: var(--f7-safe-area-top);
  /*
  --f7-popup-tablet-border-radius: var(--f7-popup-border-radius);
  */
}
.ios-vars({
  --f7-popup-tablet-border-radius: 5px;
  --f7-popup-transition-duration: 400ms;
  --f7-popup-transition-timing-function: initial;
  --f7-popup-push-border-radius: 10px;
});
.md-vars({
  --f7-popup-tablet-border-radius: 28px;
  --f7-popup-transition-duration: 600ms;
  --f7-popup-transition-timing-function: cubic-bezier(0, 1, 0.2, 1);
  --f7-popup-push-border-radius: 28px;
});
