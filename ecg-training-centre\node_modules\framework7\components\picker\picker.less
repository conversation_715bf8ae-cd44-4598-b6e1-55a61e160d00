/* === Picker === */
@import './picker-vars.less';

.picker {
  width: 100%;
  height: var(--f7-picker-height);
  &.picker-inline {
    height: var(--f7-picker-inline-height);
  }
  .popover & {
    height: var(--f7-picker-popover-height);
  }
  @media (orientation: landscape) and (max-height: 415px) {
    &:not(.picker-inline) {
      height: var(--f7-picker-landscape-height);
    }
  }
  &.sheet-modal {
    background: var(--f7-picker-sheet-bg-color, var(--f7-sheet-bg-color));
  }
}
.picker-popover {
  width: var(--f7-picker-popover-width);
  .toolbar {
    .hairline-remove(top);
    background: none;
    border-radius: var(--f7-popover-border-radius) var(--f7-popover-border-radius) 0 0;
  }
  .toolbar + .picker-columns {
    height: calc(100% - var(--f7-toolbar-height));
  }
}
.picker-columns {
  display: flex;
  overflow: hidden;
  justify-content: center;
  padding: 0;
  text-align: right;
  height: 100%;
  position: relative;
  font-size: var(--f7-picker-column-font-size);
  .popover & {
    border-radius: 0 0 var(--f7-popover-border-radius) var(--f7-popover-border-radius);
  }
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 20%;
    z-index: 100;
    pointer-events: none;
  }
  --f7-picker-mask-bg-color: transparent;
  .page & {
    --f7-picker-mask-bg-color: var(--f7-page-bg-color);
  }
  .block-strong & {
    --f7-picker-mask-bg-color: var(--f7-block-strong-bg-color);
  }
  .picker-sheet & {
    --f7-picker-mask-bg-color: var(--f7-picker-sheet-bg-color, var(--f7-sheet-bg-color));
  }
  .picker-popover & {
    --f7-picker-mask-bg-color: var(--f7-popover-bg-color);
  }
  &::before {
    top: 0;
    background-image: linear-gradient(to bottom, var(--f7-picker-mask-bg-color), transparent);
  }
  &::after {
    bottom: 0;
    background-image: linear-gradient(to top, var(--f7-picker-mask-bg-color), transparent);
  }
}
.picker-column {
  position: relative;
  max-height: 100%;
  z-index: 10;
  &.picker-column-first.picker-column-last {
    width: 100%;
  }

  &.picker-column-left {
    text-align: left;
  }
  &.picker-column-center {
    text-align: center;
  }
  &.picker-column-right {
    text-align: right;
  }
  &.picker-column-divider {
    display: flex;
    align-items: center;
    color: var(--f7-picker-divider-text-color);
  }
}
.picker-items {
  overflow: auto;
  scrollbar-width: none;
  scroll-snap-type: y mandatory;
  height: 100%;
  box-sizing: border-box;
  padding: var(--f7-picker-scroll-padding, 0px) 0px;
  .no-scrollbar();
}
.picker-item {
  height: var(--f7-picker-item-height);
  line-height: var(--f7-picker-item-height);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  left: 0;
  top: 0;
  width: 100%;
  box-sizing: border-box;
  color: var(--f7-picker-item-text-color);
  cursor: pointer;
  scroll-snap-align: center;
  span {
    padding: 0 10px;
  }
  &.picker-item-far {
    pointer-events: none;
  }
  &.picker-item-selected {
    color: var(--f7-picker-item-selected-text-color);
    transform: translate3d(0, 0, 0) rotateX(0deg);
  }
  .picker-column-free-mode & {
    scroll-snap-align: none;
  }
}
.picker-center-highlight {
  height: var(--f7-picker-item-height);
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  margin-top: calc(-1 * var(--f7-picker-item-height) / 2);
  pointer-events: none;
}
.picker-3d {
  .picker-columns {
    overflow: hidden;
  }
  .picker-column,
  .picker-items,
  .picker-item {
    transform-style: preserve-3d;
  }
  .picker-column {
    overflow: visible;
  }
  .picker-item {
    perspective: 1200px;
    overflow: visible;

    > span {
      backface-visibility: hidden;
      display: block;
      transform-style: preserve-3d;
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
      box-sizing: border-box;
      max-width: 100%;
      transform-origin: center center -100px;
    }
  }
}

.if-ios-theme({
  @import './picker-ios.less';
});
.if-md-theme({
  @import './picker-md.less';
});
