.md {
  .toggle {
    input[type='checkbox']:checked + .toggle-icon {
      background: var(--f7-toggle-active-bg-color);
      border-color: var(--f7-toggle-active-border-color);
      &:after {
        .ltr({
          transform: translateX(calc(var(--f7-toggle-width) - var(--f7-toggle-height))) scale(1);
        });
        .rtl({
          transform: translateX(calc(-1 * (var(--f7-toggle-width) - var(--f7-toggle-height)))) scale(1);
        });
        background: var(--f7-toggle-active-knob-bg-color);
      }
    }
    &.active-state input[type='checkbox']:checked + .toggle-icon {
      &:after {
        .ltr({
          transform: translateX(calc(var(--f7-toggle-width) - var(--f7-toggle-height))) scale(1.1);
        });
        .rtl({
          transform: translateX(calc(-1 * (var(--f7-toggle-width) - var(--f7-toggle-height)))) scale(1.1);
        });
      }
    }
  }
  .toggle-icon {
    background: var(--f7-toggle-inactive-bg-color);
    border: 2px solid var(--f7-toggle-inactive-border-color);
    &:after {
      background: var(--f7-toggle-inactive-knob-bg-color);
      height: calc(var(--f7-toggle-height) - 8px);
      width: calc(var(--f7-toggle-height) - 8px);
      top: 2px;
      border-radius: var(--f7-toggle-height);
      transform: scale(0.666);
      .ltr({ left: 2px; });
      .rtl({ right: 2px; });
    }
    &.active-state:after {
      transform: scale(1.1);
    }
  }
}
