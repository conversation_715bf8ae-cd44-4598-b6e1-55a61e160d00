// Paystack payment integration for Ghana
// Supports Mobile Money (MTN, Vodafone, AirtelTigo) and card payments

export interface PaymentData {
  email: string
  amount: number // Amount in pesewas (GHS * 100)
  currency: string
  reference: string
  callback_url?: string
  metadata?: {
    course_id?: string
    user_id?: string
    enrollment_id?: string
    [key: string]: any
  }
  channels?: string[] // ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
}

export interface PaymentResponse {
  status: boolean
  message: string
  data?: {
    authorization_url: string
    access_code: string
    reference: string
  }
}

export interface PaymentVerification {
  status: boolean
  message: string
  data?: {
    id: number
    domain: string
    status: string
    reference: string
    amount: number
    message: string
    gateway_response: string
    paid_at: string
    created_at: string
    channel: string
    currency: string
    ip_address: string
    metadata: any
    log: any
    fees: number
    fees_split: any
    authorization: {
      authorization_code: string
      bin: string
      last4: string
      exp_month: string
      exp_year: string
      channel: string
      card_type: string
      bank: string
      country_code: string
      brand: string
      reusable: boolean
      signature: string
      account_name: string
    }
    customer: {
      id: number
      first_name: string
      last_name: string
      email: string
      customer_code: string
      phone: string
      metadata: any
      risk_action: string
      international_format_phone: string
    }
    plan: any
    split: any
    order_id: any
    paidAt: string
    createdAt: string
    requested_amount: number
    pos_transaction_data: any
    source: any
    fees_breakdown: any
  }
}

class PaymentService {
  private publicKey: string
  private secretKey: string
  private baseUrl: string

  constructor() {
    // Use test keys for development - replace with live keys for production
    this.publicKey = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_your_public_key_here'
    this.secretKey = import.meta.env.VITE_PAYSTACK_SECRET_KEY || 'sk_test_your_secret_key_here'
    this.baseUrl = 'https://api.paystack.co'
  }

  // Initialize payment with Paystack Inline
  async initializePayment(paymentData: PaymentData): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Load Paystack inline script if not already loaded
        if (!window.PaystackPop) {
          const script = document.createElement('script')
          script.src = 'https://js.paystack.co/v1/inline.js'
          script.onload = () => {
            this.openPaystackModal(paymentData, resolve, reject)
          }
          script.onerror = () => {
            reject(new Error('Failed to load Paystack script'))
          }
          document.head.appendChild(script)
        } else {
          this.openPaystackModal(paymentData, resolve, reject)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  private openPaystackModal(
    paymentData: PaymentData,
    resolve: (value: void | PromiseLike<void>) => void,
    reject: (reason?: any) => void
  ) {
    const handler = window.PaystackPop.setup({
      key: this.publicKey,
      email: paymentData.email,
      amount: paymentData.amount,
      currency: paymentData.currency,
      ref: paymentData.reference,
      metadata: paymentData.metadata,
      channels: paymentData.channels || ['card', 'bank', 'ussd', 'mobile_money'],
      callback: (response: any) => {
        // Payment successful
        console.log('Payment successful:', response)
        resolve()
      },
      onClose: () => {
        // Payment cancelled
        reject(new Error('Payment cancelled by user'))
      }
    })

    handler.openIframe()
  }

  // Verify payment on the backend
  async verifyPayment(reference: string): Promise<PaymentVerification> {
    try {
      const response = await fetch(`${this.baseUrl}/transaction/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.secretKey}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Payment verification error:', error)
      throw error
    }
  }

  // Generate payment reference
  generateReference(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    return `ECG_${timestamp}_${random}`
  }

  // Convert GHS to pesewas
  convertToPesewas(amount: number): number {
    return Math.round(amount * 100)
  }

  // Convert pesewas to GHS
  convertToGHS(amount: number): number {
    return amount / 100
  }

  // Format amount for display
  formatAmount(amount: number, currency = 'GHS'): string {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  // Get supported payment channels for Ghana
  getGhanaPaymentChannels(): string[] {
    return [
      'card',           // Visa, Mastercard, Verve
      'mobile_money',   // MTN Mobile Money, Vodafone Cash, AirtelTigo Money
      'bank',          // Bank account
      'ussd',          // USSD codes
      'bank_transfer'  // Bank transfer
    ]
  }

  // Get mobile money providers in Ghana
  getMobileMoneyProviders(): Array<{name: string, code: string}> {
    return [
      { name: 'MTN Mobile Money', code: 'mtn' },
      { name: 'Vodafone Cash', code: 'vod' },
      { name: 'AirtelTigo Money', code: 'tgo' }
    ]
  }
}

// Course pricing helper
export class CoursePricing {
  static readonly PRICES = {
    'power-systems': 2500,      // GHS 2,500
    'renewable-energy': 2200,   // GHS 2,200
    'it-proficiency': 1800,     // GHS 1,800
    'contractors': 2000,        // GHS 2,000
    'default': 2000             // GHS 2,000
  }

  static getCoursePrice(courseCategory: string): number {
    return this.PRICES[courseCategory as keyof typeof this.PRICES] || this.PRICES.default
  }

  static getCoursePriceInPesewas(courseCategory: string): number {
    return this.getCoursePrice(courseCategory) * 100
  }

  static formatCoursePrice(courseCategory: string): string {
    const price = this.getCoursePrice(courseCategory)
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS'
    }).format(price)
  }
}

// Global payment service instance
export const paymentService = new PaymentService()

// Type declarations for Paystack
declare global {
  interface Window {
    PaystackPop: {
      setup: (config: any) => {
        openIframe: () => void
      }
    }
  }
}

export default paymentService
