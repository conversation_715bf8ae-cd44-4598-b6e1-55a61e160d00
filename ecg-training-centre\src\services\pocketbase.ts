import PocketBase from 'pocketbase'

// Initialize PocketBase instance
export const pb = new PocketBase('http://localhost:8090')

// Types for our collections
export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar?: string
  created: string
  updated: string
}

export interface Course {
  id: string
  title: string
  description: string
  category: string
  duration: string
  price: number
  instructor: string
  image?: string
  syllabus?: string
  prerequisites?: string
  level: 'beginner' | 'intermediate' | 'advanced'
  status: 'active' | 'inactive'
  created: string
  updated: string
}

export interface CourseCategory {
  id: string
  name: string
  description: string
  icon?: string
  color?: string
  created: string
  updated: string
}

export interface Instructor {
  id: string
  name: string
  bio: string
  email: string
  phone?: string
  avatar?: string
  specialization: string
  experience: number
  created: string
  updated: string
}

export interface Enrollment {
  id: string
  user: string
  course: string
  status: 'pending' | 'enrolled' | 'completed' | 'cancelled'
  payment_status: 'pending' | 'paid' | 'failed'
  payment_id?: string
  enrolled_date: string
  completion_date?: string
  created: string
  updated: string
}

export interface NewsArticle {
  id: string
  title: string
  content: string
  excerpt: string
  author: string
  image?: string
  published: boolean
  published_date?: string
  created: string
  updated: string
}

export interface Newsletter {
  id: string
  email: string
  status: 'active' | 'unsubscribed'
  created: string
  updated: string
}

export interface ContactMessage {
  id: string
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  status: 'new' | 'read' | 'replied'
  created: string
  updated: string
}

// Authentication functions
export const authService = {
  // Login user
  async login(email: string, password: string) {
    try {
      const authData = await pb.collection('users').authWithPassword(email, password)
      return authData
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  },

  // Register user
  async register(userData: {
    email: string
    password: string
    passwordConfirm: string
    name: string
    phone?: string
  }) {
    try {
      const record = await pb.collection('users').create(userData)
      return record
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    }
  },

  // Logout user
  logout() {
    pb.authStore.clear()
  },

  // Check if user is authenticated
  isAuthenticated() {
    return pb.authStore.isValid
  },

  // Get current user
  getCurrentUser() {
    return pb.authStore.model
  }
}

// Course functions
export const courseService = {
  // Get all courses
  async getCourses(page = 1, perPage = 20, filter = '') {
    try {
      const records = await pb.collection('courses').getList(page, perPage, {
        filter,
        sort: '-created',
        expand: 'category,instructor'
      })
      return records
    } catch (error) {
      console.error('Error fetching courses:', error)
      throw error
    }
  },

  // Get course by ID
  async getCourse(id: string) {
    try {
      const record = await pb.collection('courses').getOne(id, {
        expand: 'category,instructor'
      })
      return record
    } catch (error) {
      console.error('Error fetching course:', error)
      throw error
    }
  },

  // Get courses by category
  async getCoursesByCategory(categoryId: string) {
    try {
      const records = await pb.collection('courses').getFullList({
        filter: `category = "${categoryId}"`,
        sort: '-created',
        expand: 'instructor'
      })
      return records
    } catch (error) {
      console.error('Error fetching courses by category:', error)
      throw error
    }
  }
}

// Course category functions
export const categoryService = {
  // Get all categories
  async getCategories() {
    try {
      const records = await pb.collection('course_categories').getFullList({
        sort: 'name'
      })
      return records
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error
    }
  }
}

// Enrollment functions
export const enrollmentService = {
  // Enroll user in course
  async enrollInCourse(courseId: string) {
    try {
      const userId = pb.authStore.model?.id
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const record = await pb.collection('enrollments').create({
        user: userId,
        course: courseId,
        status: 'pending',
        payment_status: 'pending',
        enrolled_date: new Date().toISOString()
      })
      return record
    } catch (error) {
      console.error('Error enrolling in course:', error)
      throw error
    }
  },

  // Get user enrollments
  async getUserEnrollments() {
    try {
      const userId = pb.authStore.model?.id
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const records = await pb.collection('enrollments').getFullList({
        filter: `user = "${userId}"`,
        sort: '-created',
        expand: 'course'
      })
      return records
    } catch (error) {
      console.error('Error fetching enrollments:', error)
      throw error
    }
  }
}

// Newsletter functions
export const newsletterService = {
  // Subscribe to newsletter
  async subscribe(email: string) {
    try {
      const record = await pb.collection('newsletter').create({
        email,
        status: 'active'
      })
      return record
    } catch (error) {
      console.error('Error subscribing to newsletter:', error)
      throw error
    }
  }
}

// Contact functions
export const contactService = {
  // Send contact message
  async sendMessage(messageData: {
    name: string
    email: string
    phone?: string
    subject: string
    message: string
  }) {
    try {
      const record = await pb.collection('contact_messages').create({
        ...messageData,
        status: 'new'
      })
      return record
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }
}

// News functions
export const newsService = {
  // Get published news articles
  async getNews(page = 1, perPage = 10) {
    try {
      const records = await pb.collection('news').getList(page, perPage, {
        filter: 'published = true',
        sort: '-published_date'
      })
      return records
    } catch (error) {
      console.error('Error fetching news:', error)
      throw error
    }
  },

  // Get news article by ID
  async getNewsArticle(id: string) {
    try {
      const record = await pb.collection('news').getOne(id)
      return record
    } catch (error) {
      console.error('Error fetching news article:', error)
      throw error
    }
  }
}

export default pb
