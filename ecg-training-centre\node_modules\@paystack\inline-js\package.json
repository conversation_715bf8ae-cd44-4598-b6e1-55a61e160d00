{"name": "@paystack/inline-js", "version": "2.22.6", "description": "Client-side library to load the Paystack checkout form", "main": "lib/inline.js", "module": "es/inline.js", "files": ["dist", "lib", "es", "PUBLIC_DOCS"], "repository": {"type": "git", "url": "git+https://github.com/PaystackHQ/inline-js.git"}, "keywords": ["paystack", "inline js", "payments", "online", "nigeria"], "author": "Paystack", "license": "ISC", "bugs": {"url": "https://github.com/PaystackHQ/inline-js/issues"}, "homepage": "https://github.com/PaystackHQ/inline-js#readme", "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-eslint": "^8.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^4.0.0", "babel-jest": "^27.5.1", "commitizen": "^4.2.4", "cross-env": "^5.1.6", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "fast-async": "^6.3.7", "http-server": "^14.1.0", "husky": "^6.0.0", "jest": "^27.5.1", "jest-fetch-mock": "^3.0.3", "nodent-runtime": "^3.2.1", "prettier": "^2.5.1", "rimraf": "^2.6.3", "rollup": "^2.70.0", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-terser": "^7.0.2", "semantic-release": "^24.2.3"}, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "dev": "cross-env NODE_ENV=staging rollup --config rollup.config.js && http-server ./dev -o", "dev:local": "cross-env NODE_ENV=dev rollup --config rollup.config.js && http-server ./dev -o", "build:staging": "cross-env NODE_ENV=staging rollup --config rollup.config.js", "build:prod": "cross-env NODE_ENV=production rollup --config rollup.config.js", "build": "npm run build:prod", "test": "jest", "prepare": "husky install", "lint": "npx eslint src"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/npm", "@semantic-release/github"], "branches": ["master", {"name": "dev", "prerelease": true}]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && git cz --hook || true"}}, "engines": {"node": ">= 22.0.0"}}