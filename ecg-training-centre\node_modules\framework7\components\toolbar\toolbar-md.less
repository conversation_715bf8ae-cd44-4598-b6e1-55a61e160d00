.md {
  .toolbar {
    .link {
      justify-content: center;
      padding: 0 12px;
      min-width: 48px;
    }
    a.icon-only {
      min-width: 0;
      flex-shrink: 0;
    }
  }
  .tabbar,
  .tabbar-icons {
    .tab-link,
    .link {
      padding-left: 0;
      padding-right: 0;
    }
    a.icon-only {
      flex-shrink: initial;
    }
    .tab-link {
      transition-duration: 300ms;
      overflow: hidden;
      position: relative;
    }
    i.icon {
      position: relative;
      &::before {
        content: '';
        width: 64px;
        height: 32px;
        border-radius: 32px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%) scaleX(0.5);
        background: var(--f7-tabbar-link-active-icon-bg-color);
        opacity: 0;
        transition-duration: 200ms;
        z-index: -1;
      }
      + .tabbar-label {
        margin-top: 8px;
      }
    }
    .tab-link-active i.icon::before {
      opacity: 1;
      transform: translateX(-50%) translateY(-50%) scaleX(1);
    }
  }

  .tabbar-icons {
    .tab-link,
    .link {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }
  .tabbar-label {
    max-width: 100%;
    overflow: hidden;
    line-height: 1.2;
  }

  // Scrollable
  .tabbar-scrollable {
    .toolbar-inner {
      overflow: auto;
    }
    .tab-link,
    .link {
      padding: 0 12px;
    }
  }
}
