import {
  app_class_default,
  clicks_default,
  device_default,
  history_default,
  modal_default,
  navbar_default,
  resize_default,
  router_default,
  service_worker_default,
  statusbar_default,
  store_default,
  subnavbar_default,
  support_default,
  toolbar_default,
  touch_default,
  touch_ripple_default,
  utils_default,
  view_default
} from "./chunk-YVUCKT22.js";
import {
  Comment,
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createStaticVNode,
  createTextVNode,
  createVNode,
  guardReactiveProps,
  h,
  inject,
  mergeProps,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onBeforeUnmount,
  onBeforeUpdate,
  onMounted,
  onUpdated,
  openBlock,
  provide,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  toDisplayString,
  toRaw,
  watch,
  withCtx
} from "./chunk-VZXQDS5F.js";

// node_modules/framework7/framework7-lite.esm.js
app_class_default.use([device_default, support_default, utils_default, resize_default, touch_default, clicks_default, router_default, history_default, service_worker_default, store_default, statusbar_default, view_default, navbar_default, toolbar_default, subnavbar_default, touch_ripple_default, modal_default]);
var framework7_lite_esm_default = app_class_default;

// node_modules/framework7-vue/shared/utils.js
function noUndefinedProps(obj) {
  const o = {};
  Object.keys(obj).forEach((key) => {
    if (typeof obj[key] !== "undefined") o[key] = obj[key];
  });
  return o;
}
function isStringProp(val) {
  return typeof val === "string" && val !== "";
}
function isObject(o) {
  return typeof o === "object" && o !== null && o.constructor && o.constructor === Object;
}
function now() {
  return Date.now();
}
function extend() {
  let deep = true;
  let to;
  let from;
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  if (typeof args[0] === "boolean") {
    [deep, to] = args;
    args.splice(0, 2);
    from = args;
  } else {
    [to] = args;
    args.splice(0, 1);
    from = args;
  }
  for (let i = 0; i < from.length; i += 1) {
    const nextSource = args[i];
    if (nextSource !== void 0 && nextSource !== null) {
      const keysArray = Object.keys(Object(nextSource));
      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {
        const nextKey = keysArray[nextIndex];
        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);
        if (desc !== void 0 && desc.enumerable) {
          if (!deep) {
            to[nextKey] = nextSource[nextKey];
          } else if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {
            extend(to[nextKey], nextSource[nextKey]);
          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {
            to[nextKey] = {};
            extend(to[nextKey], nextSource[nextKey]);
          } else {
            to[nextKey] = nextSource[nextKey];
          }
        }
      }
    }
  }
  return to;
}
function classNames() {
  const classes = [];
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    args[_key2] = arguments[_key2];
  }
  args.forEach((arg) => {
    if (typeof arg === "object" && arg.constructor === Object) {
      Object.keys(arg).forEach((key) => {
        if (arg[key]) classes.push(key);
      });
    } else if (arg) classes.push(arg);
  });
  const uniqueClasses = [];
  classes.forEach((c) => {
    if (uniqueClasses.indexOf(c) < 0) uniqueClasses.push(c);
  });
  return uniqueClasses.join(" ");
}
var routerIdCounter = 0;
var routerComponentIdCounter = 0;
function unsetRouterIds() {
  routerIdCounter = 0;
  routerComponentIdCounter = 0;
}
function getRouterId() {
  routerIdCounter += 1;
  return `${now()}_${routerIdCounter}`;
}
function getComponentId() {
  routerComponentIdCounter += 1;
  return `${now()}_${routerComponentIdCounter}`;
}
function getChildren(slots, slotName) {
  if (slotName === void 0) {
    slotName = "default";
  }
  const result = [];
  const getElementsChildren = (els) => {
    if (!Array.isArray(els)) {
      return;
    }
    els.forEach((vnode) => {
      const isFragment = vnode.type === Fragment;
      if (isFragment && vnode.children) {
        getElementsChildren(vnode.children);
      } else if (vnode.type && vnode.type !== Comment) {
        result.push(vnode);
      }
    });
  };
  if (slots[slotName]) getElementsChildren(slots[slotName]());
  return result;
}

// node_modules/framework7-vue/shared/f7.js
var f7;
var f7events;
var theme = {};
var f7routers = {
  views: [],
  tabs: [],
  modals: null
};
var setTheme = () => {
  if (!f7) return;
  theme.ios = f7.theme === "ios";
  theme.md = f7.theme === "md";
};
var cleanup = () => {
  unsetRouterIds();
  delete theme.ios;
  delete theme.md;
  f7routers.views = [];
  f7routers.tabs = [];
  f7routers.modals = null;
};
var f7initEvents = () => {
  f7events = new framework7_lite_esm_default.Events();
};
var f7init = function(rootEl, params, init) {
  if (params === void 0) {
    params = {};
  }
  if (init === void 0) {
    init = true;
  }
  const f7Params = extend({}, params, {
    el: rootEl,
    init
  });
  if (typeof params.store !== "undefined") f7Params.store = params.store;
  if (!f7Params.routes) f7Params.routes = [];
  if (f7Params.userAgent && (f7Params.theme === "auto" || !f7Params.theme)) {
    const device = framework7_lite_esm_default.getDevice({
      userAgent: f7Params.userAgent
    }, true);
    theme.ios = !!device.ios;
    theme.md = !theme.ios;
  }
  if (f7 && typeof window !== "undefined") return;
  if (typeof window === "undefined") cleanup();
  const instance = new framework7_lite_esm_default(f7Params);
  f7 = instance;
  setTheme();
  if (instance.initialized) {
    f7 = instance;
    f7events.emit("ready", f7);
  } else {
    instance.on("init", () => {
      f7 = instance;
      f7events.emit("ready", f7);
    });
  }
};
var f7ready = (callback) => {
  if (!callback) return;
  if (f7 && f7.initialized) callback(f7);
  else {
    f7events.once("ready", callback);
  }
};

// node_modules/framework7-vue/shared/mixins.js
function colorClasses(props) {
  const {
    color,
    textColor,
    bgColor,
    borderColor,
    rippleColor,
    dark
  } = props;
  return {
    dark,
    [`color-${color}`]: color,
    [`text-color-${textColor}`]: textColor,
    [`bg-color-${bgColor}`]: bgColor,
    [`border-color-${borderColor}`]: borderColor,
    [`ripple-color-${rippleColor}`]: rippleColor
  };
}
var colorProps = {
  color: String,
  colorTheme: String,
  textColor: String,
  bgColor: String,
  borderColor: String,
  rippleColor: String,
  dark: Boolean
};
var iconProps = {
  icon: String,
  iconMaterial: String,
  iconF7: String,
  iconIos: String,
  iconMd: String,
  iconColor: String,
  iconSize: [String, Number],
  iconBadge: [String, Number],
  iconBadgeColor: String
};
var routerProps = {
  back: Boolean,
  external: Boolean,
  force: Boolean,
  animate: {
    type: Boolean,
    default: void 0
  },
  ignoreCache: Boolean,
  reloadCurrent: Boolean,
  reloadAll: Boolean,
  reloadPrevious: Boolean,
  reloadDetail: {
    type: Boolean,
    default: void 0
  },
  routeTabId: String,
  view: String,
  routeProps: Object,
  preventRouter: Boolean,
  transition: String,
  openIn: String
};
function routerAttrs(props) {
  const {
    force,
    reloadCurrent,
    reloadPrevious,
    reloadAll,
    reloadDetail,
    animate,
    ignoreCache,
    routeTabId,
    view,
    transition,
    openIn
  } = props;
  let dataAnimate;
  if ("animate" in props && typeof animate !== "undefined") {
    dataAnimate = animate.toString();
  }
  let dataReloadDetail;
  if ("reloadDetail" in props && typeof reloadDetail !== "undefined") {
    dataReloadDetail = reloadDetail.toString();
  }
  return {
    "data-force": force || void 0,
    "data-reload-current": reloadCurrent || void 0,
    "data-reload-all": reloadAll || void 0,
    "data-reload-previous": reloadPrevious || void 0,
    "data-reload-detail": dataReloadDetail,
    "data-animate": dataAnimate,
    "data-ignore-cache": ignoreCache || void 0,
    "data-route-tab-id": routeTabId || void 0,
    "data-view": isStringProp(view) ? view : void 0,
    "data-transition": isStringProp(transition) ? transition : void 0,
    "data-open-in": isStringProp(openIn) ? openIn : void 0
  };
}
function routerClasses(props) {
  const {
    back,
    linkBack,
    external,
    preventRouter
  } = props;
  return {
    back: back || linkBack,
    external,
    "prevent-router": preventRouter
  };
}
var actionsProps = {
  searchbarEnable: [Boolean, String],
  searchbarDisable: [Boolean, String],
  searchbarClear: [Boolean, String],
  searchbarToggle: [Boolean, String],
  // Panel
  panelOpen: [Boolean, String],
  panelClose: [Boolean, String],
  panelToggle: [Boolean, String],
  // Popup
  popupOpen: [Boolean, String],
  popupClose: [Boolean, String],
  // Actions
  actionsOpen: [Boolean, String],
  actionsClose: [Boolean, String],
  // Popover
  popoverOpen: [Boolean, String],
  popoverClose: [Boolean, String],
  // Login Screen
  loginScreenOpen: [Boolean, String],
  loginScreenClose: [Boolean, String],
  // Picker
  sheetOpen: [Boolean, String],
  sheetClose: [Boolean, String],
  // Sortable
  sortableEnable: [Boolean, String],
  sortableDisable: [Boolean, String],
  sortableToggle: [Boolean, String],
  // Card
  cardOpen: [Boolean, String],
  cardPreventOpen: [Boolean, String],
  cardClose: [Boolean, String]
};
function actionsAttrs(props) {
  const {
    searchbarEnable,
    searchbarDisable,
    searchbarClear,
    searchbarToggle,
    panelOpen,
    panelClose,
    panelToggle,
    popupOpen,
    popupClose,
    actionsOpen,
    actionsClose,
    popoverOpen,
    popoverClose,
    loginScreenOpen,
    loginScreenClose,
    sheetOpen,
    sheetClose,
    sortableEnable,
    sortableDisable,
    sortableToggle,
    cardOpen,
    cardClose
  } = props;
  return {
    "data-searchbar": isStringProp(searchbarEnable) && searchbarEnable || isStringProp(searchbarDisable) && searchbarDisable || isStringProp(searchbarClear) && searchbarClear || isStringProp(searchbarToggle) && searchbarToggle || void 0,
    "data-panel": isStringProp(panelOpen) && panelOpen || isStringProp(panelClose) && panelClose || isStringProp(panelToggle) && panelToggle || void 0,
    "data-popup": isStringProp(popupOpen) && popupOpen || isStringProp(popupClose) && popupClose || void 0,
    "data-actions": isStringProp(actionsOpen) && actionsOpen || isStringProp(actionsClose) && actionsClose || void 0,
    "data-popover": isStringProp(popoverOpen) && popoverOpen || isStringProp(popoverClose) && popoverClose || void 0,
    "data-sheet": isStringProp(sheetOpen) && sheetOpen || isStringProp(sheetClose) && sheetClose || void 0,
    "data-login-screen": isStringProp(loginScreenOpen) && loginScreenOpen || isStringProp(loginScreenClose) && loginScreenClose || void 0,
    "data-sortable": isStringProp(sortableEnable) && sortableEnable || isStringProp(sortableDisable) && sortableDisable || isStringProp(sortableToggle) && sortableToggle || void 0,
    "data-card": isStringProp(cardOpen) && cardOpen || isStringProp(cardClose) && cardClose || void 0
  };
}
function actionsClasses(props) {
  const {
    searchbarEnable,
    searchbarDisable,
    searchbarClear,
    searchbarToggle,
    panelOpen,
    panelClose,
    panelToggle,
    popupOpen,
    popupClose,
    actionsClose,
    actionsOpen,
    popoverOpen,
    popoverClose,
    loginScreenOpen,
    loginScreenClose,
    sheetOpen,
    sheetClose,
    sortableEnable,
    sortableDisable,
    sortableToggle,
    cardOpen,
    cardPreventOpen,
    cardClose
  } = props;
  return {
    "searchbar-enable": searchbarEnable || searchbarEnable === "",
    "searchbar-disable": searchbarDisable || searchbarDisable === "",
    "searchbar-clear": searchbarClear || searchbarClear === "",
    "searchbar-toggle": searchbarToggle || searchbarToggle === "",
    "panel-close": panelClose || panelClose === "",
    "panel-open": panelOpen || panelOpen === "",
    "panel-toggle": panelToggle || panelToggle === "",
    "popup-close": popupClose || popupClose === "",
    "popup-open": popupOpen || popupOpen === "",
    "actions-close": actionsClose || actionsClose === "",
    "actions-open": actionsOpen || actionsOpen === "",
    "popover-close": popoverClose || popoverClose === "",
    "popover-open": popoverOpen || popoverOpen === "",
    "sheet-close": sheetClose || sheetClose === "",
    "sheet-open": sheetOpen || sheetOpen === "",
    "login-screen-close": loginScreenClose || loginScreenClose === "",
    "login-screen-open": loginScreenOpen || loginScreenOpen === "",
    "sortable-enable": sortableEnable || sortableEnable === "",
    "sortable-disable": sortableDisable || sortableDisable === "",
    "sortable-toggle": sortableToggle || sortableToggle === "",
    "card-close": cardClose || cardClose === "",
    "card-open": cardOpen || cardOpen === "",
    "card-prevent-open": cardPreventOpen || cardPreventOpen === ""
  };
}

// node_modules/framework7-vue/shared/modal-state-classes.js
var modalStateClasses = function(_temp) {
  let {
    isOpened,
    isClosing
  } = _temp === void 0 ? {} : _temp;
  return {
    "modal-in": isOpened && !isClosing,
    "modal-out": isClosing
  };
};

// node_modules/framework7-vue/components/popup.js
function render(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var popup_default = {
  name: "f7-popup",
  render,
  props: {
    tabletFullscreen: Boolean,
    opened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: {
      type: [String, Object],
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    closeOnEscape: {
      type: Boolean,
      default: void 0
    },
    swipeToClose: {
      type: [Boolean, String],
      default: false
    },
    swipeHandler: {
      type: [String, Object],
      default: void 0
    },
    push: Boolean,
    containerEl: {
      type: [String, Object],
      default: void 0
    },
    ...colorProps
  },
  emits: ["popup:swipestart", "popup:swipemove", "popup:swipeend", "popup:swipeclose", "popup:open", "popup:opened", "popup:close", "popup:closed", "update:opened"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const f7Popup = ref(null);
    let isOpened = props.opened;
    let isClosing = false;
    const elRef = ref(null);
    const onSwipeStart = (instance) => {
      emit("popup:swipestart", instance);
    };
    const onSwipeMove = (instance) => {
      emit("popup:swipemove", instance);
    };
    const onSwipeEnd = (instance) => {
      emit("popup:swipeend", instance);
    };
    const onSwipeClose = (instance) => {
      emit("popup:swipeclose", instance);
    };
    const onOpen = (instance) => {
      isOpened = true;
      isClosing = false;
      emit("popup:open", instance);
      emit("update:opened", true);
    };
    const onOpened = (instance) => {
      emit("popup:opened", instance);
    };
    const onClose = (instance) => {
      isOpened = false;
      isClosing = true;
      emit("popup:close", instance);
    };
    const onClosed = (instance) => {
      isClosing = false;
      emit("popup:closed", instance);
      emit("update:opened", false);
    };
    watch(() => props.opened, (value) => {
      if (!f7Popup.value) return;
      if (value) {
        f7Popup.value.open();
      } else {
        f7Popup.value.close();
      }
    });
    onMounted(() => {
      if (!elRef.value) return;
      const popupParams = {
        el: elRef.value,
        on: {
          swipeStart: onSwipeStart,
          swipeMove: onSwipeMove,
          swipeEnd: onSwipeEnd,
          swipeClose: onSwipeClose,
          open: onOpen,
          opened: onOpened,
          close: onClose,
          closed: onClosed
        }
      };
      const {
        closeByBackdropClick,
        closeOnEscape,
        animate,
        backdrop,
        backdropEl,
        swipeToClose,
        swipeHandler,
        containerEl
      } = props;
      if (typeof closeByBackdropClick !== "undefined") popupParams.closeByBackdropClick = closeByBackdropClick;
      if (typeof closeOnEscape !== "undefined") popupParams.closeOnEscape = closeOnEscape;
      if (typeof animate !== "undefined") popupParams.animate = animate;
      if (typeof backdrop !== "undefined") popupParams.backdrop = backdrop;
      if (typeof backdropEl !== "undefined") popupParams.backdropEl = backdropEl;
      if (typeof swipeToClose !== "undefined") popupParams.swipeToClose = swipeToClose;
      if (typeof swipeHandler !== "undefined") popupParams.swipeHandler = swipeHandler;
      if (typeof containerEl !== "undefined") popupParams.containerEl = containerEl;
      f7ready(() => {
        f7Popup.value = f7.popup.create(popupParams);
        if (props.opened) {
          f7Popup.value.open(false, true);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7Popup.value) {
        f7Popup.value.destroy();
      }
      f7Popup.value = null;
    });
    const classes = computed(() => classNames("popup", {
      "popup-tablet-fullscreen": props.tabletFullscreen,
      "popup-push": props.push
    }, modalStateClasses({
      isOpened,
      isClosing
    }), colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/shared/use-tab.js
var useTab = (elRef, emit) => {
  const onTabShow = (el) => {
    if (elRef.value !== el) return;
    emit("tab:show", el);
  };
  const onTabHide = (el) => {
    if (elRef.value !== el) return;
    emit("tab:hide", el);
  };
  onMounted(() => {
    if (!elRef.value) return;
    f7ready(() => {
      f7.on("tabShow", onTabShow);
      f7.on("tabHide", onTabHide);
    });
  });
  onBeforeUnmount(() => {
    if (!f7) return;
    f7.off("tabShow", onTabShow);
    f7.off("tabHide", onTabHide);
  });
};

// node_modules/framework7-vue/shared/get-router-initial-component.js
var getRouterInitialComponent = (router, initialComponent) => {
  let initialComponentData;
  const {
    initialUrl
  } = router.getInitialUrl();
  const initialRoute = router.findMatchingRoute(initialUrl);
  let routeProps = {};
  if (initialRoute && initialRoute.route && initialRoute.route.options) {
    routeProps = initialRoute.route.options.props;
  }
  const isMasterRoute = (route) => {
    if (route.master === true) return true;
    if (typeof route.master === "function") return route.master(router.app);
    return false;
  };
  if (initialRoute && initialRoute.route && (initialRoute.route.component || initialRoute.route.asyncComponent) && !isMasterRoute(initialRoute.route)) {
    initialComponentData = {
      component: initialRoute.route.component || initialRoute.route.asyncComponent,
      initialComponent,
      id: getComponentId(),
      isAsync: !!initialRoute.route.asyncComponent,
      props: {
        f7route: initialRoute,
        f7router: router,
        ...routeProps,
        ...initialRoute.params
      }
    };
  }
  return {
    initialPage: initialComponentData,
    initialRoute
  };
};

// node_modules/framework7-vue/components/view.js
var view_default2 = {
  name: "f7-view",
  props: {
    tab: Boolean,
    tabActive: Boolean,
    name: String,
    initRouterOnTabShow: {
      type: Boolean,
      default: void 0
    },
    router: {
      type: Boolean,
      default: true
    },
    linksView: [Object, String],
    url: String,
    main: {
      type: Boolean,
      default: void 0
    },
    xhrCache: {
      type: Boolean,
      default: void 0
    },
    xhrCacheIgnore: Array,
    xhrCacheIgnoreGetParameters: {
      type: Boolean,
      default: void 0
    },
    xhrCacheDuration: Number,
    preloadPreviousPage: {
      type: Boolean,
      default: void 0
    },
    allowDuplicateUrls: {
      type: Boolean,
      default: void 0
    },
    reloadPages: {
      type: Boolean,
      default: void 0
    },
    reloadDetail: {
      type: Boolean,
      default: void 0
    },
    masterDetailResizable: {
      type: Boolean,
      default: void 0
    },
    masterDetailBreakpoint: Number,
    removeElements: {
      type: Boolean,
      default: void 0
    },
    removeElementsWithTimeout: {
      type: Boolean,
      default: void 0
    },
    removeElementsTimeout: Number,
    restoreScrollTopOnBack: {
      type: Boolean,
      default: void 0
    },
    loadInitialPage: {
      type: Boolean,
      default: void 0
    },
    // Swipe Back
    iosSwipeBack: {
      type: Boolean,
      default: void 0
    },
    iosSwipeBackAnimateShadow: {
      type: Boolean,
      default: void 0
    },
    iosSwipeBackAnimateOpacity: {
      type: Boolean,
      default: void 0
    },
    iosSwipeBackActiveArea: Number,
    iosSwipeBackThreshold: Number,
    mdSwipeBack: {
      type: Boolean,
      default: void 0
    },
    mdSwipeBackAnimateShadow: {
      type: Boolean,
      default: void 0
    },
    mdSwipeBackAnimateOpacity: {
      type: Boolean,
      default: void 0
    },
    mdSwipeBackActiveArea: Number,
    mdSwipeBackThreshold: Number,
    // Push State
    browserHistory: {
      type: Boolean,
      default: void 0
    },
    browserHistoryRoot: String,
    browserHistoryAnimate: {
      type: Boolean,
      default: void 0
    },
    browserHistoryAnimateOnLoad: {
      type: Boolean,
      default: void 0
    },
    browserHistorySeparator: String,
    browserHistoryOnLoad: {
      type: Boolean,
      default: void 0
    },
    browserHistoryInitialMatch: {
      type: Boolean,
      default: true
    },
    browserHistoryStoreHistory: {
      type: Boolean,
      default: void 0
    },
    // Animate Pages
    animate: {
      type: Boolean,
      default: void 0
    },
    transition: String,
    // iOS Dynamic Navbar
    iosDynamicNavbar: {
      type: Boolean,
      default: void 0
    },
    // Animate iOS Navbar Back Icon
    iosAnimateNavbarBackIcon: {
      type: Boolean,
      default: void 0
    },
    // MD Theme delay
    materialPageLoadDelay: Number,
    passRouteQueryToRequest: {
      type: Boolean,
      default: void 0
    },
    passRouteParamsToRequest: {
      type: Boolean,
      default: void 0
    },
    routes: Array,
    routesAdd: Array,
    // Routes hooks
    routesBeforeEnter: [Function, Array],
    routesBeforeLeave: [Function, Array],
    unloadTabContent: {
      type: Boolean,
      default: void 0
    },
    init: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["view:init", "view:resize", "swipeback:move", "swipeback:beforechange", "swipeback:afterchange", "swipeback:beforereset", "swipeback:afterreset", "tab:hide", "tab:show"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const initialPageComponent = null;
    const shouldInitRouter = !(props.initRouterOnTabShow && props.tab && !props.tabActive);
    let f7View = null;
    const elRef = ref(null);
    let routerData = null;
    let initialPage;
    let initialRoute;
    const onViewInit = (view) => {
      emit("view:init", view);
      if (!props.init) {
        routerData.instance = view;
        f7View = routerData.instance;
      }
    };
    const getViewParams = () => {
      const routes = toRaw(props.routes || []);
      const routesAdd = toRaw(props.routesAdd || []);
      return noUndefinedProps({
        ...props,
        routes,
        routesAdd
      });
    };
    if (f7 && !f7View && props.init) {
      const routerId = getRouterId();
      f7View = f7.views.create(elRef.value, {
        ...getViewParams(),
        routerId,
        init: false,
        on: {
          init: onViewInit
        }
      });
      routerData = {
        routerId,
        instance: f7View
      };
      f7routers.views.push(routerData);
      if (shouldInitRouter && f7View && f7View.router && (props.url || props.main)) {
        const initialData = getRouterInitialComponent(f7View.router, initialPageComponent);
        initialPage = initialData.initialPage;
        initialRoute = initialData.initialRoute;
        if (initialRoute && initialRoute.route && initialRoute.route.masterRoute) {
          initialPage = void 0;
          initialRoute = void 0;
        }
      }
    }
    const pages = ref(initialPage ? [initialPage] : []);
    const setPages = (newPages) => {
      newPages.forEach((page) => {
        page.component = toRaw(page.component);
      });
      pages.value = newPages;
    };
    const onResize = (view, width) => {
      emit("view:resize", width);
    };
    const onSwipeBackMove = (data) => {
      const swipeBackData = data;
      emit("swipeback:move", swipeBackData);
    };
    const onSwipeBackBeforeChange = (data) => {
      const swipeBackData = data;
      emit("swipeback:beforechange", swipeBackData);
    };
    const onSwipeBackAfterChange = (data) => {
      const swipeBackData = data;
      emit("swipeback:afterchange", swipeBackData);
    };
    const onSwipeBackBeforeReset = (data) => {
      const swipeBackData = data;
      emit("swipeback:beforereset", swipeBackData);
    };
    const onSwipeBackAfterReset = (data) => {
      const swipeBackData = data;
      emit("swipeback:afterreset", swipeBackData);
    };
    onMounted(() => {
      f7ready(() => {
        if (f7View) {
          routerData.el = elRef.value;
          routerData.pages = pages.value;
          routerData.setPages = (newPages) => {
            setPages([...newPages]);
          };
          if (initialPage && initialPage.isAsync && !initialPage.initialComponent) {
            initialPage.component().then(() => {
              setTimeout(() => {
                f7View.init(elRef.value);
                if (initialPage) {
                  initialPage.el = f7View.router.currentPageEl;
                  if (initialRoute && initialRoute.route && initialRoute.route.keepAlive) {
                    initialRoute.route.keepAliveData = {
                      pageEl: initialPage.el
                    };
                  }
                }
              }, 100);
            });
          } else {
            f7View.init(elRef.value);
            if (initialPage) {
              initialPage.el = f7View.router.currentPageEl;
              if (initialRoute && initialRoute.route && initialRoute.route.keepAlive) {
                initialRoute.route.keepAliveData = {
                  pageEl: initialPage.el
                };
              }
            }
          }
        } else {
          const routerId = getRouterId();
          routerData = {
            el: elRef.value,
            routerId,
            pages: pages.value,
            instance: f7View,
            setPages(newPages) {
              setPages([...newPages]);
            }
          };
          f7routers.views.push(routerData);
          routerData.instance = f7.views.create(elRef.value, {
            routerId,
            ...getViewParams(),
            on: {
              init: onViewInit
            }
          });
          f7View = routerData.instance;
        }
        if (!props.init) return;
        f7View.on("resize", onResize);
        f7View.on("swipebackMove", onSwipeBackMove);
        f7View.on("swipebackBeforeChange", onSwipeBackBeforeChange);
        f7View.on("swipebackAfterChange", onSwipeBackAfterChange);
        f7View.on("swipebackBeforeReset", onSwipeBackBeforeReset);
        f7View.on("swipebackAfterReset", onSwipeBackAfterReset);
      });
    });
    onBeforeUnmount(() => {
      if (f7View) {
        f7View.off("resize", onResize);
        f7View.off("swipebackMove", onSwipeBackMove);
        f7View.off("swipebackBeforeChange", onSwipeBackBeforeChange);
        f7View.off("swipebackAfterChange", onSwipeBackAfterChange);
        f7View.off("swipebackBeforeReset", onSwipeBackBeforeReset);
        f7View.off("swipebackAfterReset", onSwipeBackAfterReset);
        if (f7View.destroy) f7View.destroy();
        f7View = null;
      }
      f7routers.views.splice(f7routers.views.indexOf(routerData), 1);
      routerData = null;
    });
    onUpdated(() => {
      if (!routerData || !f7) return;
      f7events.emit("viewRouterDidUpdate", routerData);
    });
    useTab(elRef, emit);
    const classes = computed(() => classNames("view", {
      "view-main": props.main,
      "tab-active": props.tabActive,
      tab: props.tab
    }, colorClasses(props)));
    const getComponent = (page) => toRaw(page.component);
    const getProps = (page) => {
      const {
        component: pageComponent,
        props: pageProps
      } = page;
      let keys = [];
      const passProps = {};
      if (pageComponent && pageComponent.props) {
        if (Array.isArray(pageComponent.props)) keys = pageComponent.props.filter((prop) => typeof prop === "string");
        else keys = Object.keys(pageComponent.props);
      }
      keys.forEach((key) => {
        if (key in pageProps) passProps[key] = pageProps[key];
      });
      return passProps;
    };
    return () => {
      return h("div", {
        ref: elRef,
        class: classes.value
      }, [slots.default && slots.default(), ...pages.value.map((page) => h(getComponent(page), {
        key: page.id,
        ...getProps(page)
      }))]);
    };
  }
};

// node_modules/framework7-vue/components/login-screen.js
function render2(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var login_screen_default = {
  name: "f7-login-screen",
  render: render2,
  props: {
    opened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    containerEl: {
      type: [String, Object],
      default: void 0
    },
    ...colorProps
  },
  emits: ["loginscreen:open", "loginscreen:opened", "loginscreen:close", "loginscreen:closed", "update:opened"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const f7LoginScreen = ref(null);
    let isOpened = props.opened;
    let isClosing = false;
    const elRef = ref(null);
    const onOpen = (instance) => {
      isOpened = true;
      isClosing = false;
      emit("loginscreen:open", instance);
      emit("update:opened", true);
    };
    const onOpened = (instance) => {
      emit("loginscreen:opened", instance);
    };
    const onClose = (instance) => {
      isOpened = false;
      isClosing = true;
      emit("loginscreen:close", instance);
    };
    const onClosed = (instance) => {
      isClosing = false;
      emit("loginscreen:closed", instance);
      emit("update:opened", false);
    };
    watch(() => props.opened, (value) => {
      if (!f7LoginScreen.value) return;
      if (value) {
        f7LoginScreen.value.open();
      } else {
        f7LoginScreen.value.close();
      }
    });
    onMounted(() => {
      if (!elRef.value) return;
      f7ready(() => {
        const loginScreenParams = {
          el: elRef.value,
          on: {
            open: onOpen,
            opened: onOpened,
            close: onClose,
            closed: onClosed
          }
        };
        if (typeof props.animate !== "undefined") {
          loginScreenParams.animate = props.animate;
        }
        if (typeof props.containerEl !== "undefined") {
          loginScreenParams.containerEl = props.containerEl;
        }
        f7LoginScreen.value = f7.loginScreen.create(loginScreenParams);
        if (props.opened) {
          f7LoginScreen.value.open(false);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7LoginScreen.value) {
        f7LoginScreen.value.destroy();
      }
      f7LoginScreen.value = null;
    });
    const classes = computed(() => classNames("login-screen", modalStateClasses({
      isOpened,
      isClosing
    }), colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/sheet.js
var sheet_default = {
  name: "f7-sheet",
  props: {
    opened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    top: Boolean,
    bottom: Boolean,
    position: String,
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: {
      type: [String, Object],
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    closeByOutsideClick: {
      type: Boolean,
      default: void 0
    },
    closeOnEscape: {
      type: Boolean,
      default: void 0
    },
    push: Boolean,
    swipeToClose: {
      type: Boolean,
      default: void 0
    },
    swipeToStep: {
      type: Boolean,
      default: void 0
    },
    swipeHandler: {
      type: [String, Object],
      default: void 0
    },
    containerEl: {
      type: [String, Object],
      default: void 0
    },
    breakpoints: {
      type: Array,
      default: () => void 0
    },
    backdropBreakpoint: {
      type: Number,
      default: void 0
    },
    pushBreakpoint: {
      type: Number,
      default: void 0
    },
    ...colorProps
  },
  emits: ["sheet:stepprogress", "sheet:stepopen", "sheet:stepclose", "sheet:open", "sheet:opened", "sheet:close", "sheet:closed", "sheet:breakpoint", "update:opened"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    let f7Sheet = null;
    let isOpened = props.opened;
    let isClosing = false;
    const elRef = ref(null);
    const onBreakpoint = (instance, breakpoint) => {
      emit("sheet:breakpoint", instance, breakpoint);
    };
    const onStepProgress = (instance, progress) => {
      emit("sheet:stepprogress", instance, progress);
    };
    const onStepOpen = (instance) => {
      emit("sheet:stepopen", instance);
    };
    const onStepClose = (instance) => {
      emit("sheet:stepclose", instance);
    };
    const onOpen = (instance) => {
      isOpened = true;
      isClosing = false;
      emit("sheet:open", instance);
      emit("update:opened", true);
    };
    const onOpened = (instance) => {
      emit("sheet:opened", instance);
    };
    const onClose = (instance) => {
      isOpened = false;
      isClosing = true;
      emit("sheet:close", instance);
    };
    const onClosed = (instance) => {
      isClosing = false;
      emit("sheet:closed", instance);
      emit("update:opened", false);
    };
    onMounted(() => {
      if (!elRef.value) return;
      const sheetParams = {
        el: elRef.value,
        on: {
          open: onOpen,
          opened: onOpened,
          close: onClose,
          closed: onClosed,
          stepOpen: onStepOpen,
          stepClose: onStepClose,
          stepProgress: onStepProgress,
          breakpoint: onBreakpoint
        }
      };
      const {
        animate,
        backdrop,
        backdropEl,
        closeByBackdropClick,
        closeByOutsideClick,
        closeOnEscape,
        swipeToClose,
        swipeToStep,
        swipeHandler,
        containerEl,
        breakpoints,
        backdropBreakpoint,
        pushBreakpoint
      } = props;
      if (typeof animate !== "undefined") sheetParams.animate = animate;
      if (typeof backdrop !== "undefined") sheetParams.backdrop = backdrop;
      if (typeof backdropEl !== "undefined") sheetParams.backdropEl = backdropEl;
      if (typeof closeByBackdropClick !== "undefined") sheetParams.closeByBackdropClick = closeByBackdropClick;
      if (typeof closeByOutsideClick !== "undefined") sheetParams.closeByOutsideClick = closeByOutsideClick;
      if (typeof closeOnEscape !== "undefined") sheetParams.closeOnEscape = closeOnEscape;
      if (typeof swipeToClose !== "undefined") sheetParams.swipeToClose = swipeToClose;
      if (typeof swipeToStep !== "undefined") sheetParams.swipeToStep = swipeToStep;
      if (typeof swipeHandler !== "undefined") sheetParams.swipeHandler = swipeHandler;
      if (typeof containerEl !== "undefined") sheetParams.containerEl = containerEl;
      if (typeof breakpoints !== "undefined") sheetParams.breakpoints = breakpoints;
      if (typeof backdropBreakpoint !== "undefined") sheetParams.backdropBreakpoint = backdropBreakpoint;
      if (typeof pushBreakpoint !== "undefined") sheetParams.pushBreakpoint = pushBreakpoint;
      f7ready(() => {
        f7Sheet = f7.sheet.create(sheetParams);
        if (props.opened) {
          f7Sheet.open(false);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7Sheet) {
        f7Sheet.destroy();
      }
      f7Sheet = null;
    });
    watch(() => props.opened, (value) => {
      if (!f7Sheet) return;
      if (value) {
        f7Sheet.open();
      } else {
        f7Sheet.close();
      }
    });
    ["closeByBackdropClick", "closeByOutsideClick", "closeOnEscape", "swipeToClose"].forEach((propName) => {
      watch(() => props[propName], (propValue) => {
        if (!f7Sheet) return;
        f7Sheet.params[propName] = propValue;
      });
    });
    const classes = computed(() => {
      let positionComputed = "bottom";
      if (props.position) positionComputed = props.position;
      else if (props.top) positionComputed = "top";
      else if (props.bottom) positionComputed = "bottom";
      return classNames("sheet-modal", `sheet-modal-${positionComputed}`, {
        "sheet-modal-push": props.push
      }, modalStateClasses({
        isOpened,
        isClosing
      }), colorClasses(props));
    });
    const fixedTags = "navbar toolbar tabbar subnavbar searchbar messagebar fab list-index panel".split(" ").map((tagName) => `f7-${tagName}`);
    return () => {
      const fixedList = [];
      const staticList = [];
      if (slots.default) {
        slots.default().forEach((vnode) => {
          if (typeof vnode === "undefined") return;
          const tag = vnode.type && vnode.type.name ? vnode.type.name : vnode.type;
          if (fixedTags.indexOf(tag) >= 0) {
            fixedList.push(vnode);
          } else {
            staticList.push(vnode);
          }
        });
      }
      return h("div", {
        class: classes.value,
        ref: elRef
      }, [fixedList, slots.fixed && slots.fixed(), h("div", {
        class: "sheet-modal-inner"
      }, [staticList, slots.static && slots.static()])]);
    };
  }
};

// node_modules/framework7-vue/components/popover.js
var _hoisted_1 = {
  key: 0,
  class: "popover-arrow"
};
var _hoisted_2 = {
  class: "popover-inner"
};
function render3(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_ctx.arrow !== false ? (openBlock(), createElementBlock("div", _hoisted_1)) : createCommentVNode("", true), createBaseVNode("div", _hoisted_2, [renderSlot(_ctx.$slots, "default")])], 2);
}
var popover_default = {
  name: "f7-popover",
  render: render3,
  props: {
    opened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    targetEl: {
      type: [String, Object],
      default: void 0
    },
    arrow: {
      type: Boolean,
      default: void 0
    },
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: {
      type: [String, Object],
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    closeByOutsideClick: {
      type: Boolean,
      default: void 0
    },
    closeOnEscape: {
      type: Boolean,
      default: void 0
    },
    containerEl: {
      type: [String, Object],
      default: void 0
    },
    verticalPosition: {
      type: String,
      default: void 0
    },
    ...colorProps
  },
  emits: ["popover:open", "popover:opened", "popover:close", "popover:closed", "update:opened"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const f7Popover = ref(null);
    let isOpened = props.opened;
    let isClosing = false;
    const elRef = ref(null);
    const onOpen = (instance) => {
      isOpened = true;
      isClosing = false;
      emit("popover:open", instance);
      emit("update:opened", true);
    };
    const onOpened = (instance) => {
      emit("popover:opened", instance);
    };
    const onClose = (instance) => {
      isOpened = false;
      isClosing = true;
      emit("popover:close", instance);
    };
    const onClosed = (instance) => {
      isClosing = false;
      emit("popover:closed", instance);
      emit("update:opened", false);
    };
    watch(() => props.opened, (value) => {
      if (!f7Popover.value) return;
      if (value) {
        f7Popover.value.open();
      } else {
        f7Popover.value.close();
      }
    });
    onMounted(() => {
      if (!elRef.value) return;
      const popoverParams = {
        el: elRef.value,
        on: {
          open: onOpen,
          opened: onOpened,
          close: onClose,
          closed: onClosed
        }
      };
      const {
        targetEl,
        closeByBackdropClick,
        closeByOutsideClick,
        closeOnEscape,
        arrow,
        backdrop,
        backdropEl,
        containerEl,
        verticalPosition
      } = props;
      if (typeof targetEl !== "undefined") popoverParams.targetEl = targetEl;
      if (typeof closeByBackdropClick !== "undefined") popoverParams.closeByBackdropClick = closeByBackdropClick;
      if (typeof closeByOutsideClick !== "undefined") popoverParams.closeByOutsideClick = closeByOutsideClick;
      if (typeof closeOnEscape !== "undefined") popoverParams.closeOnEscape = closeOnEscape;
      if (typeof arrow !== "undefined") popoverParams.arrow = arrow;
      if (typeof backdrop !== "undefined") popoverParams.backdrop = backdrop;
      if (typeof backdropEl !== "undefined") popoverParams.backdropEl = backdropEl;
      if (typeof containerEl !== "undefined") popoverParams.containerEl = containerEl;
      if (typeof verticalPosition !== "undefined") popoverParams.verticalPosition = verticalPosition;
      f7ready(() => {
        f7Popover.value = f7.popover.create(popoverParams);
        if (props.opened) {
          f7Popover.value.open(targetEl, false);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7Popover.value) {
        f7Popover.value.destroy();
      }
      f7Popover.value = null;
    });
    const classes = computed(() => classNames("popover", modalStateClasses({
      isOpened,
      isClosing
    }), colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/panel.js
var _hoisted_12 = {
  key: 0,
  class: "panel-resize-handler"
};
function render4(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default"), _ctx.resizable ? (openBlock(), createElementBlock("div", _hoisted_12)) : createCommentVNode("", true)], 2);
}
var panel_default = {
  name: "f7-panel",
  render: render4,
  props: {
    side: String,
    effect: String,
    cover: Boolean,
    reveal: Boolean,
    push: Boolean,
    floating: Boolean,
    left: Boolean,
    right: Boolean,
    opened: Boolean,
    resizable: Boolean,
    backdrop: {
      type: Boolean,
      default: true
    },
    backdropEl: {
      type: String,
      default: void 0
    },
    containerEl: {
      type: String,
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    visibleBreakpoint: {
      type: Number,
      default: void 0
    },
    collapsedBreakpoint: {
      type: Number,
      default: void 0
    },
    swipe: Boolean,
    swipeNoFollow: Boolean,
    swipeOnlyClose: Boolean,
    swipeActiveArea: {
      type: Number,
      default: 0
    },
    swipeThreshold: {
      type: Number,
      default: 0
    },
    ...colorProps
  },
  emits: ["panel:open", "panel:opened", "panel:close", "panel:closed", "click", "panel:backdropclick", "panel:swipe", "panel:swipeopen", "panel:breakpoint", "panel:collapsedbreakpoint", "panel:resize", "update:opened"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Panel = null;
    const elRef = ref(null);
    let isOpened = false;
    let isClosing = false;
    let isCollapsed = false;
    let isBreakpoint = false;
    const onOpen = (event) => {
      isOpened = true;
      isClosing = false;
      emit("panel:open", event);
      emit("update:opened", true);
    };
    const onOpened = (event) => {
      emit("panel:opened", event);
    };
    const onClose = (event) => {
      isOpened = false;
      isClosing = true;
      emit("panel:close", event);
    };
    const onClosed = (event) => {
      isClosing = false;
      emit("panel:closed", event);
      emit("update:opened", false);
    };
    const onBackdropClick = (event) => {
      emit("click", event);
      emit("panel:backdropclick", event);
    };
    const onSwipe = (event) => {
      emit("panel:swipe", event);
    };
    const onSwipeOpen = (event) => {
      emit("panel:swipeopen", event);
    };
    const onBreakpoint = (event) => {
      isBreakpoint = true;
      isCollapsed = false;
      emit("panel:breakpoint", event);
    };
    const onCollapsedBreakpoint = (event) => {
      isBreakpoint = false;
      isCollapsed = true;
      emit("panel:collapsedbreakpoint", event);
    };
    const onResize = function() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      emit("panel:resize", ...args);
    };
    watch(() => props.resizable, (newValue) => {
      if (!f7Panel) return;
      if (newValue) f7Panel.enableResizable();
      else f7Panel.disableResizable();
    });
    watch(() => props.opened, (newValue) => {
      if (!f7Panel) return;
      if (newValue) {
        f7Panel.open();
      } else {
        f7Panel.close();
      }
    });
    onMounted(() => {
      f7ready(() => {
        const $ = f7.$;
        if (!$) return;
        if ($(".panel-backdrop").length === 0) {
          $('<div class="panel-backdrop"></div>').insertBefore(elRef.value);
        }
        const params = noUndefinedProps({
          el: elRef.value,
          resizable: props.resizable,
          backdrop: props.backdrop,
          backdropEl: props.backdropEl,
          containerEl: props.containerEl,
          closeByBackdropClick: props.containerEl,
          visibleBreakpoint: props.visibleBreakpoint,
          collapsedBreakpoint: props.collapsedBreakpoint,
          swipe: props.swipe,
          swipeNoFollow: props.swipeNoFollow,
          swipeOnlyClose: props.swipeOnlyClose,
          swipeActiveArea: props.swipeActiveArea,
          swipeThreshold: props.swipeThreshold,
          on: {
            open: onOpen,
            opened: onOpened,
            close: onClose,
            closed: onClosed,
            backdropClick: onBackdropClick,
            swipe: onSwipe,
            swipeOpen: onSwipeOpen,
            collapsedBreakpoint: onCollapsedBreakpoint,
            breakpoint: onBreakpoint,
            resize: onResize
          }
        });
        f7Panel = f7.panel.create(params);
        if (props.opened) {
          f7Panel.open(false);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7Panel && f7Panel.destroy) {
        f7Panel.destroy();
      }
      f7Panel = null;
    });
    const classes = computed(() => {
      const sideComputed = props.side || (props.left ? "left" : "right");
      const effectComputed = props.effect || (props.reveal ? "reveal" : props.push ? "push" : props.floating ? "floating" : "cover");
      return classNames("panel", {
        "panel-in": isOpened && !isClosing && !isBreakpoint,
        "panel-in-breakpoint": isBreakpoint,
        "panel-in-collapsed": isCollapsed,
        "panel-resizable": props.resizable,
        [`panel-${sideComputed}`]: sideComputed,
        [`panel-${effectComputed}`]: effectComputed
      }, colorClasses(props));
    });
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/shared/router-open-in.js
var routerOpenIn = (router, url, options) => {
  const navigateOptions = {
    url,
    route: {
      path: url,
      options: {
        ...options,
        openIn: void 0
      }
    }
  };
  const params = {
    ...options
  };
  if (options.openIn === "popup") {
    params.component = {
      setup() {
        return () => h(popup_default, {
          class: "popup-router-open-in",
          "data-url": url
        }, [h(view_default2, {
          linksView: router.view.selector,
          url,
          ignoreOpenIn: true
        })]);
      }
    };
    navigateOptions.route.popup = params;
  }
  if (options.openIn === "loginScreen") {
    params.component = {
      setup() {
        return () => h(login_screen_default, {
          class: "login-screen-router-open-in",
          "data-url": url
        }, [h(view_default2, {
          linksView: router.view.selector,
          url,
          ignoreOpenIn: true
        })]);
      }
    };
    navigateOptions.route.loginScreen = params;
  }
  if (options.openIn === "sheet") {
    params.component = {
      setup() {
        return () => h(sheet_default, {
          class: "sheet-modal-router-open-in",
          "data-url": url
        }, [h(view_default2, {
          linksView: router.view.selector,
          url,
          ignoreOpenIn: true
        })]);
      }
    };
    navigateOptions.route.sheet = params;
  }
  if (options.openIn === "popover") {
    params.targetEl = options.clickedEl || options.targetEl;
    params.component = {
      setup() {
        return () => h(popover_default, {
          class: "popover-router-open-in",
          "data-url": url,
          targetEl: options.clickedEl || options.targetEl
        }, [h(view_default2, {
          linksView: router.view.selector,
          url,
          ignoreOpenIn: true
        })]);
      }
    };
    navigateOptions.route.popover = params;
  }
  if (options.openIn.indexOf("panel") >= 0) {
    const parts = options.openIn.split(":");
    const side = parts[1] || "left";
    const effect = parts[2] || "cover";
    params.component = {
      setup() {
        return () => h(panel_default, {
          class: "panel-router-open-in",
          "data-url": url,
          side,
          effect
        }, [h(view_default2, {
          linksView: router.view.selector,
          url,
          ignoreOpenIn: true
        })]);
      }
    };
    navigateOptions.route.panel = params;
  }
  return router.navigate(navigateOptions);
};

// node_modules/framework7-vue/shared/components-router.js
var getChildrenArray = (el) => {
  const arr = [];
  for (let i = 0; i < el.children.length; i += 1) {
    arr.push(el.children[i]);
  }
  return arr;
};
var hasSameChildren = (childrenBefore, childrenAfter) => {
  if (childrenBefore.length !== childrenAfter.length) return false;
  const set = /* @__PURE__ */ new Set([...childrenBefore, ...childrenAfter]);
  if (set.size === childrenBefore.length) return true;
  return false;
};
var components_router_default = {
  proto: {
    openIn(router, navigateUrl, options) {
      return routerOpenIn(router, navigateUrl, options);
    },
    pageComponentLoader(_ref) {
      let {
        routerEl,
        component,
        options,
        resolve,
        reject
      } = _ref;
      const router = this;
      const routerId = router.id;
      const el = routerEl;
      let viewRouter;
      f7routers.views.forEach((data) => {
        if (data.el && data.el === routerEl || data.routerId && data.routerId === routerId) {
          viewRouter = data;
        }
      });
      if (!viewRouter) {
        reject();
        return;
      }
      const pageData = {
        component,
        id: getComponentId(),
        props: extend({
          f7route: options.route,
          f7router: router
        }, options.route.params, options.props || {})
      };
      let resolved;
      const childrenBefore = getChildrenArray(el);
      function onDidUpdate(componentRouterData) {
        if (componentRouterData !== viewRouter || resolved) return;
        const childrenAfter = getChildrenArray(el);
        if (hasSameChildren(childrenBefore, childrenAfter)) return;
        f7events.off("viewRouterDidUpdate", onDidUpdate);
        const pageEl = el.children[el.children.length - 1];
        pageData.el = pageEl;
        resolve(pageEl);
        resolved = true;
      }
      f7events.on("viewRouterDidUpdate", onDidUpdate);
      viewRouter.pages.push(pageData);
      viewRouter.setPages(viewRouter.pages);
    },
    removePage($pageEl) {
      if (!$pageEl) return;
      const router = this;
      let f7Page;
      if ("length" in $pageEl && $pageEl[0]) f7Page = $pageEl[0].f7Page;
      else f7Page = $pageEl.f7Page;
      if (f7Page && f7Page.route && f7Page.route.route && f7Page.route.route.keepAlive) {
        router.app.$($pageEl).remove();
        return;
      }
      let viewRouter;
      f7routers.views.forEach((data) => {
        if (data.el && data.el === router.el) {
          viewRouter = data;
        }
      });
      let pageEl;
      if ("length" in $pageEl) {
        if ($pageEl.length === 0) return;
        pageEl = $pageEl[0];
      } else {
        pageEl = $pageEl;
      }
      if (!pageEl) return;
      let pageComponentFound;
      viewRouter.pages.forEach((page, index) => {
        if (page.el === pageEl) {
          pageComponentFound = true;
          viewRouter.pages.splice(index, 1);
          viewRouter.setPages(viewRouter.pages);
        }
      });
      if (!pageComponentFound) {
        pageEl.parentNode.removeChild(pageEl);
      }
    },
    tabComponentLoader(_temp) {
      let {
        tabEl,
        component,
        options,
        resolve,
        reject
      } = _temp === void 0 ? {} : _temp;
      const router = this;
      if (!tabEl) reject();
      let tabRouter;
      f7routers.tabs.forEach((tabData) => {
        if (tabData.el && tabData.el === tabEl) {
          tabRouter = tabData;
        }
      });
      if (!tabRouter) {
        reject();
        return;
      }
      const id = getComponentId();
      const tabContent = {
        id,
        component,
        props: extend({
          f7route: options.route,
          f7router: router
        }, options.route.route && options.route.route.tab && options.route.route.tab.options && options.route.route.tab.options.props || {}, options.route.params, options.props || {})
      };
      let resolved;
      function onDidUpdate(componentRouterData) {
        if (componentRouterData !== tabRouter || resolved) return;
        f7events.off("tabRouterDidUpdate", onDidUpdate);
        const tabContentEl = tabEl.children[0];
        resolve(tabContentEl);
        resolved = true;
      }
      f7events.on("tabRouterDidUpdate", onDidUpdate);
      tabRouter.setTabContent(tabContent);
    },
    removeTabContent(tabEl) {
      if (!tabEl) return;
      let tabRouter;
      f7routers.tabs.forEach((tabData) => {
        if (tabData.el && tabData.el === tabEl) {
          tabRouter = tabData;
        }
      });
      if (!tabRouter) {
        tabEl.innerHTML = "";
        return;
      }
      tabRouter.setTabContent(null);
    },
    modalComponentLoader(_temp2) {
      let {
        component,
        options,
        resolve,
        reject
      } = _temp2 === void 0 ? {} : _temp2;
      const router = this;
      const modalsRouter = f7routers.modals;
      if (!modalsRouter) {
        reject();
        return;
      }
      const modalData = {
        component,
        id: getComponentId(),
        props: extend({
          f7route: options.route,
          f7router: router
        }, options.route.params, options.props || {})
      };
      let resolved;
      function onDidUpdate() {
        if (resolved) return;
        f7events.off("modalsRouterDidUpdate", onDidUpdate);
        const modalEl = modalsRouter.el.children[modalsRouter.el.children.length - 1];
        modalData.el = modalEl;
        resolve(modalEl);
        resolved = true;
      }
      f7events.on("modalsRouterDidUpdate", onDidUpdate);
      modalsRouter.modals.push(modalData);
      modalsRouter.setModals(modalsRouter.modals);
    },
    removeModal(modalEl) {
      const modalsRouter = f7routers.modals;
      if (!modalsRouter) return;
      let modalDataToRemove;
      modalsRouter.modals.forEach((modalData) => {
        if (modalData.el === modalEl) modalDataToRemove = modalData;
      });
      modalsRouter.modals.splice(modalsRouter.modals.indexOf(modalDataToRemove), 1);
      modalsRouter.setModals(modalsRouter.modals);
    }
  }
};

// node_modules/framework7-vue/shared/plugin.js
var Framework7Vue = {
  name: "vuePlugin",
  installed: false,
  install(params) {
    if (params === void 0) {
      params = {};
    }
    if (Framework7Vue.installed) return;
    Framework7Vue.installed = true;
    f7initEvents();
    const {
      theme: paramsTheme,
      userAgent
    } = params;
    if (paramsTheme === "md") theme.md = true;
    if (paramsTheme === "ios") theme.ios = true;
    const needThemeCalc = typeof window === "undefined" ? !!userAgent : true;
    if (needThemeCalc && (!paramsTheme || paramsTheme === "auto")) {
      const device = framework7_lite_esm_default.getDevice({
        userAgent
      }, true);
      theme.ios = !!device.ios;
      theme.md = !theme.ios;
    }
    f7ready(() => {
      setTheme();
    });
    framework7_lite_esm_default.Router.use(components_router_default);
  }
};
var plugin_default = Framework7Vue;

// node_modules/framework7-vue/shared/use-store.js
var useStore = function() {
  let store = arguments.length <= 0 ? void 0 : arguments[0];
  let getter = arguments.length <= 1 ? void 0 : arguments[1];
  if (arguments.length === 1) {
    store = f7.store;
    getter = arguments.length <= 0 ? void 0 : arguments[0];
  }
  const obj = store._gettersPlain[getter];
  const valueRef = ref(obj.value);
  const callback = (v) => {
    valueRef.value = v;
  };
  obj.onUpdated(callback);
  onBeforeUnmount(() => {
    store.__removeCallback(callback);
  });
  return valueRef;
};

// node_modules/framework7-vue/components/accordion-content.js
function render5(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var accordion_content_default = {
  name: "f7-accordion-content",
  render: render5,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("accordion-item-content", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/accordion-item.js
function render6(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var accordion_item_default = {
  name: "f7-accordion-item",
  render: render6,
  props: {
    opened: Boolean,
    ...colorProps
  },
  emits: ["accordion:beforeopen", "accordion:open", "accordion:opened", "accordion:beforeclose", "accordion:close", "accordion:closed"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    const onBeforeOpen = (el, prevent) => {
      if (elRef.value !== el) return;
      emit("accordion:beforeopen", prevent);
    };
    const onOpen = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:open");
    };
    const onOpened = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:opened");
    };
    const onBeforeClose = (el, prevent) => {
      if (elRef.value !== el) return;
      emit("accordion:beforeclose", prevent);
    };
    const onClose = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:close");
    };
    const onClosed = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:closed");
    };
    const attachEvents = () => {
      f7ready(() => {
        f7.on("accordionBeforeOpen", onBeforeOpen);
        f7.on("accordionOpen", onOpen);
        f7.on("accordionOpened", onOpened);
        f7.on("accordionBeforeClose", onBeforeClose);
        f7.on("accordionClose", onClose);
        f7.on("accordionClosed", onClosed);
      });
    };
    const detachEvents = () => {
      f7.off("accordionBeforeOpen", onBeforeOpen);
      f7.off("accordionOpen", onOpen);
      f7.off("accordionOpened", onOpened);
      f7.off("accordionBeforeClose", onBeforeClose);
      f7.off("accordionClose", onClose);
      f7.off("accordionClosed", onClosed);
    };
    onMounted(() => attachEvents());
    onBeforeUnmount(() => detachEvents());
    const classes = computed(() => classNames("accordion-item", {
      "accordion-item-opened": props.opened
    }, colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/accordion-toggle.js
function render7(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var accordion_toggle_default = {
  name: "f7-accordion-toggle",
  render: render7,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("accordion-item-toggle", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/accordion.js
function render8(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var accordion_default = {
  name: "f7-accordion",
  render: render8,
  props: {
    accordionOpposite: Boolean,
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("accordion-list", props.accordionOpposite && "accordion-opposite", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/actions-button.js
var _hoisted_13 = {
  key: 0,
  class: "actions-button-media"
};
var _hoisted_22 = {
  class: "actions-button-text"
};
function render9(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [_ctx.hasMedia ? (openBlock(), createElementBlock("div", _hoisted_13, [renderSlot(_ctx.$slots, "media")])) : createCommentVNode("", true), createBaseVNode("div", _hoisted_22, [renderSlot(_ctx.$slots, "default")])], 2);
}
var actions_button_default = {
  name: "f7-actions-button",
  render: render9,
  props: {
    strong: Boolean,
    close: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      slots,
      emit
    } = _ref;
    const elRef = ref(null);
    const onClick = (e) => {
      if (elRef.value && props.close && f7) {
        f7.actions.close(f7.$(elRef.value).parents(".actions-modal"));
      }
      emit("click", e);
    };
    const hasMedia = computed(() => slots && !!slots.media);
    const classes = computed(() => classNames({
      "actions-button": true,
      "actions-button-strong": props.strong
    }, colorClasses(props)));
    return {
      classes,
      elRef,
      hasMedia,
      onClick
    };
  }
};

// node_modules/framework7-vue/components/actions-group.js
function render10(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var actions_group_default = {
  name: "f7-actions-group",
  render: render10,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("actions-group", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/actions-label.js
function render11(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var actions_label_default = {
  name: "f7-actions-label",
  render: render11,
  props: {
    strong: Boolean,
    ...colorProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onClick = (e) => {
      emit("click", e);
    };
    const classes = computed(() => classNames("actions-label", {
      "actions-button-strong": props.strong
    }, colorClasses(props)));
    return {
      classes,
      onClick
    };
  }
};

// node_modules/framework7-vue/components/actions.js
function render12(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var actions_default = {
  name: "f7-actions",
  render: render12,
  props: {
    tabletFullscreen: Boolean,
    opened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    grid: Boolean,
    target: {
      type: [String, Object],
      default: void 0
    },
    convertToPopover: {
      type: Boolean,
      default: void 0
    },
    forceToPopover: {
      type: Boolean,
      default: void 0
    },
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: {
      type: [String, Object],
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    closeByOutsideClick: {
      type: Boolean,
      default: void 0
    },
    closeOnEscape: {
      type: Boolean,
      default: void 0
    },
    containerEl: {
      type: [String, Object],
      default: void 0
    },
    ...colorProps
  },
  emits: ["actions:open", "actions:opened", "actions:close", "actions:closed", "update:opened"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Actions = null;
    let isOpened = props.opened;
    let isClosing = false;
    const elRef = ref(null);
    const onOpen = (instance) => {
      isOpened = true;
      isClosing = false;
      emit("actions:open", instance);
      emit("update:opened", true);
    };
    const onOpened = (instance) => {
      emit("actions:opened", instance);
    };
    const onClose = (instance) => {
      isOpened = false;
      isClosing = true;
      emit("actions:close", instance);
    };
    const onClosed = (instance) => {
      isClosing = false;
      emit("actions:closed", instance);
      emit("update:opened", false);
    };
    watch(() => props.opened, (value) => {
      if (!f7Actions) return;
      if (value) {
        f7Actions.open();
      } else {
        f7Actions.close();
      }
    });
    onMounted(() => {
      if (!elRef.value) return;
      const {
        target,
        convertToPopover,
        forceToPopover,
        closeByBackdropClick,
        closeByOutsideClick,
        closeOnEscape,
        backdrop,
        backdropEl,
        grid,
        containerEl
      } = props;
      const params = {
        el: elRef.value,
        grid,
        on: {
          open: onOpen,
          opened: onOpened,
          close: onClose,
          closed: onClosed
        }
      };
      if (typeof target !== "undefined") params.target = target;
      if (typeof convertToPopover !== "undefined") params.convertToPopover = convertToPopover;
      if (typeof forceToPopover !== "undefined") params.forceToPopover = forceToPopover;
      if (typeof closeByBackdropClick !== "undefined") params.closeByBackdropClick = closeByBackdropClick;
      if (typeof closeByOutsideClick !== "undefined") params.closeByOutsideClick = closeByOutsideClick;
      if (typeof closeOnEscape !== "undefined") params.closeOnEscape = closeOnEscape;
      if (typeof backdrop !== "undefined") params.backdrop = backdrop;
      if (typeof backdropEl !== "undefined") params.backdropEl = backdropEl;
      if (typeof containerEl !== "undefined") params.containerEl = containerEl;
      f7ready(() => {
        f7Actions = f7.popup.create(params);
        if (props.opened) {
          f7Actions.open(false);
        }
      });
    });
    onBeforeUnmount(() => {
      if (f7Actions) {
        f7Actions.destroy();
      }
      f7Actions = null;
    });
    const classes = computed(() => classNames("actions-modal", {
      "actions-grid": props.grid
    }, modalStateClasses({
      isOpened,
      isClosing
    }), colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/routable-modals.js
var routable_modals_default = {
  name: "f7-routable-modals",
  setup() {
    const elRef = ref(null);
    const modals = ref([]);
    const routerData = ref(null);
    onMounted(() => {
      routerData.value = {
        modals,
        el: elRef.value,
        setModals(newModals) {
          newModals.forEach((modal) => {
            modal.component = toRaw(modal.component);
          });
          modals.value = [...newModals];
        }
      };
      f7routers.modals = routerData.value;
    });
    onUpdated(() => {
      if (!routerData.value || !f7) return;
      f7events.emit("modalsRouterDidUpdate", routerData.value);
    });
    onBeforeUnmount(() => {
      if (!routerData.value) return;
      f7routers.modals = null;
      routerData.value = null;
    });
    const getComponent = (modal) => toRaw(modal.component);
    const getProps = (modal) => {
      const {
        component: modalComponent,
        props: modalProps
      } = modal;
      let keys = [];
      const passProps = {};
      if (modalComponent && modalComponent.props) keys = Object.keys(modalComponent.props);
      keys.forEach((key) => {
        if (key in modalProps) passProps[key] = modalProps[key];
      });
      return passProps;
    };
    return () => {
      return h("div", {
        ref: elRef,
        class: "framework7-modals"
      }, [...modals.value.map((modal) => {
        return h(getComponent(modal), {
          key: modal.id,
          ...getProps(modal)
        });
      })]);
    };
  }
};

// node_modules/framework7-vue/components/app.js
function render13(_ctx, _cache) {
  const _component_routable_modals = resolveComponent("routable-modals");
  return openBlock(), createElementBlock("div", {
    id: "framework7-root",
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default"), createVNode(_component_routable_modals)], 2);
}
var app_default = {
  name: "f7-app",
  render: render13,
  components: {
    RoutableModals: routable_modals_default
  },
  inheritAttrs: false,
  props: {
    name: {
      type: String,
      default: void 0
    },
    theme: {
      type: String,
      default: void 0
    },
    routes: {
      type: Array,
      default: () => []
    },
    store: Object,
    darkMode: {
      type: [Boolean, String],
      default: false
    },
    colors: {
      type: Object,
      default: () => void 0
    },
    lazyModulesPath: {
      type: String,
      default: void 0
    },
    initOnDeviceReady: {
      type: Boolean,
      default: void 0
    },
    iosTranslucentBars: {
      type: Boolean,
      default: void 0
    },
    iosTranslucentModals: {
      type: Boolean,
      default: void 0
    },
    userAgent: {
      type: String,
      default: void 0
    },
    url: {
      type: String,
      default: void 0
    },
    // components
    accordion: {
      type: Object,
      default: () => void 0
    },
    actions: {
      type: Object,
      default: () => void 0
    },
    areaChart: {
      type: Object,
      default: () => void 0
    },
    autocomplete: {
      type: Object,
      default: () => void 0
    },
    calendar: {
      type: Object,
      default: () => void 0
    },
    card: {
      type: Object,
      default: () => void 0
    },
    colorPicker: {
      type: Object,
      default: () => void 0
    },
    dialog: {
      type: Object,
      default: () => void 0
    },
    form: {
      type: Object,
      default: () => void 0
    },
    gauge: {
      type: Object,
      default: () => void 0
    },
    infiniteScroll: {
      type: Object,
      default: () => void 0
    },
    input: {
      type: Object,
      default: () => void 0
    },
    listIndex: {
      type: Object,
      default: () => void 0
    },
    loginScreen: {
      type: Object,
      default: () => void 0
    },
    messagebar: {
      type: Object,
      default: () => void 0
    },
    messages: {
      type: Object,
      default: () => void 0
    },
    navbar: {
      type: Object,
      default: () => void 0
    },
    notification: {
      type: Object,
      default: () => void 0
    },
    panel: {
      type: Object,
      default: () => void 0
    },
    photoBrowser: {
      type: Object,
      default: () => void 0
    },
    picker: {
      type: Object,
      default: () => void 0
    },
    pieChart: {
      type: Object,
      default: () => void 0
    },
    popover: {
      type: Object,
      default: () => void 0
    },
    popup: {
      type: Object,
      default: () => void 0
    },
    range: {
      type: Object,
      default: () => void 0
    },
    searchbar: {
      type: Object,
      default: () => void 0
    },
    sheet: {
      type: Object,
      default: () => void 0
    },
    smartSelect: {
      type: Object,
      default: () => void 0
    },
    sortable: {
      type: Object,
      default: () => void 0
    },
    statusbar: {
      type: Object,
      default: () => void 0
    },
    stepper: {
      type: Object,
      default: () => void 0
    },
    swipeout: {
      type: Object,
      default: () => void 0
    },
    textEditor: {
      type: Object,
      default: () => void 0
    },
    toast: {
      type: Object,
      default: () => void 0
    },
    toolbar: {
      type: Object,
      default: () => void 0
    },
    tooltip: {
      type: Object,
      default: () => void 0
    },
    view: {
      type: Object,
      default: () => void 0
    },
    virtualList: {
      type: Object,
      default: () => void 0
    },
    // modules
    clicks: {
      type: Object,
      default: () => void 0
    },
    serviceWorker: {
      type: Object,
      default: () => void 0
    },
    touch: {
      type: Object,
      default: () => void 0
    },
    ...colorProps
  },
  setup(props) {
    const elRef = ref(null);
    const routes = toRaw(props.routes);
    if (!f7 || typeof window === "undefined") {
      f7init(elRef.value, noUndefinedProps({
        ...props,
        routes
      }), false);
    }
    onMounted(() => {
      const parentEl = elRef.value && elRef.value.parentNode;
      if (typeof document !== "undefined" && parentEl && parentEl !== document.body && parentEl.parentNode === document.body) {
        parentEl.style.height = "100%";
      }
      if (f7) {
        f7.init(elRef.value);
        return;
      }
      f7init(elRef.value, noUndefinedProps({
        ...props,
        routes
      }), true);
    });
    const classes = computed(() => classNames("framework7-root", colorClasses(props)));
    return {
      classes,
      elRef
    };
  }
};

// node_modules/framework7-vue/components/area-chart.js
var _hoisted_14 = ["width", "height", "viewBox"];
var _hoisted_23 = ["data-index", "x1", "x2", "y2"];
var _hoisted_3 = {
  key: 0,
  class: "area-chart-axis"
};
var _hoisted_4 = {
  key: 0
};
var _hoisted_5 = {
  key: 1,
  class: "area-chart-legend"
};
function render14(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [(openBlock(), createElementBlock("svg", {
    ref: "svgElRef",
    xmlns: "http://www.w3.org/2000/svg",
    width: _ctx.width,
    height: _ctx.height,
    viewBox: `0 0 ${_ctx.width} ${_ctx.height}`,
    preserveAspectRatio: "none"
  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.chartData, (data, index) => {
    return openBlock(), createBlock(resolveDynamicComponent(_ctx.ChartTag), {
      key: `${_ctx.ChartTag}-${index}`,
      fill: _ctx.lineChart ? void 0 : data.color,
      stroke: _ctx.lineChart ? data.color : void 0,
      "fill-rule": "evenodd",
      points: _ctx.lineChart ? void 0 : data.points,
      d: _ctx.lineChart ? data.points : void 0
    }, null, 8, ["fill", "stroke", "points", "d"]);
  }), 128)), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.verticalLines, (line, index) => {
    return openBlock(), createElementBlock("line", {
      key: `line-${index}`,
      "data-index": index,
      fill: "#000",
      x1: line,
      y1: 0,
      x2: line,
      y2: _ctx.height,
      class: normalizeClass(_ctx.classNames({
        "area-chart-current-line": _ctx.currentIndex === index
      }))
    }, null, 10, _hoisted_23);
  }), 128))], 8, _hoisted_14)), _ctx.axis ? (openBlock(), createElementBlock("div", _hoisted_3, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.axisLabels, (label, index) => {
    return openBlock(), createElementBlock("span", {
      key: index
    }, [_ctx.visibleLegends.includes(label) ? (openBlock(), createElementBlock("span", _hoisted_4, toDisplayString(_ctx.formatAxisLabelMethod(label)), 1)) : createCommentVNode("", true)]);
  }), 128))])) : createCommentVNode("", true), _ctx.legend ? (openBlock(), createElementBlock("div", _hoisted_5, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.datasets, (dataset, index) => {
    return openBlock(), createBlock(resolveDynamicComponent(_ctx.LegendItemTag), {
      key: index,
      class: normalizeClass(_ctx.classNames("area-chart-legend-item", {
        "area-chart-legend-item-hidden": _ctx.hiddenDatasets.includes(index),
        "area-chart-legend-button": _ctx.toggleDatasets
      })),
      type: _ctx.toggleDatasets ? "button" : void 0,
      onClick: ($event) => _ctx.toggleDataset(index)
    }, {
      default: withCtx(() => [createBaseVNode("span", {
        style: normalizeStyle({
          backgroundColor: dataset.color
        })
      }, null, 4), createTextVNode(" " + toDisplayString(_ctx.formatLegendLabelMethod(dataset.label)), 1)]),
      _: 2
    }, 1032, ["class", "type", "onClick"]);
  }), 128))])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2);
}
var area_chart_default = {
  name: "f7-area-chart",
  render: render14,
  props: {
    lineChart: Boolean,
    datasets: {
      type: Array,
      default: () => []
    },
    axis: Boolean,
    axisLabels: {
      type: Array,
      default: () => []
    },
    tooltip: Boolean,
    legend: Boolean,
    toggleDatasets: Boolean,
    width: {
      type: Number,
      default: 640
    },
    height: {
      type: Number,
      default: 320
    },
    maxAxisLabels: {
      type: Number,
      default: 8
    },
    formatAxisLabel: Function,
    formatLegendLabel: Function,
    formatTooltip: Function,
    formatTooltipAxisLabel: Function,
    formatTooltipTotal: Function,
    formatTooltipDataset: Function
  },
  emits: ["select"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Tooltip = null;
    const currentIndex = ref(null);
    const hiddenDatasets = ref([]);
    const elRef = ref(null);
    const svgElRef = ref(null);
    const linesOffsets = ref(null);
    const visibleLegends = computed(() => {
      if (!props.maxAxisLabels || props.axisLabels.length <= props.maxAxisLabels) return props.axisLabels;
      const skipStep = Math.ceil(props.axisLabels.length / props.maxAxisLabels);
      const filtered = props.axisLabels.filter((label, index) => index % skipStep === 0);
      return filtered;
    });
    const summValues = computed(() => {
      const summ = [];
      props.datasets.filter((dataset, index) => !hiddenDatasets.value.includes(index)).forEach((_ref2) => {
        let {
          values
        } = _ref2;
        values.forEach((value, valueIndex) => {
          if (!summ[valueIndex]) summ[valueIndex] = 0;
          summ[valueIndex] += value;
        });
      });
      return summ;
    });
    const chartData = computed(() => {
      const {
        datasets,
        lineChart,
        width,
        height
      } = props;
      const data = [];
      if (!datasets.length) {
        return data;
      }
      const lastValues = datasets[0].values.map(() => 0);
      let maxValue = 0;
      if (lineChart) {
        datasets.forEach((_ref3) => {
          let {
            values
          } = _ref3;
          const datasetMaxValue = Math.max(...values);
          if (datasetMaxValue > maxValue) maxValue = datasetMaxValue;
        });
      } else {
        maxValue = Math.max(...summValues.value);
      }
      datasets.filter((dataset, index) => !hiddenDatasets.value.includes(index)).forEach((_ref4) => {
        let {
          label,
          values,
          color
        } = _ref4;
        const points = values.map((originalValue, valueIndex) => {
          lastValues[valueIndex] += originalValue;
          const value = lineChart ? originalValue : lastValues[valueIndex];
          const x = valueIndex / (values.length - 1) * width;
          const y = height - value / maxValue * height;
          if (lineChart) {
            return `${valueIndex === 0 ? "M" : "L"}${x},${y}`;
          }
          return `${x} ${y}`;
        });
        if (!lineChart) {
          points.push(`${width} ${height} 0 ${height}`);
        }
        data.push({
          label,
          points: points.join(" "),
          color
        });
      });
      return data.reverse();
    });
    const verticalLines = computed(() => {
      const lines = [];
      if (!props.datasets.length) {
        return lines;
      }
      const values = props.datasets[0].values;
      values.forEach((value, valueIndex) => {
        const x = valueIndex / (values.length - 1) * props.width;
        lines.push(x);
      });
      return lines;
    });
    const toggleDataset = (index) => {
      if (!props.toggleDatasets) return;
      if (hiddenDatasets.value.includes(index)) {
        hiddenDatasets.value.splice(hiddenDatasets.value.indexOf(index), 1);
      } else {
        hiddenDatasets.value.push(index);
      }
      hiddenDatasets.value = [...hiddenDatasets.value];
    };
    const formatAxisLabelMethod = (label) => {
      if (props.formatAxisLabel) return props.formatAxisLabel(label);
      return label;
    };
    const formatLegendLabelMethod = (label) => {
      if (props.formatLegendLabel) return props.formatLegendLabel(label);
      return label;
    };
    const calcLinesOffsets = () => {
      const lines = svgElRef.value.querySelectorAll("line");
      linesOffsets.value = [];
      for (let i = 0; i < lines.length; i += 1) {
        linesOffsets.value.push(lines[i].getBoundingClientRect().left);
      }
    };
    const formatTooltip = () => {
      const index = currentIndex.value;
      if (index === null) return "";
      let total = 0;
      const currentValues = props.datasets.filter((dataset, i) => !hiddenDatasets.value.includes(i)).map((dataset) => ({
        color: dataset.color,
        label: dataset.label,
        value: dataset.values[index]
      }));
      currentValues.forEach((dataset) => {
        total += dataset.value;
      });
      if (props.formatTooltip) {
        return props.formatTooltip({
          index,
          total,
          datasets: currentValues
        });
      }
      let labelText = props.formatTooltipAxisLabel ? props.formatTooltipAxisLabel(props.axisLabels[index]) : formatAxisLabelMethod(props.axisLabels[index]);
      if (!labelText) labelText = "";
      const totalText = props.formatTooltipTotal ? props.formatTooltipTotal(total) : total;
      const datasetsText = currentValues.length > 0 ? `
      <ul class="area-chart-tooltip-list">
        ${currentValues.map((_ref5) => {
        let {
          label,
          color,
          value
        } = _ref5;
        const valueText = props.formatTooltipDataset ? props.formatTooltipDataset(label, value, color) : `${label}: ${value}`;
        return `
              <li><span style="background-color: ${color};"></span>${valueText}</li>
            `;
      }).join("")}
      </ul>` : "";
      return `
      <div class="area-chart-tooltip-label">${labelText}</div>
      <div class="area-chart-tooltip-total">${totalText}</div>
      ${datasetsText}
    `;
    };
    const setTooltip = () => {
      const {
        tooltip,
        datasets
      } = props;
      const index = currentIndex.value;
      if (!tooltip) return;
      const hasVisibleDataSets = datasets.filter((dataset, i) => !hiddenDatasets.value.includes(i)).length > 0;
      if (!hasVisibleDataSets) {
        if (f7Tooltip && f7Tooltip.hide) f7Tooltip.hide();
        return;
      }
      if (index !== null && !f7Tooltip) {
        f7Tooltip = f7.tooltip.create({
          trigger: "manual",
          containerEl: elRef.value,
          targetEl: svgElRef.value.querySelector(`line[data-index="${index}"]`),
          text: formatTooltip(),
          cssClass: "area-chart-tooltip"
        });
        if (f7Tooltip && f7Tooltip.show) {
          f7Tooltip.show();
        }
        return;
      }
      if (!f7Tooltip || !f7Tooltip.hide || !f7Tooltip.show) {
        return;
      }
      if (index !== null) {
        f7Tooltip.setText(formatTooltip());
        f7Tooltip.setTargetEl(svgElRef.value.querySelector(`line[data-index="${index}"]`));
        f7Tooltip.show();
      } else {
        f7Tooltip.hide();
      }
    };
    const onMouseEnter = () => {
      calcLinesOffsets();
    };
    const onMouseMove = (e) => {
      if (!linesOffsets.value) {
        calcLinesOffsets();
      }
      let currentLeft = e.pageX;
      if (typeof currentLeft === "undefined") currentLeft = 0;
      const distances = linesOffsets.value.map((left) => Math.abs(currentLeft - left));
      const minDistance = Math.min(...distances);
      const closestIndex = distances.indexOf(minDistance);
      currentIndex.value = closestIndex;
    };
    const onMouseLeave = () => {
      currentIndex.value = null;
    };
    watch(() => currentIndex.value, () => {
      emit("select", currentIndex.value);
      setTooltip();
    });
    onMounted(() => {
      if (!svgElRef.value) return;
      svgElRef.value.addEventListener("mouseenter", onMouseEnter);
      svgElRef.value.addEventListener("mousemove", onMouseMove);
      svgElRef.value.addEventListener("mouseleave", onMouseLeave);
    });
    onBeforeUnmount(() => {
      if (f7Tooltip && f7Tooltip.destroy) {
        f7Tooltip.destroy();
      }
      f7Tooltip = null;
      if (!svgElRef.value) return;
      svgElRef.value.removeEventListener("mouseenter", onMouseEnter);
      svgElRef.value.removeEventListener("mousemove", onMouseMove);
      svgElRef.value.removeEventListener("mouseleave", onMouseLeave);
    });
    const classes = computed(() => classNames("area-chart"));
    const LegendItemTag = computed(() => props.toggleDatasets ? "button" : "span");
    const ChartTag = computed(() => props.lineChart ? "path" : "polygon");
    return {
      currentIndex,
      hiddenDatasets,
      visibleLegends,
      chartData,
      verticalLines,
      elRef,
      svgElRef,
      classes,
      toggleDataset,
      formatAxisLabelMethod,
      formatLegendLabelMethod,
      LegendItemTag,
      ChartTag,
      classNames
    };
  }
};

// node_modules/framework7-vue/shared/use-tooltip.js
var useTooltip = (elRef, props) => {
  let f7Tooltip = null;
  const {
    tooltip,
    tooltipTrigger
  } = props;
  onMounted(() => {
    if (!elRef.value) return;
    if (!tooltip) return;
    f7ready(() => {
      f7Tooltip = f7.tooltip.create({
        targetEl: elRef.value,
        text: tooltip,
        trigger: tooltipTrigger
      });
    });
  });
  onBeforeUnmount(() => {
    if (f7Tooltip && f7Tooltip.destroy) {
      f7Tooltip.destroy();
      f7Tooltip = null;
    }
  });
  watch(() => props.tooltip, (value) => {
    if (!value && f7Tooltip) {
      f7Tooltip.destroy();
      f7Tooltip = null;
      return;
    }
    if (value && !f7Tooltip && f7) {
      f7Tooltip = f7.tooltip.create({
        targetEl: elRef.value,
        text: value,
        trigger: tooltipTrigger
      });
      return;
    }
    if (!value || !f7Tooltip) return;
    f7Tooltip.setText(value);
  });
};

// node_modules/framework7-vue/components/badge.js
function render15(_ctx, _cache) {
  return openBlock(), createElementBlock("span", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var badge_default = {
  name: "f7-badge",
  render: render15,
  props: {
    tooltip: String,
    tooltipTrigger: String,
    ...colorProps
  },
  setup(props) {
    const elRef = ref(null);
    useTooltip(elRef, props);
    const classes = computed(() => classNames("badge", colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/block-footer.js
function render16(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var block_footer_default = {
  name: "f7-block-footer",
  render: render16,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("block-footer", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/block-header.js
function render17(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var block_header_default = {
  name: "f7-block-header",
  render: render17,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("block-header", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/block-title.js
var _hoisted_15 = ["medium"];
function render18(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    medium: _ctx.medium
  }, [renderSlot(_ctx.$slots, "default")], 10, _hoisted_15);
}
var block_title_default = {
  name: "f7-block-title",
  render: render18,
  props: {
    large: Boolean,
    medium: Boolean,
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => {
      const {
        large,
        medium
      } = props;
      return classNames("block-title", {
        "block-title-large": large,
        "block-title-medium": medium
      }, colorClasses(props));
    });
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/block.js
function render19(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var block_default = {
  name: "f7-block",
  render: render19,
  props: {
    inset: Boolean,
    insetIos: Boolean,
    insetMd: Boolean,
    xsmallInset: Boolean,
    xsmallInsetIos: Boolean,
    xsmallInsetMd: Boolean,
    smallInset: Boolean,
    smallInsetIos: Boolean,
    smallInsetMd: Boolean,
    mediumInset: Boolean,
    mediumInsetIos: Boolean,
    mediumInsetMd: Boolean,
    largeInset: Boolean,
    largeInsetIos: Boolean,
    largeInsetMd: Boolean,
    xlargeInset: Boolean,
    xlargeInsetIos: Boolean,
    xlargeInsetMd: Boolean,
    strong: Boolean,
    strongIos: Boolean,
    strongMd: Boolean,
    outline: Boolean,
    outlineIos: Boolean,
    outlineMd: Boolean,
    tabs: Boolean,
    tab: Boolean,
    tabActive: Boolean,
    accordionList: Boolean,
    accordionOpposite: Boolean,
    ...colorProps
  },
  emits: ["tab:hide", "tab:show"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    useTab(elRef, emit);
    const classes = computed(() => {
      const {
        inset,
        insetIos,
        insetMd,
        xsmallInset,
        xsmallInsetIos,
        xsmallInsetMd,
        smallInset,
        smallInsetIos,
        smallInsetMd,
        mediumInset,
        mediumInsetIos,
        mediumInsetMd,
        largeInset,
        largeInsetIos,
        largeInsetMd,
        xlargeInset,
        xlargeInsetIos,
        xlargeInsetMd,
        strong,
        strongIos,
        strongMd,
        outline,
        outlineIos,
        outlineMd,
        accordionList,
        accordionOpposite,
        tabs,
        tab,
        tabActive
      } = props;
      return classNames("block", {
        inset,
        "inset-ios": insetIos,
        "inset-md": insetMd,
        "xsmall-inset": xsmallInset,
        "xsmall-inset-ios": xsmallInsetIos,
        "xsmall-inset-md": xsmallInsetMd,
        "small-inset": smallInset,
        "small-inset-ios": smallInsetIos,
        "small-inset-md": smallInsetMd,
        "medium-inset": mediumInset,
        "medium-inset-ios": mediumInsetIos,
        "medium-inset-md": mediumInsetMd,
        "large-inset": largeInset,
        "large-inset-ios": largeInsetIos,
        "large-inset-md": largeInsetMd,
        "xlarge-inset": xlargeInset,
        "xlarge-inset-ios": xlargeInsetIos,
        "xlarge-inset-md": xlargeInsetMd,
        "block-strong": strong,
        "block-strong-ios": strongIos,
        "block-strong-md": strongMd,
        "accordion-list": accordionList,
        "accordion-opposite": accordionOpposite,
        tabs,
        tab,
        "tab-active": tabActive,
        "block-outline": outline,
        "block-outline-md": outlineMd,
        "block-outline-ios": outlineIos
      }, colorClasses(props));
    });
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/breadcrumbs-collapsed.js
var _hoisted_16 = {
  ref: "elRef",
  class: "breadcrumbs-collapsed"
};
var _hoisted_24 = createBaseVNode("span", null, null, -1);
function render20(_ctx, _cache) {
  return openBlock(), createElementBlock("div", _hoisted_16, [_hoisted_24, renderSlot(_ctx.$slots, "default")], 512);
}
var breadcrumbs_collapsed_default = {
  name: "f7-breadcrumbs-collapsed",
  render: render20,
  props: {},
  setup() {
    const elRef = ref(null);
    return {
      elRef
    };
  }
};

// node_modules/framework7-vue/components/breadcrumbs-item.js
function render21(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass({
      "breadcrumbs-item": true,
      "breadcrumbs-item-active": _ctx.active
    })
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var breadcrumbs_item_default = {
  name: "f7-breadcrumbs-item",
  render: render21,
  props: {
    active: Boolean
  },
  setup() {
    const elRef = ref(null);
    return {
      elRef
    };
  }
};

// node_modules/framework7-vue/components/breadcrumbs-separator.js
var _hoisted_17 = {
  ref: "elRef",
  class: "breadcrumbs-separator"
};
function render22(_ctx, _cache) {
  return openBlock(), createElementBlock("div", _hoisted_17, null, 512);
}
var breadcrumbs_separator_default = {
  name: "f7-breadcrumbs-separator",
  render: render22,
  props: {},
  setup() {
    const elRef = ref(null);
    return {
      elRef
    };
  }
};

// node_modules/framework7-vue/components/breadcrumbs.js
var _hoisted_18 = {
  ref: "elRef",
  class: "breadcrumbs"
};
function render23(_ctx, _cache) {
  return openBlock(), createElementBlock("div", _hoisted_18, [renderSlot(_ctx.$slots, "default")], 512);
}
var breadcrumbs_default = {
  name: "f7-breadcrumbs",
  render: render23,
  props: {},
  setup() {
    const elRef = ref(null);
    return {
      elRef
    };
  }
};

// node_modules/framework7-vue/shared/use-theme.js
var useTheme = () => {
  const t = ref(f7 ? theme : null);
  if (!f7) {
    f7ready(() => {
      t.value = theme;
    });
  }
  return t;
};

// node_modules/framework7-vue/components/preloader.js
var _hoisted_19 = {
  key: 0,
  class: "preloader-inner"
};
var _hoisted_25 = createBaseVNode("svg", {
  viewBox: "0 0 36 36"
}, [createBaseVNode("circle", {
  cx: "18",
  cy: "18",
  r: "16"
})], -1);
var _hoisted_32 = [_hoisted_25];
var _hoisted_42 = {
  key: 1,
  class: "preloader-inner"
};
var _hoisted_52 = createStaticVNode('<span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span><span class="preloader-inner-line"></span>', 8);
var _hoisted_132 = [_hoisted_52];
var _hoisted_142 = {
  key: 2,
  class: "preloader-inner"
};
function render24(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    style: normalizeStyle(_ctx.style)
  }, [_ctx.theme && _ctx.theme.md ? (openBlock(), createElementBlock("span", _hoisted_19, _hoisted_32)) : _ctx.theme && _ctx.theme.ios ? (openBlock(), createElementBlock("span", _hoisted_42, _hoisted_132)) : !_ctx.theme ? (openBlock(), createElementBlock("span", _hoisted_142)) : createCommentVNode("", true)], 6);
}
var preloader_default = {
  name: "f7-preloader",
  render: render24,
  props: {
    size: [Number, String],
    ...colorProps
  },
  setup(props) {
    const theme2 = useTheme();
    const classes = computed(() => classNames("preloader", {
      preloader: true
    }, colorClasses(props)));
    const style = computed(() => {
      const preloaderStyle = {};
      let sizeComputed = props.size;
      if (sizeComputed && typeof sizeComputed === "string" && sizeComputed.indexOf("px") >= 0) {
        sizeComputed = sizeComputed.replace("px", "");
      }
      if (sizeComputed) {
        preloaderStyle.width = `${sizeComputed}px`;
        preloaderStyle.height = `${sizeComputed}px`;
        preloaderStyle["--f7-preloader-size"] = `${sizeComputed}px`;
      }
      return preloaderStyle;
    });
    return {
      classes,
      style,
      theme: theme2
    };
  }
};

// node_modules/framework7-vue/components/icon.js
function render25(_ctx, _cache) {
  return openBlock(), createElementBlock("i", {
    ref: "elRef",
    style: normalizeStyle({
      fontSize: _ctx.sizeComputed,
      width: _ctx.sizeComputed,
      height: _ctx.sizeComputed
    }),
    class: normalizeClass(_ctx.classesComputed)
  }, [createTextVNode(toDisplayString(_ctx.iconText) + " ", 1), renderSlot(_ctx.$slots, "default")], 6);
}
var icon_default = {
  name: "f7-icon",
  render: render25,
  props: {
    material: String,
    f7: String,
    icon: String,
    ios: String,
    md: String,
    tooltip: String,
    tooltipTrigger: String,
    size: [String, Number],
    ...colorProps
  },
  setup(props) {
    const elRef = ref(null);
    const theme2 = useTheme();
    useTooltip(elRef, props);
    const classesComputed = computed(() => {
      const {
        ios,
        md,
        f7: f72,
        material,
        icon
      } = props;
      let classes = {
        icon: true
      };
      let themeIcon;
      if (theme2.value && theme2.value.ios) themeIcon = ios;
      else if (theme2.value && theme2.value.md) themeIcon = md;
      if (themeIcon) {
        const parts = themeIcon.split(":");
        const prop = parts[0];
        const value = parts[1];
        if (prop === "material" || prop === "f7") {
          classes["material-icons"] = prop === "material";
          classes["f7-icons"] = prop === "f7";
        }
        if (prop === "icon") {
          classes[value] = true;
        }
        if (icon) classes[icon] = true;
      } else {
        classes = {
          icon: true,
          "material-icons": material,
          "f7-icons": f72
        };
        if (icon) classes[icon] = true;
      }
      return classNames(classes, colorClasses(props));
    });
    const sizeComputed = computed(() => {
      let size = props.size;
      if (typeof props.size === "number" || parseFloat(props.size) === props.size * 1) {
        size = `${props.size}px`;
      }
      return size;
    });
    const iconText = computed(() => {
      const {
        ios,
        md,
        f7: f72,
        material
      } = props;
      let text = material || f72;
      if (md && theme2.value && theme2.value.md && (md.indexOf("material:") >= 0 || md.indexOf("f7:") >= 0)) {
        text = md.split(":")[1];
      } else if (ios && theme2.value && theme2.value.ios && (ios.indexOf("material:") >= 0 || ios.indexOf("f7:") >= 0)) {
        text = ios.split(":")[1];
      }
      return text;
    });
    return {
      elRef,
      sizeComputed,
      classesComputed,
      iconText
    };
  }
};

// node_modules/framework7-vue/components/use-icon.js
function render26(_ctx, _cache) {
  const _component_f7_badge = resolveComponent("f7-badge");
  const _component_f7_icon = resolveComponent("f7-icon");
  return openBlock(), createBlock(_component_f7_icon, normalizeProps(guardReactiveProps(_ctx.icon.props)), {
    default: withCtx(() => [_ctx.icon.badge ? (openBlock(), createBlock(_component_f7_badge, normalizeProps(mergeProps({
      key: 0
    }, _ctx.icon.badge.props)), {
      default: withCtx(() => [createTextVNode(toDisplayString(_ctx.icon.badge.content), 1)]),
      _: 1
    }, 16)) : createCommentVNode("", true)]),
    _: 1
  }, 16);
}
var use_icon_default = {
  name: "f7-use-icon",
  render: render26,
  components: {
    f7Icon: icon_default,
    f7Badge: badge_default
  },
  props: {
    icon: Object
  }
};

// node_modules/framework7-vue/shared/use-icon.js
var useIcon = function(props) {
  if (props === void 0) {
    props = {};
  }
  const {
    icon,
    iconMaterial,
    iconF7,
    iconMd,
    iconIos,
    iconColor,
    iconSize,
    iconBadge,
    badgeColor,
    iconBadgeColor
  } = props;
  if (icon || iconMaterial || iconF7 || iconMd || iconIos) {
    return {
      props: {
        material: iconMaterial,
        f7: iconF7,
        icon,
        md: iconMd,
        ios: iconIos,
        color: iconColor,
        size: iconSize
      },
      badge: iconBadge || iconBadge === 0 ? {
        props: {
          color: badgeColor || iconBadgeColor
        },
        content: iconBadge
      } : null
    };
  }
  return null;
};

// node_modules/framework7-vue/shared/use-route-props.js
var useRouteProps = function(elRef, _temp) {
  let {
    routeProps
  } = _temp === void 0 ? {} : _temp;
  onMounted(() => {
    if (elRef.value && routeProps) {
      elRef.value.f7RouteProps = routeProps;
    }
  });
  onUpdated(() => {
    if (elRef.value && routeProps) {
      elRef.value.f7RouteProps = routeProps;
    } else if (elRef.value && elRef.value.f7RouteProps) {
      delete elRef.value.f7RouteProps;
    }
  });
};

// node_modules/framework7-vue/components/button.js
var _hoisted_110 = {
  key: 1
};
var _hoisted_26 = {
  key: 1
};
function render27(_ctx, _cache) {
  const _component_f7_preloader = resolveComponent("f7-preloader");
  const _component_f7_use_icon = resolveComponent("f7-use-icon");
  return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), mergeProps({
    ref: "elRef",
    class: _ctx.classesComputed
  }, _ctx.attrs), {
    default: withCtx(() => [_ctx.preloader ? (openBlock(), createElementBlock(Fragment, {
      key: 0
    }, [createVNode(_component_f7_preloader, {
      size: _ctx.preloaderSize,
      color: _ctx.preloaderColor
    }, null, 8, ["size", "color"]), createBaseVNode("span", null, [_ctx.icon ? (openBlock(), createBlock(_component_f7_use_icon, {
      key: 0,
      icon: _ctx.icon
    }, null, 8, ["icon"])) : createCommentVNode("", true), _ctx.text ? (openBlock(), createElementBlock("span", _hoisted_110, toDisplayString(_ctx.text), 1)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")])], 64)) : (openBlock(), createElementBlock(Fragment, {
      key: 1
    }, [_ctx.icon ? (openBlock(), createBlock(_component_f7_use_icon, {
      key: 0,
      icon: _ctx.icon
    }, null, 8, ["icon"])) : createCommentVNode("", true), _ctx.text ? (openBlock(), createElementBlock("span", _hoisted_26, toDisplayString(_ctx.text), 1)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 64))]),
    _: 3
  }, 16, ["class"]);
}
var button_default = {
  name: "f7-button",
  render: render27,
  components: {
    f7Preloader: preloader_default,
    f7UseIcon: use_icon_default
  },
  props: {
    text: String,
    tabLink: [Boolean, String],
    tabLinkActive: Boolean,
    type: String,
    href: {
      type: [String, Boolean],
      default: "#"
    },
    target: String,
    round: Boolean,
    roundMd: Boolean,
    roundIos: Boolean,
    fill: Boolean,
    fillMd: Boolean,
    fillIos: Boolean,
    tonal: Boolean,
    tonalMd: Boolean,
    tonalIos: Boolean,
    large: Boolean,
    largeMd: Boolean,
    largeIos: Boolean,
    small: Boolean,
    smallMd: Boolean,
    smallIos: Boolean,
    raised: Boolean,
    raisedMd: Boolean,
    raisedIos: Boolean,
    outline: Boolean,
    outlineMd: Boolean,
    outlineIos: Boolean,
    active: Boolean,
    disabled: Boolean,
    tooltip: String,
    tooltipTrigger: String,
    preloader: Boolean,
    preloaderSize: [Number, String],
    preloaderColor: String,
    loading: Boolean,
    ...iconProps,
    ...colorProps,
    ...actionsProps,
    ...routerProps
  },
  setup(props) {
    const elRef = ref(null);
    useTooltip(elRef, props);
    useRouteProps(elRef, props);
    const icon = computed(() => useIcon(props));
    const tag = computed(() => props.type === "submit" || props.type === "reset" || props.type === "button" ? "button" : "a");
    const attrs = computed(() => {
      const {
        href,
        tabLink,
        target,
        type
      } = props;
      let hrefComputed = href;
      if (href === true) hrefComputed = "#";
      if (href === false || tag.value === "button") hrefComputed = void 0;
      return extend({
        href: hrefComputed,
        target,
        type,
        "data-tab": isStringProp(tabLink) && tabLink || void 0
      }, routerAttrs(props), actionsAttrs(props));
    });
    const classesComputed = computed(() => {
      const {
        tabLink,
        tabLinkActive,
        round,
        roundMd,
        roundIos,
        fill,
        fillMd,
        fillIos,
        tonal,
        tonalMd,
        tonalIos,
        large,
        largeMd,
        largeIos,
        small,
        smallMd,
        smallIos,
        raised,
        raisedMd,
        raisedIos,
        outline,
        outlineMd,
        outlineIos,
        active,
        disabled,
        preloader,
        loading
      } = props;
      return classNames("button", {
        "tab-link": tabLink || tabLink === "",
        "tab-link-active": tabLinkActive,
        "button-round": round,
        "button-round-ios": roundIos,
        "button-round-md": roundMd,
        "button-fill": fill,
        "button-fill-ios": fillIos,
        "button-fill-md": fillMd,
        "button-tonal": tonal,
        "button-tonal-ios": tonalIos,
        "button-tonal-md": tonalMd,
        "button-large": large,
        "button-large-ios": largeIos,
        "button-large-md": largeMd,
        "button-small": small,
        "button-small-ios": smallIos,
        "button-small-md": smallMd,
        "button-raised": raised,
        "button-raised-ios": raisedIos,
        "button-raised-md": raisedMd,
        "button-active": active,
        "button-outline": outline,
        "button-outline-ios": outlineIos,
        "button-outline-md": outlineMd,
        "button-preloader": preloader,
        "button-loading": loading,
        disabled
      }, colorClasses(props), routerClasses(props), actionsClasses(props));
    });
    return {
      tag,
      elRef,
      attrs,
      classesComputed,
      icon
    };
  }
};

// node_modules/framework7-vue/components/card-content.js
function render28(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var card_content_default = {
  name: "f7-card-content",
  render: render28,
  props: {
    padding: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("card-content", {
      "card-content-padding": props.padding
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/card-footer.js
function render29(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var card_footer_default = {
  name: "f7-card-footer",
  render: render29,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("card-footer", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/card-header.js
function render30(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var card_header_default = {
  name: "f7-card-header",
  render: render30,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("card-header", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/card.js
var _hoisted_111 = ["data-animate", "data-hide-navbar-on-open", "data-hide-toolbar-on-open", "data-hide-statusbar-on-open", "data-scrollable-el", "data-swipe-to-close", "data-close-by-backdrop-click", "data-backdrop", "data-backdrop-el"];
function render31(_ctx, _cache) {
  const _component_f7_card_header = resolveComponent("f7-card-header");
  const _component_f7_card_content = resolveComponent("f7-card-content");
  const _component_f7_card_footer = resolveComponent("f7-card-footer");
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    "data-animate": typeof _ctx.animate === "undefined" ? _ctx.animate : _ctx.animate.toString(),
    "data-hide-navbar-on-open": typeof _ctx.hideNavbarOnOpen === "undefined" ? _ctx.hideNavbarOnOpen : _ctx.hideNavbarOnOpen.toString(),
    "data-hide-toolbar-on-open": typeof _ctx.hideToolbarOnOpen === "undefined" ? _ctx.hideToolbarOnOpen : _ctx.hideToolbarOnOpen.toString(),
    "data-hide-statusbar-on-open": typeof _ctx.hideStatusbarOnOpen === "undefined" ? _ctx.hideStatusbarOnOpen : _ctx.hideStatusbarOnOpen.toString(),
    "data-scrollable-el": _ctx.scrollableEl,
    "data-swipe-to-close": typeof _ctx.swipeToClose === "undefined" ? _ctx.swipeToClose : _ctx.swipeToClose.toString(),
    "data-close-by-backdrop-click": typeof _ctx.closeByBackdropClick === "undefined" ? _ctx.closeByBackdropClick : _ctx.closeByBackdropClick.toString(),
    "data-backdrop": typeof _ctx.backdrop === "undefined" ? _ctx.backdrop : _ctx.backdrop.toString(),
    "data-backdrop-el": _ctx.backdropEl
  }, [_ctx.hasHeader ? (openBlock(), createBlock(_component_f7_card_header, {
    key: 0
  }, {
    default: withCtx(() => [createTextVNode(toDisplayString(_ctx.title) + " ", 1), renderSlot(_ctx.$slots, "header")]),
    _: 3
  })) : createCommentVNode("", true), _ctx.hasContent ? (openBlock(), createBlock(_component_f7_card_content, {
    key: 1,
    padding: _ctx.padding
  }, {
    default: withCtx(() => [createTextVNode(toDisplayString(_ctx.content) + " ", 1), renderSlot(_ctx.$slots, "content")]),
    _: 3
  }, 8, ["padding"])) : createCommentVNode("", true), _ctx.hasFooter ? (openBlock(), createBlock(_component_f7_card_footer, {
    key: 2
  }, {
    default: withCtx(() => [createTextVNode(toDisplayString(_ctx.footer) + " ", 1), renderSlot(_ctx.$slots, "footer")]),
    _: 3
  })) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 10, _hoisted_111);
}
var card_default = {
  name: "f7-card",
  render: render31,
  components: {
    f7CardHeader: card_header_default,
    f7CardContent: card_content_default,
    f7CardFooter: card_footer_default
  },
  props: {
    title: [String, Number],
    content: [String, Number],
    footer: [String, Number],
    raised: Boolean,
    outline: Boolean,
    outlineIos: Boolean,
    outlineMd: Boolean,
    headerDivider: Boolean,
    footerDivider: Boolean,
    expandable: Boolean,
    expandableAnimateWidth: Boolean,
    expandableOpened: Boolean,
    animate: {
      type: Boolean,
      default: void 0
    },
    hideNavbarOnOpen: {
      type: Boolean,
      default: void 0
    },
    hideToolbarOnOpen: {
      type: Boolean,
      default: void 0
    },
    hideStatusbarOnOpen: {
      type: Boolean,
      default: void 0
    },
    scrollableEl: {
      type: String,
      default: void 0
    },
    swipeToClose: {
      type: Boolean,
      default: void 0
    },
    closeByBackdropClick: {
      type: Boolean,
      default: void 0
    },
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: {
      type: String,
      default: void 0
    },
    padding: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["card:beforeopen", "card:open", "card:opened", "card:close", "card:closed", "update:expandableOpened"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const elRef = ref(null);
    const open = () => {
      if (!elRef.value) return;
      f7.card.open(elRef.value);
    };
    const close = () => {
      if (!elRef.value) return;
      f7.card.close(elRef.value);
    };
    const onBeforeOpen = (el, prevent) => {
      if (elRef.value !== el) return;
      emit("card:beforeopen", el, prevent);
    };
    const onOpen = (el) => {
      if (elRef.value !== el) return;
      emit("card:open", el);
      emit("update:expandableOpened", true);
    };
    const onOpened = (el, pageEl) => {
      if (elRef.value !== el) return;
      emit("card:opened", el, pageEl);
    };
    const onClose = (el) => {
      if (elRef.value !== el) return;
      emit("card:close", el);
    };
    const onClosed = (el, pageEl) => {
      if (elRef.value !== el) return;
      emit("card:closed", el, pageEl);
      emit("update:expandableOpened", false);
    };
    onMounted(() => {
      if (!props.expandable || !elRef.value) return;
      f7ready(() => {
        if (props.expandable && props.expandableOpened) {
          f7.card.open(elRef.value, false);
        }
        f7.on("cardBeforeOpen", onBeforeOpen);
        f7.on("cardOpen", onOpen);
        f7.on("cardOpened", onOpened);
        f7.on("cardClose", onClose);
        f7.on("cardClosed", onClosed);
      });
    });
    onBeforeUnmount(() => {
      f7.off("cardBeforeOpen", onBeforeOpen);
      f7.off("cardOpen", onOpen);
      f7.off("cardOpened", onOpened);
      f7.off("cardClose", onClose);
      f7.off("cardClosed", onClosed);
    });
    watch(() => props.expandableOpened, (value) => {
      if (value) {
        open();
      } else {
        close();
      }
    });
    const hasHeader = computed(() => props.title || slots.header);
    const hasContent = computed(() => props.content || slots.content);
    const hasFooter = computed(() => props.footer || slots.footer);
    const classes = computed(() => classNames("card", {
      "card-raised": props.raised,
      "card-outline": props.outline,
      "card-outline-ios": props.outlineIos,
      "card-outline-md": props.outlineMd,
      "card-expandable": props.expandable,
      "card-expandable-animate-width": props.expandableAnimateWidth,
      "card-header-divider": props.headerDivider,
      "card-footer-divider": props.footerDivider
    }, colorClasses(props)));
    return {
      elRef,
      classes,
      hasHeader,
      hasContent,
      hasFooter
    };
  }
};

// node_modules/framework7-vue/components/checkbox.js
var _hoisted_112 = ["name", "value", "disabled", "readonly", "checked"];
var _hoisted_27 = createBaseVNode("i", {
  class: "icon-checkbox"
}, null, -1);
function render32(_ctx, _cache) {
  return openBlock(), createElementBlock("label", {
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("input", {
    ref: "inputElRef",
    type: "checkbox",
    name: _ctx.name,
    value: _ctx.value,
    disabled: _ctx.disabled,
    readonly: _ctx.readonly,
    checked: _ctx.checked,
    onChange: _cache[0] || (_cache[0] = function() {
      return _ctx.onChange && _ctx.onChange(...arguments);
    })
  }, null, 40, _hoisted_112), _hoisted_27, renderSlot(_ctx.$slots, "default")], 2);
}
var checkbox_default = {
  name: "f7-checkbox",
  render: render32,
  props: {
    checked: Boolean,
    indeterminate: Boolean,
    name: [Number, String],
    value: {
      type: [Number, String, Boolean],
      default: void 0
    },
    disabled: Boolean,
    readonly: Boolean,
    ...colorProps
  },
  emits: ["update:checked", "change"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const inputElRef = ref(null);
    const onChange = (event) => {
      emit("update:checked", event.target.checked);
      emit("change", event);
    };
    onMounted(() => {
      if (inputElRef.value) {
        inputElRef.value.indeterminate = !!props.indeterminate;
      }
    });
    watch(() => props.indeterminate, (newValue) => {
      if (inputElRef.value) {
        inputElRef.value.indeterminate = !!newValue;
      }
    });
    const classes = computed(() => classNames({
      checkbox: true,
      disabled: props.disabled
    }, colorClasses(props)));
    return {
      inputElRef,
      classes,
      onChange
    };
  }
};

// node_modules/framework7-vue/components/chip.js
var _hoisted_113 = {
  key: 1,
  class: "chip-label"
};
function render33(_ctx, _cache) {
  const _component_f7_use_icon = resolveComponent("f7-use-icon");
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_ctx.hasMedia ? (openBlock(), createElementBlock("div", {
    key: 0,
    class: normalizeClass(_ctx.mediaClasses)
  }, [_ctx.icon ? (openBlock(), createBlock(_component_f7_use_icon, {
    key: 0,
    icon: _ctx.icon
  }, null, 8, ["icon"])) : createCommentVNode("", true), createTextVNode(" " + toDisplayString(_ctx.media) + " ", 1), renderSlot(_ctx.$slots, "media")], 2)) : createCommentVNode("", true), _ctx.hasLabel ? (openBlock(), createElementBlock("div", _hoisted_113, [createTextVNode(toDisplayString(_ctx.text) + " ", 1), renderSlot(_ctx.$slots, "text"), renderSlot(_ctx.$slots, "default")])) : createCommentVNode("", true), _ctx.deleteable ? (openBlock(), createElementBlock("a", {
    key: 2,
    class: "chip-delete",
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onDeleteClick && _ctx.onDeleteClick(...arguments);
    })
  })) : createCommentVNode("", true)], 2);
}
var chip_default = {
  name: "f7-chip",
  render: render33,
  components: {
    f7UseIcon: use_icon_default
  },
  props: {
    media: String,
    text: [String, Number],
    deleteable: Boolean,
    mediaBgColor: String,
    mediaTextColor: String,
    outline: Boolean,
    tooltip: String,
    tooltipTrigger: String,
    ...iconProps,
    ...colorProps
  },
  emits: ["delete"],
  setup(props, _ref) {
    let {
      slots,
      emit
    } = _ref;
    const elRef = ref(null);
    const onDeleteClick = (event) => {
      emit("delete", event);
    };
    useTooltip(elRef, props);
    const icon = computed(() => useIcon(props));
    const mediaClasses = computed(() => classNames("chip-media", props.mediaTextColor && `text-color-${props.mediaTextColor}`, props.mediaBgColor && `bg-color-${props.mediaBgColor}`));
    const classes = computed(() => classNames("chip", {
      "chip-outline": props.outline
    }, colorClasses(props)));
    const hasLabel = computed(() => {
      return props.text || slots && (slots.text || slots.default);
    });
    const hasMedia = computed(() => {
      return props.media || icon.value || slots && slots.media;
    });
    return {
      classes,
      icon,
      mediaClasses,
      elRef,
      hasLabel,
      hasMedia,
      onDeleteClick
    };
  }
};

// node_modules/framework7-vue/components/fab-backdrop.js
function render34(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var fab_backdrop_default = {
  name: "f7-fab-backdrop",
  render: render34,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("fab-backdrop", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/fab-button.js
var _hoisted_114 = ["target"];
var _hoisted_28 = {
  key: 0,
  class: "fab-label"
};
function render35(_ctx, _cache) {
  return openBlock(), createElementBlock("a", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    target: _ctx.target,
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [renderSlot(_ctx.$slots, "default"), _ctx.label ? (openBlock(), createElementBlock("span", _hoisted_28, toDisplayString(_ctx.label), 1)) : createCommentVNode("", true)], 10, _hoisted_114);
}
var fab_button_default = {
  name: "f7-fab-button",
  render: render35,
  props: {
    fabClose: Boolean,
    label: String,
    target: String,
    tooltip: String,
    tooltipTrigger: String,
    ...colorProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    const onClick = (e) => {
      emit("click", e);
    };
    useTooltip(elRef, props);
    const classes = computed(() => classNames({
      "fab-close": props.fabClose,
      "fab-label-button": props.label
    }, colorClasses(props)));
    return {
      classes,
      onClick,
      elRef
    };
  }
};

// node_modules/framework7-vue/components/fab-buttons.js
function render36(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var fab_buttons_default = {
  name: "f7-fab-buttons",
  render: render36,
  props: {
    position: {
      type: String,
      default: "top"
    },
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("fab-buttons", `fab-buttons-${props.position}`, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/fab.js
var fab_default = {
  name: "f7-fab",
  props: {
    morphTo: String,
    href: [Boolean, String],
    target: String,
    text: String,
    position: {
      type: String,
      default: "right-bottom"
    },
    tooltip: String,
    tooltipTrigger: String,
    ...colorProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const elRef = ref(null);
    const onClick = (e) => {
      emit("click", e);
    };
    useTooltip(elRef, props);
    const hrefComputed = computed(() => {
      let href = props.href;
      if (href === true) href = "#";
      if (href === false) href = void 0;
      return href;
    });
    return () => {
      const linkChildren = [];
      const rootChildren = [];
      let textEl;
      let linkEl;
      const {
        link: linkSlots,
        default: defaultSlots,
        root: rootSlots,
        text: textSlots
      } = slots;
      if (defaultSlots) {
        defaultSlots().forEach((vnode) => {
          if (typeof vnode === "undefined") return;
          const tag = vnode.type && vnode.type.name ? vnode.type.name : vnode.type;
          if (tag === "FabButtons" || tag === "f7-fab-buttons") rootChildren.push(vnode);
          else linkChildren.push(vnode);
        });
      }
      if (props.text || textSlots) {
        textEl = h("div", {
          class: "fab-text"
        }, [props.text, textSlots && textSlots()]);
      }
      if (linkChildren.length || linkSlots || textEl) {
        linkEl = h("a", {
          target: props.target,
          href: hrefComputed.value,
          onClick
        }, [linkChildren, textEl, linkSlots && linkSlots()]);
      }
      const classes = classNames("fab", `fab-${props.position}`, {
        "fab-morph": props.morphTo,
        "fab-extended": typeof textEl !== "undefined"
      }, colorClasses(props));
      return h("div", {
        class: classes,
        "data-morph-to": props.morphTo,
        ref: elRef
      }, [linkEl, rootChildren, rootSlots && rootSlots()]);
    };
  }
};

// node_modules/framework7-vue/components/gauge.js
var _hoisted_115 = {
  class: "gauge"
};
var _hoisted_29 = ["width", "height", "viewBox"];
var _hoisted_33 = ["d", "stroke", "stroke-width", "fill"];
var _hoisted_43 = ["d", "stroke", "stroke-width", "stroke-dasharray", "stroke-dashoffset", "fill"];
var _hoisted_53 = ["stroke", "stroke-width", "fill", "cx", "cy", "r"];
var _hoisted_6 = ["transform", "stroke", "stroke-width", "stroke-dasharray", "stroke-dashoffset", "fill", "cx", "cy", "r"];
var _hoisted_7 = ["y", "font-weight", "font-size", "fill", "dy", "dominant-baseline"];
var _hoisted_8 = ["y", "font-weight", "font-size", "fill", "dy", "dominant-baseline"];
function render37(_ctx, _cache) {
  return openBlock(), createElementBlock("div", _hoisted_115, [(openBlock(), createElementBlock("svg", {
    class: "gauge-svg",
    width: `${_ctx.size}px`,
    height: `${_ctx.semiCircle ? _ctx.size / 2 : _ctx.size}px`,
    viewBox: `0 0 ${_ctx.size} ${_ctx.semiCircle ? _ctx.size / 2 : _ctx.size}`
  }, [_ctx.semiCircle ? (openBlock(), createElementBlock("path", {
    key: 0,
    class: "gauge-back-semi",
    d: `M${_ctx.size - _ctx.borderWidth / 2},${_ctx.size / 2} a1,1 0 0,0 -${_ctx.size - _ctx.borderWidth},0`,
    stroke: _ctx.borderBgColor,
    "stroke-width": _ctx.borderWidth,
    fill: _ctx.bgColor || "none"
  }, null, 8, _hoisted_33)) : createCommentVNode("", true), _ctx.semiCircle ? (openBlock(), createElementBlock("path", {
    key: 1,
    class: "gauge-front-semi",
    d: `M${_ctx.size - _ctx.borderWidth / 2},${_ctx.size / 2} a1,1 0 0,0 -${_ctx.size - _ctx.borderWidth},0`,
    stroke: _ctx.borderColor,
    "stroke-width": _ctx.borderWidth,
    "stroke-dasharray": _ctx.length / 2,
    "stroke-dashoffset": _ctx.length / 2 * (1 + _ctx.progress),
    fill: _ctx.borderBgColor ? "none" : _ctx.bgColor || "none"
  }, null, 8, _hoisted_43)) : createCommentVNode("", true), !_ctx.semiCircle && _ctx.borderBgColor ? (openBlock(), createElementBlock("circle", {
    key: 2,
    class: "gauge-back-circle",
    stroke: _ctx.borderBgColor,
    "stroke-width": _ctx.borderWidth,
    fill: _ctx.bgColor || "none",
    cx: _ctx.size / 2,
    cy: _ctx.size / 2,
    r: _ctx.radius
  }, null, 8, _hoisted_53)) : createCommentVNode("", true), !_ctx.semiCircle ? (openBlock(), createElementBlock("circle", {
    key: 3,
    class: "gauge-front-circle",
    transform: `rotate(-90 ${_ctx.size / 2} ${_ctx.size / 2})`,
    stroke: _ctx.borderColor,
    "stroke-width": _ctx.borderWidth,
    "stroke-dasharray": _ctx.length,
    "stroke-dashoffset": _ctx.length * (1 - _ctx.progress),
    fill: _ctx.borderBgColor ? "none" : _ctx.bgColor || "none",
    cx: _ctx.size / 2,
    cy: _ctx.size / 2,
    r: _ctx.radius
  }, null, 8, _hoisted_6)) : createCommentVNode("", true), _ctx.valueText ? (openBlock(), createElementBlock("text", {
    key: 4,
    class: "gauge-value-text",
    x: "50%",
    y: _ctx.semiCircle ? "100%" : "50%",
    "font-weight": _ctx.valueFontWeight,
    "font-size": _ctx.valueFontSize,
    fill: _ctx.valueTextColor,
    dy: _ctx.semiCircle ? _ctx.labelText ? -_ctx.labelFontSize - 15 : -5 : 0,
    "text-anchor": "middle",
    "dominant-baseline": !_ctx.semiCircle ? "middle" : null
  }, toDisplayString(_ctx.valueText), 9, _hoisted_7)) : createCommentVNode("", true), _ctx.labelText ? (openBlock(), createElementBlock("text", {
    key: 5,
    class: "gauge-label-text",
    x: "50%",
    y: _ctx.semiCircle ? "100%" : "50%",
    "font-weight": _ctx.labelFontWeight,
    "font-size": _ctx.labelFontSize,
    fill: _ctx.labelTextColor,
    dy: _ctx.semiCircle ? -5 : _ctx.valueText ? _ctx.valueFontSize / 2 + 10 : 0,
    "text-anchor": "middle",
    "dominant-baseline": !_ctx.semiCircle ? "middle" : null
  }, toDisplayString(_ctx.labelText), 9, _hoisted_8)) : createCommentVNode("", true)], 8, _hoisted_29))]);
}
var gauge_default = {
  name: "f7-gauge",
  render: render37,
  props: {
    type: {
      type: String,
      default: "circle"
    },
    value: {
      type: [Number, String],
      default: 0
    },
    size: {
      type: [Number, String],
      default: 200
    },
    bgColor: {
      type: String,
      default: "transparent"
    },
    borderBgColor: {
      type: String,
      default: "#eeeeee"
    },
    borderColor: {
      type: String,
      default: "#000000"
    },
    borderWidth: {
      type: [Number, String],
      default: 10
    },
    valueText: [Number, String],
    valueTextColor: {
      type: String,
      default: "#000000"
    },
    valueFontSize: {
      type: [Number, String],
      default: 31
    },
    valueFontWeight: {
      type: [Number, String],
      default: 500
    },
    labelText: String,
    labelTextColor: {
      type: String,
      default: "#888888"
    },
    labelFontSize: {
      type: [Number, String],
      default: 14
    },
    labelFontWeight: {
      type: [Number, String],
      default: 400
    }
  },
  setup(props) {
    const semiCircle = computed(() => props.type === "semicircle");
    const radius = computed(() => props.size / 2 - props.borderWidth / 2);
    const length = computed(() => 2 * Math.PI * radius.value);
    const progress = computed(() => Math.max(Math.min(props.value, 1), 0));
    return {
      semiCircle,
      radius,
      length,
      progress
    };
  }
};

// node_modules/framework7-vue/components/toggle.js
var _hoisted_116 = ["name", "disabled", "readonly", "checked", "value"];
var _hoisted_210 = createBaseVNode("span", {
  class: "toggle-icon"
}, null, -1);
function render38(_ctx, _cache) {
  return openBlock(), createElementBlock("label", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("input", {
    type: "checkbox",
    name: _ctx.name,
    disabled: _ctx.disabled,
    readonly: _ctx.readonly,
    checked: _ctx.checked,
    value: _ctx.value,
    onChange: _cache[0] || (_cache[0] = function() {
      return _ctx.onChange && _ctx.onChange(...arguments);
    })
  }, null, 40, _hoisted_116), _hoisted_210], 2);
}
var toggle_default = {
  name: "f7-toggle",
  render: render38,
  props: {
    init: {
      type: Boolean,
      default: true
    },
    checked: Boolean,
    disabled: Boolean,
    readonly: Boolean,
    name: String,
    value: [String, Number, Array],
    tooltip: String,
    tooltipTrigger: String,
    ...colorProps
  },
  emits: ["change", "toggle:change", "update:checked"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Toggle = null;
    const elRef = ref(null);
    useTooltip(elRef, props);
    const onChange = (event) => {
      emit("change", event);
    };
    watch(() => props.checked, (newValue) => {
      if (!f7Toggle) return;
      f7Toggle.checked = newValue;
    });
    onMounted(() => {
      f7ready(() => {
        if (!props.init || !elRef.value) return;
        f7Toggle = f7.toggle.create({
          el: elRef.value,
          on: {
            change(toggleInstance) {
              emit("toggle:change", toggleInstance.checked);
              emit("update:checked", toggleInstance.checked);
            }
          }
        });
      });
    });
    onBeforeUnmount(() => {
      if (f7Toggle && f7Toggle.destroy && f7Toggle.$el) {
        f7Toggle.destroy();
      }
      f7Toggle = null;
    });
    const classes = computed(() => classNames("toggle", {
      disabled: props.disabled
    }, colorClasses(props)));
    return {
      classes,
      elRef,
      f7Toggle,
      onChange
    };
  }
};

// node_modules/framework7-vue/components/range.js
var _hoisted_117 = ["name"];
function render39(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_ctx.input ? (openBlock(), createElementBlock("input", {
    key: 0,
    id: "inputId",
    type: "range",
    name: _ctx.name
  }, null, 8, _hoisted_117)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2);
}
var range_default = {
  name: "f7-range",
  render: render39,
  props: {
    init: {
      type: Boolean,
      default: true
    },
    value: {
      type: [Number, Array, String],
      default: 0
    },
    min: {
      type: [Number, String],
      default: 0
    },
    max: {
      type: [Number, String],
      default: 100
    },
    step: {
      type: [Number, String],
      default: 1
    },
    label: {
      type: Boolean,
      default: false
    },
    dual: {
      type: Boolean,
      default: false
    },
    vertical: {
      type: Boolean,
      default: false
    },
    verticalReversed: {
      type: Boolean,
      default: false
    },
    draggableBar: {
      type: Boolean,
      default: true
    },
    formatLabel: Function,
    scale: {
      type: Boolean,
      default: false
    },
    scaleSteps: {
      type: Number,
      default: 5
    },
    scaleSubSteps: {
      type: Number,
      default: 0
    },
    formatScaleLabel: Function,
    limitKnobPosition: {
      type: Boolean,
      default: void 0
    },
    name: String,
    input: Boolean,
    inputId: String,
    disabled: Boolean,
    ...colorProps
  },
  emits: ["range:change", "range:changed", "rangeChange", "rangeChanged", "update:value"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Range = null;
    const elRef = ref(null);
    watch(() => props.value, (newValue) => {
      if (!f7Range) return;
      const rangeValue = f7Range.value;
      if (Array.isArray(newValue) && Array.isArray(rangeValue)) {
        if (rangeValue[0] !== newValue[0] || rangeValue[1] !== newValue[1]) {
          f7Range.setValue(newValue);
        }
      } else {
        f7Range.setValue(newValue);
      }
    });
    onMounted(() => {
      f7ready(() => {
        if (!props.init || !elRef.value) return;
        f7Range = f7.range.create(noUndefinedProps({
          el: elRef.value,
          ...props,
          on: {
            change(range, val) {
              emit("range:change", val);
              emit("rangeChange", val);
            },
            changed(range, val) {
              emit("range:changed", val);
              emit("rangeChanged", val);
              emit("update:value", val);
            }
          }
        }));
      });
    });
    onBeforeUnmount(() => {
      if (f7Range && f7Range.destroy) f7Range.destroy();
      f7Range = null;
    });
    const classes = computed(() => classNames("range-slider", {
      "range-slider-horizontal": !props.vertical,
      "range-slider-vertical": props.vertical,
      "range-slider-vertical-reversed": props.vertical && props.verticalReversed,
      disabled: props.disabled
    }, colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/text-editor.js
var _hoisted_118 = {
  class: "text-editor-content",
  contenteditable: ""
};
function render40(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "root-start"), createBaseVNode("div", _hoisted_118, [renderSlot(_ctx.$slots, "default")]), renderSlot(_ctx.$slots, "root-end"), renderSlot(_ctx.$slots, "root")], 2);
}
var text_editor_default = {
  name: "f7-text-editor",
  render: render40,
  props: {
    mode: {
      type: String,
      default: void 0
    },
    value: {
      type: String,
      default: void 0
    },
    buttons: Array,
    customButtons: Object,
    dividers: {
      type: Boolean,
      default: void 0
    },
    imageUrlText: {
      type: String,
      default: void 0
    },
    linkUrlText: {
      type: String,
      default: void 0
    },
    placeholder: {
      type: String,
      default: void 0
    },
    clearFormattingOnPaste: {
      type: Boolean,
      default: void 0
    },
    resizable: {
      type: Boolean,
      default: false
    },
    ...colorProps
  },
  emits: ["texteditor:change", "texteditor:input", "texteditor:focus", "texteditor:blur", "texteditor:buttonclick", "texteditor:keyboardopen", "texteditor:keyboardclose", "texteditor:popoveropen", "texteditor:popoverclose", "texteditor:insertlink", "texteditor:insertimage", "texteditorChange", "texteditorInput", "texteditorFocus", "texteditorBlur"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7TextEditor = null;
    const elRef = ref(null);
    const onChange = (editor, editorValue) => {
      emit("texteditor:change", editorValue);
      emit("texteditorChange", editorValue);
    };
    const onInput = (editor, editorValue) => {
      emit("texteditor:input", editorValue);
      emit("texteditorInput", editorValue);
    };
    const onFocus = () => {
      emit("texteditor:focus");
      emit("texteditorFocus");
    };
    const onBlur = () => {
      emit("texteditor:blur");
      emit("texteditorBlur");
    };
    const onButtonClick = (editor, button) => {
      emit("texteditor:buttonclick", button);
    };
    const onKeyboardOpen = () => {
      emit("texteditor:keyboardopen");
    };
    const onKeyboardClose = () => {
      emit("texteditor:keyboardclose");
    };
    const onPopoverOpen = () => {
      emit("texteditor:popoveropen");
    };
    const onPopoverClose = () => {
      emit("texteditor:popoverclose");
    };
    const onInsertLink = (editor, url) => {
      emit("texteditor:insertlink", url);
    };
    const onInsertImage = (editor, url) => {
      emit("texteditor:insertimage", url);
    };
    watch(() => props.value, (newValue) => {
      if (f7TextEditor) {
        f7TextEditor.setValue(newValue);
      }
    });
    onMounted(() => {
      const params = noUndefinedProps({
        el: elRef.value,
        mode: props.mode,
        value: props.value,
        buttons: props.buttons,
        customButtons: props.customButtons,
        dividers: props.dividers,
        imageUrlText: props.imageUrlText,
        linkUrlText: props.linkUrlText,
        placeholder: props.placeholder,
        clearFormattingOnPaste: props.clearFormattingOnPaste,
        on: {
          change: onChange,
          input: onInput,
          focus: onFocus,
          blur: onBlur,
          buttonClick: onButtonClick,
          keyboardOpen: onKeyboardOpen,
          keyboardClose: onKeyboardClose,
          popoverOpen: onPopoverOpen,
          popoverClose: onPopoverClose,
          insertLink: onInsertLink,
          insertImage: onInsertImage
        }
      });
      f7ready(() => {
        f7TextEditor = f7.textEditor.create(params);
      });
    });
    onBeforeUnmount(() => {
      if (f7TextEditor && f7TextEditor.destroy) {
        f7TextEditor.destroy();
      }
      f7TextEditor = null;
    });
    const classes = computed(() => classNames("text-editor", props.resizable && "text-editor-resizable", colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/input.js
var input_default = {
  name: "f7-input",
  props: {
    type: String,
    name: String,
    value: {
      type: [String, Number, Array, Date, Object],
      default: void 0
    },
    inputmode: String,
    placeholder: String,
    inputId: [String, Number],
    size: [String, Number],
    accept: [String, Number],
    autocomplete: [String],
    autocorrect: [String],
    autocapitalize: [String],
    spellcheck: [String],
    autofocus: Boolean,
    autosave: String,
    checked: Boolean,
    disabled: Boolean,
    max: [String, Number],
    min: [String, Number],
    step: [String, Number],
    maxlength: [String, Number],
    minlength: [String, Number],
    multiple: Boolean,
    readonly: Boolean,
    required: Boolean,
    inputStyle: [String, Object],
    pattern: String,
    validate: [Boolean, String],
    validateOnBlur: Boolean,
    onValidate: Function,
    tabindex: [String, Number],
    resizable: Boolean,
    clearButton: Boolean,
    // Form
    noFormStoreData: Boolean,
    noStoreData: Boolean,
    ignoreStoreData: Boolean,
    // Error, Info
    errorMessage: String,
    errorMessageForce: Boolean,
    info: String,
    // Outline
    outline: Boolean,
    // Components
    wrap: {
      type: Boolean,
      default: true
    },
    dropdown: {
      type: [String, Boolean],
      default: "auto"
    },
    // Datepicker
    calendarParams: Object,
    // Colorpicker
    colorPickerParams: Object,
    // Text editor
    textEditorParams: Object,
    ...colorProps
  },
  emits: ["input", "focus", "blur", "change", "textarea:resize", "input:notempty", "input:empty", "input:clear", "texteditor:change", "calendar:change", "colorpicker:change", "update:value"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    let f7Calendar = null;
    let f7ColorPicker = null;
    const inputInvalid = ref(false);
    const inputFocused = ref(false);
    const elRef = ref(null);
    const inputElRef = ref(null);
    let updateInputOnDidUpdate = false;
    const getDomValue = () => {
      if (!inputElRef.value) return void 0;
      return inputElRef.value.value;
    };
    const domValue = ref(getDomValue());
    const inputHasValue = computed(() => {
      if (props.type === "datepicker" && Array.isArray(props.value) && props.value.length === 0) {
        return false;
      }
      return typeof props.value === "undefined" ? domValue.value || domValue.value === 0 : props.value || props.value === 0;
    });
    const validateInput = () => {
      if (!f7 || !inputElRef.value) return;
      const validity = inputElRef.value.validity;
      if (!validity) return;
      if (!validity.valid) {
        if (props.onValidate) props.onValidate(false);
        if (inputInvalid.value !== true) {
          inputInvalid.value = true;
        }
      } else {
        if (props.onValidate) props.onValidate(true);
        if (inputInvalid.value !== false) {
          inputInvalid.value = false;
        }
      }
    };
    const onTextareaResize = (event) => {
      emit("textarea:resize", event);
    };
    const onInputNotEmpty = (event) => {
      emit("input:notempty", event);
    };
    const onInputEmpty = (event) => {
      emit("input:empty", event);
    };
    const onInputClear = (event) => {
      emit("input:clear", event);
    };
    const onInput = function() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      emit("input", ...args);
      if (inputElRef.value) {
        domValue.value = inputElRef.value.value;
      }
      if (!(props.validateOnBlur || props.validateOnBlur === "") && (props.validate || props.validate === "") && inputElRef.value) {
        validateInput();
      }
      if (inputElRef.value && props.type !== "texteditor" && props.type !== "colorpicker" && props.type !== "datepicker") {
        emit("update:value", inputElRef.value.value);
      }
    };
    const onFocus = function() {
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      emit("focus", ...args);
      inputFocused.value = true;
    };
    const onBlur = function() {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      emit("blur", ...args);
      if ((props.validate || props.validate === "" || props.validateOnBlur || props.validateOnBlur === "") && inputElRef.value) {
        validateInput();
      }
      inputFocused.value = false;
    };
    const onChange = function() {
      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
        args[_key4] = arguments[_key4];
      }
      emit("change", ...args);
      if (props.type === "texteditor") {
        emit("texteditor:change", args[1]);
        emit("update:value", args[1]);
      }
    };
    onMounted(() => {
      const {
        type,
        resizable,
        clearButton,
        value,
        calendarParams,
        colorPickerParams,
        validate,
        validateOnBlur
      } = props;
      f7ready(() => {
        if (type === "range" || type === "toggle") return;
        if (!inputElRef.value) return;
        inputElRef.value.addEventListener("input:notempty", onInputNotEmpty, false);
        if (type === "textarea" && resizable) {
          inputElRef.value.addEventListener("textarea:resize", onTextareaResize, false);
        }
        if (clearButton) {
          inputElRef.value.addEventListener("input:empty", onInputEmpty, false);
          inputElRef.value.addEventListener("input:clear", onInputClear, false);
        }
        if (type === "datepicker") {
          f7Calendar = f7.calendar.create({
            inputEl: inputElRef.value,
            value,
            on: {
              change(calendar, calendarValue) {
                emit("calendar:change", calendarValue);
                emit("update:value", calendarValue);
              }
            },
            ...calendarParams || {}
          });
        }
        if (type === "colorpicker") {
          f7ColorPicker = f7.colorPicker.create({
            inputEl: inputElRef.value,
            value,
            on: {
              change(colorPicker, colorPickerValue) {
                emit("colorpicker:change", colorPickerValue);
                emit("update:value", colorPickerValue);
              }
            },
            ...colorPickerParams || {}
          });
        }
        f7.input.checkEmptyState(inputElRef.value);
        if (!(validateOnBlur || validateOnBlur === "") && (validate || validate === "") && typeof value !== "undefined" && value !== null && value !== "") {
          setTimeout(() => {
            validateInput();
          }, 0);
        }
        if (resizable) {
          f7.input.resizeTextarea(inputElRef.value);
        }
      });
    });
    onBeforeUnmount(() => {
      if (props.type === "range" || props.type === "toggle") return;
      if (!inputElRef.value) return;
      inputElRef.value.removeEventListener("input:notempty", onInputNotEmpty, false);
      if (props.type === "textarea" && props.resizable) {
        inputElRef.value.removeEventListener("textarea:resize", onTextareaResize, false);
      }
      if (props.clearButton) {
        inputElRef.value.removeEventListener("input:empty", onInputEmpty, false);
        inputElRef.value.removeEventListener("input:clear", onInputClear, false);
      }
      if (f7Calendar && f7Calendar.destroy) {
        f7Calendar.destroy();
        f7Calendar = null;
      }
      if (f7ColorPicker && f7ColorPicker.destroy) {
        f7ColorPicker.destroy();
        f7ColorPicker = null;
      }
    });
    onUpdated(() => {
      if (!f7) return;
      if (updateInputOnDidUpdate) {
        if (!inputElRef.value) return;
        updateInputOnDidUpdate = false;
        f7.input.checkEmptyState(inputElRef.value);
        if (props.validate && !props.validateOnBlur) {
          validateInput();
        }
        if (props.resizable) {
          f7.input.resizeTextarea(inputElRef.value);
        }
      }
    });
    watch(() => props.colorPickerParams, (newValue) => {
      if (!f7 || !f7ColorPicker) return;
      extend(f7ColorPicker.params, newValue || {});
    });
    watch(() => props.calendarParams, (newValue) => {
      if (!f7 || !f7Calendar) return;
      extend(f7Calendar.params, newValue || {});
    });
    watch(() => props.value, (newValue) => {
      if (props.type === "range" || props.type === "toggle" || !f7) return;
      updateInputOnDidUpdate = true;
      if (f7Calendar) {
        f7Calendar.setValue(newValue);
      }
      if (f7ColorPicker) {
        f7ColorPicker.setValue(newValue);
      }
    });
    const createInput = (InputTag, children) => {
      const needsValue = props.type !== "file" && props.type !== "datepicker" && props.type !== "colorpicker";
      const needsType = InputTag === "input";
      let inputType = props.type;
      if (inputType === "datepicker" || inputType === "colorpicker") {
        inputType = "text";
      }
      const inputClassName = classNames({
        resizable: inputType === "textarea" && props.resizable,
        "no-store-data": props.noFormStoreData || props.noStoreData || props.ignoreStoreData,
        "input-invalid": props.errorMessage && props.errorMessageForce || inputInvalid.value,
        "input-with-value": inputHasValue.value,
        "input-focused": inputFocused.value
      });
      let inputValue;
      if (needsValue) {
        if (typeof props.value !== "undefined") inputValue = props.value;
        else inputValue = domValue.value;
      }
      const valueProps = {};
      if (props.type !== "datepicker" && props.type !== "colorpicker") {
        if ("value" in props) valueProps.value = inputValue;
      }
      const inputProps = noUndefinedProps({
        name: props.name,
        type: needsType ? inputType : void 0,
        placeholder: props.placeholder,
        inputmode: props.inputmode,
        id: props.inputId,
        size: props.size,
        accept: props.accept,
        autocomplete: props.autocomplete,
        autoCorrect: props.autocorrect,
        autocapitalize: props.autocapitalize,
        spellcheck: props.spellcheck,
        autofocus: props.autofocus,
        autoSave: props.autosave,
        checked: props.checked,
        disabled: props.disabled,
        max: props.max,
        maxlength: props.maxlength,
        min: props.min,
        minlength: props.minlength,
        step: props.step,
        multiple: props.multiple,
        readonly: props.readonly,
        required: props.required,
        pattern: props.pattern,
        validate: typeof props.validate === "string" && props.validate.length ? props.validate : void 0,
        tabindex: props.tabindex
      });
      return h(InputTag, {
        ref: inputElRef,
        style: props.inputStyle,
        ...inputProps,
        "data-validate": props.validate === true || props.validate === "" || props.validateOnBlur === true || props.validateOnBlur === "" ? true : void 0,
        "data-validate-on-blur": props.validateOnBlur === true || props.validateOnBlur === "" ? true : void 0,
        "data-error-message": props.errorMessageForce ? void 0 : props.errorMessage,
        class: inputClassName,
        onFocus,
        onBlur,
        onInput,
        onChange,
        ...valueProps
      }, [children]);
    };
    const wrapClasses = computed(() => classNames("input", {
      "input-outline": props.outline,
      "input-dropdown": props.dropdown === "auto" ? props.type === "select" : props.dropdown,
      "input-invalid": props.errorMessage && props.errorMessageForce || inputInvalid.value
    }, colorClasses(props)));
    return () => {
      let inputEl;
      if (props.type === "select" || props.type === "textarea" || props.type === "file") {
        if (props.type === "select") {
          inputEl = createInput("select", slots.default && slots.default());
        } else if (props.type === "file") {
          inputEl = createInput("input");
        } else {
          inputEl = createInput("textarea");
        }
      } else if (slots.default || !props.type) {
        inputEl = slots.default();
      } else if (props.type === "toggle") {
        inputEl = h(toggle_default, {
          checked: props.checked,
          readonly: props.readonly,
          name: props.name,
          value: props.value,
          disabled: props.disabled,
          id: props.inputId,
          onChange
        });
      } else if (props.type === "range") {
        inputEl = h(range_default, {
          value: props.value,
          disabled: props.disabled,
          min: props.min,
          max: props.max,
          step: props.step,
          name: props.name,
          id: props.inputId,
          input: true,
          onRangeChange: onChange
        });
      } else if (props.type === "texteditor") {
        inputEl = h(text_editor_default, {
          value: props.value,
          resizable: props.resizable,
          placeholder: props.placeholder,
          onTextEditorFocus: onFocus,
          onTextEditorBlur: onBlur,
          onTextEditorInput: onInput,
          onTextEditorChange: onChange,
          ...props.textEditorParams || {}
        });
      } else {
        inputEl = createInput("input");
      }
      if (!props.wrap) return inputEl;
      return h("div", {
        class: wrapClasses.value,
        ref: elRef
      }, [inputEl, (props.errorMessage || slots["error-message"]) && props.errorMessageForce && h("div", {
        class: "input-error-message"
      }, [props.errorMessage, slots["error-message"] && slots["error-message"]()]), props.clearButton && h("span", {
        class: "input-clear-button"
      }), (props.info || slots.info) && h("div", {
        class: "input-info"
      }, [props.info, slots.info && slots.info()])]);
    };
  }
};

// node_modules/framework7-vue/shared/use-smart-select.js
var useSmartSelect = (props, setInstance, getEl) => {
  let f7SmartSelect;
  onMounted(() => {
    f7ready(() => {
      if (props.smartSelect) {
        const ssParams = extend({
          el: getEl()
        }, props.smartSelectParams || {});
        f7SmartSelect = f7.smartSelect.create(ssParams);
        setInstance(f7SmartSelect);
      }
    });
  });
  onBeforeUnmount(() => {
    if (f7SmartSelect && f7SmartSelect.destroy) {
      f7SmartSelect.destroy();
    }
    f7SmartSelect = null;
    setInstance(f7SmartSelect);
  });
};

// node_modules/framework7-vue/components/link.js
function render41(_ctx, _cache) {
  const _component_f7_use_icon = resolveComponent("f7-use-icon");
  const _component_f7_badge = resolveComponent("f7-badge");
  return openBlock(), createElementBlock("a", mergeProps({
    ref: "elRef",
    class: _ctx.classes
  }, _ctx.attrs), [_ctx.icon ? (openBlock(), createBlock(_component_f7_use_icon, {
    key: 0,
    icon: _ctx.icon
  }, null, 8, ["icon"])) : createCommentVNode("", true), _ctx.text ? (openBlock(), createElementBlock("span", {
    key: 1,
    class: normalizeClass(_ctx.isTabbarIcons ? "tabbar-label" : "")
  }, [createTextVNode(toDisplayString(_ctx.text) + " ", 1), _ctx.badge ? (openBlock(), createBlock(_component_f7_badge, {
    key: 0,
    color: _ctx.badgeColor
  }, {
    default: withCtx(() => [createTextVNode(toDisplayString(_ctx.badge), 1)]),
    _: 1
  }, 8, ["color"])) : createCommentVNode("", true)], 2)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 16);
}
var link_default = {
  name: "f7-link",
  render: render41,
  components: {
    f7Badge: badge_default,
    f7UseIcon: use_icon_default
  },
  props: {
    noLinkClass: Boolean,
    text: String,
    tabLink: [Boolean, String],
    tabLinkActive: Boolean,
    tabbarLabel: Boolean,
    iconOnly: Boolean,
    badge: [String, Number],
    badgeColor: [String],
    href: {
      type: [String, Boolean],
      default: "#"
    },
    target: String,
    tooltip: String,
    tooltipTrigger: String,
    smartSelect: Boolean,
    smartSelectParams: Object,
    ...iconProps,
    ...colorProps,
    ...actionsProps,
    ...routerProps
  },
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    const elRef = ref(null);
    let f7SmartSelect = null;
    useTooltip(elRef, props);
    useRouteProps(elRef, props);
    useSmartSelect(props, (instance) => {
      f7SmartSelect = instance;
    }, () => {
      return elRef.value;
    });
    const TabbarContext = inject("TabbarContext", {
      value: {}
    });
    const isTabbarIcons = computed(() => props.tabbarLabel || TabbarContext.value.tabbarHasIcons);
    const attrs = computed(() => {
      const {
        href,
        tabLink,
        target
      } = props;
      let hrefComputed = href;
      if (href === true) hrefComputed = "#";
      if (href === false) hrefComputed = void 0;
      return {
        href: hrefComputed,
        target,
        "data-tab": isStringProp(tabLink) && tabLink || void 0,
        ...routerAttrs(props),
        ...actionsAttrs(props)
      };
    });
    const classes = computed(() => {
      const {
        iconOnly,
        text,
        noLinkClass,
        tabLink,
        tabLinkActive,
        smartSelect
      } = props;
      let iconOnlyComputed;
      const hasChildren = slots && slots.default;
      if (iconOnly || !text && !hasChildren) {
        iconOnlyComputed = true;
      } else {
        iconOnlyComputed = false;
      }
      return classNames({
        link: !(noLinkClass || isTabbarIcons.value),
        "icon-only": iconOnlyComputed,
        "tab-link": tabLink || tabLink === "",
        "tab-link-active": tabLinkActive,
        "smart-select": smartSelect
      }, colorClasses(props), routerClasses(props), actionsClasses(props));
    });
    const icon = computed(() => useIcon(props));
    return {
      elRef,
      icon,
      isTabbarIcons,
      attrs,
      classes,
      f7SmartSelect
    };
  }
};

// node_modules/framework7-vue/components/list-button.js
function render42(_ctx, _cache) {
  return openBlock(), createElementBlock("li", null, [createBaseVNode("a", mergeProps({
    ref: "linkElRef",
    class: _ctx.linkClasses
  }, _ctx.linkAttrs, {
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }), [createTextVNode(toDisplayString(_ctx.title) + " " + toDisplayString(_ctx.text) + " ", 1), renderSlot(_ctx.$slots, "default")], 16)]);
}
var list_button_default = {
  name: "f7-list-button",
  render: render42,
  props: {
    title: [String, Number],
    text: [String, Number],
    tabLink: [Boolean, String],
    tabLinkActive: Boolean,
    link: [Boolean, String],
    href: [Boolean, String],
    target: String,
    tooltip: String,
    tooltipTrigger: String,
    ...colorProps,
    ...actionsProps,
    ...routerProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const linkElRef = ref(null);
    const onClick = (e) => {
      emit("click", e);
    };
    useTooltip(linkElRef, props);
    useRouteProps(linkElRef, props);
    const linkAttrs = computed(() => {
      return {
        href: typeof props.link === "boolean" && typeof props.href === "boolean" ? "#" : props.link || props.href,
        target: props.target,
        "data-tab": isStringProp(props.tabLink) && props.tabLink,
        ...routerAttrs(props),
        ...actionsAttrs(props)
      };
    });
    const linkClasses = computed(() => {
      return classNames({
        "list-button": true,
        "tab-link": props.tabLink || props.tabLink === "",
        "tab-link-active": props.tabLinkActive,
        ...colorClasses(props),
        ...routerClasses(props),
        ...actionsClasses(props)
      });
    });
    return {
      linkAttrs,
      linkClasses,
      onClick,
      linkElRef
    };
  }
};

// node_modules/framework7-vue/components/list-group.js
var _hoisted_119 = ["data-sortable-move-elements"];
function render43(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    "data-sortable-move-elements": typeof _ctx.sortableMoveElements !== "undefined" ? _ctx.sortableMoveElements.toString() : void 0
  }, [createBaseVNode("ul", null, [renderSlot(_ctx.$slots, "default")])], 10, _hoisted_119);
}
var list_group_default = {
  name: "f7-list-group",
  render: render43,
  props: {
    mediaList: Boolean,
    simpleList: Boolean,
    sortable: Boolean,
    sortableOpposite: Boolean,
    sortableTapHold: Boolean,
    sortableMoveElements: {
      type: Boolean,
      default: void 0
    },
    ...colorProps
  },
  setup(props) {
    const ListContextParent = inject("ListContext", {
      value: {
        listIsMedia: props.mediaList,
        listIsSimple: props.simpleList,
        listIsSortable: props.sortable,
        listIsSortableOpposite: props.sortableOpposite
      }
    });
    const ListContext = computed(() => ({
      listIsMedia: props.mediaList || ListContextParent.value.listIsMedia,
      listIsSimple: props.simpleList || ListContextParent.value.listIsSimple,
      listIsSortable: props.sortable || ListContextParent.value.listIsSortable,
      listIsSortableOpposite: props.sortableOpposite || ListContextParent.value.listIsSortableOpposite
    }));
    provide("ListContext", ListContext);
    const classes = computed(() => classNames("list-group", {
      "media-list": props.mediaList,
      sortable: props.sortable,
      "sortable-tap-hold": props.sortableTapHold,
      "sortable-opposite": props.sortableOpposite
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/list-index.js
function render44(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var list_index_default = {
  name: "f7-list-index",
  render: render44,
  props: {
    init: {
      type: Boolean,
      default: true
    },
    listEl: [String, Object],
    indexes: {
      type: [String, Array],
      default: "auto"
    },
    scrollList: {
      type: Boolean,
      default: true
    },
    label: {
      type: Boolean,
      default: false
    },
    iosItemHeight: {
      type: Number,
      default: 14
    },
    mdItemHeight: {
      type: Number,
      default: 14
    },
    ...colorProps
  },
  emits: ["listindex:select"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7ListIndex = null;
    const elRef = ref(null);
    const update = () => {
      if (!f7ListIndex) return;
      f7ListIndex.update();
    };
    const scrollListToIndex = (indexContent) => {
      if (!f7ListIndex) return;
      f7ListIndex.scrollListToIndex(indexContent);
    };
    watch(() => props.indexes, (newValue) => {
      if (!f7ListIndex) return;
      f7ListIndex.params.indexes = newValue;
      update();
    });
    onMounted(() => {
      if (!props.init) return;
      f7ready(() => {
        f7ListIndex = f7.listIndex.create({
          el: elRef.value,
          listEl: props.listEl,
          indexes: props.indexes,
          iosItemHeight: props.iosItemHeight,
          mdItemHeight: props.mdItemHeight,
          scrollList: props.scrollList,
          label: props.label,
          on: {
            select(index, itemContent, itemIndex) {
              emit("listindex:select", itemContent, itemIndex);
            }
          }
        });
      });
    });
    onBeforeUnmount(() => {
      if (f7ListIndex && f7ListIndex.destroy) {
        f7ListIndex.destroy();
      }
      f7ListIndex = null;
    });
    const classes = computed(() => classNames("list-index", colorClasses(props)));
    return {
      elRef,
      classes,
      update,
      scrollListToIndex
    };
  }
};

// node_modules/framework7-vue/components/list-input.js
var list_input_default = {
  name: "f7-list-input",
  props: {
    sortable: {
      type: Boolean,
      default: void 0
    },
    media: String,
    dropdown: {
      type: [String, Boolean],
      default: "auto"
    },
    wrap: {
      type: Boolean,
      default: true
    },
    // Inputs
    input: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: "text"
    },
    name: String,
    value: {
      type: [String, Number, Array, Date, Object],
      default: void 0
    },
    inputmode: String,
    readonly: Boolean,
    required: Boolean,
    disabled: Boolean,
    placeholder: String,
    inputId: [String, Number],
    size: [String, Number],
    accept: [String, Number],
    autocomplete: [String],
    autocorrect: [String],
    autocapitalize: [String],
    spellcheck: [String],
    autofocus: Boolean,
    autosave: String,
    max: [String, Number],
    min: [String, Number],
    step: [String, Number],
    maxlength: [String, Number],
    minlength: [String, Number],
    multiple: Boolean,
    inputStyle: [String, Object],
    pattern: String,
    validate: [Boolean, String],
    validateOnBlur: Boolean,
    onValidate: Function,
    tabindex: [String, Number],
    resizable: Boolean,
    clearButton: Boolean,
    // Form
    noFormStoreData: Boolean,
    noStoreData: Boolean,
    ignoreStoreData: Boolean,
    // Error, Info
    errorMessage: String,
    errorMessageForce: Boolean,
    info: String,
    // Outline
    outline: Boolean,
    // Label
    label: [String, Number],
    floatingLabel: Boolean,
    // Datepicker
    calendarParams: Object,
    // Colorpicker
    colorPickerParams: Object,
    // Text editor
    textEditorParams: Object,
    ...colorProps
  },
  emits: ["textarea:resize", "input:notempty", "input:empty", "input:clear", "texteditor:change", "calendar:change", "colorpicker:change", "change", "focus", "blur", "input", "update:value"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const inputInvalid = ref(false);
    const inputFocused = ref(false);
    const ListContext = inject("ListContext", {
      value: {
        listIsMedia: false,
        listIsSortable: false,
        listIsSortableOpposite: false,
        listIsSimple: false
      }
    });
    let f7Calendar = null;
    let f7ColorPicker = null;
    const elRef = ref(null);
    const inputElRef = ref(null);
    const itemContentElRef = ref(null);
    let updateInputOnDidUpdate = false;
    const getDomValue = () => {
      if (!inputElRef.value) return void 0;
      return inputElRef.value.value;
    };
    const domValue = ref(getDomValue());
    const inputHasValue = computed(() => {
      if (props.type === "datepicker" && Array.isArray(props.value) && props.value.length === 0) {
        return false;
      }
      return typeof props.value === "undefined" ? domValue.value || domValue.value === 0 : props.value || props.value === 0;
    });
    const validateInput = () => {
      if (!f7 || !inputElRef.value) return;
      const validity = inputElRef.value.validity;
      if (!validity) return;
      if (!validity.valid) {
        if (props.onValidate) props.onValidate(false);
        if (inputInvalid.value !== true) {
          inputInvalid.value = true;
        }
      } else {
        if (props.onValidate) props.onValidate(true);
        if (inputInvalid.value !== false) {
          inputInvalid.value = false;
        }
      }
    };
    const onTextareaResize = (event) => {
      emit("textarea:resize", event);
    };
    const onInputNotEmpty = (event) => {
      emit("input:notempty", event);
    };
    const onInputEmpty = (event) => {
      emit("input:empty", event);
    };
    const onInputClear = (event) => {
      emit("input:clear", event);
    };
    const onInput = function() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      emit("input", ...args);
      if (inputElRef.value) {
        domValue.value = inputElRef.value.value;
      }
      if (!(props.validateOnBlur || props.validateOnBlur === "") && (props.validate || props.validate === "") && inputElRef.value) {
        validateInput(inputElRef.value);
      }
      if (inputElRef.value && props.type !== "texteditor" && props.type !== "colorpicker" && props.type !== "datepicker") {
        emit("update:value", inputElRef.value.value);
      }
    };
    const onFocus = function() {
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      emit("focus", ...args);
      inputFocused.value = true;
    };
    const onBlur = function() {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      emit("blur", ...args);
      if ((props.validate || props.validate === "" || props.validateOnBlur || props.validateOnBlur === "") && inputElRef.value) {
        validateInput(inputElRef.value);
      }
      inputFocused.value = false;
    };
    const onChange = function() {
      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
        args[_key4] = arguments[_key4];
      }
      emit("change", ...args);
      if (props.type === "texteditor") {
        emit("texteditor:change", args[0]);
        emit("update:value", args[0]);
      }
    };
    onMounted(() => {
      if (!elRef.value && !itemContentElRef.value) return;
      f7ready(() => {
        if (!inputElRef.value) return;
        inputElRef.value.addEventListener("input:notempty", onInputNotEmpty, false);
        inputElRef.value.addEventListener("textarea:resize", onTextareaResize, false);
        inputElRef.value.addEventListener("input:empty", onInputEmpty, false);
        inputElRef.value.addEventListener("input:clear", onInputClear, false);
        if (props.type === "datepicker") {
          f7Calendar = f7.calendar.create({
            inputEl: inputElRef.value,
            value: props.value,
            on: {
              change(calendar, calendarValue) {
                emit("update:value", calendarValue);
                emit("calendar:change", calendarValue);
              }
            },
            ...props.calendarParams || {}
          });
        }
        if (props.type === "colorpicker") {
          f7ColorPicker = f7.colorPicker.create({
            inputEl: inputElRef.value,
            value: props.value,
            on: {
              change(colorPicker, colorPickerValue) {
                emit("update:value", colorPickerValue);
                emit("colorpicker:change", colorPickerValue);
              }
            },
            ...props.colorPickerParams || {}
          });
        }
        if (!(props.validateOnBlur || props.validateOnBlur === "") && (props.validate || props.validate === "") && typeof props.value !== "undefined" && props.value !== null && props.value !== "") {
          setTimeout(() => {
            validateInput();
          }, 0);
        }
        if (props.type === "textarea" && props.resizable) {
          f7.input.resizeTextarea(inputElRef.value);
        }
      });
    });
    onBeforeUnmount(() => {
      if (inputElRef.value) {
        inputElRef.value.removeEventListener("input:notempty", onInputNotEmpty, false);
        inputElRef.value.removeEventListener("textarea:resize", onTextareaResize, false);
        inputElRef.value.removeEventListener("input:empty", onInputEmpty, false);
        inputElRef.value.removeEventListener("input:clear", onInputClear, false);
      }
      if (f7Calendar && f7Calendar.destroy) {
        f7Calendar.destroy();
        f7Calendar = null;
      }
      if (f7ColorPicker && f7ColorPicker.destroy) {
        f7ColorPicker.destroy();
        f7ColorPicker = null;
      }
    });
    onUpdated(() => {
      if (!f7) return;
      if (updateInputOnDidUpdate) {
        if (!inputElRef.value) return;
        updateInputOnDidUpdate = false;
        if (props.validate && !props.validateOnBlur) {
          validateInput();
        }
        if (props.type === "textarea" && props.resizable) {
          f7.input.resizeTextarea(inputElRef.value);
        }
      }
    });
    watch(() => props.colorPickerParams, (newValue) => {
      if (!f7 || !f7ColorPicker) return;
      extend(f7ColorPicker.params, newValue || {});
    });
    watch(() => props.calendarParams, (newValue) => {
      if (!f7 || !f7Calendar) return;
      extend(f7Calendar.params, newValue || {});
    });
    watch(() => props.value, (newValue) => {
      if (!f7) return;
      updateInputOnDidUpdate = true;
      if (f7Calendar) {
        f7Calendar.setValue(newValue);
      }
      if (f7ColorPicker) {
        f7ColorPicker.setValue(newValue);
      }
    });
    const isSortableComputed = computed(() => props.sortable === true || props.sortable === false ? props.sortable : ListContext.value.listIsSortable || false);
    const createInput = (InputTag, children) => {
      const needsValue = props.type !== "file" && props.type !== "datepicker" && props.type !== "colorpicker";
      const needsType = InputTag === "input";
      let inputType = props.type;
      if (inputType === "datepicker" || inputType === "colorpicker") {
        inputType = "text";
      }
      const inputClassName = classNames({
        resizable: inputType === "textarea" && props.resizable,
        "no-store-data": props.noFormStoreData || props.noStoreData || props.ignoreStoreData,
        "input-invalid": props.errorMessage && props.errorMessageForce || inputInvalid.value,
        "input-with-value": inputHasValue.value,
        "input-focused": inputFocused.value
      });
      let inputValue;
      if (needsValue) {
        if (typeof props.value !== "undefined") inputValue = props.value;
        else inputValue = domValue.value;
      }
      const valueProps = {};
      if (props.type !== "datepicker" && props.type !== "colorpicker") {
        if ("value" in props) valueProps.value = inputValue;
      }
      const inputProps = noUndefinedProps({
        name: props.name,
        type: needsType ? inputType : void 0,
        placeholder: props.placeholder,
        inputmode: props.inputmode,
        id: props.inputId,
        size: props.size,
        accept: props.accept,
        autocomplete: props.autocomplete,
        autocorrect: props.autocorrect,
        autocapitalize: props.autocapitalize,
        spellcheck: props.spellcheck,
        autofocus: props.autofocus,
        autosave: props.autosave,
        disabled: props.disabled,
        max: props.max,
        maxlength: props.maxlength,
        min: props.min,
        minlength: props.minlength,
        step: props.step,
        multiple: props.multiple,
        readonly: props.readonly,
        required: props.required,
        pattern: props.pattern,
        validate: typeof props.validate === "string" && props.validate.length ? props.validate : void 0,
        tabindex: props.tabindex
      });
      return h(InputTag, {
        ref: inputElRef,
        style: props.inputStyle,
        ...inputProps,
        "data-validate": props.validate === true || props.validate === "" || props.validateOnBlur === true || props.validateOnBlur === "" ? true : void 0,
        "data-validate-on-blur": props.validateOnBlur === true || props.validateOnBlur === "" ? true : void 0,
        "data-error-message": props.errorMessageForce ? void 0 : props.errorMessage,
        class: inputClassName,
        onFocus,
        onBlur,
        onInput,
        onChange,
        ...valueProps
      }, [children]);
    };
    return () => {
      let inputEl;
      if (props.input) {
        if (props.type === "select" || props.type === "textarea" || props.type === "file") {
          if (props.type === "select") {
            inputEl = createInput("select", slots.default && slots.default());
          } else if (props.type === "file") {
            inputEl = createInput("input");
          } else {
            inputEl = createInput("textarea");
          }
        } else if (props.type === "texteditor") {
          inputEl = h(text_editor_default, {
            value: props.value,
            resizable: props.resizable,
            placeholder: props.placeholder,
            onTexteditorFocus: onFocus,
            onTexteditorBlur: onBlur,
            onTexteditorInput: onInput,
            onTexteditorChange: onChange,
            ...props.textEditorParams || {}
          });
        } else {
          inputEl = createInput("input");
        }
      }
      const hasErrorMessage = !!props.errorMessage || slots["error-message"];
      const ItemContent = h("div", {
        ref: itemContentElRef,
        class: classNames("item-content item-input", !props.wrap && {
          disabled: props.disabled
        }, !props.wrap && colorClasses(props), {
          "item-input-outline": props.outline,
          "item-input-focused": inputFocused.value,
          "item-input-with-info": !!props.info || slots.info,
          "item-input-with-value": inputHasValue.value,
          "item-input-with-error-message": hasErrorMessage && props.errorMessageForce || inputInvalid.value,
          "item-input-invalid": hasErrorMessage && props.errorMessageForce || inputInvalid.value
        })
      }, [slots["content-start"] && slots["content-start"](), (props.media || slots.media) && h("div", {
        class: "item-media"
      }, [props.media && h("img", {
        src: props.media
      }), slots.media && slots.media()]), h("div", {
        class: "item-inner"
      }, [slots["inner-start"] && slots["inner-start"](), (props.label || slots.label) && h("div", {
        class: classNames("item-title item-label", {
          "item-floating-label": props.floatingLabel
        })
      }, [props.label, slots.label && slots.label()]), h("div", {
        class: classNames("item-input-wrap", {
          "input-dropdown": props.dropdown === "auto" ? props.type === "select" : props.dropdown
        })
      }, [inputEl, slots.input && slots.input(), hasErrorMessage && props.errorMessageForce && h("div", {
        class: "item-input-error-message"
      }, [props.errorMessage, slots["error-message"] && slots["error-message"]()]), props.clearButton && h("span", {
        class: "input-clear-button"
      }), (props.info || slots.info) && h("div", {
        class: "item-input-info"
      }, [props.info, slots.info && slots.info()])]), slots.inner && slots.inner(), slots["inner-end"] && slots["inner-end"]()]), slots.content && slots.content(), slots["content-end"] && slots["content-end"]()]);
      if (!props.wrap) return ItemContent;
      return h("li", {
        ref: elRef,
        class: classNames({
          disabled: props.disabled
        }, colorClasses(props))
      }, [slots["root-start"] && slots["root-start"](), ItemContent, isSortableComputed.value && h("div", {
        class: "sortable-handler"
      }), slots.root && slots.root(), slots["root-end"] && slots["root-end"]()]);
    };
  }
};

// node_modules/framework7-vue/components/list-item.js
var ListItemContent = function(_temp) {
  let {
    props,
    slots,
    inputElRef,
    onChange,
    onClick,
    isMediaComputed,
    isSortableComputed,
    isSortableOppositeComputed,
    itemContentClasses
  } = _temp === void 0 ? {} : _temp;
  const {
    radio,
    checkbox,
    value,
    name,
    readonly,
    disabled,
    checked,
    required,
    media,
    header,
    footer,
    title,
    subtitle,
    text,
    after,
    badge,
    badgeColor,
    swipeout,
    sortable,
    accordionItem
  } = props;
  let titleEl;
  let afterWrapEl;
  let afterEl;
  let badgeEl;
  let innerEl;
  let titleRowEl;
  let subtitleEl;
  let textEl;
  let mediaEl;
  let inputEl;
  let inputIconEl;
  let headerEl;
  let footerEl;
  if (radio || checkbox) {
    inputEl = h("input", {
      ref: inputElRef,
      value,
      name,
      checked,
      readonly,
      disabled,
      required,
      type: radio ? "radio" : "checkbox",
      onChange
    });
    inputIconEl = h("i", {
      class: `icon icon-${radio ? "radio" : "checkbox"}`
    });
  }
  if (media || slots.media) {
    let mediaImgEl;
    if (media) {
      mediaImgEl = h("img", {
        src: media
      });
    }
    mediaEl = h("div", {
      class: "item-media"
    }, [mediaImgEl, slots.media && slots.media()]);
  }
  if (header || slots.header) {
    headerEl = h("div", {
      class: "item-header"
    }, [header, slots.header && slots.header()]);
  }
  if (footer || slots.footer) {
    footerEl = h("div", {
      class: "item-footer"
    }, [footer, slots.footer && slots.footer()]);
  }
  if (title || slots.title || !isMediaComputed.value && headerEl || !isMediaComputed.value && footerEl) {
    titleEl = h("div", {
      class: "item-title"
    }, [!isMediaComputed.value && headerEl, title, slots.title && slots.title(), !isMediaComputed.value && footerEl]);
  }
  if (subtitle || slots.subtitle) {
    subtitleEl = h("div", {
      class: "item-subtitle"
    }, [subtitle, slots.subtitle && slots.subtitle()]);
  }
  if (text || slots.text) {
    textEl = h("div", {
      class: "item-text"
    }, [text, slots.text && slots.text()]);
  }
  if (after || badge || slots.after) {
    if (after) {
      afterEl = h("span", [after]);
    }
    if (badge) {
      badgeEl = h(badge_default, {
        color: badgeColor
      }, () => badge);
    }
    afterWrapEl = h("div", {
      class: "item-after"
    }, [slots["after-start"] && slots["after-start"](), afterEl, badgeEl, slots.after && slots.after(), slots["after-end"] && slots["after-end"]()]);
  }
  if (isMediaComputed.value) {
    titleRowEl = h("div", {
      class: "item-title-row"
    }, [slots["before-title"] && slots["before-title"](), titleEl, slots["after-title"] && slots["after-title"](), afterWrapEl]);
    innerEl = h("div", {
      class: "item-inner"
    }, [slots["inner-start"] && slots["inner-start"], headerEl, titleRowEl, subtitleEl, textEl, swipeout || accordionItem ? null : slots.default && slots.default(), slots.inner && slots.inner(), footerEl, slots["inner-end"] && slots["inner-end"]]);
  } else {
    innerEl = h("div", {
      class: "item-inner"
    }, [slots["inner-start"] && slots["inner-start"](), slots["before-title"] && slots["before-title"](), titleEl, slots["after-title"] && slots["after-title"](), afterWrapEl, swipeout || accordionItem ? null : slots.default && slots.default(), slots.inner && slots.inner(), slots["inner-end"] && slots["inner-end"]()]);
  }
  const ItemContentTag = checkbox || radio ? "label" : "div";
  return h(ItemContentTag, {
    class: itemContentClasses.value,
    onClick
  }, [isSortableComputed.value && sortable !== false && isSortableOppositeComputed.value && h("div", {
    class: "sortable-handler"
  }), slots["content-start"] && slots["content-start"](), inputEl, inputIconEl, mediaEl, innerEl, slots.content && slots.content(), slots["content-end"] && slots["content-end"]()]);
};
var list_item_default = {
  name: "f7-list-item",
  props: {
    title: [String, Number],
    text: [String, Number],
    media: String,
    subtitle: [String, Number],
    header: [String, Number],
    footer: [String, Number],
    // Tooltip
    tooltip: String,
    tooltipTrigger: String,
    // Link Props
    link: [Boolean, String],
    target: String,
    tabLink: [Boolean, String],
    tabLinkActive: Boolean,
    selected: Boolean,
    after: [String, Number],
    badge: [String, Number],
    badgeColor: String,
    mediaItem: Boolean,
    mediaList: Boolean,
    groupTitle: Boolean,
    swipeout: Boolean,
    swipeoutOpened: Boolean,
    sortable: {
      type: Boolean,
      default: void 0
    },
    sortableOpposite: {
      type: Boolean,
      default: void 0
    },
    accordionItem: Boolean,
    accordionItemOpened: Boolean,
    // Smart Select
    smartSelect: Boolean,
    smartSelectParams: Object,
    // Links Chevron (Arrow) Icon
    noChevron: Boolean,
    chevronCenter: Boolean,
    // Inputs
    checkbox: Boolean,
    radio: Boolean,
    radioIcon: String,
    checkboxIcon: String,
    checked: Boolean,
    indeterminate: Boolean,
    name: String,
    value: {
      type: [String, Number, Array],
      default: void 0
    },
    readonly: Boolean,
    required: Boolean,
    disabled: Boolean,
    virtualListIndex: Number,
    ...colorProps,
    ...actionsProps,
    ...routerProps
  },
  emits: ["click", "swipeout", "swipeout:overswipeenter", "swipeout:overswipeexit", "swipeout:deleted", "swipeout:delete", "swipeout:close", "swipeout:closed", "swipeout:open", "swipeout:opened", "accordion:beforeclose", "accordion:close", "accordion:closed", "accordion:beforeopen", "accordion:open", "accordion:opened", "change", "update:checked"],
  setup(props, _ref) {
    let {
      slots,
      emit
    } = _ref;
    const ListContext = inject("ListContext", {
      value: {
        listIsMedia: false,
        listIsSortable: false,
        listIsSortableOpposite: false,
        listIsSimple: false
      }
    });
    const listIsMedia = computed(() => ListContext.value.listIsMedia || false);
    const listIsSortable = computed(() => ListContext.value.listIsSortable || false);
    const listIsSortableOpposite = computed(() => ListContext.value.listIsSortableOpposite || false);
    const listIsSimple = computed(() => ListContext.value.listIsSimple || false);
    const elRef = ref(null);
    const linkElRef = ref(null);
    const inputElRef = ref(null);
    const onClick = (event) => {
      if (event.target.tagName.toLowerCase() !== "input") {
        emit("click", event);
      }
    };
    const onSwipeoutOverswipeEnter = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:overswipeenter");
    };
    const onSwipeoutOverswipeExit = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:overswipeexit");
    };
    const onSwipeoutDeleted = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:deleted");
    };
    const onSwipeoutDelete = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:delete");
    };
    const onSwipeoutClose = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:close");
    };
    const onSwipeoutClosed = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:closed");
    };
    const onSwipeoutOpen = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:open");
    };
    const onSwipeoutOpened = (el) => {
      if (elRef.value !== el) return;
      emit("swipeout:opened");
    };
    const onSwipeout = (el, progress) => {
      if (elRef.value !== el) return;
      emit("swipeout", progress);
    };
    const onAccBeforeClose = (el, prevent) => {
      if (elRef.value !== el) return;
      emit("accordion:beforeclose", prevent);
    };
    const onAccClose = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:close");
    };
    const onAccClosed = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:closed");
    };
    const onAccBeforeOpen = (el, prevent) => {
      if (elRef.value !== el) return;
      emit("accordion:beforeopen", prevent);
    };
    const onAccOpen = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:open");
    };
    const onAccOpened = (el) => {
      if (elRef.value !== el) return;
      emit("accordion:opened");
    };
    const onChange = (event) => {
      emit("change", event);
      emit("update:checked", event.target.checked);
    };
    useTooltip(elRef, props);
    useRouteProps(linkElRef, props);
    useSmartSelect(props, () => {
    }, () => elRef.value.querySelector("a.smart-select"));
    watch(() => props.swipeoutOpened, (newValue) => {
      if (!props.swipeout || !elRef.value || !f7) return;
      if (newValue) {
        f7.swipeout.open(elRef.value);
      } else {
        f7.swipeout.close(elRef.value);
      }
    });
    onMounted(() => {
      f7ready(() => {
        if (props.swipeout) {
          f7.on("swipeoutOpen", onSwipeoutOpen);
          f7.on("swipeoutOpened", onSwipeoutOpened);
          f7.on("swipeoutClose", onSwipeoutClose);
          f7.on("swipeoutClosed", onSwipeoutClosed);
          f7.on("swipeoutDelete", onSwipeoutDelete);
          f7.on("swipeoutDeleted", onSwipeoutDeleted);
          f7.on("swipeoutOverswipeEnter", onSwipeoutOverswipeEnter);
          f7.on("swipeoutOverswipeExit", onSwipeoutOverswipeExit);
          f7.on("swipeout", onSwipeout);
        }
        if (props.accordionItem) {
          f7.on("accordionBeforeOpen", onAccBeforeOpen);
          f7.on("accordionOpen", onAccOpen);
          f7.on("accordionOpened", onAccOpened);
          f7.on("accordionBeforeClose", onAccBeforeClose);
          f7.on("accordionClose", onAccClose);
          f7.on("accordionClosed", onAccClosed);
        }
        if (props.swipeout && props.swipeoutOpened) {
          f7.swipeout.open(elRef.value);
        }
      });
      if (props.checkbox && inputElRef.value) {
        inputElRef.value.indeterminate = !!props.indeterminate;
      }
    });
    onBeforeUnmount(() => {
      if (!f7) return;
      f7.off("swipeoutOpen", onSwipeoutOpen);
      f7.off("swipeoutOpened", onSwipeoutOpened);
      f7.off("swipeoutClose", onSwipeoutClose);
      f7.off("swipeoutClosed", onSwipeoutClosed);
      f7.off("swipeoutDelete", onSwipeoutDelete);
      f7.off("swipeoutDeleted", onSwipeoutDeleted);
      f7.off("swipeoutOverswipeEnter", onSwipeoutOverswipeEnter);
      f7.off("swipeoutOverswipeExit", onSwipeoutOverswipeExit);
      f7.off("swipeout", onSwipeout);
      f7.off("accordionBeforeOpen", onAccBeforeOpen);
      f7.off("accordionOpen", onAccOpen);
      f7.off("accordionOpened", onAccOpened);
      f7.off("accordionBeforeClose", onAccBeforeClose);
      f7.off("accordionClose", onAccClose);
      f7.off("accordionClosed", onAccClosed);
    });
    watch(() => props.indeterminate, (newValue) => {
      if (inputElRef.value) {
        inputElRef.value.indeterminate = !!newValue;
      }
    });
    const isMediaComputed = computed(() => props.mediaItem || props.mediaList || listIsMedia.value);
    const isSortableComputed = computed(() => props.sortable === true || props.sortable === false ? props.sortable : listIsSortable.value);
    const isSortableOppositeComputed = computed(() => isSortableComputed.value && (props.sortableOpposite || listIsSortableOpposite.value));
    const linkAttrs = computed(() => ({
      href: props.href === false ? void 0 : props.link === true ? props.href || "" : props.link || props.href,
      target: props.target,
      "data-tab": isStringProp(props.tabLink) && props.tabLink || void 0,
      ...routerAttrs(props),
      ...actionsAttrs(props)
    }));
    const linkClasses = computed(() => classNames({
      "item-link": true,
      "smart-select": props.smartSelect,
      "tab-link": props.tabLink || props.tabLink === "",
      "tab-link-active": props.tabLinkActive,
      "item-selected": props.selected
    }, routerClasses(props), actionsClasses(props)));
    const itemContentClasses = computed(() => classNames("item-content", {
      "item-checkbox": props.checkbox,
      "item-radio": props.radio,
      "item-checkbox-icon-start": props.checkbox && props.checkboxIcon === "start",
      "item-checkbox-icon-end": props.checkbox && props.checkboxIcon === "end",
      "item-radio-icon-start": props.radio && props.radioIcon === "start",
      "item-radio-icon-end": props.radio && props.radioIcon === "end"
    }, colorClasses(props)));
    const liClasses = computed(() => classNames({
      "list-group-title": props.groupTitle,
      "media-item": isMediaComputed.value,
      swipeout: props.swipeout,
      "accordion-item": props.accordionItem,
      "accordion-item-opened": props.accordionItemOpened,
      disabled: props.disabled && !(props.radio || props.checkbox),
      "no-chevron": props.noChevron,
      "chevron-center": props.chevronCenter,
      "disallow-sorting": props.sortable === false
    }, colorClasses(props)));
    return () => {
      let linkEl;
      let itemContentEl;
      if (!listIsSimple.value) {
        itemContentEl = ListItemContent({
          props,
          slots,
          inputElRef,
          onChange,
          onClick: props.link || props.href || props.accordionItem || props.smartSelect ? void 0 : onClick,
          isMediaComputed,
          isSortableComputed,
          isSortableOppositeComputed,
          itemContentClasses
        });
        if (props.link || props.href || props.accordionItem || props.smartSelect) {
          linkEl = h("a", {
            ref: linkElRef,
            class: linkClasses.value,
            ...linkAttrs.value,
            onClick
          }, [itemContentEl]);
        }
      }
      if (props.groupTitle) {
        return h("li", {
          ref: elRef,
          class: liClasses.value,
          "data-virtual-list-index": props.virtualListIndex,
          onClick
        }, [props.title, slots.default && slots.default()]);
      }
      if (listIsSimple.value) {
        return h("li", {
          ref: elRef,
          class: liClasses.value,
          onClick,
          "data-virtual-list-index": props.virtualListIndex
        }, [props.title, slots.default && slots.default()]);
      }
      const linkItemEl = props.link || props.href || props.smartSelect || props.accordionItem ? linkEl : itemContentEl;
      return h("li", {
        ref: elRef,
        class: liClasses.value,
        "data-virtual-list-index": props.virtualListIndex
      }, [slots["root-start"] && slots["root-start"](), props.swipeout ? h("div", {
        class: "swipeout-content"
      }, [linkItemEl]) : linkItemEl, isSortableComputed.value && props.sortable !== false && !isSortableOppositeComputed.value && h("div", {
        class: "sortable-handler"
      }), (props.swipeout || props.accordionItem) && slots.default(), slots.root && slots.root(), slots["root-end"] && slots["root-end"]()]);
    };
  }
};

// node_modules/framework7-vue/components/list.js
var list_default = {
  name: "f7-list",
  props: {
    inset: Boolean,
    insetIos: Boolean,
    insetMd: Boolean,
    xsmallInset: Boolean,
    xsmallInsetIos: Boolean,
    xsmallInsetMd: Boolean,
    smallInset: Boolean,
    smallInsetIos: Boolean,
    smallInsetMd: Boolean,
    mediumInset: Boolean,
    mediumInsetIos: Boolean,
    mediumInsetMd: Boolean,
    largeInset: Boolean,
    largeInsetIos: Boolean,
    largeInsetMd: Boolean,
    xlargeInset: Boolean,
    xlargeInsetIos: Boolean,
    xlargeInsetMd: Boolean,
    strong: Boolean,
    strongIos: Boolean,
    strongMd: Boolean,
    outline: Boolean,
    outlineIos: Boolean,
    outlineMd: Boolean,
    dividers: Boolean,
    dividersIos: Boolean,
    dividersMd: Boolean,
    mediaList: Boolean,
    sortable: Boolean,
    sortableTapHold: Boolean,
    sortableEnabled: Boolean,
    sortableMoveElements: {
      type: Boolean,
      default: void 0
    },
    sortableOpposite: Boolean,
    accordionList: Boolean,
    accordionOpposite: Boolean,
    contactsList: Boolean,
    simpleList: Boolean,
    linksList: Boolean,
    menuList: Boolean,
    // Links Chevron (Arrow) Icon
    noChevron: Boolean,
    chevronCenter: Boolean,
    // Tab
    tab: Boolean,
    tabActive: Boolean,
    // Form
    form: Boolean,
    formStoreData: Boolean,
    // Virtual List
    virtualList: Boolean,
    virtualListParams: Object,
    ...colorProps
  },
  emits: ["submit", "sortable:enable", "sortable:disable", "sortable:sort", "sortable:move", "virtual:itembeforeinsert", "virtual:beforeclear", "virtual:itemsbeforeinsert", "virtual:itemsafterinsert", "tab:hide", "tab:show"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    let f7VirtualList = null;
    const elRef = ref(null);
    const onSubmit = (event) => {
      emit("submit", event);
    };
    const onSortableEnable = (el) => {
      if (elRef.value !== el) return;
      emit("sortable:enable");
    };
    const onSortableDisable = (el) => {
      if (elRef.value !== el) return;
      emit("sortable:disable");
    };
    const onSortableSort = (el, sortData, listEl) => {
      if (elRef.value !== listEl) return;
      emit("sortable:sort", sortData);
    };
    const onSortableMove = (el, listEl) => {
      if (elRef.value !== listEl) return;
      emit("sortable:move", el, listEl);
    };
    useTab(elRef, emit);
    onMounted(() => {
      f7ready(() => {
        f7.on("sortableEnable", onSortableEnable);
        f7.on("sortableDisable", onSortableDisable);
        f7.on("sortableSort", onSortableSort);
        f7.on("sortableMove", onSortableMove);
        if (!props.virtualList) return;
        const vlParams = props.virtualListParams || {};
        if (!vlParams.renderItem && !vlParams.renderExternal) return;
        if (vlParams.items) vlParams.items = toRaw(vlParams.items);
        f7VirtualList = f7.virtualList.create(extend({
          el: elRef.value,
          on: {
            itemBeforeInsert(itemEl, item) {
              const vl = this;
              emit("virtual:itembeforeinsert", vl, itemEl, item);
            },
            beforeClear(fragment) {
              const vl = this;
              emit("virtual:beforeclear", vl, fragment);
            },
            itemsBeforeInsert(fragment) {
              const vl = this;
              emit("virtual:itemsbeforeinsert", vl, fragment);
            },
            itemsAfterInsert(fragment) {
              const vl = this;
              emit("virtual:itemsafterinsert", vl, fragment);
            }
          }
        }, vlParams));
      });
    });
    onBeforeUnmount(() => {
      if (!f7) return;
      f7.off("sortableEnable", onSortableEnable);
      f7.off("sortableDisable", onSortableDisable);
      f7.off("sortableSort", onSortableSort);
      f7.off("sortableMove", onSortableMove);
      if (!(props.virtualList && f7VirtualList)) return;
      if (f7VirtualList.destroy) f7VirtualList.destroy();
      f7VirtualList = null;
    });
    const classes = computed(() => classNames("list", {
      inset: props.inset,
      "inset-ios": props.insetIos,
      "inset-md": props.insetMd,
      "xsmall-inset": props.xsmallInset,
      "xsmall-inset-ios": props.xsmallInsetIos,
      "xsmall-inset-md": props.xsmallInsetMd,
      "small-inset": props.smallInset,
      "small-inset-ios": props.smallInsetIos,
      "small-inset-md": props.smallInsetMd,
      "medium-inset": props.mediumInset,
      "medium-inset-ios": props.mediumInsetIos,
      "medium-inset-md": props.mediumInsetMd,
      "large-inset": props.largeInset,
      "large-inset-ios": props.largeInsetIos,
      "large-inset-md": props.largeInsetMd,
      "xlarge-inset": props.xlargeInset,
      "xlarge-inset-ios": props.xlargeInsetIos,
      "xlarge-inset-md": props.xlargeInsetMd,
      "list-strong": props.strong,
      "list-strong-ios": props.strongIos,
      "list-strong-md": props.strongMd,
      "list-outline": props.outline,
      "list-outline-ios": props.outlineIos,
      "list-outline-md": props.outlineMd,
      "list-dividers": props.dividers,
      "list-dividers-ios": props.dividersIos,
      "list-dividers-md": props.dividersMd,
      "media-list": props.mediaList,
      "simple-list": props.simpleList,
      "links-list": props.linksList,
      "menu-list": props.menuList,
      sortable: props.sortable,
      "sortable-tap-hold": props.sortableTapHold,
      "sortable-enabled": props.sortableEnabled,
      "sortable-opposite": props.sortableOpposite,
      "accordion-list": props.accordionList,
      "accordion-opposite": props.accordionOpposite,
      "contacts-list": props.contactsList,
      "virtual-list": props.virtualList,
      tab: props.tab,
      "tab-active": props.tabActive,
      "form-store-data": props.formStoreData,
      "no-chevron": props.noChevron,
      "chevron-center": props.chevronCenter
    }, colorClasses(props)));
    const ListTag = computed(() => props.form ? "form" : "div");
    const ListContext = computed(() => ({
      listIsMedia: props.mediaList,
      listIsSimple: props.simpleList,
      listIsSortable: props.sortable,
      listIsSortableOpposite: props.sortableOpposite
    }));
    provide("ListContext", ListContext);
    return () => {
      const {
        list: slotsList,
        default: slotsDefault
      } = slots;
      const rootChildrenBeforeList = [];
      const rootChildrenAfterList = [];
      const ulChildren = slotsList && typeof slotsList === "function" ? [slotsList()] : [];
      let wasUlChild = false;
      if (slotsDefault) {
        slotsDefault().forEach((vnode) => {
          if (typeof vnode === "undefined") return;
          const tag = vnode.type && vnode.type.name ? vnode.type.name : vnode.type;
          if (tag && typeof tag === "symbol") {
            wasUlChild = true;
            ulChildren.push(vnode);
          } else if (!tag || tag && !(tag === "li" || tag.indexOf("f7-list-item") >= 0 || tag.indexOf("f7-list-button") >= 0 || tag.indexOf("f7-list-input") >= 0)) {
            if (wasUlChild) rootChildrenAfterList.push(vnode);
            else rootChildrenBeforeList.push(vnode);
          } else if (tag) {
            wasUlChild = true;
            ulChildren.push(vnode);
          }
        });
      }
      return h(ListTag.value, {
        ref: elRef,
        class: classes.value,
        "data-sortable-move-elements": typeof props.sortableMoveElements !== "undefined" ? props.sortableMoveElements.toString() : void 0,
        onSubmit
      }, [slots["before-list"] && slots["before-list"](), rootChildrenBeforeList, ulChildren.length > 0 && h("ul", ulChildren), slots["after-list"] && slots["after-list"](), rootChildrenAfterList]);
    };
  }
};

// node_modules/framework7-vue/components/login-screen-title.js
function render45(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var login_screen_title_default = {
  name: "f7-login-screen-title",
  render: render45,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("login-screen-title", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/message.js
var _hoisted_120 = {
  class: "message-content"
};
var _hoisted_211 = {
  key: 0,
  class: "message-image"
};
var _hoisted_34 = ["src"];
var _hoisted_44 = {
  key: 1,
  class: "message-text-header"
};
var _hoisted_54 = {
  key: 0,
  class: "message-typing-indicator"
};
var _hoisted_62 = createBaseVNode("div", null, null, -1);
var _hoisted_72 = createBaseVNode("div", null, null, -1);
var _hoisted_82 = createBaseVNode("div", null, null, -1);
var _hoisted_9 = [_hoisted_62, _hoisted_72, _hoisted_82];
var _hoisted_10 = {
  key: 3,
  class: "message-text-footer"
};
function render46(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    onClick: _cache[6] || (_cache[6] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [renderSlot(_ctx.$slots, "start"), _ctx.hasAvatar ? (openBlock(), createElementBlock("div", {
    key: 0,
    class: "message-avatar",
    style: normalizeStyle({
      backgroundImage: _ctx.avatar && `url(${_ctx.avatar})`
    }),
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onAvatarClick && _ctx.onAvatarClick(...arguments);
    })
  }, [renderSlot(_ctx.$slots, "avatar")], 4)) : createCommentVNode("", true), createBaseVNode("div", _hoisted_120, [renderSlot(_ctx.$slots, "content-start"), _ctx.hasName ? (openBlock(), createElementBlock("div", {
    key: 0,
    class: "message-name",
    onClick: _cache[1] || (_cache[1] = function() {
      return _ctx.onNameClick && _ctx.onNameClick(...arguments);
    })
  }, [createTextVNode(toDisplayString(_ctx.name) + " ", 1), renderSlot(_ctx.$slots, "name")])) : createCommentVNode("", true), _ctx.hasHeader ? (openBlock(), createElementBlock("div", {
    key: 1,
    class: "message-header",
    onClick: _cache[2] || (_cache[2] = function() {
      return _ctx.onHeaderClick && _ctx.onHeaderClick(...arguments);
    })
  }, [createTextVNode(toDisplayString(_ctx.header) + " ", 1), renderSlot(_ctx.$slots, "header")])) : createCommentVNode("", true), createBaseVNode("div", {
    class: "message-bubble",
    onClick: _cache[4] || (_cache[4] = function() {
      return _ctx.onBubbleClick && _ctx.onBubbleClick(...arguments);
    })
  }, [renderSlot(_ctx.$slots, "bubble-start"), _ctx.hasImage ? (openBlock(), createElementBlock("div", _hoisted_211, [renderSlot(_ctx.$slots, "image", {}, () => [createBaseVNode("img", {
    src: _ctx.image
  }, null, 8, _hoisted_34)])])) : createCommentVNode("", true), _ctx.hasTextHeader ? (openBlock(), createElementBlock("div", _hoisted_44, [createTextVNode(toDisplayString(_ctx.textHeader) + " ", 1), renderSlot(_ctx.$slots, "text-header")])) : createCommentVNode("", true), _ctx.hasText ? (openBlock(), createElementBlock("div", {
    key: 2,
    class: "message-text",
    onClick: _cache[3] || (_cache[3] = function() {
      return _ctx.onTextClick && _ctx.onTextClick(...arguments);
    })
  }, [createTextVNode(toDisplayString(_ctx.text) + " ", 1), renderSlot(_ctx.$slots, "text"), _ctx.typing ? (openBlock(), createElementBlock("div", _hoisted_54, _hoisted_9)) : createCommentVNode("", true)])) : createCommentVNode("", true), _ctx.hasTextFooter ? (openBlock(), createElementBlock("div", _hoisted_10, [createTextVNode(toDisplayString(_ctx.textFooter) + " ", 1), renderSlot(_ctx.$slots, "text-footer")])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "bubble-end"), renderSlot(_ctx.$slots, "default")]), _ctx.hasFooter ? (openBlock(), createElementBlock("div", {
    key: 2,
    class: "message-footer",
    onClick: _cache[5] || (_cache[5] = function() {
      return _ctx.onFooterClick && _ctx.onFooterClick(...arguments);
    })
  }, [createTextVNode(toDisplayString(_ctx.footer) + " ", 1), renderSlot(_ctx.$slots, "footer")])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "content-end")]), renderSlot(_ctx.$slots, "end")], 2);
}
var message_default = {
  name: "f7-message",
  render: render46,
  props: {
    text: String,
    name: String,
    avatar: String,
    type: {
      type: String,
      default: "sent"
    },
    image: String,
    header: String,
    footer: String,
    textHeader: String,
    textFooter: String,
    first: Boolean,
    last: Boolean,
    tail: Boolean,
    sameName: Boolean,
    sameHeader: Boolean,
    sameFooter: Boolean,
    sameAvatar: Boolean,
    typing: Boolean,
    ...colorProps
  },
  emits: ["click", "click:name", "click:text", "click:avatar", "click:header", "click:footer", "click:bubble"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const onClick = (event) => {
      emit("click", event);
    };
    const onNameClick = (event) => {
      emit("click:name", event);
    };
    const onTextClick = (event) => {
      emit("click:text", event);
    };
    const onAvatarClick = (event) => {
      emit("click:avatar", event);
    };
    const onHeaderClick = (event) => {
      emit("click:header", event);
    };
    const onFooterClick = (event) => {
      emit("click:footer", event);
    };
    const onBubbleClick = (event) => {
      emit("click:bubble", event);
    };
    const classes = computed(() => classNames("message", {
      "message-sent": props.type === "sent",
      "message-received": props.type === "received",
      "message-typing": props.typing,
      "message-first": props.first,
      "message-last": props.last,
      "message-tail": props.tail,
      "message-same-name": props.sameName,
      "message-same-header": props.sameHeader,
      "message-same-footer": props.sameFooter,
      "message-same-avatar": props.sameAvatar
    }, colorClasses(props)));
    const hasAvatar = computed(() => slots.avatar || props.avatar);
    const hasName = computed(() => slots.name || props.name);
    const hasHeader = computed(() => slots.header || props.header);
    const hasImage = computed(() => slots.image || props.image);
    const hasTextHeader = computed(() => slots["text-header"] || props.textHeader);
    const hasText = computed(() => slots.text || props.text || props.typing);
    const hasTextFooter = computed(() => slots["text-footer"] || props.textFooter);
    const hasFooter = computed(() => slots.footer || props.footer);
    return {
      classes,
      onClick,
      onNameClick,
      onTextClick,
      onAvatarClick,
      onHeaderClick,
      onFooterClick,
      onBubbleClick,
      hasAvatar,
      hasName,
      hasHeader,
      hasImage,
      hasTextHeader,
      hasText,
      hasTextFooter,
      hasFooter
    };
  }
};

// node_modules/framework7-vue/components/messagebar-attachment.js
var _hoisted_121 = ["src"];
function render47(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes),
    onClick: _cache[1] || (_cache[1] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [_ctx.image ? (openBlock(), createElementBlock("img", {
    key: 0,
    src: _ctx.image
  }, null, 8, _hoisted_121)) : createCommentVNode("", true), _ctx.deletable ? (openBlock(), createElementBlock("span", {
    key: 1,
    class: "messagebar-attachment-delete",
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onDeleteClick && _ctx.onDeleteClick(...arguments);
    })
  })) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2);
}
var messagebar_attachment_default = {
  name: "f7-messagebar-attachment",
  render: render47,
  props: {
    image: String,
    deletable: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["attachment:click", "attachment:delete"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onClick = (event) => {
      emit("attachment:click", event);
    };
    const onDeleteClick = (event) => {
      emit("attachment:delete", event);
    };
    const classes = computed(() => classNames("messagebar-attachment", colorClasses(props)));
    return {
      classes,
      onClick,
      onDeleteClick
    };
  }
};

// node_modules/framework7-vue/components/messagebar-attachments.js
function render48(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var messagebar_attachments_default = {
  name: "f7-messagebar-attachments",
  render: render48,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("messagebar-attachments", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/messagebar-sheet-image.js
var _hoisted_122 = ["checked"];
var _hoisted_212 = createBaseVNode("i", {
  class: "icon icon-checkbox"
}, null, -1);
var _hoisted_35 = ["src"];
function render49(_ctx, _cache) {
  return openBlock(), createElementBlock("label", {
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("input", {
    type: "checkbox",
    checked: _ctx.checked,
    onChange: _cache[0] || (_cache[0] = function() {
      return _ctx.onChange && _ctx.onChange(...arguments);
    })
  }, null, 40, _hoisted_122), _hoisted_212, _ctx.image ? (openBlock(), createElementBlock("img", {
    key: 0,
    src: _ctx.image
  }, null, 8, _hoisted_35)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2);
}
var messagebar_sheet_image_default = {
  name: "f7-messagebar-sheet-image",
  render: render49,
  props: {
    image: String,
    checked: Boolean,
    ...colorProps
  },
  emits: ["checked", "unchecked", "change", "update:checked"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onChange = (event) => {
      if (event.target.checked) emit("checked", event);
      else emit("unchecked", event);
      emit("update:checked", event.target.checked);
      emit("change", event);
    };
    const classes = computed(() => classNames("messagebar-sheet-image", "checkbox", colorClasses(props)));
    return {
      classes,
      onChange
    };
  }
};

// node_modules/framework7-vue/components/messagebar-sheet-item.js
function render50(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var messagebar_sheet_item_default = {
  name: "f7-messagebar-sheet-item",
  render: render50,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("messagebar-sheet-item", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/messagebar-sheet.js
function render51(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var messagebar_sheet_default = {
  name: "f7-messagebar-sheet",
  render: render51,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("messagebar-sheet", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/messagebar.js
var messagebar_default = {
  name: "f7-messagebar",
  props: {
    sheetVisible: Boolean,
    attachmentsVisible: Boolean,
    top: Boolean,
    resizable: {
      type: Boolean,
      default: true
    },
    bottomOffset: {
      type: Number,
      default: 0
    },
    topOffset: {
      type: Number,
      default: 0
    },
    maxHeight: Number,
    resizePage: {
      type: Boolean,
      default: true
    },
    sendLink: String,
    value: [String, Number, Array],
    disabled: Boolean,
    readonly: Boolean,
    textareaId: [Number, String],
    name: String,
    placeholder: {
      type: String,
      default: "Message"
    },
    init: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["change", "input", "focus", "blur", "submit", "send", "click", "messagebar:attachmentdelete", "messagebar:attachmentclick", "messagebar:resizepage", "update:value"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    const elRef = ref(null);
    const areaElRef = ref(null);
    let f7Messagebar = null;
    let updateSheetVisible = false;
    let updateAttachmentsVisible = false;
    const onChange = (event) => {
      emit("change", event);
    };
    const onInput = (event) => {
      emit("input", event);
      emit("update:value", event.target.value);
    };
    const onFocus = (event) => {
      emit("focus", event);
    };
    const onBlur = (event) => {
      emit("blur", event);
    };
    const onClick = (event) => {
      const inputValue = areaElRef.value.$el;
      const clear = f7Messagebar ? () => {
        f7Messagebar.clear();
      } : () => {
      };
      emit("submit", inputValue, clear);
      emit("send", inputValue, clear);
      emit("click", event);
    };
    const onAttachmentDelete = (instance, attachmentEl, attachmentElIndex) => {
      emit("messagebar:attachmentdelete", instance, attachmentEl, attachmentElIndex);
    };
    const onAttachmentClick = (instance, attachmentEl, attachmentElIndex) => {
      emit("messagebar:attachmentclick", instance, attachmentEl, attachmentElIndex);
    };
    const onResizePage = (instance) => {
      emit("messagebar:resizepage", instance);
    };
    watch(() => props.sheetVisible, () => {
      if (!props.resizable || !f7Messagebar) return;
      updateSheetVisible = true;
    });
    watch(() => props.attachmentsVisible, () => {
      if (!props.resizable || !f7Messagebar) return;
      updateAttachmentsVisible = true;
    });
    onMounted(() => {
      if (!props.init) return;
      if (!elRef.value) return;
      const params = noUndefinedProps({
        el: elRef.value,
        top: props.top,
        resizePage: props.resizePage,
        bottomOffset: props.bottomOffset,
        topOffset: props.topOffset,
        maxHeight: props.maxHeight,
        on: {
          attachmentDelete: onAttachmentDelete,
          attachmentClick: onAttachmentClick,
          resizePage: onResizePage
        }
      });
      f7ready(() => {
        f7Messagebar = f7.messagebar.create(params);
      });
    });
    onUpdated(() => {
      if (!f7Messagebar) return;
      if (updateSheetVisible) {
        updateSheetVisible = false;
        f7Messagebar.sheetVisible = props.sheetVisible;
        f7Messagebar.resizePage();
      }
      if (updateAttachmentsVisible) {
        updateAttachmentsVisible = false;
        f7Messagebar.attachmentsVisible = props.attachmentsVisible;
        f7Messagebar.resizePage();
      }
    });
    onBeforeUnmount(() => {
      if (f7Messagebar && f7Messagebar.destroy) f7Messagebar.destroy();
      f7Messagebar = null;
    });
    const classes = computed(() => classNames("toolbar", "messagebar", {
      "messagebar-attachments-visible": props.attachmentsVisible,
      "messagebar-sheet-visible": props.sheetVisible
    }, colorClasses(props)));
    return () => {
      const valueProps = {};
      if ("value" in props) valueProps.value = props.value;
      const {
        default: slotsDefault,
        "before-inner": slotsBeforeInner,
        "after-inner": slotsAfterInner,
        "send-link": slotsSendLink,
        "inner-start": slotsInnerStart,
        "inner-end": slotsInnerEnd,
        "before-area": slotsBeforeArea,
        "after-area": slotsAfterArea
      } = slots;
      const innerEndEls = [];
      let messagebarAttachmentsEl;
      let messagebarSheetEl;
      if (slotsDefault) {
        slotsDefault().forEach((vnode) => {
          if (typeof vnode === "undefined") return;
          const tag = vnode.type && vnode.type.name ? vnode.type.name : vnode.type;
          if (tag && (tag.indexOf("messagebar-attachments") >= 0 || tag === "F7MessagebarAttachments" || tag === "f7-messagebar-attachments")) {
            messagebarAttachmentsEl = vnode;
          } else if (tag && (tag.indexOf("messagebar-sheet") >= 0 || tag === "F7MessagebarSheet" || tag === "f7-messagebar-sheet")) {
            messagebarSheetEl = vnode;
          } else {
            innerEndEls.push(vnode);
          }
        });
      }
      return h("div", {
        class: classes.value,
        ref: elRef
      }, [slotsBeforeInner && slotsBeforeInner(), h("div", {
        class: "toolbar-inner"
      }, [slotsInnerStart && slotsInnerStart(), h("div", {
        class: "messagebar-area"
      }, [slotsBeforeArea && slotsBeforeArea(), messagebarAttachmentsEl, h(input_default, {
        inputId: props.textareaId,
        ref: areaElRef,
        type: "textarea",
        wrap: false,
        placeholder: props.placeholder,
        disabled: props.disabled,
        name: props.name,
        readonly: props.readonly,
        resizable: props.resizable,
        onInput,
        onChange,
        onFocus,
        onBlur,
        ...valueProps
      }), slotsAfterArea && slotsAfterArea()]), (props.sendLink && props.sendLink.length > 0 || slotsSendLink) && h(link_default, {
        onClick
      }, [slotsSendLink ? slotsSendLink() : props.sendLink]), slotsInnerEnd && slotsInnerEnd(), innerEndEls]), slotsAfterInner && slotsAfterInner(), messagebarSheetEl]);
    };
  }
};

// node_modules/framework7-vue/components/messages-title.js
function render52(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var messages_title_default = {
  name: "f7-messages-title",
  render: render52,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("messages-title", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/messages.js
function render53(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var messages_default = {
  name: "f7-messages",
  render: render53,
  props: {
    autoLayout: {
      type: Boolean,
      default: false
    },
    messages: {
      type: Array,
      default() {
        return [];
      }
    },
    newMessagesFirst: {
      type: Boolean,
      default: false
    },
    scrollMessages: {
      type: Boolean,
      default: true
    },
    scrollMessagesOnEdge: {
      type: Boolean,
      default: true
    },
    typing: {
      type: Boolean,
      default: false
    },
    firstMessageRule: Function,
    lastMessageRule: Function,
    tailMessageRule: Function,
    sameNameMessageRule: Function,
    sameHeaderMessageRule: Function,
    sameFooterMessageRule: Function,
    sameAvatarMessageRule: Function,
    customClassMessageRule: Function,
    renderMessage: Function,
    init: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    let f7Messages = null;
    let childrenBeforeUpdated = null;
    const elRef = ref(null);
    onMounted(() => {
      if (!props.init) return;
      f7ready(() => {
        f7Messages = f7.messages.create(noUndefinedProps({
          el: elRef.value,
          autoLayout: props.autoLayout,
          messages: props.messages,
          newMessagesFirst: props.newMessagesFirst,
          scrollMessages: props.scrollMessages,
          scrollMessagesOnEdge: props.scrollMessagesOnEdge,
          firstMessageRule: props.firstMessageRule,
          lastMessageRule: props.lastMessageRule,
          tailMessageRule: props.tailMessageRule,
          sameNameMessageRule: props.sameNameMessageRule,
          sameHeaderMessageRule: props.sameHeaderMessageRule,
          sameFooterMessageRule: props.sameFooterMessageRule,
          sameAvatarMessageRule: props.sameAvatarMessageRule,
          customClassMessageRule: props.customClassMessageRule,
          renderMessage: props.renderMessage
        }));
        if (f7Messages && props.typing) {
          f7Messages.showTyping();
        }
      });
    });
    onBeforeUpdate(() => {
      if (!props.init || !elRef.value) return;
      const children = elRef.value.children;
      if (!children) return;
      childrenBeforeUpdated = children.length;
      for (let i = 0; i < children.length; i += 1) {
        children[i].classList.add("message-appeared");
      }
      const childrenAfterUpdate = getChildren(slots);
      if (f7Messages && props.scrollMessages && childrenBeforeUpdated !== childrenAfterUpdate) {
        f7Messages.setScrollData();
      }
    });
    onUpdated(() => {
      if (!props.init) return;
      if (!elRef.value) return;
      const children = elRef.value.children;
      if (!children) return;
      const childerAftterUpdated = children.length;
      for (let i = 0; i < children.length; i += 1) {
        if (!children[i].classList.contains("message-appeared")) {
          children[i].classList.add("message-appear-from-bottom");
        }
      }
      if (f7Messages && f7Messages.layout && props.autoLayout) {
        f7Messages.layout();
      }
      if (childerAftterUpdated !== childrenBeforeUpdated && f7Messages && f7Messages.scroll && f7Messages.scrollData && props.scrollMessages) {
        f7Messages.scrollWithEdgeCheck(true);
      }
    });
    onBeforeUnmount(() => {
      if (f7Messages && f7Messages.destroy) f7Messages.destroy();
      f7Messages = null;
    });
    watch(() => props.typing, (newValue) => {
      if (!f7Messages) return;
      if (newValue) f7Messages.showTyping();
      else f7Messages.hideTyping();
    });
    const classes = computed(() => classNames("messages", colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/nav-left.js
function render54(_ctx, _cache) {
  const _component_f7_link = resolveComponent("f7-link");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [_ctx.backLink ? (openBlock(), createBlock(_component_f7_link, {
    key: 0,
    href: _ctx.backLinkUrl || "#",
    back: "",
    icon: "icon-back",
    force: _ctx.backLinkForce || void 0,
    class: normalizeClass(!_ctx.text ? "icon-only" : void 0),
    text: _ctx.text,
    onClick: _ctx.onBackClick
  }, null, 8, ["href", "force", "class", "text", "onClick"])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2);
}
var nav_left_default = {
  name: "f7-nav-left",
  render: render54,
  components: {
    f7Link: link_default
  },
  props: {
    backLink: [Boolean, String],
    backLinkUrl: String,
    backLinkForce: Boolean,
    backLinkShowText: {
      type: Boolean,
      default: void 0
    },
    sliding: Boolean,
    ...colorProps
  },
  emits: ["back:click", "click:back"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onBackClick = (event) => {
      emit("back:click", event);
      emit("click:back", event);
    };
    const theme2 = useTheme();
    const text = computed(() => {
      let needBackLinkText = props.backLinkShowText;
      if (typeof needBackLinkText === "undefined") needBackLinkText = !theme2.value.md;
      if (props.backLink) {
        return props.backLink !== true && needBackLinkText ? props.backLink : void 0;
      }
      return void 0;
    });
    const classes = computed(() => classNames("left", {
      sliding: props.sliding
    }, colorClasses(props)));
    return {
      classes,
      onBackClick,
      text
    };
  }
};

// node_modules/framework7-vue/components/nav-right.js
function render55(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var nav_right_default = {
  name: "f7-nav-right",
  render: render55,
  props: {
    sliding: Boolean,
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("right", {
      sliding: props.sliding
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/nav-title-large.js
var _hoisted_123 = {
  class: "title-large-text"
};
function render56(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("div", _hoisted_123, [renderSlot(_ctx.$slots, "default")])], 2);
}
var nav_title_large_default = {
  name: "f7-nav-title-large",
  render: render56,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("title-large", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/nav-title.js
var _hoisted_124 = {
  key: 0,
  class: "subtitle"
};
function render57(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default"), createTextVNode(" " + toDisplayString(_ctx.title) + " ", 1), _ctx.subtitle ? (openBlock(), createElementBlock("span", _hoisted_124, toDisplayString(_ctx.subtitle), 1)) : createCommentVNode("", true)], 2);
}
var nav_title_default = {
  name: "f7-nav-title",
  render: render57,
  props: {
    title: String,
    subtitle: String,
    sliding: Boolean,
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("title", {
      sliding: props.sliding
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/navbar.js
var _hoisted_125 = createBaseVNode("div", {
  class: "navbar-bg"
}, null, -1);
var _hoisted_213 = {
  key: 3,
  className: "title-large"
};
var _hoisted_36 = {
  className: "title-large-text"
};
function render58(_ctx, _cache) {
  const _component_f7_nav_left = resolveComponent("f7-nav-left");
  const _component_f7_nav_title = resolveComponent("f7-nav-title");
  const _component_f7_nav_right = resolveComponent("f7-nav-right");
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_hoisted_125, renderSlot(_ctx.$slots, "before-inner"), createBaseVNode("div", {
    class: normalizeClass(_ctx.innerClasses)
  }, [_ctx.hasLeft ? (openBlock(), createBlock(_component_f7_nav_left, {
    key: 0,
    "back-link": _ctx.backLink,
    "back-link-url": _ctx.backLinkUrl,
    "back-link-force": _ctx.backLinkForce,
    "back-link-show-text": _ctx.backLinkShowText,
    "onBack:click": _ctx.onBackClick
  }, {
    default: withCtx(() => [renderSlot(_ctx.$slots, "nav-left"), renderSlot(_ctx.$slots, "left")]),
    _: 3
  }, 8, ["back-link", "back-link-url", "back-link-force", "back-link-show-text", "onBack:click"])) : createCommentVNode("", true), _ctx.hasTitle ? (openBlock(), createBlock(_component_f7_nav_title, {
    key: 1,
    title: _ctx.title,
    subtitle: _ctx.subtitle
  }, {
    default: withCtx(() => [renderSlot(_ctx.$slots, "title")]),
    _: 3
  }, 8, ["title", "subtitle"])) : createCommentVNode("", true), _ctx.hasRight ? (openBlock(), createBlock(_component_f7_nav_right, {
    key: 2
  }, {
    default: withCtx(() => [renderSlot(_ctx.$slots, "nav-right"), renderSlot(_ctx.$slots, "right")]),
    _: 3
  })) : createCommentVNode("", true), _ctx.hasLargeTitle ? (openBlock(), createElementBlock("div", _hoisted_213, [createBaseVNode("div", _hoisted_36, [createTextVNode(toDisplayString(_ctx.largeTitle) + " ", 1), renderSlot(_ctx.$slots, "title-large")])])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")], 2), renderSlot(_ctx.$slots, "after-inner")], 2);
}
var navbar_default2 = {
  name: "f7-navbar",
  render: render58,
  components: {
    f7NavLeft: nav_left_default,
    f7NavTitle: nav_title_default,
    f7NavRight: nav_right_default
  },
  props: {
    backLink: [Boolean, String],
    backLinkUrl: String,
    backLinkForce: Boolean,
    backLinkShowText: {
      type: Boolean,
      default: void 0
    },
    sliding: {
      type: Boolean,
      default: true
    },
    title: String,
    subtitle: String,
    hidden: Boolean,
    outline: {
      type: Boolean,
      default: true
    },
    innerClass: String,
    innerClassName: String,
    large: Boolean,
    largeTransparent: Boolean,
    transparent: Boolean,
    titleLarge: String,
    ...colorProps
  },
  emits: ["navbar:hide", "navbar:show", "navbar:expand", "navbar:collapse", "navbar:transparentshow", "navbar:transparenthide", "click:back", "back:click"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    let routerPositionClass = "";
    let largeCollapsed = false;
    let routerNavbarRole = null;
    let routerNavbarRoleDetailRoot = false;
    let routerNavbarMasterStack = false;
    let transparentVisible = false;
    const elRef = ref(null);
    const theme2 = useTheme();
    const onHide = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      emit("navbar:hide");
    };
    const onShow = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      emit("navbar:show");
    };
    const onExpand = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      largeCollapsed = false;
      emit("navbar:expand");
    };
    const onCollapse = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      largeCollapsed = true;
      emit("navbar:collapse");
    };
    const onNavbarTransparentShow = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      transparentVisible = true;
      emit("navbar:transparentshow");
    };
    const onNavbarTransparentHide = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      transparentVisible = false;
      emit("navbar:transparenthide");
    };
    const onNavbarPosition = (navbarEl, position) => {
      if (elRef.value !== navbarEl) return;
      routerPositionClass = position ? `navbar-${position}` : "";
    };
    const onNavbarRole = (navbarEl, rolesData) => {
      if (elRef.value !== navbarEl) return;
      routerNavbarRole = rolesData.role;
      routerNavbarRoleDetailRoot = rolesData.detailRoot;
    };
    const onNavbarMasterStack = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      routerNavbarMasterStack = true;
    };
    const onNavbarMasterUnstack = (navbarEl) => {
      if (elRef.value !== navbarEl) return;
      routerNavbarMasterStack = false;
    };
    const hide = (animate) => {
      if (!f7) return;
      f7.navbar.hide(elRef.value, animate);
    };
    const show = (animate) => {
      if (!f7) return;
      f7.navbar.show(elRef.value, animate);
    };
    const size = () => {
      if (!f7) return;
      f7.navbar.size(elRef.value);
    };
    const onBackClick = (event) => {
      emit("back:click", event);
      emit("click:back", event);
    };
    onMounted(() => {
      if (!elRef.value) return;
      f7ready(() => {
        f7.navbar.size(elRef.value);
        f7.on("navbarShow", onShow);
        f7.on("navbarHide", onHide);
        f7.on("navbarCollapse", onCollapse);
        f7.on("navbarExpand", onExpand);
        f7.on("navbarPosition", onNavbarPosition);
        f7.on("navbarRole", onNavbarRole);
        f7.on("navbarMasterStack", onNavbarMasterStack);
        f7.on("navbarMasterUnstack", onNavbarMasterUnstack);
        f7.on("navbarTransparentShow", onNavbarTransparentShow);
        f7.on("navbarTransparentHide", onNavbarTransparentHide);
      });
    });
    onBeforeUnmount(() => {
      if (!f7) return;
      f7.off("navbarShow", onShow);
      f7.off("navbarHide", onHide);
      f7.off("navbarCollapse", onCollapse);
      f7.off("navbarExpand", onExpand);
      f7.off("navbarPosition", onNavbarPosition);
      f7.off("navbarRole", onNavbarRole);
      f7.off("navbarMasterStack", onNavbarMasterStack);
      f7.off("navbarMasterUnstack", onNavbarMasterUnstack);
      f7.off("navbarTransparentShow", onNavbarTransparentShow);
      f7.off("navbarTransparentHide", onNavbarTransparentHide);
    });
    const addLeftTitleClass = computed(() => theme2.value && theme2.value.ios && f7 && !f7.params.navbar.iosCenterTitle);
    const addCenterTitleClass = computed(() => theme2.value && theme2.value.md && f7 && f7.params.navbar.mdCenterTitle);
    const isLarge = computed(() => props.large || props.largeTransparent);
    const isTransparent = computed(() => props.transparent || isLarge.value && props.largeTransparent);
    const isTransparentVisible = computed(() => isTransparent.value && transparentVisible);
    const classes = computed(() => classNames("navbar", routerPositionClass, {
      "navbar-hidden": props.hidden,
      "navbar-large": isLarge.value,
      "navbar-large-collapsed": isLarge.value && largeCollapsed,
      "navbar-transparent": isTransparent.value,
      "navbar-transparent-visible": isTransparentVisible.value,
      "navbar-master": routerNavbarRole === "master",
      "navbar-master-detail": routerNavbarRole === "detail",
      "navbar-master-detail-root": routerNavbarRoleDetailRoot === true,
      "navbar-master-stacked": routerNavbarMasterStack === true,
      "no-outline": !props.outline
    }, colorClasses(props)));
    const largeTitle = computed(() => {
      let largeTitleText = props.titleLarge;
      if (!largeTitleText && props.large && props.title) largeTitleText = props.title;
      return largeTitleText;
    });
    const hasLeft = computed(() => {
      return props.backLink || slots["nav-left"] || slots.left;
    });
    const hasTitle = computed(() => {
      return props.title || props.subtitle || slots.title;
    });
    const hasRight = computed(() => {
      return slots["nav-right"] || slots.right;
    });
    const hasLargeTitle = computed(() => {
      return largeTitle.value || slots["title-large"];
    });
    const innerClasses = computed(() => {
      return classNames("navbar-inner", props.innerClass, props.innerClassName, {
        sliding: props.sliding,
        "navbar-inner-left-title": addLeftTitleClass.value,
        "navbar-inner-centered-title": addCenterTitleClass.value
      });
    });
    return {
      elRef,
      classes,
      innerClasses,
      hide,
      show,
      size,
      largeTitle,
      hasLeft,
      hasTitle,
      hasRight,
      hasLargeTitle,
      onBackClick
    };
  }
};

// node_modules/framework7-vue/components/page-content.js
var _hoisted_126 = ["data-ptr-distance", "data-ptr-mousewheel", "data-infinite-distance"];
var _hoisted_214 = {
  key: 0,
  class: "ptr-preloader"
};
var _hoisted_37 = createBaseVNode("div", {
  class: "ptr-arrow"
}, null, -1);
var _hoisted_45 = {
  key: 3,
  class: "ptr-preloader"
};
var _hoisted_55 = createBaseVNode("div", {
  class: "ptr-arrow"
}, null, -1);
function render59(_ctx, _cache) {
  const _component_f7_preloader = resolveComponent("f7-preloader");
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    "data-ptr-distance": _ctx.ptrDistance || void 0,
    "data-ptr-mousewheel": _ctx.ptrMousewheel || void 0,
    "data-infinite-distance": _ctx.infiniteDistance || void 0
  }, [_ctx.ptr && _ctx.ptrPreloader && !_ctx.ptrBottom ? (openBlock(), createElementBlock("div", _hoisted_214, [createVNode(_component_f7_preloader), _hoisted_37])) : createCommentVNode("", true), _ctx.infinite && _ctx.infinitePreloader && _ctx.infiniteTop ? (openBlock(), createBlock(_component_f7_preloader, {
    key: 1,
    class: "infinite-scroll-preloader"
  })) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default"), _ctx.infinite && _ctx.infinitePreloader && !_ctx.infiniteTop ? (openBlock(), createBlock(_component_f7_preloader, {
    key: 2,
    class: "infinite-scroll-preloader"
  })) : createCommentVNode("", true), _ctx.ptr && _ctx.ptrPreloader && _ctx.ptrBottom ? (openBlock(), createElementBlock("div", _hoisted_45, [createVNode(_component_f7_preloader), _hoisted_55])) : createCommentVNode("", true)], 10, _hoisted_126);
}
var page_content_default = {
  name: "f7-page-content",
  render: render59,
  components: {
    f7Preloader: preloader_default
  },
  props: {
    tab: Boolean,
    tabActive: Boolean,
    ptr: Boolean,
    ptrDistance: Number,
    ptrPreloader: {
      type: Boolean,
      default: true
    },
    ptrBottom: Boolean,
    ptrMousewheel: Boolean,
    infinite: Boolean,
    infiniteTop: Boolean,
    infiniteDistance: Number,
    infinitePreloader: {
      type: Boolean,
      default: true
    },
    hideBarsOnScroll: Boolean,
    hideNavbarOnScroll: Boolean,
    hideToolbarOnScroll: Boolean,
    messagesContent: Boolean,
    loginScreen: Boolean,
    ...colorProps
  },
  emits: ["ptr:pullstart", "ptr:pullmove", "ptr:pullend", "ptr:refresh", "ptr:done", "infinite", "ptrPullStart", "ptrPullMove", "ptrPullEnd", "ptrRefresh", "ptrDone", "tab:hide", "tab:show"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    const onPtrPullStart = (el) => {
      if (elRef.value !== el) return;
      emit("ptr:pullstart");
      emit("ptrPullStart");
    };
    const onPtrPullMove = (el) => {
      if (elRef.value !== el) return;
      emit("ptr:pullmove");
      emit("ptrPullMove");
    };
    const onPtrPullEnd = (el) => {
      if (elRef.value !== el) return;
      emit("ptr:pullend");
      emit("ptrPullEnd");
    };
    const onPtrRefresh = (el, done) => {
      if (elRef.value !== el) return;
      emit("ptr:refresh", done);
      emit("ptrRefresh", done);
    };
    const onPtrDone = (el) => {
      if (elRef.value !== el) return;
      emit("ptr:done");
      emit("ptrDone");
    };
    const onInfinite = (el) => {
      if (elRef.value !== el) return;
      emit("infinite");
    };
    useTab(elRef, emit);
    onMounted(() => {
      f7ready(() => {
        if (props.ptr) {
          f7.on("ptrPullStart", onPtrPullStart);
          f7.on("ptrPullMove", onPtrPullMove);
          f7.on("ptrPullEnd", onPtrPullEnd);
          f7.on("ptrRefresh", onPtrRefresh);
          f7.on("ptrDone", onPtrDone);
        }
        if (props.infinite) {
          f7.on("infinite", onInfinite);
        }
      });
    });
    onBeforeUnmount(() => {
      if (!f7) return;
      f7.off("ptrPullStart", onPtrPullStart);
      f7.off("ptrPullMove", onPtrPullMove);
      f7.off("ptrPullEnd", onPtrPullEnd);
      f7.off("ptrRefresh", onPtrRefresh);
      f7.off("ptrDone", onPtrDone);
      f7.off("infinite", onInfinite);
    });
    const classes = computed(() => classNames("page-content", {
      tab: props.tab,
      "tab-active": props.tabActive,
      "ptr-content": props.ptr,
      "ptr-bottom": props.ptrBottom,
      "infinite-scroll-content": props.infinite,
      "infinite-scroll-top": props.infiniteTop,
      "hide-bars-on-scroll": props.hideBarsOnScroll,
      "hide-navbar-on-scroll": props.hideNavbarOnScroll,
      "hide-toolbar-on-scroll": props.hideToolbarOnScroll,
      "messages-content": props.messagesContent,
      "login-screen-content": props.loginScreen
    }, colorClasses(props)));
    return {
      elRef,
      classes
    };
  }
};

// node_modules/framework7-vue/components/page.js
var page_default = {
  name: "f7-page",
  props: {
    name: String,
    withSubnavbar: {
      type: Boolean,
      default: void 0
    },
    subnavbar: {
      type: Boolean,
      default: void 0
    },
    withNavbarLarge: {
      type: Boolean,
      default: void 0
    },
    navbarLarge: {
      type: Boolean,
      default: void 0
    },
    noNavbar: Boolean,
    noToolbar: Boolean,
    tabs: Boolean,
    pageContent: {
      type: Boolean,
      default: true
    },
    noSwipeback: Boolean,
    // Page Content Props
    ptr: Boolean,
    ptrDistance: Number,
    ptrPreloader: {
      type: Boolean,
      default: true
    },
    ptrBottom: Boolean,
    ptrMousewheel: Boolean,
    infinite: Boolean,
    infiniteTop: Boolean,
    infiniteDistance: Number,
    infinitePreloader: {
      type: Boolean,
      default: true
    },
    hideBarsOnScroll: Boolean,
    hideNavbarOnScroll: Boolean,
    hideToolbarOnScroll: Boolean,
    messagesContent: Boolean,
    loginScreen: Boolean,
    ...colorProps
  },
  emits: ["page:mounted", "page:init", "page:reinit", "page:beforein", "page:beforeout", "page:afterout", "page:afterin", "page:beforeremove", "page:beforeunmount", "page:tabshow", "page:tabhide", "ptr:pullstart", "ptr:pullmove", "ptr:pullend", "ptr:refresh", "ptr:done", "infinite"],
  setup(props, _ref) {
    let {
      emit,
      slots
    } = _ref;
    let hasSubnavbar = false;
    let hasNavbarLarge = false;
    let hasNavbarLargeCollapsed = false;
    let hasCardExpandableOpened = false;
    let routerPositionClass = "";
    let routerPageRole = null;
    let routerPageRoleDetailRoot = false;
    let routerPageMasterStack = false;
    const elRef = ref(null);
    const onPageMounted = (page) => {
      if (elRef.value !== page.el) return;
      emit("page:mounted", page);
    };
    const onPageInit = (page) => {
      if (elRef.value !== page.el) return;
      if (typeof props.withSubnavbar === "undefined" && typeof props.subnavbar === "undefined") {
        if (page.$navbarEl && page.$navbarEl.length && page.$navbarEl.find(".subnavbar").length || page.$el.children(".navbar").find(".subnavbar").length) {
          hasSubnavbar = true;
        }
      }
      if (typeof props.withNavbarLarge === "undefined" && typeof props.navbarLarge === "undefined") {
        if (page.$navbarEl && page.$navbarEl.hasClass("navbar-large")) {
          hasNavbarLarge = true;
        }
      }
      emit("page:init", page);
    };
    const onPageReinit = (page) => {
      if (elRef.value !== page.el) return;
      emit("page:reinit", page);
    };
    const onPageBeforeIn = (page) => {
      if (elRef.value !== page.el) return;
      if (!page.swipeBack) {
        if (page.from === "next") {
          routerPositionClass = "page-next";
        }
        if (page.from === "previous") {
          routerPositionClass = "page-previous";
        }
      }
      emit("page:beforein", page);
    };
    const onPageBeforeOut = (page) => {
      if (elRef.value !== page.el) return;
      emit("page:beforeout", page);
    };
    const onPageAfterOut = (page) => {
      if (elRef.value !== page.el) return;
      if (page.to === "next") {
        routerPositionClass = "page-next";
      }
      if (page.to === "previous") {
        routerPositionClass = "page-previous";
      }
      emit("page:afterout", page);
    };
    const onPageAfterIn = (page) => {
      if (elRef.value !== page.el) return;
      routerPositionClass = "page-current";
      emit("page:afterin", page);
    };
    const onPageBeforeRemove = (page) => {
      if (elRef.value !== page.el) return;
      emit("page:beforeremove", page);
    };
    const onPageBeforeUnmount = (page) => {
      if (elRef.value !== page.el) return;
      emit("page:beforeunmount", page);
    };
    const onPagePosition = (pageEl, position) => {
      if (elRef.value !== pageEl) return;
      routerPositionClass = `page-${position}`;
    };
    const onPageRole = (pageEl, rolesData) => {
      if (elRef.value !== pageEl) return;
      routerPageRole = rolesData.role;
      routerPageRoleDetailRoot = rolesData.detailRoot;
    };
    const onPageMasterStack = (pageEl) => {
      if (elRef.value !== pageEl) return;
      routerPageMasterStack = true;
    };
    const onPageMasterUnstack = (pageEl) => {
      if (elRef.value !== pageEl) return;
      routerPageMasterStack = false;
    };
    const onPageNavbarLargeCollapsed = (pageEl) => {
      if (elRef.value !== pageEl) return;
      hasNavbarLargeCollapsed = true;
    };
    const onPageNavbarLargeExpanded = (pageEl) => {
      if (elRef.value !== pageEl) return;
      hasNavbarLargeCollapsed = false;
    };
    const onCardOpened = (cardEl, pageEl) => {
      if (elRef.value !== pageEl) return;
      hasCardExpandableOpened = true;
    };
    const onCardClose = (cardEl, pageEl) => {
      if (elRef.value !== pageEl) return;
      hasCardExpandableOpened = false;
    };
    const onPageTabShow = (pageEl) => {
      if (elRef.value !== pageEl) return;
      emit("page:tabshow");
    };
    const onPageTabHide = (pageEl) => {
      if (elRef.value !== pageEl) return;
      emit("page:tabhide");
    };
    const onPtrPullStart = () => {
      emit("ptr:pullstart");
    };
    const onPtrPullMove = () => {
      emit("ptr:pullmove");
    };
    const onPtrPullEnd = () => {
      emit("ptr:pullend");
    };
    const onPtrRefresh = (done) => {
      emit("ptr:refresh", done);
    };
    const onPtrDone = () => {
      emit("ptr:done");
    };
    const onInfinite = () => {
      emit("infinite");
    };
    onMounted(() => {
      f7ready(() => {
        f7.on("pageMounted", onPageMounted);
        f7.on("pageInit", onPageInit);
        f7.on("pageReinit", onPageReinit);
        f7.on("pageBeforeIn", onPageBeforeIn);
        f7.on("pageBeforeOut", onPageBeforeOut);
        f7.on("pageAfterOut", onPageAfterOut);
        f7.on("pageAfterIn", onPageAfterIn);
        f7.on("pageBeforeRemove", onPageBeforeRemove);
        f7.on("pageBeforeUnmount", onPageBeforeUnmount);
        f7.on("pagePosition", onPagePosition);
        f7.on("pageRole", onPageRole);
        f7.on("pageMasterStack", onPageMasterStack);
        f7.on("pageMasterUnstack", onPageMasterUnstack);
        f7.on("pageNavbarLargeCollapsed", onPageNavbarLargeCollapsed);
        f7.on("pageNavbarLargeExpanded", onPageNavbarLargeExpanded);
        f7.on("cardOpened", onCardOpened);
        f7.on("cardClose", onCardClose);
        f7.on("pageTabShow", onPageTabShow);
        f7.on("pageTabHide", onPageTabHide);
      });
    });
    onBeforeUnmount(() => {
      if (!f7) return;
      f7.off("pageMounted", onPageMounted);
      f7.off("pageInit", onPageInit);
      f7.off("pageReinit", onPageReinit);
      f7.off("pageBeforeIn", onPageBeforeIn);
      f7.off("pageBeforeOut", onPageBeforeOut);
      f7.off("pageAfterOut", onPageAfterOut);
      f7.off("pageAfterIn", onPageAfterIn);
      f7.off("pageBeforeRemove", onPageBeforeRemove);
      f7.off("pageBeforeUnmount", onPageBeforeUnmount);
      f7.off("pagePosition", onPagePosition);
      f7.off("pageRole", onPageRole);
      f7.off("pageMasterStack", onPageMasterStack);
      f7.off("pageMasterUnstack", onPageMasterUnstack);
      f7.off("pageNavbarLargeCollapsed", onPageNavbarLargeCollapsed);
      f7.off("pageNavbarLargeExpanded", onPageNavbarLargeExpanded);
      f7.off("cardOpened", onCardOpened);
      f7.off("cardClose", onCardClose);
      f7.off("pageTabShow", onPageTabShow);
      f7.off("pageTabHide", onPageTabHide);
    });
    const classes = computed(() => classNames("page", routerPositionClass, {
      tabs: props.tabs,
      "page-with-subnavbar": props.subnavbar || props.withSubnavbar,
      "page-with-navbar-large": props.navbarLarge || props.withNavbarLarge,
      "no-navbar": props.noNavbar,
      "no-toolbar": props.noToolbar,
      "no-swipeback": props.noSwipeback,
      "page-master": routerPageRole === "master",
      "page-master-detail": routerPageRole === "detail",
      "page-master-detail-root": routerPageRoleDetailRoot === true,
      "page-master-stacked": routerPageMasterStack === true,
      "page-with-navbar-large-collapsed": hasNavbarLargeCollapsed === true,
      "page-with-card-opened": hasCardExpandableOpened === true,
      "login-screen-page": props.loginScreen
    }, colorClasses(props)));
    const fixedTags = "navbar toolbar tabbar subnavbar searchbar messagebar fab list-index panel".split(" ").map((tagName) => `f7-${tagName}`);
    return () => {
      const fixedList = [];
      const staticList = [];
      const {
        static: slotsStatic,
        fixed: slotsFixed,
        default: slotsDefault
      } = slots;
      let hasSubnavbarComputed = false;
      let hasNavbarLargeComputed = false;
      let hasMessages = props.messagesContent;
      const slotsDefaultRendered = slotsDefault && slotsDefault();
      if (slotsDefaultRendered) {
        slotsDefaultRendered.forEach((vnode) => {
          if (typeof vnode === "undefined") return;
          const tag = vnode.type && vnode.type.name ? vnode.type.name : vnode.type;
          let isFixedTag = false;
          if (!tag) {
            if (props.pageContent || props.pageContent === "") staticList.push(vnode);
            return;
          }
          if (tag === "f7-subnavbar") hasSubnavbarComputed = true;
          if (tag === "f7-navbar") {
            if (vnode.props && (vnode.props.large || vnode.props.large === "")) hasNavbarLargeComputed = true;
          }
          if (typeof hasMessages === "undefined" && tag === "f7-messages") hasMessages = true;
          if (fixedTags.indexOf(tag) >= 0) {
            isFixedTag = true;
          }
          if (props.pageContent) {
            if (isFixedTag) fixedList.push(vnode);
            else staticList.push(vnode);
          }
        });
      }
      let classesValue = classes.value;
      if ((hasSubnavbarComputed || hasSubnavbar) && typeof props.subnavbar === "undefined" && typeof props.withSubnavbar === "undefined" && classesValue.indexOf("page-with-subnavbar") < 0) {
        classesValue += " page-with-subnavbar";
      }
      if ((hasNavbarLargeComputed || hasNavbarLarge) && typeof props.navbarLarge === "undefined" && typeof props.withNavbarLarge === "undefined" && classesValue.indexOf("page-with-navbar-large") < 0) {
        classesValue += " page-with-navbar-large";
      }
      if (!props.pageContent) {
        return h("div", {
          class: classesValue,
          ref: elRef,
          "data-name": props.name
        }, [slotsFixed && slotsFixed(), slotsStatic && slotsStatic(), slotsDefault && slotsDefaultRendered]);
      }
      return h("div", {
        class: classesValue,
        ref: elRef,
        "data-name": props.name
      }, [fixedList, slotsFixed && slotsFixed(), h(page_content_default, {
        ptr: props.ptr,
        ptrDistance: props.ptrDistance,
        ptrPreloader: props.ptrPreloader,
        ptrBottom: props.ptrBottom,
        ptrMousewheel: props.ptrMousewheel,
        infinite: props.infinite,
        infiniteTop: props.infiniteTop,
        infiniteDistance: props.infiniteDistance,
        infinitePreloader: props.infinitePreloader,
        hideBarsOnScroll: props.hideBarsOnScroll,
        hideNavbarOnScroll: props.hideNavbarOnScroll,
        hideToolbarOnScroll: props.hideToolbarOnScroll,
        messagesContent: props.messagesContent || hasMessages,
        loginScreen: props.loginScreen,
        onPtrPullStart,
        onPtrPullMove,
        onPtrPullEnd,
        onPtrRefresh,
        onPtrDone,
        onInfinite
      }, () => [slotsStatic && slotsStatic(), staticList])]);
    };
  }
};

// node_modules/framework7-vue/components/photo-browser.js
var photo_browser_default = {
  name: "f7-photo-browser",
  props: {
    init: {
      type: Boolean,
      default: true
    },
    params: Object,
    photos: Array,
    thumbs: Array,
    exposition: {
      type: Boolean,
      default: true
    },
    expositionHideCaptions: {
      type: Boolean,
      default: false
    },
    type: {
      type: String
    },
    navbar: {
      type: Boolean,
      default: true
    },
    toolbar: {
      type: Boolean,
      default: true
    },
    theme: {
      type: String
    },
    captionsTheme: {
      type: String
    },
    iconsColor: {
      type: String
    },
    swipeToClose: {
      type: Boolean,
      default: true
    },
    pageBackLinkText: {
      type: String,
      default: void 0
    },
    popupCloseLinkIcon: {
      type: Boolean,
      default: void 0
    },
    popupCloseLinkText: {
      type: String,
      default: void 0
    },
    navbarOfText: {
      type: String,
      default: void 0
    },
    navbarShowCount: {
      type: Boolean,
      default: void 0
    },
    swiper: {
      type: Object
    },
    url: {
      type: String
    },
    routableModals: {
      type: Boolean,
      default: false
    },
    virtualSlides: {
      type: Boolean,
      default: true
    },
    view: [String, Object],
    renderNavbar: Function,
    renderToolbar: Function,
    renderCaption: Function,
    renderObject: Function,
    renderLazyPhoto: Function,
    renderPhoto: Function,
    renderPage: Function,
    renderPopup: Function,
    renderStandalone: Function,
    renderThumb: Function
  },
  emits: ["photobrowser:open", "photobrowser:close", "photobrowser:opened", "photobrowser:closed", "photobrowser:swipetoclose"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7PhotoBrowser = null;
    const open = (index) => {
      return f7PhotoBrowser.open(index);
    };
    const close = () => {
      return f7PhotoBrowser.close();
    };
    const expositionToggle = () => {
      return f7PhotoBrowser.expositionToggle();
    };
    const expositionEnable = () => {
      return f7PhotoBrowser.expositionEnable();
    };
    const expositionDisable = () => {
      return f7PhotoBrowser.expositionDisable();
    };
    watch(() => props.photos, (value) => {
      const pb = f7PhotoBrowser;
      if (!pb) return;
      pb.params.photos = value;
      if (pb.opened && pb.swiper) {
        pb.swiper.update();
      }
    });
    watch(() => props.thumbs, (value) => {
      const pb = f7PhotoBrowser;
      if (!pb) return;
      pb.params.thumbs = value;
      if (pb.opened && pb.thumbsSwiper) {
        pb.thumbsSwiper.update();
      }
    });
    onMounted(() => {
      if (!props.init) return;
      f7ready(() => {
        let paramsComputed;
        if (typeof props.params !== "undefined") {
          paramsComputed = props.params;
        } else {
          paramsComputed = {
            ...props
          };
          delete paramsComputed.params;
        }
        Object.keys(paramsComputed).forEach((param) => {
          if (typeof paramsComputed[param] === "undefined" || paramsComputed[param] === "") delete paramsComputed[param];
        });
        paramsComputed = extend({}, paramsComputed, {
          on: {
            open() {
              emit("photobrowser:open");
            },
            close() {
              emit("photobrowser:close");
            },
            opened() {
              emit("photobrowser:opened");
            },
            closed() {
              emit("photobrowser:closed");
            },
            swipeToClose() {
              emit("photobrowser:swipetoclose");
            }
          }
        });
        f7PhotoBrowser = f7.photoBrowser.create(paramsComputed);
      });
    });
    onBeforeUnmount(() => {
      if (f7PhotoBrowser && f7PhotoBrowser.destroy) f7PhotoBrowser.destroy();
      f7PhotoBrowser = null;
    });
    return {
      open,
      close,
      expositionToggle,
      expositionEnable,
      expositionDisable
    };
  },
  render() {
    return null;
  }
};

// node_modules/framework7-vue/components/pie-chart.js
var _hoisted_127 = ["width", "height", "viewBox"];
var _hoisted_215 = ["d", "fill", "data-index", "onClick", "onMouseenter"];
function render60(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [(openBlock(), createElementBlock("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: _ctx.size,
    height: _ctx.size,
    viewBox: `-${_ctx.size / 3} -${_ctx.size / 3} ${_ctx.size * 2 / 3} ${_ctx.size * 2 / 3}`,
    style: normalizeStyle({
      transform: "rotate(-90deg)"
    })
  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.paths, (path, index) => {
    return openBlock(), createElementBlock("path", {
      key: path.label || index,
      d: path.points,
      fill: path.color,
      "data-index": index,
      class: normalizeClass(_ctx.classNames({
        "pie-chart-hidden": _ctx.currentIndex !== null && _ctx.currentIndex !== index
      })),
      onClick: ($event) => _ctx.setCurrentIndex(index),
      onMouseenter: ($event) => _ctx.setCurrentIndex(index),
      onMouseleave: _cache[0] || (_cache[0] = ($event) => _ctx.setCurrentIndex(null))
    }, null, 42, _hoisted_215);
  }), 128))], 12, _hoisted_127)), renderSlot(_ctx.$slots, "default")], 2);
}
var pie_chart_default = {
  name: "f7-pie-chart",
  render: render60,
  props: {
    size: {
      type: Number,
      default: 320
    },
    tooltip: Boolean,
    datasets: {
      type: Array,
      default: () => []
    },
    formatTooltip: Function
  },
  emits: ["select"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    let f7Tooltip = null;
    const currentIndex = ref(null);
    const setCurrentIndex = (index) => {
      currentIndex.value = index;
    };
    const summValue = computed(() => {
      let summ = 0;
      props.datasets.map((d) => d.value || 0).forEach((value) => {
        summ += value;
      });
      return summ;
    });
    const paths = computed(() => {
      const p = [];
      let cumulativePercentage = 0;
      function getCoordinatesForPercentage(percentage) {
        const x = Math.cos(2 * Math.PI * percentage) * (props.size / 3);
        const y = Math.sin(2 * Math.PI * percentage) * (props.size / 3);
        return [x, y];
      }
      props.datasets.forEach((_ref2) => {
        let {
          value,
          label,
          color
        } = _ref2;
        const percentage = value / summValue.value;
        const [startX, startY] = getCoordinatesForPercentage(cumulativePercentage);
        cumulativePercentage += percentage;
        const [endX, endY] = getCoordinatesForPercentage(cumulativePercentage);
        const largeArcFlag = percentage > 0.5 ? 1 : 0;
        const points = [
          `M ${startX} ${startY}`,
          // Move
          `A ${props.size / 3} ${props.size / 3} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
          // Arc
          "L 0 0"
          // Line
        ].join(" ");
        p.push({
          points,
          label,
          color
        });
      });
      return p;
    });
    const formatTooltipText = () => {
      if (currentIndex.value === null) return "";
      const {
        value,
        label,
        color
      } = props.datasets[currentIndex.value];
      const percentage = value / summValue.value * 100;
      const round = (v) => {
        if (parseInt(v, 10) === v) return v;
        return Math.round(v * 100) / 100;
      };
      if (props.formatTooltip) {
        return props.formatTooltip({
          index: currentIndex.value,
          value,
          label,
          color,
          percentage
        });
      }
      const tooltipText = `${label ? `${label}: ` : ""}${round(value)} (${round(percentage)}%)`;
      return `
      <div class="pie-chart-tooltip-label">
        <span class="pie-chart-tooltip-color" style="background-color: ${color};"></span> ${tooltipText}
      </div>
    `;
    };
    const setTooltip = () => {
      const index = currentIndex.value;
      if (index === null && !f7Tooltip) return;
      if (!props.tooltip || !elRef.value || !f7) return;
      if (index !== null && !f7Tooltip) {
        f7Tooltip = f7.tooltip.create({
          trigger: "manual",
          containerEl: elRef.value,
          targetEl: elRef.value.querySelector(`path[data-index="${index}"]`),
          text: formatTooltipText(),
          cssClass: "pie-chart-tooltip"
        });
        f7Tooltip.show();
        return;
      }
      if (!f7Tooltip) return;
      if (index !== null) {
        f7Tooltip.setText(formatTooltipText());
        f7Tooltip.setTargetEl(elRef.value.querySelector(`path[data-index="${index}"]`));
        f7Tooltip.show();
      } else {
        f7Tooltip.hide();
      }
    };
    watch(() => currentIndex.value, () => {
      emit("select", currentIndex.value, props.datasets[currentIndex.value]);
      setTooltip();
    });
    onBeforeUnmount(() => {
      if (f7Tooltip && f7Tooltip.destroy) {
        f7Tooltip.destroy();
      }
      f7Tooltip = null;
    });
    const classes = computed(() => classNames("pie-chart"));
    return {
      elRef,
      currentIndex,
      classes,
      paths,
      classNames,
      setCurrentIndex
    };
  }
};

// node_modules/framework7-vue/components/progressbar.js
var _hoisted_128 = ["data-progress"];
function render61(_ctx, _cache) {
  return openBlock(), createElementBlock("span", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    "data-progress": _ctx.progress
  }, [createBaseVNode("span", {
    style: normalizeStyle(_ctx.transformStyle)
  }, null, 4)], 10, _hoisted_128);
}
var progressbar_default = {
  name: "f7-progressbar",
  render: render61,
  props: {
    progress: Number,
    infinite: Boolean,
    ...colorProps
  },
  setup(props) {
    const elRef = ref(null);
    const set = (newProgress, speed) => {
      if (!f7) return;
      f7.progressbar.set(elRef.value, newProgress, speed);
    };
    const transformStyle = computed(() => ({
      transform: props.progress ? `translate3d(${-100 + props.progress}%, 0, 0)` : "",
      WebkitTransform: props.progress ? `translate3d(${-100 + props.progress}%, 0, 0)` : ""
    }));
    const classes = computed(() => classNames("progressbar", {
      "progressbar-infinite": props.infinite
    }, colorClasses(props)));
    return {
      classes,
      set,
      transformStyle,
      elRef
    };
  }
};

// node_modules/framework7-vue/components/radio.js
var _hoisted_129 = ["name", "value", "disabled", "readonly", "checked"];
var _hoisted_216 = createBaseVNode("i", {
  class: "icon-radio"
}, null, -1);
function render62(_ctx, _cache) {
  return openBlock(), createElementBlock("label", {
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("input", {
    ref: "inputElRef",
    type: "radio",
    name: _ctx.name,
    value: _ctx.value,
    disabled: _ctx.disabled,
    readonly: _ctx.readonly,
    checked: _ctx.checked,
    onChange: _cache[0] || (_cache[0] = function() {
      return _ctx.onChange && _ctx.onChange(...arguments);
    })
  }, null, 40, _hoisted_129), _hoisted_216, renderSlot(_ctx.$slots, "default")], 2);
}
var radio_default = {
  name: "f7-radio",
  render: render62,
  props: {
    checked: Boolean,
    name: [Number, String],
    value: {
      type: [Number, String, Boolean],
      default: void 0
    },
    disabled: Boolean,
    readonly: Boolean,
    ...colorProps
  },
  emits: ["update:checked", "change"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const inputElRef = ref(null);
    const onChange = (event) => {
      emit("update:checked", event.target.checked);
      emit("change", event);
    };
    const classes = computed(() => classNames({
      radio: true,
      disabled: props.disabled
    }, colorClasses(props)));
    return {
      inputElRef,
      classes,
      onChange
    };
  }
};

// node_modules/framework7-vue/components/searchbar.js
var _hoisted_130 = {
  class: "searchbar-inner"
};
var _hoisted_217 = {
  class: "searchbar-input-wrap"
};
var _hoisted_38 = ["value", "placeholder", "spellcheck"];
var _hoisted_46 = createBaseVNode("i", {
  class: "searchbar-icon"
}, null, -1);
function render63(_ctx, _cache) {
  return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {
    ref: "elRef",
    class: normalizeClass(_ctx.classes),
    onSubmit: _ctx.onSubmit
  }, {
    default: withCtx(() => [renderSlot(_ctx.$slots, "before-inner"), createBaseVNode("div", _hoisted_130, [renderSlot(_ctx.$slots, "inner-start"), createBaseVNode("div", _hoisted_217, [renderSlot(_ctx.$slots, "input-wrap-start"), createBaseVNode("input", {
      value: _ctx.value,
      placeholder: _ctx.placeholder,
      spellcheck: _ctx.spellcheck,
      type: "search",
      onInput: _cache[0] || (_cache[0] = function() {
        return _ctx.onInput && _ctx.onInput(...arguments);
      }),
      onChange: _cache[1] || (_cache[1] = function() {
        return _ctx.onChange && _ctx.onChange(...arguments);
      }),
      onFocus: _cache[2] || (_cache[2] = function() {
        return _ctx.onFocus && _ctx.onFocus(...arguments);
      }),
      onBlur: _cache[3] || (_cache[3] = function() {
        return _ctx.onBlur && _ctx.onBlur(...arguments);
      })
    }, null, 40, _hoisted_38), _hoisted_46, _ctx.clearButton ? (openBlock(), createElementBlock("span", {
      key: 0,
      class: "input-clear-button",
      onClick: _cache[4] || (_cache[4] = function() {
        return _ctx.onClearButtonClick && _ctx.onClearButtonClick(...arguments);
      })
    })) : createCommentVNode("", true), renderSlot(_ctx.$slots, "input-wrap-end")]), _ctx.disableButton ? (openBlock(), createElementBlock("span", {
      key: 0,
      class: "searchbar-disable-button",
      onClick: _cache[5] || (_cache[5] = function() {
        return _ctx.onDisableButtonClick && _ctx.onDisableButtonClick(...arguments);
      })
    }, toDisplayString(_ctx.disableButtonText), 1)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "inner-end"), renderSlot(_ctx.$slots, "default")]), renderSlot(_ctx.$slots, "after-inner")]),
    _: 3
  }, 40, ["class", "onSubmit"]);
}
var searchbar_default = {
  name: "f7-searchbar",
  render: render63,
  props: {
    outline: {
      type: Boolean,
      default: true
    },
    form: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: "Search"
    },
    spellcheck: {
      type: Boolean,
      default: void 0
    },
    disableButton: {
      type: Boolean,
      default: true
    },
    disableButtonText: {
      type: String,
      default: "Cancel"
    },
    clearButton: {
      type: Boolean,
      default: true
    },
    // Input Value
    value: [String, Number, Array],
    // SB Params
    inputEvents: {
      type: String,
      default: "change input compositionend"
    },
    expandable: Boolean,
    inline: Boolean,
    searchContainer: [String, Object],
    searchIn: {
      type: String,
      default: ".item-title"
    },
    searchItem: {
      type: String,
      default: "li"
    },
    searchGroup: {
      type: String,
      default: ".list-group"
    },
    searchGroupTitle: {
      type: String,
      default: ".list-group-title"
    },
    foundEl: {
      type: [String, Object],
      default: ".searchbar-found"
    },
    notFoundEl: {
      type: [String, Object],
      default: ".searchbar-not-found"
    },
    backdrop: {
      type: Boolean,
      default: void 0
    },
    backdropEl: [String, Object],
    hideOnEnableEl: {
      type: [String, Object],
      default: ".searchbar-hide-on-enable"
    },
    hideOnSearchEl: {
      type: [String, Object],
      default: ".searchbar-hide-on-search"
    },
    ignore: {
      type: String,
      default: ".searchbar-ignore"
    },
    customSearch: {
      type: Boolean,
      default: false
    },
    removeDiacritics: {
      type: Boolean,
      default: false
    },
    hideGroupTitles: {
      type: Boolean,
      default: true
    },
    hideGroups: {
      type: Boolean,
      default: true
    },
    init: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["change", "input", "focus", "blur", "submit", "click:clear", "click:disable", "searchbar:search", "searchbar:clear", "searchbar:enable", "searchbar:disable", "update:value"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Searchbar = null;
    const elRef = ref(null);
    const search = (query) => {
      if (!f7Searchbar) return void 0;
      return f7Searchbar.search(query);
    };
    const enable = () => {
      if (!f7Searchbar) return void 0;
      return f7Searchbar.enable();
    };
    const disable = () => {
      if (!f7Searchbar) return void 0;
      return f7Searchbar.disable();
    };
    const toggle = () => {
      if (!f7Searchbar) return void 0;
      return f7Searchbar.toggle();
    };
    const clear = () => {
      if (!f7Searchbar) return void 0;
      return f7Searchbar.clear();
    };
    const onChange = (event) => {
      emit("change", event);
    };
    const onInput = (event) => {
      emit("input", event);
      emit("update:value", event.target.value);
    };
    const onFocus = (event) => {
      emit("focus", event);
    };
    const onBlur = (event) => {
      emit("blur", event);
    };
    const onSubmit = (event) => {
      emit("submit", event);
    };
    const onClearButtonClick = (event) => {
      emit("click:clear", event);
    };
    const onDisableButtonClick = (event) => {
      emit("click:disable", event);
    };
    onMounted(() => {
      if (!props.init) return;
      f7ready(() => {
        const params = noUndefinedProps({
          el: elRef.value,
          inputEvents: props.inputEvents,
          searchContainer: props.searchContainer,
          searchIn: props.searchIn,
          searchItem: props.searchItem,
          searchGroup: props.searchGroup,
          searchGroupTitle: props.searchGroupTitle,
          hideOnEnableEl: props.hideOnEnableEl,
          hideOnSearchEl: props.hideOnSearchEl,
          foundEl: props.foundEl,
          notFoundEl: props.notFoundEl,
          backdrop: props.backdrop,
          backdropEl: props.backdropEl,
          disableButton: props.disableButton,
          ignore: props.ignore,
          customSearch: props.customSearch,
          removeDiacritics: props.removeDiacritics,
          hideGroupTitles: props.hideGroupTitles,
          hideGroups: props.hideGroups,
          expandable: props.expandable,
          inline: props.inline,
          on: {
            search(searchbar, query, previousQuery) {
              emit("searchbar:search", searchbar, query, previousQuery);
            },
            clear(searchbar, previousQuery) {
              emit("searchbar:clear", searchbar, previousQuery);
            },
            enable(searchbar) {
              emit("searchbar:enable", searchbar);
            },
            disable(searchbar) {
              emit("searchbar:disable", searchbar);
            }
          }
        });
        Object.keys(params).forEach((key) => {
          if (params[key] === "") {
            delete params[key];
          }
        });
        f7Searchbar = f7.searchbar.create(params);
      });
    });
    onBeforeUnmount(() => {
      if (f7Searchbar && f7Searchbar.destroy) f7Searchbar.destroy();
      f7Searchbar = null;
    });
    const classes = computed(() => classNames("searchbar", {
      "searchbar-inline": props.inline,
      "no-outline": !props.outline,
      "searchbar-expandable": props.expandable
    }, colorClasses(props)));
    const tag = computed(() => props.form ? "form" : "div");
    return {
      elRef,
      tag,
      classes,
      search,
      enable,
      disable,
      toggle,
      clear,
      onChange,
      onInput,
      onFocus,
      onBlur,
      onSubmit,
      onClearButtonClick,
      onDisableButtonClick
    };
  }
};

// node_modules/framework7-vue/components/segmented.js
var _hoisted_131 = {
  key: 0,
  class: "segmented-highlight"
};
function render64(_ctx, _cache) {
  return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {
    class: normalizeClass(_ctx.classes)
  }, {
    default: withCtx(() => [renderSlot(_ctx.$slots, "default"), _ctx.strong || _ctx.strongIos || _ctx.strongMd ? (openBlock(), createElementBlock("span", _hoisted_131)) : createCommentVNode("", true)]),
    _: 3
  }, 8, ["class"]);
}
var segmented_default = {
  name: "f7-segmented",
  render: render64,
  props: {
    raised: Boolean,
    raisedIos: Boolean,
    raisedMd: Boolean,
    round: Boolean,
    roundIos: Boolean,
    roundMd: Boolean,
    strong: Boolean,
    strongIos: Boolean,
    strongMd: Boolean,
    tag: {
      type: String,
      default: "div"
    },
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames({
      segmented: true,
      "segmented-raised": props.raised,
      "segmented-raised-ios": props.raisedIos,
      "segmented-raised-md": props.raisedMd,
      "segmented-round": props.round,
      "segmented-round-ios": props.roundIos,
      "segmented-round-md": props.roundMd,
      "segmented-strong": props.strong,
      "segmented-strong-ios": props.strongIos,
      "segmented-strong-md": props.strongMd
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/skeleton-elements/vue/SkeletonBlock.js
var SkeletonBlock_default = {
  name: "skeleton-block",
  props: {
    tag: {
      type: String,
      default: "div"
    },
    width: [String, Number],
    height: [String, Number],
    borderRadius: String,
    effect: String
  },
  render() {
    return h(this.tag, {
      class: {
        "skeleton-block": true,
        [`skeleton-effect-${this.effect}`]: this.effect
      },
      style: {
        width: this.width,
        height: this.height,
        borderRadius: this.borderRadius
      }
    }, this.$slots.default && this.$slots.default());
  }
};

// node_modules/skeleton-elements/vue/SkeletonText.js
var SkeletonText_default = {
  name: "skeleton-text",
  props: {
    tag: {
      type: String,
      default: "span"
    },
    effect: String
  },
  render() {
    return h(this.tag, {
      class: {
        "skeleton-text": true,
        [`skeleton-effect-${this.effect}`]: this.effect
      }
    }, this.$slots.default && this.$slots.default());
  }
};

// node_modules/skeleton-elements/utils/multiply-svg-points.js
function multiplySvgPoints(pointsString, iconSize, width, height) {
  const iconMaxSize = Math.min(width, height) * 0.5;
  const scale = iconMaxSize / iconSize;
  return pointsString.replace(/([0-9,\.]{1,})/g, (coords) => {
    coords = coords.split(",").map((p) => parseFloat(p));
    const x = coords[0] * scale + width / 2 - iconSize * scale / 2;
    const y = coords[1] * scale + height / 2 - iconSize * scale / 2;
    if (iconMaxSize >= 100) {
      return `${Math.round(x)},${Math.round(y)}`;
    }
    return `${x},${y}`;
  });
}
var multiply_svg_points_default = multiplySvgPoints;

// node_modules/skeleton-elements/vue/SkeletonImage.js
var SkeletonImage_default = {
  name: "skeleton-image",
  props: {
    tag: {
      type: String,
      default: "span"
    },
    width: {
      type: Number,
      default: 1200
    },
    height: {
      type: Number,
      default: 600
    },
    color: String,
    iconColor: String,
    showIcon: {
      type: Boolean,
      default: true
    },
    effect: String,
    borderRadius: String
  },
  render() {
    const {
      tag,
      color,
      iconColor,
      showIcon,
      width,
      height,
      effect,
      borderRadius
    } = this;
    return h(tag, {
      class: {
        "skeleton-image": true,
        [`skeleton-effect-${effect}`]: effect
      }
    }, [h("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      width,
      height,
      viewBox: `0 0 ${width} ${height}`,
      style: {
        borderRadius
      },
      preserveAspectRatio: "none"
    }, [h("polygon", {
      style: {
        fill: color
      },
      fillRule: "evenodd",
      points: `0 0 ${width} 0 ${width} ${height} 0 ${height}`
    }), showIcon && h("path", {
      style: {
        fill: iconColor
      },
      d: multiply_svg_points_default("M7.7148,49.5742 L48.2852,49.5742 C53.1836,49.5742 55.6446,47.1367 55.6446,42.3086 L55.6446,13.6914 C55.6446,8.8633 53.1836,6.4258 48.2852,6.4258 L7.7148,6.4258 C2.8398,6.4258 0.3554,8.8398 0.3554,13.6914 L0.3554,42.3086 C0.3554,47.1602 2.8398,49.5742 7.7148,49.5742 Z M39.2851,27.9414 C38.2304,27.0039 37.0351,26.5118 35.7695,26.5118 C34.457,26.5118 33.3085,26.9571 32.2304,27.918 L21.6366,37.3867 L17.3007,33.4492 C16.3163,32.582 15.2617,32.1133 14.1366,32.1133 C13.1054,32.1133 12.0976,32.5586 11.1366,33.4258 L4.1288,39.7305 L4.1288,13.8789 C4.1288,11.4414 5.4413,10.1992 7.7851,10.1992 L48.2147,10.1992 C50.535,10.1992 51.8708,11.4414 51.8708,13.8789 L51.8708,39.7539 L39.2851,27.9414 Z M17.8163,28.1992 C20.8398,28.1992 23.3241,25.7149 23.3241,22.668 C23.3241,19.6445 20.8398,17.1367 17.8163,17.1367 C14.7695,17.1367 12.2851,19.6445 12.2851,22.668 C12.2851,25.7149 14.7695,28.1992 17.8163,28.1992 Z", 56, width, height)
    })]), this.$slots.default && this.$slots.default()]);
  }
};

// node_modules/skeleton-elements/vue/SkeletonAvatar.js
var SkeletonAvatar_default = {
  name: "skeleton-avatar",
  props: {
    tag: {
      type: String,
      default: "span"
    },
    size: {
      type: Number,
      default: 48
    },
    borderRadius: {
      type: [String, Number],
      default: "50%"
    },
    color: String,
    iconColor: String,
    showIcon: {
      type: Boolean,
      default: true
    },
    effect: String
  },
  render() {
    const {
      tag,
      color,
      iconColor,
      showIcon,
      size,
      borderRadius,
      effect
    } = this;
    return h(tag, {
      class: {
        "skeleton-avatar": true,
        [`skeleton-effect-${effect}`]: effect
      }
    }, [h("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      viewBox: `0 0 ${size} ${size}`,
      preserveAspectRatio: "none"
    }, [h("rect", {
      style: {
        fill: color
      },
      fillRule: "evenodd",
      width: size,
      height: size,
      rx: borderRadius
    }), showIcon && h("path", {
      style: {
        fill: iconColor
      },
      d: multiply_svg_points_default("M28.22461,27.1590817 C34.9209931,27.1590817 40.6829044,21.1791004 40.6829044,13.3926332 C40.6829044,5.69958662 34.8898972,0 28.22461,0 C21.5594557,0 15.7663156,5.82423601 15.7663156,13.4549579 C15.7663156,21.1791004 21.5594557,27.1590817 28.22461,27.1590817 Z M8.66515427,56 L47.7841986,56 C52.6739629,56 54.4181241,54.5984253 54.4181241,51.8576005 C54.4181241,43.8219674 44.358068,32.7341519 28.22461,32.7341519 C12.0600561,32.7341519 2,43.8219674 2,51.8576005 C2,54.5984253 3.74402832,56 8.66515427,56 Z", 56, size, size)
    })]), this.$slots.default && this.$slots.default()]);
  }
};

// node_modules/framework7-vue/components/skeleton-avatar.js
var skeleton_avatar_default = SkeletonAvatar_default;

// node_modules/framework7-vue/components/skeleton-block.js
var skeleton_block_default = SkeletonBlock_default;

// node_modules/framework7-vue/components/skeleton-image.js
var skeleton_image_default = SkeletonImage_default;

// node_modules/framework7-vue/components/skeleton-text.js
var skeleton_text_default = SkeletonText_default;

// node_modules/framework7-vue/components/stepper.js
var _hoisted_133 = {
  key: 0,
  class: "stepper-input-wrap"
};
var _hoisted_218 = ["id", "name", "type", "min", "max", "step", "value", "readonly"];
var _hoisted_39 = {
  key: 1,
  class: "stepper-value"
};
function render65(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [createBaseVNode("div", {
    class: "stepper-button-minus",
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onMinusClick && _ctx.onMinusClick(...arguments);
    })
  }), _ctx.input && !_ctx.buttonsOnly ? (openBlock(), createElementBlock("div", _hoisted_133, [createBaseVNode("input", {
    id: _ctx.inputId,
    name: _ctx.name,
    type: _ctx.inputType,
    min: _ctx.inputType === "number" ? _ctx.min : void 0,
    max: _ctx.inputType === "number" ? _ctx.max : void 0,
    step: _ctx.inputType === "number" ? _ctx.step : void 0,
    value: _ctx.value,
    readonly: _ctx.inputReadonly,
    onInput: _cache[1] || (_cache[1] = function() {
      return _ctx.onInput && _ctx.onInput(...arguments);
    }),
    onChange: _cache[2] || (_cache[2] = function() {
      return _ctx.onChange && _ctx.onChange(...arguments);
    })
  }, null, 40, _hoisted_218)])) : createCommentVNode("", true), !_ctx.input && !_ctx.buttonsOnly ? (openBlock(), createElementBlock("div", _hoisted_39, toDisplayString(_ctx.value), 1)) : createCommentVNode("", true), createBaseVNode("div", {
    class: "stepper-button-plus",
    onClick: _cache[3] || (_cache[3] = function() {
      return _ctx.onPlusClick && _ctx.onPlusClick(...arguments);
    })
  })], 2);
}
var stepper_default = {
  name: "f7-stepper",
  render: render65,
  props: {
    init: {
      type: Boolean,
      default: true
    },
    value: {
      type: Number,
      default: 0
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    step: {
      type: Number,
      default: 1
    },
    formatValue: Function,
    name: String,
    inputId: String,
    input: {
      type: Boolean,
      default: true
    },
    inputType: {
      type: String,
      default: "text"
    },
    inputReadonly: {
      type: Boolean,
      default: false
    },
    autorepeat: {
      type: Boolean,
      default: false
    },
    autorepeatDynamic: {
      type: Boolean,
      default: false
    },
    wraps: {
      type: Boolean,
      default: false
    },
    manualInputMode: {
      type: Boolean,
      default: false
    },
    decimalPoint: {
      type: Number,
      default: 4
    },
    buttonsEndInputMode: {
      type: Boolean,
      default: true
    },
    disabled: Boolean,
    buttonsOnly: Boolean,
    round: Boolean,
    roundMd: Boolean,
    roundIos: Boolean,
    fill: Boolean,
    fillMd: Boolean,
    fillIos: Boolean,
    large: Boolean,
    largeMd: Boolean,
    largeIos: Boolean,
    small: Boolean,
    smallMd: Boolean,
    smallIos: Boolean,
    raised: Boolean,
    raisedMd: Boolean,
    raisedIos: Boolean,
    ...colorProps
  },
  emits: ["input", "change", "stepper:minusclick", "stepper:plusclick", "stepper:change", "update:value"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    let f7Stepper = null;
    const elRef = ref(null);
    const increment = () => {
      if (!f7Stepper) return;
      f7Stepper.increment();
    };
    const decrement = () => {
      if (!f7Stepper) return;
      f7Stepper.decrement();
    };
    const setValue = (newValue) => {
      if (f7Stepper && f7Stepper.setValue) f7Stepper.setValue(newValue);
    };
    const getValue = () => {
      if (f7Stepper && f7Stepper.getValue) {
        return f7Stepper.getValue();
      }
      return void 0;
    };
    const onInput = (event) => {
      emit("input", event, f7Stepper);
    };
    const onChange = (event) => {
      emit("change", event, f7Stepper);
    };
    const onMinusClick = (event) => {
      emit("stepper:minusclick", event, f7Stepper);
    };
    const onPlusClick = (event) => {
      emit("stepper:plusclick", event, f7Stepper);
    };
    watch(() => props.value, (newValue) => {
      if (!f7Stepper) return;
      f7Stepper.setValue(newValue);
    });
    onMounted(() => {
      f7ready(() => {
        if (!props.init || !elRef.value) return;
        f7Stepper = f7.stepper.create(noUndefinedProps({
          el: elRef.value,
          min: props.min,
          max: props.max,
          value: props.value,
          step: props.step,
          formatValue: props.formatValue,
          autorepeat: props.autorepeat,
          autorepeatDynamic: props.autorepeatDynamic,
          wraps: props.wraps,
          manualInputMode: props.manualInputMode,
          decimalPoint: props.decimalPoint,
          buttonsEndInputMode: props.buttonsEndInputMode,
          on: {
            change(stepper, newValue) {
              emit("stepper:change", newValue);
              emit("update:value", newValue);
            }
          }
        }));
      });
    });
    onBeforeUnmount(() => {
      if (f7Stepper && f7Stepper.destroy) {
        f7Stepper.destroy();
      }
      f7Stepper = null;
    });
    const classes = computed(() => classNames("stepper", {
      disabled: props.disabled,
      "stepper-round": props.round,
      "stepper-round-ios": props.roundIos,
      "stepper-round-md": props.roundMd,
      "stepper-fill": props.fill,
      "stepper-fill-ios": props.fillIos,
      "stepper-fill-md": props.fillMd,
      "stepper-large": props.large,
      "stepper-large-ios": props.largeIos,
      "stepper-large-md": props.largeMd,
      "stepper-small": props.small,
      "stepper-small-ios": props.smallIos,
      "stepper-small-md": props.smallMd,
      "stepper-raised": props.raised,
      "stepper-raised-ios": props.raisedIos,
      "stepper-raised-md": props.raisedMd
    }, colorClasses(props)));
    return {
      elRef,
      classes,
      increment,
      decrement,
      setValue,
      getValue,
      onInput,
      onChange,
      onMinusClick,
      onPlusClick
    };
  }
};

// node_modules/framework7-vue/components/subnavbar.js
var _hoisted_134 = {
  key: 0,
  class: "subnavbar-inner"
};
var _hoisted_219 = {
  key: 0,
  class: "subnavbar-title"
};
function render66(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [_ctx.inner ? (openBlock(), createElementBlock("div", _hoisted_134, [_ctx.title ? (openBlock(), createElementBlock("div", _hoisted_219, toDisplayString(_ctx.title), 1)) : createCommentVNode("", true), renderSlot(_ctx.$slots, "default")])) : renderSlot(_ctx.$slots, "default", {
    key: 1
  })], 2);
}
var subnavbar_default2 = {
  name: "f7-subnavbar",
  render: render66,
  props: {
    sliding: Boolean,
    title: String,
    inner: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("subnavbar", {
      sliding: props.sliding
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/swipeout-actions.js
function render67(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var swipeout_actions_default = {
  name: "f7-swipeout-actions",
  render: render67,
  props: {
    left: Boolean,
    right: Boolean,
    side: String,
    ...colorProps
  },
  setup(props) {
    const side = computed(() => {
      let sideComputed = props.side;
      if (!sideComputed) {
        if (props.left) sideComputed = "left";
        if (props.right) sideComputed = "right";
      }
      return sideComputed;
    });
    const classes = computed(() => classNames(`swipeout-actions-${side.value}`, colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/swipeout-button.js
var _hoisted_135 = ["href", "data-confirm", "data-confirm-title"];
function render68(_ctx, _cache) {
  return openBlock(), createElementBlock("a", {
    href: _ctx.href || "#",
    "data-confirm": _ctx.confirmText || void 0,
    "data-confirm-title": _ctx.confirmTitle || void 0,
    class: normalizeClass(_ctx.classes),
    onClick: _cache[0] || (_cache[0] = function() {
      return _ctx.onClick && _ctx.onClick(...arguments);
    })
  }, [createTextVNode(toDisplayString(_ctx.text) + " ", 1), renderSlot(_ctx.$slots, "default")], 10, _hoisted_135);
}
var swipeout_button_default = {
  name: "f7-swipeout-button",
  render: render68,
  props: {
    text: String,
    confirmTitle: String,
    confirmText: String,
    overswipe: Boolean,
    close: Boolean,
    delete: Boolean,
    href: String,
    ...colorProps
  },
  emits: ["click"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onClick = (e) => {
      emit("click", e);
    };
    const classes = computed(() => classNames({
      "swipeout-overswipe": props.overswipe,
      "swipeout-delete": props.delete,
      "swipeout-close": props.close
    }, colorClasses(props)));
    return {
      classes,
      onClick
    };
  }
};

// node_modules/framework7-vue/components/tab.js
function render69(_ctx, _cache) {
  return _ctx.isSwiper ? (openBlock(), createElementBlock("swiper-slide", {
    key: 0,
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_ctx.tabContent ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.getComponent(_ctx.tabContent)), mergeProps({
    key: _ctx.tabContent.id
  }, _ctx.getProps(_ctx.tabContent)), null, 16)) : renderSlot(_ctx.$slots, "default", {
    key: 1
  })], 2)) : (openBlock(), createElementBlock("div", {
    key: 1,
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [_ctx.tabContent ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.getComponent(_ctx.tabContent)), mergeProps({
    key: _ctx.tabContent.id
  }, _ctx.getProps(_ctx.tabContent)), null, 16)) : renderSlot(_ctx.$slots, "default", {
    key: 1
  })], 2));
}
var tab_default = {
  name: "f7-tab",
  render: render69,
  props: {
    tabActive: Boolean,
    ...colorProps
  },
  emits: ["tab:show", "tab:hide"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    const routerData = ref(null);
    const route = inject("f7route", null);
    const router = inject("f7route", null);
    const isSwiper = inject("TabsSwipeableContext", false);
    let initialTabContent = null;
    if (!routerData.value && route && route.route && route.route.tab && route.route.tab.id === props.id) {
      const {
        component,
        asyncComponent,
        options: tabRouteOptions
      } = route.route.tab;
      if (component || asyncComponent) {
        const parentProps = route.route.options && route.route.options.props;
        initialTabContent = {
          id: getComponentId(),
          component: component || asyncComponent,
          isAsync: !!asyncComponent,
          props: {
            ...parentProps || {},
            ...tabRouteOptions && tabRouteOptions.props || {},
            f7router: router,
            f7route: route,
            ...route.params
          }
        };
      }
    }
    const tabContent = ref(initialTabContent || null);
    const setTabContent = (newContent) => {
      tabContent.value = newContent;
    };
    if (f7 && !routerData.value) {
      routerData.value = {
        setTabContent
      };
      f7routers.tabs.push(routerData.value);
    }
    onMounted(() => {
      if (elRef.value && initialTabContent) {
        elRef.value.f7RouterTabLoaded = true;
      }
      f7ready(() => {
        if (!routerData.value) {
          routerData.value = {
            el: elRef.value,
            setTabContent
          };
          f7routers.tabs.push(routerData.value);
        } else {
          routerData.value.el = elRef.value;
        }
      });
    });
    onBeforeUnmount(() => {
      if (!routerData.value) return;
      f7routers.tabs.splice(f7routers.tabs.indexOf(routerData.value), 1);
      routerData.value = null;
    });
    onUpdated(() => {
      if (!routerData.value || !f7) return;
      f7events.emit("tabRouterDidUpdate", routerData.value);
    });
    useTab(elRef, emit);
    const classes = computed(() => classNames("tab", {
      "tab-active": props.tabActive
    }, colorClasses(props)));
    const getComponent = (content) => toRaw(content.component);
    const getProps = (content) => {
      const {
        component: tabComponent,
        props: tabProps
      } = content;
      let keys = [];
      const passProps = {};
      if (tabComponent && tabComponent.props) keys = Object.keys(tabComponent.props);
      keys.forEach((key) => {
        if (key in tabProps) passProps[key] = tabProps[key];
      });
      return passProps;
    };
    return {
      elRef,
      classes,
      tabContent,
      getComponent,
      getProps,
      isSwiper
    };
  }
};

// node_modules/framework7-vue/components/tabs.js
var _hoisted_136 = ["init"];
function render70(_ctx, _cache) {
  return _ctx.animated ? (openBlock(), createElementBlock("div", {
    key: 0,
    ref: "elRef",
    class: normalizeClass(_ctx.classNames("tabs-animated-wrap", _ctx.classes))
  }, [createBaseVNode("div", {
    class: normalizeClass(_ctx.tabsClasses)
  }, [renderSlot(_ctx.$slots, "default")], 2)], 2)) : _ctx.swipeable ? (openBlock(), createElementBlock("swiper-container", {
    key: 1,
    ref: "elRef",
    class: normalizeClass(_ctx.classNames(_ctx.tabsClasses, _ctx.classes)),
    init: _ctx.swiperParams ? "false" : "true"
  }, [renderSlot(_ctx.$slots, "default")], 10, _hoisted_136)) : (openBlock(), createElementBlock("div", {
    key: 2,
    ref: "elRef",
    class: normalizeClass(_ctx.classNames(_ctx.tabsClasses, _ctx.classes))
  }, [renderSlot(_ctx.$slots, "default")], 2));
}
var tabs_default = {
  name: "f7-tabs",
  render: render70,
  props: {
    animated: Boolean,
    swipeable: Boolean,
    routable: Boolean,
    swiperParams: {
      type: Object,
      default: void 0
    },
    ...colorProps
  },
  setup(props) {
    const elRef = ref(null);
    onMounted(() => {
      if (!props.swipeable || !props.swiperParams) return;
      if (!elRef.value) return;
      Object.assign(elRef.value, props.swiperParams);
      elRef.value.initialize();
    });
    const classes = computed(() => classNames(colorClasses(props)));
    const tabsClasses = computed(() => classNames({
      tabs: true,
      "tabs-routable": props.routable
    }));
    const TabsSwipeableContext = computed(() => props.swipeable);
    provide("TabsSwipeableContext", TabsSwipeableContext);
    return {
      elRef,
      classes,
      tabsClasses,
      classNames
    };
  }
};

// node_modules/framework7-vue/components/toolbar.js
var _hoisted_137 = {
  key: 0,
  className: "toolbar-inner"
};
function render71(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "before-inner"), _ctx.inner ? (openBlock(), createElementBlock("div", _hoisted_137, [renderSlot(_ctx.$slots, "default")])) : renderSlot(_ctx.$slots, "default", {
    key: 1
  }), renderSlot(_ctx.$slots, "after-inner")], 2);
}
var toolbar_default2 = {
  name: "f7-toolbar",
  render: render71,
  props: {
    tabbar: Boolean,
    icons: Boolean,
    scrollable: Boolean,
    hidden: Boolean,
    outline: {
      type: Boolean,
      default: true
    },
    position: {
      type: String,
      default: void 0
    },
    topMd: {
      type: Boolean,
      default: void 0
    },
    topIos: {
      type: Boolean,
      default: void 0
    },
    top: {
      type: Boolean,
      default: void 0
    },
    bottomMd: {
      type: Boolean,
      default: void 0
    },
    bottomIos: {
      type: Boolean,
      default: void 0
    },
    bottom: {
      type: Boolean,
      default: void 0
    },
    inner: {
      type: Boolean,
      default: true
    },
    ...colorProps
  },
  emits: ["toolbar:hide", "toolbar:show"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const elRef = ref(null);
    const theme2 = useTheme();
    const onHide = (toolbarEl) => {
      if (elRef.value !== toolbarEl) return;
      emit("toolbar:hide");
    };
    const onShow = (toolbarEl) => {
      if (elRef.value !== toolbarEl) return;
      emit("toolbar:show");
    };
    const hide = (animate) => {
      if (!f7) return;
      f7.toolbar.hide(elRef.value, animate);
    };
    const show = (animate) => {
      if (!f7) return;
      f7.toolbar.show(elRef.value, animate);
    };
    onMounted(() => {
      if (props.tabbar && f7 && elRef.value) {
        f7.toolbar.setHighlight(elRef.value);
      }
      f7.on("toolbarShow", onShow);
      f7.on("toolbarHide", onHide);
    });
    onBeforeUnmount(() => {
      f7.off("toolbarShow", onShow);
      f7.off("toolbarHide", onHide);
    });
    const TabbarContext = computed(() => ({
      tabbarHasIcons: props.icons
    }));
    provide("TabbarContext", TabbarContext);
    const classes = computed(() => {
      const {
        tabbar,
        bottomMd,
        bottomIos,
        bottom,
        position,
        topMd,
        topIos,
        top,
        icons,
        scrollable,
        hidden,
        outline
      } = props;
      return classNames("toolbar", {
        tabbar,
        "toolbar-bottom": theme2.value && theme2.value.md && bottomMd || theme2.value && theme2.value.ios && bottomIos || bottom || position === "bottom",
        "toolbar-top": theme2.value && theme2.value.md && topMd || theme2.value && theme2.value.ios && topIos || top || position === "top",
        "tabbar-icons": icons,
        "tabbar-scrollable": scrollable,
        "toolbar-hidden": hidden,
        "no-outline": !outline
      }, colorClasses(props));
    });
    return {
      classes,
      elRef,
      hide,
      show
    };
  }
};

// node_modules/framework7-vue/components/treeview-item.js
var _hoisted_138 = {
  key: 0,
  className: "treeview-toggle"
};
var _hoisted_220 = {
  className: "treeview-item-content"
};
var _hoisted_310 = {
  className: "treeview-item-label"
};
var _hoisted_47 = {
  key: 0,
  className: "treeview-item-children"
};
function render72(_ctx, _cache) {
  const _component_f7_use_icon = resolveComponent("f7-use-icon");
  return openBlock(), createElementBlock("div", {
    ref: "elRef",
    class: normalizeClass(_ctx.classes)
  }, [(openBlock(), createBlock(resolveDynamicComponent(_ctx.itemRootTag), mergeProps({
    class: _ctx.itemRootClasses
  }, _ctx.itemRootAttrs, {
    onClick: _ctx.onClick
  }), {
    default: withCtx(() => [renderSlot(_ctx.$slots, "root-start"), _ctx.needToggle ? (openBlock(), createElementBlock("div", _hoisted_138)) : createCommentVNode("", true), createBaseVNode("div", _hoisted_220, [renderSlot(_ctx.$slots, "content-start"), _ctx.icon ? (openBlock(), createBlock(_component_f7_use_icon, {
      key: 0,
      icon: _ctx.icon
    }, null, 8, ["icon"])) : createCommentVNode("", true), renderSlot(_ctx.$slots, "media"), createBaseVNode("div", _hoisted_310, [renderSlot(_ctx.$slots, "label-start"), createTextVNode(" " + toDisplayString(_ctx.label) + " ", 1), renderSlot(_ctx.$slots, "label")]), renderSlot(_ctx.$slots, "content"), renderSlot(_ctx.$slots, "content-end")]), renderSlot(_ctx.$slots, "root"), renderSlot(_ctx.$slots, "root-end")]),
    _: 3
  }, 16, ["class", "onClick"])), _ctx.hasChildren ? (openBlock(), createElementBlock("div", _hoisted_47, [renderSlot(_ctx.$slots, "children-start"), renderSlot(_ctx.$slots, "default"), renderSlot(_ctx.$slots, "children")])) : createCommentVNode("", true)], 2);
}
var treeview_item_default = {
  name: "f7-treeview-item",
  render: render72,
  components: {
    f7UseIcon: use_icon_default
  },
  props: {
    toggle: {
      type: Boolean,
      default: void 0
    },
    itemToggle: Boolean,
    selectable: Boolean,
    selected: Boolean,
    opened: Boolean,
    label: String,
    loadChildren: Boolean,
    link: {
      type: [Boolean, String],
      default: void 0
    },
    ...colorProps,
    ...actionsProps,
    ...iconProps,
    ...routerProps
  },
  emits: ["click", "treeview:open", "treeview:close", "treeview:loadchildren"],
  setup(props, _ref) {
    let {
      slots,
      emit
    } = _ref;
    const elRef = ref(null);
    const hasChildren = computed(() => {
      return slots.default || slots.children || slots["children-start"];
    });
    const needToggle = computed(() => typeof props.toggle === "undefined" ? hasChildren.value : props.toggle);
    const icon = computed(() => useIcon(props));
    const onClick = (event) => {
      emit("click", event);
    };
    const onOpen = (el) => {
      if (elRef.value !== el) return;
      emit("treeview:open", el);
    };
    const onClose = (el) => {
      if (elRef.value !== el) return;
      emit("treeview:close", el);
    };
    const onLoadChildren = (el, done) => {
      if (elRef.value !== el) return;
      emit("treeview:loadchildren", el, done);
    };
    const attachEvents = () => {
      if (!elRef.value) return;
      f7ready(() => {
        f7.on("treeviewOpen", onOpen);
        f7.on("treeviewClose", onClose);
        f7.on("treeviewLoadChildren", onLoadChildren);
      });
    };
    const detachEvents = () => {
      if (!f7) return;
      f7.off("treeviewOpen", onOpen);
      f7.off("treeviewClose", onClose);
      f7.off("treeviewLoadChildren", onLoadChildren);
    };
    onMounted(() => attachEvents());
    onBeforeUnmount(() => detachEvents());
    const classes = computed(() => classNames("treeview-item", {
      "treeview-item-opened": props.opened,
      "treeview-load-children": props.loadChildren
    }, colorClasses(props)));
    const itemRootClasses = computed(() => classNames("treeview-item-root", {
      "treeview-item-selectable": props.selectable,
      "treeview-item-selected": props.selected,
      "treeview-item-toggle": props.itemToggle
    }, routerClasses(props), actionsClasses(props)));
    const itemRootTag = computed(() => props.link || props.link === "" ? "a" : "div");
    const itemRootAttrs = computed(() => {
      let href = props.link;
      if (props.link === true) href = "#";
      if (props.link === false) href = void 0;
      return {
        href,
        ...routerAttrs(props),
        ...actionsAttrs(props)
      };
    });
    return {
      itemRootTag,
      itemRootAttrs,
      itemRootClasses,
      classes,
      icon,
      onClick,
      hasChildren,
      needToggle,
      elRef
    };
  }
};

// node_modules/framework7-vue/components/treeview.js
function render73(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var treeview_default = {
  name: "f7-treeview",
  render: render73,
  props: {
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("treeview", colorClasses(props)));
    return {
      classes
    };
  }
};

// node_modules/framework7-vue/components/views.js
function render74(_ctx, _cache) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classes)
  }, [renderSlot(_ctx.$slots, "default")], 2);
}
var views_default = {
  name: "f7-views",
  render: render74,
  props: {
    tabs: Boolean,
    ...colorProps
  },
  setup(props) {
    const classes = computed(() => classNames("views", {
      tabs: props.tabs
    }, colorClasses(props)));
    return {
      classes
    };
  }
};

export {
  f7,
  theme,
  f7ready,
  popup_default,
  view_default2 as view_default,
  login_screen_default,
  sheet_default,
  popover_default,
  panel_default,
  plugin_default,
  useStore,
  accordion_content_default,
  accordion_item_default,
  accordion_toggle_default,
  accordion_default,
  actions_button_default,
  actions_group_default,
  actions_label_default,
  actions_default,
  routable_modals_default,
  app_default,
  area_chart_default,
  badge_default,
  block_footer_default,
  block_header_default,
  block_title_default,
  block_default,
  breadcrumbs_collapsed_default,
  breadcrumbs_item_default,
  breadcrumbs_separator_default,
  breadcrumbs_default,
  preloader_default,
  icon_default,
  use_icon_default,
  button_default,
  card_content_default,
  card_footer_default,
  card_header_default,
  card_default,
  checkbox_default,
  chip_default,
  fab_backdrop_default,
  fab_button_default,
  fab_buttons_default,
  fab_default,
  gauge_default,
  toggle_default,
  range_default,
  text_editor_default,
  input_default,
  link_default,
  list_button_default,
  list_group_default,
  list_index_default,
  list_input_default,
  list_item_default,
  list_default,
  login_screen_title_default,
  message_default,
  messagebar_attachment_default,
  messagebar_attachments_default,
  messagebar_sheet_image_default,
  messagebar_sheet_item_default,
  messagebar_sheet_default,
  messagebar_default,
  messages_title_default,
  messages_default,
  nav_left_default,
  nav_right_default,
  nav_title_large_default,
  nav_title_default,
  navbar_default2 as navbar_default,
  page_content_default,
  page_default,
  photo_browser_default,
  pie_chart_default,
  progressbar_default,
  radio_default,
  searchbar_default,
  segmented_default,
  skeleton_avatar_default,
  skeleton_block_default,
  skeleton_image_default,
  skeleton_text_default,
  stepper_default,
  subnavbar_default2 as subnavbar_default,
  swipeout_actions_default,
  swipeout_button_default,
  tab_default,
  tabs_default,
  toolbar_default2 as toolbar_default,
  treeview_item_default,
  treeview_default,
  views_default
};
//# sourceMappingURL=chunk-54QNRS63.js.map
