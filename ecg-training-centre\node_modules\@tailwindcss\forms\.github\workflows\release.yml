name: Release

on:
  release:
    types: [published]

permissions:
  contents: read
  id-token: write

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22]

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Test
        run: npm test

      - name: Calculate environment variables
        run: |
          echo "RELEASE_CHANNEL=$(npm run release-channel --silent)" >> $GITHUB_ENV

      - name: Publish
        run: npm publish --provenance --tag ${{ env.RELEASE_CHANNEL }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
