.ios {
  // Buttons
  .button {
    transition-duration: 100ms;
    &.active-state {
      background-color: var(--f7-button-pressed-bg-color, rgba(var(--f7-theme-color-rgb), 0.15));
    }
  }
  .button-fill,
  .button-fill-ios {
    --f7-button-pressed-bg-color: var(
      --f7-button-fill-pressed-bg-color,
      var(--f7-theme-color-tint)
    );
  }

  .button-small,
  .button-small-ios {
    transition-duration: 200ms;
  }
}
