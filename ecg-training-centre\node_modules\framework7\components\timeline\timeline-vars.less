:root {
  --f7-timeline-inner-block-margin-vertical: 16px;
  --f7-timeline-divider-margin-horizontal: 16px;
  --f7-timeline-horizontal-date-height: 34px;
  --f7-timeline-year-height: 24px;
  --f7-timeline-year-font-weight: inherit;
  --f7-timeline-month-height: 24px;
  --f7-timeline-month-font-size: inherit;
  --f7-timeline-month-font-weight: inherit;
  --f7-timeline-item-text-font-weight: inherit;
  --f7-timeline-item-subtitle-font-weight: inherit;
}
.ios-vars({
  --f7-timeline-divider-color: #bbb;
  --f7-timeline-padding-horizontal: 16px;
  --f7-timeline-margin-vertical: 35px;
  --f7-timeline-item-inner-border-radius: 7px;
  --f7-timeline-item-time-font-size: 13px;
  --f7-timeline-item-title-font-size: 17px;
  --f7-timeline-item-title-line-height: inherit;
  --f7-timeline-item-title-font-weight: 600;
  --f7-timeline-item-subtitle-font-size: 15px;
  --f7-timeline-item-subtitle-line-height: inherit;
  --f7-timeline-item-text-font-size: inherit;
  --f7-timeline-item-text-color: inherit;
  --f7-timeline-item-text-line-height: inherit;
  --f7-timeline-year-font-size: 16px;
  --f7-timeline-horizontal-item-padding: 10px;
  .light-vars({
    --f7-timeline-item-inner-bg-color: #fff;
    --f7-timeline-item-time-text-color: rgba(0,0,0,0.45);
    --f7-timeline-horizontal-item-border-color: rgba(0,0,0,0.22);
    --f7-timeline-horizontal-item-date-border-color: rgba(0,0,0,0.22);
  });
  .dark-vars({
    --f7-timeline-horizontal-item-border-color: rgba(255, 255, 255, 0.15);
    --f7-timeline-horizontal-item-date-border-color: rgba(255, 255, 255, 0.15);
    --f7-timeline-item-inner-bg-color: #1c1c1d;
    --f7-timeline-item-time-text-color: rgba(255,255,255,0.55);
  });
});
.md-vars({
  --f7-timeline-padding-horizontal: 16px;
  --f7-timeline-margin-vertical: 32px;
  --f7-timeline-item-inner-border-radius: 16px;
  --f7-timeline-item-time-font-size: 13px;
  --f7-timeline-item-title-font-size: 16px;
  --f7-timeline-item-title-line-height: inherit;
  --f7-timeline-item-title-font-weight: 400;
  --f7-timeline-item-subtitle-font-size: inherit;
  --f7-timeline-item-subtitle-line-height: inherit;
  --f7-timeline-item-text-font-size: inherit;
  --f7-timeline-item-text-line-height: inherit;
  --f7-timeline-year-font-size: 16px;
  --f7-timeline-horizontal-item-padding: 12px;
  --f7-timeline-horizontal-item-date-border-color: transparent;
});
.md-color-vars({
  --f7-timeline-divider-color: var(--f7-md-outline);
  --f7-timeline-item-text-color: var(--f7-md-on-surface);
  --f7-timeline-horizontal-item-border-color: var(--f7-md-outline);
  --f7-timeline-item-inner-bg-color: var(--f7-md-surface-1);
  --f7-timeline-item-time-text-color: var(--f7-md-on-surface-variant);
});
