:root {
  --f7-actions-grid-button-font-size: 12px;
  --f7-actions-grid-button-icon-size: 48px;
}
.ios-vars({
  --f7-actions-transition-timing-function: initial;
  --f7-actions-border-radius: 13px;
  /*
  --f7-actions-button-text-color: var(--f7-theme-color);
  */
  --f7-actions-grid-button-text-color: #757575;
  --f7-actions-button-padding: 0px;
  --f7-actions-button-text-align: center;
  --f7-actions-button-height: 57px;
  --f7-actions-button-height-landscape: 44px;
  --f7-actions-button-font-size: 20px;
  --f7-actions-button-icon-size: 28px;
  --f7-actions-button-justify-content: center;
  --f7-actions-label-padding: 8px 10px;
  --f7-actions-label-font-weight: 400;
  --f7-actions-label-font-size: 13px;
  --f7-actions-label-justify-content: center;
  --f7-actions-group-border-color: transparent;
  --f7-actions-group-margin: 8px;
  .light-vars({
    --f7-actions-bg-color: rgba(255,255,255,0.95);
    --f7-actions-bg-color-rgb: 255, 255, 255;
    --f7-actions-button-border-color: rgba(0,0,0,0.2);
    --f7-actions-button-pressed-bg-color: rgba(230,230,230,0.9);
    --f7-actions-button-pressed-bg-color-rgb: 230, 230, 230;
    --f7-actions-label-text-color: #8a8a8a;
  });
  .dark-vars({
    --f7-actions-bg-color: rgba(45,45,45,0.95);
    --f7-actions-bg-color-rgb: 45, 45, 45;
    --f7-actions-button-border-color: rgba(255, 255, 255, 0.15);
    --f7-actions-button-pressed-bg-color: rgba(50,50,50,0.9);
    --f7-actions-button-pressed-bg-color-rgb: 50, 50, 50;
    --f7-actions-label-text-color:rgba(255,255,255,0.55);

  })
});
.md-vars({
  --f7-actions-transition-timing-function: cubic-bezier(0, 0.8, 0.34, 1);
  --f7-actions-border-radius: 16px;
  --f7-actions-button-border-color: transparent;
  --f7-actions-button-padding: 0 16px;
  --f7-actions-button-text-align: left;
  --f7-actions-button-height: 48px;
  --f7-actions-button-height-landscape: 48px;
  --f7-actions-button-font-size: 16px;
  --f7-actions-button-icon-size: 24px;
  --f7-actions-button-justify-content: space-between;
  --f7-actions-label-padding: 12px 16px;
  --f7-actions-label-font-size: 14px;
  --f7-actions-label-font-weight: 500;
  --f7-actions-label-justify-content: flex-start;
  --f7-actions-group-margin: 0px;
  .light-vars({
    --f7-actions-button-pressed-bg-color: #e5e5e5;
  });
  .dark-vars({
    --f7-actions-button-pressed-bg-color: #2e2e2e;
  });
});
.md-color-vars({
  --f7-actions-button-text-color: var(--f7-md-on-surface);
  --f7-actions-bg-color: var(--f7-md-surface-3);
  --f7-actions-group-border-color: var(--f7-md-outline);
  --f7-actions-label-text-color: var(--f7-theme-color);
  --f7-actions-grid-button-text-color: var(--f7-md-on-surface);
});
