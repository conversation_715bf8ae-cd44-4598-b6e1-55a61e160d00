{"name": "framework7-vue", "version": "8.3.4", "description": "Build full featured iOS & Android apps using Framework7 & Vue", "type": "module", "exports": {".": {"import": "./framework7-vue.js", "types": "./framework7-vue.d.ts"}, "./bundle": {"import": "./framework7-vue-bundle.js", "types": "./framework7-vue.d.ts"}, "./components/*": "./components/*", "./shared/*": "./shared/*"}, "typings": "framework7-vue.d.ts", "typesVersions": {"*": {"bundle": ["framework7-vue-bundle.d.ts"]}}, "web-types": "framework7-vue-web-types.json", "repository": {"type": "git", "url": "https://github.com/framework7io/framework7.git"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "mobile", "framework", "framework7", "<PERSON><PERSON>", "ios", "iphone", "ipad", "apple", "phonegap", "capacitor", "native", "touch", "appstore", "app", "f7", "material", "android", "google", "googleplay"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/framework7io/framework7/issues"}, "homepage": "https://framework7.io/vue/", "funding": {"type": "patreon", "url": "https://www.patreon.com/framework7"}, "releaseDate": "September 18, 2024"}