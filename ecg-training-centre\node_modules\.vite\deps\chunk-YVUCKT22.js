import {
  __export
} from "./chunk-PZ5AY32C.js";

// node_modules/dom7/dom7.esm.js
var dom7_esm_exports = {};
__export(dom7_esm_exports, {
  $: () => $,
  add: () => add,
  addClass: () => addClass,
  animate: () => animate,
  animationEnd: () => animationEnd,
  append: () => append,
  appendTo: () => appendTo,
  attr: () => attr,
  blur: () => blur,
  change: () => change,
  children: () => children,
  click: () => click,
  closest: () => closest,
  css: () => css,
  data: () => data,
  dataset: () => dataset,
  default: () => dom7_esm_default,
  detach: () => detach,
  each: () => each,
  empty: () => empty,
  eq: () => eq,
  filter: () => filter,
  find: () => find,
  focus: () => focus,
  focusin: () => focusin,
  focusout: () => focusout,
  hasClass: () => hasClass,
  height: () => height,
  hide: () => hide,
  html: () => html,
  index: () => index,
  insertAfter: () => insertAfter,
  insertBefore: () => insertBefore,
  is: () => is,
  keydown: () => keydown,
  keypress: () => keypress,
  keyup: () => keyup,
  mousedown: () => mousedown,
  mouseenter: () => mouseenter,
  mouseleave: () => mouseleave,
  mousemove: () => mousemove,
  mouseout: () => mouseout,
  mouseover: () => mouseover,
  mouseup: () => mouseup,
  next: () => next,
  nextAll: () => nextAll,
  off: () => off,
  offset: () => offset,
  on: () => on,
  once: () => once,
  outerHeight: () => outerHeight,
  outerWidth: () => outerWidth,
  parent: () => parent,
  parents: () => parents,
  prepend: () => prepend,
  prependTo: () => prependTo,
  prev: () => prev,
  prevAll: () => prevAll,
  prop: () => prop,
  remove: () => remove,
  removeAttr: () => removeAttr,
  removeClass: () => removeClass,
  removeData: () => removeData,
  resize: () => resize,
  scroll: () => scroll,
  scrollLeft: () => scrollLeft,
  scrollTo: () => scrollTo,
  scrollTop: () => scrollTop,
  show: () => show,
  siblings: () => siblings,
  stop: () => stop,
  styles: () => styles,
  submit: () => submit,
  text: () => text,
  toggleClass: () => toggleClass,
  touchend: () => touchend,
  touchmove: () => touchmove,
  touchstart: () => touchstart,
  transform: () => transform,
  transition: () => transition,
  transitionEnd: () => transitionEnd,
  transitionStart: () => transitionStart,
  trigger: () => trigger,
  val: () => val,
  value: () => value,
  width: () => width
});

// node_modules/ssr-window/ssr-window.esm.js
function isObject(obj) {
  return obj !== null && typeof obj === "object" && "constructor" in obj && obj.constructor === Object;
}
function extend(target = {}, src = {}) {
  Object.keys(src).forEach((key) => {
    if (typeof target[key] === "undefined")
      target[key] = src[key];
    else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {
      extend(target[key], src[key]);
    }
  });
}
var ssrDocument = {
  body: {},
  addEventListener() {
  },
  removeEventListener() {
  },
  activeElement: {
    blur() {
    },
    nodeName: ""
  },
  querySelector() {
    return null;
  },
  querySelectorAll() {
    return [];
  },
  getElementById() {
    return null;
  },
  createEvent() {
    return {
      initEvent() {
      }
    };
  },
  createElement() {
    return {
      children: [],
      childNodes: [],
      style: {},
      setAttribute() {
      },
      getElementsByTagName() {
        return [];
      }
    };
  },
  createElementNS() {
    return {};
  },
  importNode() {
    return null;
  },
  location: {
    hash: "",
    host: "",
    hostname: "",
    href: "",
    origin: "",
    pathname: "",
    protocol: "",
    search: ""
  }
};
function getDocument() {
  const doc = typeof document !== "undefined" ? document : {};
  extend(doc, ssrDocument);
  return doc;
}
var ssrWindow = {
  document: ssrDocument,
  navigator: {
    userAgent: ""
  },
  location: {
    hash: "",
    host: "",
    hostname: "",
    href: "",
    origin: "",
    pathname: "",
    protocol: "",
    search: ""
  },
  history: {
    replaceState() {
    },
    pushState() {
    },
    go() {
    },
    back() {
    }
  },
  CustomEvent: function CustomEvent() {
    return this;
  },
  addEventListener() {
  },
  removeEventListener() {
  },
  getComputedStyle() {
    return {
      getPropertyValue() {
        return "";
      }
    };
  },
  Image() {
  },
  Date() {
  },
  screen: {},
  setTimeout() {
  },
  clearTimeout() {
  },
  matchMedia() {
    return {};
  },
  requestAnimationFrame(callback) {
    if (typeof setTimeout === "undefined") {
      callback();
      return null;
    }
    return setTimeout(callback, 0);
  },
  cancelAnimationFrame(id2) {
    if (typeof setTimeout === "undefined") {
      return;
    }
    clearTimeout(id2);
  }
};
function getWindow() {
  const win = typeof window !== "undefined" ? window : {};
  extend(win, ssrWindow);
  return win;
}

// node_modules/dom7/dom7.esm.js
function makeReactive(obj) {
  const proto = obj.__proto__;
  Object.defineProperty(obj, "__proto__", {
    get() {
      return proto;
    },
    set(value2) {
      proto.__proto__ = value2;
    }
  });
}
var Dom7 = class extends Array {
  constructor(items) {
    if (typeof items === "number") {
      super(items);
    } else {
      super(...items || []);
      makeReactive(this);
    }
  }
};
function arrayFlat(arr = []) {
  const res = [];
  arr.forEach((el) => {
    if (Array.isArray(el)) {
      res.push(...arrayFlat(el));
    } else {
      res.push(el);
    }
  });
  return res;
}
function arrayFilter(arr, callback) {
  return Array.prototype.filter.call(arr, callback);
}
function arrayUnique(arr) {
  const uniqueArray = [];
  for (let i = 0; i < arr.length; i += 1) {
    if (uniqueArray.indexOf(arr[i]) === -1) uniqueArray.push(arr[i]);
  }
  return uniqueArray;
}
function toCamelCase(string) {
  return string.toLowerCase().replace(/-(.)/g, (match, group) => group.toUpperCase());
}
function qsa(selector, context) {
  if (typeof selector !== "string") {
    return [selector];
  }
  const a = [];
  const res = context.querySelectorAll(selector);
  for (let i = 0; i < res.length; i += 1) {
    a.push(res[i]);
  }
  return a;
}
function $(selector, context) {
  const window2 = getWindow();
  const document2 = getDocument();
  let arr = [];
  if (!context && selector instanceof Dom7) {
    return selector;
  }
  if (!selector) {
    return new Dom7(arr);
  }
  if (typeof selector === "string") {
    const html2 = selector.trim();
    if (html2.indexOf("<") >= 0 && html2.indexOf(">") >= 0) {
      let toCreate = "div";
      if (html2.indexOf("<li") === 0) toCreate = "ul";
      if (html2.indexOf("<tr") === 0) toCreate = "tbody";
      if (html2.indexOf("<td") === 0 || html2.indexOf("<th") === 0) toCreate = "tr";
      if (html2.indexOf("<tbody") === 0) toCreate = "table";
      if (html2.indexOf("<option") === 0) toCreate = "select";
      const tempParent = document2.createElement(toCreate);
      tempParent.innerHTML = html2;
      for (let i = 0; i < tempParent.childNodes.length; i += 1) {
        arr.push(tempParent.childNodes[i]);
      }
    } else {
      arr = qsa(selector.trim(), context || document2);
    }
  } else if (selector.nodeType || selector === window2 || selector === document2) {
    arr.push(selector);
  } else if (Array.isArray(selector)) {
    if (selector instanceof Dom7) return selector;
    arr = selector;
  }
  return new Dom7(arrayUnique(arr));
}
$.fn = Dom7.prototype;
function addClass(...classes) {
  const classNames = arrayFlat(classes.map((c) => c.split(" ")));
  this.forEach((el) => {
    el.classList.add(...classNames);
  });
  return this;
}
function removeClass(...classes) {
  const classNames = arrayFlat(classes.map((c) => c.split(" ")));
  this.forEach((el) => {
    el.classList.remove(...classNames);
  });
  return this;
}
function toggleClass(...classes) {
  const classNames = arrayFlat(classes.map((c) => c.split(" ")));
  this.forEach((el) => {
    classNames.forEach((className) => {
      el.classList.toggle(className);
    });
  });
}
function hasClass(...classes) {
  const classNames = arrayFlat(classes.map((c) => c.split(" ")));
  return arrayFilter(this, (el) => {
    return classNames.filter((className) => el.classList.contains(className)).length > 0;
  }).length > 0;
}
function attr(attrs, value2) {
  if (arguments.length === 1 && typeof attrs === "string") {
    if (this[0]) return this[0].getAttribute(attrs);
    return void 0;
  }
  for (let i = 0; i < this.length; i += 1) {
    if (arguments.length === 2) {
      this[i].setAttribute(attrs, value2);
    } else {
      for (const attrName in attrs) {
        this[i][attrName] = attrs[attrName];
        this[i].setAttribute(attrName, attrs[attrName]);
      }
    }
  }
  return this;
}
function removeAttr(attr2) {
  for (let i = 0; i < this.length; i += 1) {
    this[i].removeAttribute(attr2);
  }
  return this;
}
function prop(props, value2) {
  if (arguments.length === 1 && typeof props === "string") {
    if (this[0]) return this[0][props];
  } else {
    for (let i = 0; i < this.length; i += 1) {
      if (arguments.length === 2) {
        this[i][props] = value2;
      } else {
        for (const propName in props) {
          this[i][propName] = props[propName];
        }
      }
    }
    return this;
  }
  return this;
}
function data(key, value2) {
  let el;
  if (typeof value2 === "undefined") {
    el = this[0];
    if (!el) return void 0;
    if (el.dom7ElementDataStorage && key in el.dom7ElementDataStorage) {
      return el.dom7ElementDataStorage[key];
    }
    const dataKey = el.getAttribute(`data-${key}`);
    if (dataKey) {
      return dataKey;
    }
    return void 0;
  }
  for (let i = 0; i < this.length; i += 1) {
    el = this[i];
    if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};
    el.dom7ElementDataStorage[key] = value2;
  }
  return this;
}
function removeData(key) {
  for (let i = 0; i < this.length; i += 1) {
    const el = this[i];
    if (el.dom7ElementDataStorage && el.dom7ElementDataStorage[key]) {
      el.dom7ElementDataStorage[key] = null;
      delete el.dom7ElementDataStorage[key];
    }
  }
}
function dataset() {
  const el = this[0];
  if (!el) return void 0;
  const dataset2 = {};
  if (el.dataset) {
    for (const dataKey in el.dataset) {
      dataset2[dataKey] = el.dataset[dataKey];
    }
  } else {
    for (let i = 0; i < el.attributes.length; i += 1) {
      const attr2 = el.attributes[i];
      if (attr2.name.indexOf("data-") >= 0) {
        dataset2[toCamelCase(attr2.name.split("data-")[1])] = attr2.value;
      }
    }
  }
  for (const key in dataset2) {
    if (dataset2[key] === "false") dataset2[key] = false;
    else if (dataset2[key] === "true") dataset2[key] = true;
    else if (parseFloat(dataset2[key]) === dataset2[key] * 1) dataset2[key] *= 1;
  }
  return dataset2;
}
function val(value2) {
  if (typeof value2 === "undefined") {
    const el = this[0];
    if (!el) return void 0;
    if (el.multiple && el.nodeName.toLowerCase() === "select") {
      const values = [];
      for (let i = 0; i < el.selectedOptions.length; i += 1) {
        values.push(el.selectedOptions[i].value);
      }
      return values;
    }
    return el.value;
  }
  for (let i = 0; i < this.length; i += 1) {
    const el = this[i];
    if (Array.isArray(value2) && el.multiple && el.nodeName.toLowerCase() === "select") {
      for (let j = 0; j < el.options.length; j += 1) {
        el.options[j].selected = value2.indexOf(el.options[j].value) >= 0;
      }
    } else {
      el.value = value2;
    }
  }
  return this;
}
function value(value2) {
  return this.val(value2);
}
function transform(transform2) {
  for (let i = 0; i < this.length; i += 1) {
    this[i].style.transform = transform2;
  }
  return this;
}
function transition(duration) {
  for (let i = 0; i < this.length; i += 1) {
    this[i].style.transitionDuration = typeof duration !== "string" ? `${duration}ms` : duration;
  }
  return this;
}
function on(...args) {
  let [eventType, targetSelector, listener, capture] = args;
  if (typeof args[1] === "function") {
    [eventType, listener, capture] = args;
    targetSelector = void 0;
  }
  if (!capture) capture = false;
  function handleLiveEvent(e) {
    const target = e.target;
    if (!target) return;
    const eventData = e.target.dom7EventData || [];
    if (eventData.indexOf(e) < 0) {
      eventData.unshift(e);
    }
    if ($(target).is(targetSelector)) listener.apply(target, eventData);
    else {
      const parents2 = $(target).parents();
      for (let k = 0; k < parents2.length; k += 1) {
        if ($(parents2[k]).is(targetSelector)) listener.apply(parents2[k], eventData);
      }
    }
  }
  function handleEvent(e) {
    const eventData = e && e.target ? e.target.dom7EventData || [] : [];
    if (eventData.indexOf(e) < 0) {
      eventData.unshift(e);
    }
    listener.apply(this, eventData);
  }
  const events = eventType.split(" ");
  let j;
  for (let i = 0; i < this.length; i += 1) {
    const el = this[i];
    if (!targetSelector) {
      for (j = 0; j < events.length; j += 1) {
        const event = events[j];
        if (!el.dom7Listeners) el.dom7Listeners = {};
        if (!el.dom7Listeners[event]) el.dom7Listeners[event] = [];
        el.dom7Listeners[event].push({
          listener,
          proxyListener: handleEvent
        });
        el.addEventListener(event, handleEvent, capture);
      }
    } else {
      for (j = 0; j < events.length; j += 1) {
        const event = events[j];
        if (!el.dom7LiveListeners) el.dom7LiveListeners = {};
        if (!el.dom7LiveListeners[event]) el.dom7LiveListeners[event] = [];
        el.dom7LiveListeners[event].push({
          listener,
          proxyListener: handleLiveEvent
        });
        el.addEventListener(event, handleLiveEvent, capture);
      }
    }
  }
  return this;
}
function off(...args) {
  let [eventType, targetSelector, listener, capture] = args;
  if (typeof args[1] === "function") {
    [eventType, listener, capture] = args;
    targetSelector = void 0;
  }
  if (!capture) capture = false;
  const events = eventType.split(" ");
  for (let i = 0; i < events.length; i += 1) {
    const event = events[i];
    for (let j = 0; j < this.length; j += 1) {
      const el = this[j];
      let handlers;
      if (!targetSelector && el.dom7Listeners) {
        handlers = el.dom7Listeners[event];
      } else if (targetSelector && el.dom7LiveListeners) {
        handlers = el.dom7LiveListeners[event];
      }
      if (handlers && handlers.length) {
        for (let k = handlers.length - 1; k >= 0; k -= 1) {
          const handler = handlers[k];
          if (listener && handler.listener === listener) {
            el.removeEventListener(event, handler.proxyListener, capture);
            handlers.splice(k, 1);
          } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {
            el.removeEventListener(event, handler.proxyListener, capture);
            handlers.splice(k, 1);
          } else if (!listener) {
            el.removeEventListener(event, handler.proxyListener, capture);
            handlers.splice(k, 1);
          }
        }
      }
    }
  }
  return this;
}
function once(...args) {
  const dom = this;
  let [eventName, targetSelector, listener, capture] = args;
  if (typeof args[1] === "function") {
    [eventName, listener, capture] = args;
    targetSelector = void 0;
  }
  function onceHandler(...eventArgs) {
    listener.apply(this, eventArgs);
    dom.off(eventName, targetSelector, onceHandler, capture);
    if (onceHandler.dom7proxy) {
      delete onceHandler.dom7proxy;
    }
  }
  onceHandler.dom7proxy = listener;
  return dom.on(eventName, targetSelector, onceHandler, capture);
}
function trigger(...args) {
  const window2 = getWindow();
  const events = args[0].split(" ");
  const eventData = args[1];
  for (let i = 0; i < events.length; i += 1) {
    const event = events[i];
    for (let j = 0; j < this.length; j += 1) {
      const el = this[j];
      if (window2.CustomEvent) {
        const evt = new window2.CustomEvent(event, {
          detail: eventData,
          bubbles: true,
          cancelable: true
        });
        el.dom7EventData = args.filter((data2, dataIndex) => dataIndex > 0);
        el.dispatchEvent(evt);
        el.dom7EventData = [];
        delete el.dom7EventData;
      }
    }
  }
  return this;
}
function transitionStart(callback) {
  const dom = this;
  function fireCallBack(e) {
    if (e.target !== this) return;
    callback.call(this, e);
    dom.off("transitionstart", fireCallBack);
  }
  if (callback) {
    dom.on("transitionstart", fireCallBack);
  }
  return this;
}
function transitionEnd(callback) {
  const dom = this;
  function fireCallBack(e) {
    if (e.target !== this) return;
    callback.call(this, e);
    dom.off("transitionend", fireCallBack);
  }
  if (callback) {
    dom.on("transitionend", fireCallBack);
  }
  return this;
}
function animationEnd(callback) {
  const dom = this;
  function fireCallBack(e) {
    if (e.target !== this) return;
    callback.call(this, e);
    dom.off("animationend", fireCallBack);
  }
  if (callback) {
    dom.on("animationend", fireCallBack);
  }
  return this;
}
function width() {
  const window2 = getWindow();
  if (this[0] === window2) {
    return window2.innerWidth;
  }
  if (this.length > 0) {
    return parseFloat(this.css("width"));
  }
  return null;
}
function outerWidth(includeMargins) {
  if (this.length > 0) {
    if (includeMargins) {
      const styles2 = this.styles();
      return this[0].offsetWidth + parseFloat(styles2.getPropertyValue("margin-right")) + parseFloat(styles2.getPropertyValue("margin-left"));
    }
    return this[0].offsetWidth;
  }
  return null;
}
function height() {
  const window2 = getWindow();
  if (this[0] === window2) {
    return window2.innerHeight;
  }
  if (this.length > 0) {
    return parseFloat(this.css("height"));
  }
  return null;
}
function outerHeight(includeMargins) {
  if (this.length > 0) {
    if (includeMargins) {
      const styles2 = this.styles();
      return this[0].offsetHeight + parseFloat(styles2.getPropertyValue("margin-top")) + parseFloat(styles2.getPropertyValue("margin-bottom"));
    }
    return this[0].offsetHeight;
  }
  return null;
}
function offset() {
  if (this.length > 0) {
    const window2 = getWindow();
    const document2 = getDocument();
    const el = this[0];
    const box = el.getBoundingClientRect();
    const body = document2.body;
    const clientTop = el.clientTop || body.clientTop || 0;
    const clientLeft = el.clientLeft || body.clientLeft || 0;
    const scrollTop2 = el === window2 ? window2.scrollY : el.scrollTop;
    const scrollLeft2 = el === window2 ? window2.scrollX : el.scrollLeft;
    return {
      top: box.top + scrollTop2 - clientTop,
      left: box.left + scrollLeft2 - clientLeft
    };
  }
  return null;
}
function hide() {
  for (let i = 0; i < this.length; i += 1) {
    this[i].style.display = "none";
  }
  return this;
}
function show() {
  const window2 = getWindow();
  for (let i = 0; i < this.length; i += 1) {
    const el = this[i];
    if (el.style.display === "none") {
      el.style.display = "";
    }
    if (window2.getComputedStyle(el, null).getPropertyValue("display") === "none") {
      el.style.display = "block";
    }
  }
  return this;
}
function styles() {
  const window2 = getWindow();
  if (this[0]) return window2.getComputedStyle(this[0], null);
  return {};
}
function css(props, value2) {
  const window2 = getWindow();
  let i;
  if (arguments.length === 1) {
    if (typeof props === "string") {
      if (this[0]) return window2.getComputedStyle(this[0], null).getPropertyValue(props);
    } else {
      for (i = 0; i < this.length; i += 1) {
        for (const prop2 in props) {
          this[i].style[prop2] = props[prop2];
        }
      }
      return this;
    }
  }
  if (arguments.length === 2 && typeof props === "string") {
    for (i = 0; i < this.length; i += 1) {
      this[i].style[props] = value2;
    }
    return this;
  }
  return this;
}
function each(callback) {
  if (!callback) return this;
  this.forEach((el, index2) => {
    callback.apply(el, [el, index2]);
  });
  return this;
}
function filter(callback) {
  const result = arrayFilter(this, callback);
  return $(result);
}
function html(html2) {
  if (typeof html2 === "undefined") {
    return this[0] ? this[0].innerHTML : null;
  }
  for (let i = 0; i < this.length; i += 1) {
    this[i].innerHTML = html2;
  }
  return this;
}
function text(text2) {
  if (typeof text2 === "undefined") {
    return this[0] ? this[0].textContent.trim() : null;
  }
  for (let i = 0; i < this.length; i += 1) {
    this[i].textContent = text2;
  }
  return this;
}
function is(selector) {
  const window2 = getWindow();
  const document2 = getDocument();
  const el = this[0];
  let compareWith;
  let i;
  if (!el || typeof selector === "undefined") return false;
  if (typeof selector === "string") {
    if (el.matches) return el.matches(selector);
    if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);
    if (el.msMatchesSelector) return el.msMatchesSelector(selector);
    compareWith = $(selector);
    for (i = 0; i < compareWith.length; i += 1) {
      if (compareWith[i] === el) return true;
    }
    return false;
  }
  if (selector === document2) {
    return el === document2;
  }
  if (selector === window2) {
    return el === window2;
  }
  if (selector.nodeType || selector instanceof Dom7) {
    compareWith = selector.nodeType ? [selector] : selector;
    for (i = 0; i < compareWith.length; i += 1) {
      if (compareWith[i] === el) return true;
    }
    return false;
  }
  return false;
}
function index() {
  let child = this[0];
  let i;
  if (child) {
    i = 0;
    while ((child = child.previousSibling) !== null) {
      if (child.nodeType === 1) i += 1;
    }
    return i;
  }
  return void 0;
}
function eq(index2) {
  if (typeof index2 === "undefined") return this;
  const length = this.length;
  if (index2 > length - 1) {
    return $([]);
  }
  if (index2 < 0) {
    const returnIndex = length + index2;
    if (returnIndex < 0) return $([]);
    return $([this[returnIndex]]);
  }
  return $([this[index2]]);
}
function append(...els) {
  let newChild;
  const document2 = getDocument();
  for (let k = 0; k < els.length; k += 1) {
    newChild = els[k];
    for (let i = 0; i < this.length; i += 1) {
      if (typeof newChild === "string") {
        const tempDiv = document2.createElement("div");
        tempDiv.innerHTML = newChild;
        while (tempDiv.firstChild) {
          this[i].appendChild(tempDiv.firstChild);
        }
      } else if (newChild instanceof Dom7) {
        for (let j = 0; j < newChild.length; j += 1) {
          this[i].appendChild(newChild[j]);
        }
      } else {
        this[i].appendChild(newChild);
      }
    }
  }
  return this;
}
function appendTo(parent2) {
  $(parent2).append(this);
  return this;
}
function prepend(newChild) {
  const document2 = getDocument();
  let i;
  let j;
  for (i = 0; i < this.length; i += 1) {
    if (typeof newChild === "string") {
      const tempDiv = document2.createElement("div");
      tempDiv.innerHTML = newChild;
      for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {
        this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);
      }
    } else if (newChild instanceof Dom7) {
      for (j = 0; j < newChild.length; j += 1) {
        this[i].insertBefore(newChild[j], this[i].childNodes[0]);
      }
    } else {
      this[i].insertBefore(newChild, this[i].childNodes[0]);
    }
  }
  return this;
}
function prependTo(parent2) {
  $(parent2).prepend(this);
  return this;
}
function insertBefore(selector) {
  const before = $(selector);
  for (let i = 0; i < this.length; i += 1) {
    if (before.length === 1) {
      before[0].parentNode.insertBefore(this[i], before[0]);
    } else if (before.length > 1) {
      for (let j = 0; j < before.length; j += 1) {
        before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);
      }
    }
  }
}
function insertAfter(selector) {
  const after = $(selector);
  for (let i = 0; i < this.length; i += 1) {
    if (after.length === 1) {
      after[0].parentNode.insertBefore(this[i], after[0].nextSibling);
    } else if (after.length > 1) {
      for (let j = 0; j < after.length; j += 1) {
        after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);
      }
    }
  }
}
function next(selector) {
  if (this.length > 0) {
    if (selector) {
      if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {
        return $([this[0].nextElementSibling]);
      }
      return $([]);
    }
    if (this[0].nextElementSibling) return $([this[0].nextElementSibling]);
    return $([]);
  }
  return $([]);
}
function nextAll(selector) {
  const nextEls = [];
  let el = this[0];
  if (!el) return $([]);
  while (el.nextElementSibling) {
    const next2 = el.nextElementSibling;
    if (selector) {
      if ($(next2).is(selector)) nextEls.push(next2);
    } else nextEls.push(next2);
    el = next2;
  }
  return $(nextEls);
}
function prev(selector) {
  if (this.length > 0) {
    const el = this[0];
    if (selector) {
      if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {
        return $([el.previousElementSibling]);
      }
      return $([]);
    }
    if (el.previousElementSibling) return $([el.previousElementSibling]);
    return $([]);
  }
  return $([]);
}
function prevAll(selector) {
  const prevEls = [];
  let el = this[0];
  if (!el) return $([]);
  while (el.previousElementSibling) {
    const prev2 = el.previousElementSibling;
    if (selector) {
      if ($(prev2).is(selector)) prevEls.push(prev2);
    } else prevEls.push(prev2);
    el = prev2;
  }
  return $(prevEls);
}
function siblings(selector) {
  return this.nextAll(selector).add(this.prevAll(selector));
}
function parent(selector) {
  const parents2 = [];
  for (let i = 0; i < this.length; i += 1) {
    if (this[i].parentNode !== null) {
      if (selector) {
        if ($(this[i].parentNode).is(selector)) parents2.push(this[i].parentNode);
      } else {
        parents2.push(this[i].parentNode);
      }
    }
  }
  return $(parents2);
}
function parents(selector) {
  const parents2 = [];
  for (let i = 0; i < this.length; i += 1) {
    let parent2 = this[i].parentNode;
    while (parent2) {
      if (selector) {
        if ($(parent2).is(selector)) parents2.push(parent2);
      } else {
        parents2.push(parent2);
      }
      parent2 = parent2.parentNode;
    }
  }
  return $(parents2);
}
function closest(selector) {
  let closest2 = this;
  if (typeof selector === "undefined") {
    return $([]);
  }
  if (!closest2.is(selector)) {
    closest2 = closest2.parents(selector).eq(0);
  }
  return closest2;
}
function find(selector) {
  const foundElements = [];
  for (let i = 0; i < this.length; i += 1) {
    const found = this[i].querySelectorAll(selector);
    for (let j = 0; j < found.length; j += 1) {
      foundElements.push(found[j]);
    }
  }
  return $(foundElements);
}
function children(selector) {
  const children2 = [];
  for (let i = 0; i < this.length; i += 1) {
    const childNodes = this[i].children;
    for (let j = 0; j < childNodes.length; j += 1) {
      if (!selector || $(childNodes[j]).is(selector)) {
        children2.push(childNodes[j]);
      }
    }
  }
  return $(children2);
}
function remove() {
  for (let i = 0; i < this.length; i += 1) {
    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);
  }
  return this;
}
function detach() {
  return this.remove();
}
function add(...els) {
  const dom = this;
  let i;
  let j;
  for (i = 0; i < els.length; i += 1) {
    const toAdd = $(els[i]);
    for (j = 0; j < toAdd.length; j += 1) {
      dom.push(toAdd[j]);
    }
  }
  return dom;
}
function empty() {
  for (let i = 0; i < this.length; i += 1) {
    const el = this[i];
    if (el.nodeType === 1) {
      for (let j = 0; j < el.childNodes.length; j += 1) {
        if (el.childNodes[j].parentNode) {
          el.childNodes[j].parentNode.removeChild(el.childNodes[j]);
        }
      }
      el.textContent = "";
    }
  }
  return this;
}
function scrollTo(...args) {
  const window2 = getWindow();
  let [left, top, duration, easing, callback] = args;
  if (args.length === 4 && typeof easing === "function") {
    callback = easing;
    [left, top, duration, callback, easing] = args;
  }
  if (typeof easing === "undefined") easing = "swing";
  return this.each(function animate2() {
    const el = this;
    let currentTop;
    let currentLeft;
    let maxTop;
    let maxLeft;
    let newTop;
    let newLeft;
    let scrollTop2;
    let scrollLeft2;
    let animateTop = top > 0 || top === 0;
    let animateLeft = left > 0 || left === 0;
    if (typeof easing === "undefined") {
      easing = "swing";
    }
    if (animateTop) {
      currentTop = el.scrollTop;
      if (!duration) {
        el.scrollTop = top;
      }
    }
    if (animateLeft) {
      currentLeft = el.scrollLeft;
      if (!duration) {
        el.scrollLeft = left;
      }
    }
    if (!duration) return;
    if (animateTop) {
      maxTop = el.scrollHeight - el.offsetHeight;
      newTop = Math.max(Math.min(top, maxTop), 0);
    }
    if (animateLeft) {
      maxLeft = el.scrollWidth - el.offsetWidth;
      newLeft = Math.max(Math.min(left, maxLeft), 0);
    }
    let startTime = null;
    if (animateTop && newTop === currentTop) animateTop = false;
    if (animateLeft && newLeft === currentLeft) animateLeft = false;
    function render(time = (/* @__PURE__ */ new Date()).getTime()) {
      if (startTime === null) {
        startTime = time;
      }
      const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);
      const easeProgress = easing === "linear" ? progress : 0.5 - Math.cos(progress * Math.PI) / 2;
      let done;
      if (animateTop) scrollTop2 = currentTop + easeProgress * (newTop - currentTop);
      if (animateLeft) scrollLeft2 = currentLeft + easeProgress * (newLeft - currentLeft);
      if (animateTop && newTop > currentTop && scrollTop2 >= newTop) {
        el.scrollTop = newTop;
        done = true;
      }
      if (animateTop && newTop < currentTop && scrollTop2 <= newTop) {
        el.scrollTop = newTop;
        done = true;
      }
      if (animateLeft && newLeft > currentLeft && scrollLeft2 >= newLeft) {
        el.scrollLeft = newLeft;
        done = true;
      }
      if (animateLeft && newLeft < currentLeft && scrollLeft2 <= newLeft) {
        el.scrollLeft = newLeft;
        done = true;
      }
      if (done) {
        if (callback) callback();
        return;
      }
      if (animateTop) el.scrollTop = scrollTop2;
      if (animateLeft) el.scrollLeft = scrollLeft2;
      window2.requestAnimationFrame(render);
    }
    window2.requestAnimationFrame(render);
  });
}
function scrollTop(...args) {
  let [top, duration, easing, callback] = args;
  if (args.length === 3 && typeof easing === "function") {
    [top, duration, callback, easing] = args;
  }
  const dom = this;
  if (typeof top === "undefined") {
    if (dom.length > 0) return dom[0].scrollTop;
    return null;
  }
  return dom.scrollTo(void 0, top, duration, easing, callback);
}
function scrollLeft(...args) {
  let [left, duration, easing, callback] = args;
  if (args.length === 3 && typeof easing === "function") {
    [left, duration, callback, easing] = args;
  }
  const dom = this;
  if (typeof left === "undefined") {
    if (dom.length > 0) return dom[0].scrollLeft;
    return null;
  }
  return dom.scrollTo(left, void 0, duration, easing, callback);
}
function animate(initialProps, initialParams) {
  const window2 = getWindow();
  const els = this;
  const a = {
    props: Object.assign({}, initialProps),
    params: Object.assign({
      duration: 300,
      easing: "swing"
      // or 'linear'
      /* Callbacks
      begin(elements)
      complete(elements)
      progress(elements, complete, remaining, start, tweenValue)
      */
    }, initialParams),
    elements: els,
    animating: false,
    que: [],
    easingProgress(easing, progress) {
      if (easing === "swing") {
        return 0.5 - Math.cos(progress * Math.PI) / 2;
      }
      if (typeof easing === "function") {
        return easing(progress);
      }
      return progress;
    },
    stop() {
      if (a.frameId) {
        window2.cancelAnimationFrame(a.frameId);
      }
      a.animating = false;
      a.elements.each((el) => {
        const element = el;
        delete element.dom7AnimateInstance;
      });
      a.que = [];
    },
    done(complete) {
      a.animating = false;
      a.elements.each((el) => {
        const element = el;
        delete element.dom7AnimateInstance;
      });
      if (complete) complete(els);
      if (a.que.length > 0) {
        const que = a.que.shift();
        a.animate(que[0], que[1]);
      }
    },
    animate(props, params) {
      if (a.animating) {
        a.que.push([props, params]);
        return a;
      }
      const elements = [];
      a.elements.each((el, index2) => {
        let initialFullValue;
        let initialValue;
        let unit;
        let finalValue;
        let finalFullValue;
        if (!el.dom7AnimateInstance) a.elements[index2].dom7AnimateInstance = a;
        elements[index2] = {
          container: el
        };
        Object.keys(props).forEach((prop2) => {
          initialFullValue = window2.getComputedStyle(el, null).getPropertyValue(prop2).replace(",", ".");
          initialValue = parseFloat(initialFullValue);
          unit = initialFullValue.replace(initialValue, "");
          finalValue = parseFloat(props[prop2]);
          finalFullValue = props[prop2] + unit;
          elements[index2][prop2] = {
            initialFullValue,
            initialValue,
            unit,
            finalValue,
            finalFullValue,
            currentValue: initialValue
          };
        });
      });
      let startTime = null;
      let time;
      let elementsDone = 0;
      let propsDone = 0;
      let done;
      let began = false;
      a.animating = true;
      function render() {
        time = (/* @__PURE__ */ new Date()).getTime();
        let progress;
        let easeProgress;
        if (!began) {
          began = true;
          if (params.begin) params.begin(els);
        }
        if (startTime === null) {
          startTime = time;
        }
        if (params.progress) {
          params.progress(els, Math.max(Math.min((time - startTime) / params.duration, 1), 0), startTime + params.duration - time < 0 ? 0 : startTime + params.duration - time, startTime);
        }
        elements.forEach((element) => {
          const el = element;
          if (done || el.done) return;
          Object.keys(props).forEach((prop2) => {
            if (done || el.done) return;
            progress = Math.max(Math.min((time - startTime) / params.duration, 1), 0);
            easeProgress = a.easingProgress(params.easing, progress);
            const {
              initialValue,
              finalValue,
              unit
            } = el[prop2];
            el[prop2].currentValue = initialValue + easeProgress * (finalValue - initialValue);
            const currentValue = el[prop2].currentValue;
            if (finalValue > initialValue && currentValue >= finalValue || finalValue < initialValue && currentValue <= finalValue) {
              el.container.style[prop2] = finalValue + unit;
              propsDone += 1;
              if (propsDone === Object.keys(props).length) {
                el.done = true;
                elementsDone += 1;
              }
              if (elementsDone === elements.length) {
                done = true;
              }
            }
            if (done) {
              a.done(params.complete);
              return;
            }
            el.container.style[prop2] = currentValue + unit;
          });
        });
        if (done) return;
        a.frameId = window2.requestAnimationFrame(render);
      }
      a.frameId = window2.requestAnimationFrame(render);
      return a;
    }
  };
  if (a.elements.length === 0) {
    return els;
  }
  let animateInstance;
  for (let i = 0; i < a.elements.length; i += 1) {
    if (a.elements[i].dom7AnimateInstance) {
      animateInstance = a.elements[i].dom7AnimateInstance;
    } else a.elements[i].dom7AnimateInstance = a;
  }
  if (!animateInstance) {
    animateInstance = a;
  }
  if (initialProps === "stop") {
    animateInstance.stop();
  } else {
    animateInstance.animate(a.props, a.params);
  }
  return els;
}
function stop() {
  const els = this;
  for (let i = 0; i < els.length; i += 1) {
    if (els[i].dom7AnimateInstance) {
      els[i].dom7AnimateInstance.stop();
    }
  }
}
var noTrigger = "resize scroll".split(" ");
function shortcut(name) {
  function eventHandler(...args) {
    if (typeof args[0] === "undefined") {
      for (let i = 0; i < this.length; i += 1) {
        if (noTrigger.indexOf(name) < 0) {
          if (name in this[i]) this[i][name]();
          else {
            $(this[i]).trigger(name);
          }
        }
      }
      return this;
    }
    return this.on(name, ...args);
  }
  return eventHandler;
}
var click = shortcut("click");
var blur = shortcut("blur");
var focus = shortcut("focus");
var focusin = shortcut("focusin");
var focusout = shortcut("focusout");
var keyup = shortcut("keyup");
var keydown = shortcut("keydown");
var keypress = shortcut("keypress");
var submit = shortcut("submit");
var change = shortcut("change");
var mousedown = shortcut("mousedown");
var mousemove = shortcut("mousemove");
var mouseup = shortcut("mouseup");
var mouseenter = shortcut("mouseenter");
var mouseleave = shortcut("mouseleave");
var mouseout = shortcut("mouseout");
var mouseover = shortcut("mouseover");
var touchstart = shortcut("touchstart");
var touchend = shortcut("touchend");
var touchmove = shortcut("touchmove");
var resize = shortcut("resize");
var scroll = shortcut("scroll");
var dom7_esm_default = $;

// node_modules/framework7/shared/dom7.js
Object.keys(dom7_esm_exports).forEach((methodName) => {
  if (methodName === "$") return;
  $.fn[methodName] = dom7_esm_exports[methodName];
});
var dom7_default = $;

// node_modules/framework7/shared/utils.js
var utils_exports = {};
__export(utils_exports, {
  bindMethods: () => bindMethods,
  cancelAnimationFrame: () => cancelAnimationFrame,
  colorHexToRgb: () => colorHexToRgb,
  colorHsbToHsl: () => colorHsbToHsl,
  colorHslToHsb: () => colorHslToHsb,
  colorHslToRgb: () => colorHslToRgb,
  colorRgbToHex: () => colorRgbToHex,
  colorRgbToHsl: () => colorRgbToHsl,
  colorThemeCSSProperties: () => colorThemeCSSProperties,
  colorThemeCSSStyles: () => colorThemeCSSStyles,
  deleteProps: () => deleteProps,
  eventNameToColonCase: () => eventNameToColonCase,
  extend: () => extend2,
  flattenArray: () => flattenArray,
  getTranslate: () => getTranslate,
  id: () => id,
  iosPreloaderContent: () => iosPreloaderContent,
  isObject: () => isObject2,
  mdPreloaderContent: () => mdPreloaderContent,
  merge: () => merge,
  nextFrame: () => nextFrame,
  nextTick: () => nextTick,
  now: () => now,
  parseUrlQuery: () => parseUrlQuery,
  requestAnimationFrame: () => requestAnimationFrame,
  serializeObject: () => serializeObject,
  uniqueNumber: () => uniqueNumber
});

// node_modules/framework7/shared/material-color-utils.js
function signum(num) {
  return num < 0 ? -1 : 0 === num ? 0 : 1;
}
function lerp(start, stop2, amount) {
  return (1 - amount) * start + amount * stop2;
}
function clampInt(min, max, input) {
  return input < min ? min : input > max ? max : input;
}
function clampDouble(min, max, input) {
  return input < min ? min : input > max ? max : input;
}
function sanitizeDegreesDouble(degrees) {
  return (degrees %= 360) < 0 && (degrees += 360), degrees;
}
function rotationDirection(from, to) {
  return sanitizeDegreesDouble(to - from) <= 180 ? 1 : -1;
}
function differenceDegrees(a, b) {
  return 180 - Math.abs(Math.abs(a - b) - 180);
}
function matrixMultiply(row, matrix) {
  return [row[0] * matrix[0][0] + row[1] * matrix[0][1] + row[2] * matrix[0][2], row[0] * matrix[1][0] + row[1] * matrix[1][1] + row[2] * matrix[1][2], row[0] * matrix[2][0] + row[1] * matrix[2][1] + row[2] * matrix[2][2]];
}
var SRGB_TO_XYZ = [[0.41233895, 0.35762064, 0.18051042], [0.2126, 0.7152, 0.0722], [0.01932141, 0.11916382, 0.95034478]];
var XYZ_TO_SRGB = [[3.2413774792388685, -1.5376652402851851, -0.49885366846268053], [-0.9691452513005321, 1.8758853451067872, 0.04156585616912061], [0.05562093689691305, -0.20395524564742123, 1.0571799111220335]];
var WHITE_POINT_D65 = [95.047, 100, 108.883];
function argbFromRgb(red, green, blue) {
  return (255 << 24 | (255 & red) << 16 | (255 & green) << 8 | 255 & blue) >>> 0;
}
function argbFromLinrgb(linrgb) {
  return argbFromRgb(delinearized(linrgb[0]), delinearized(linrgb[1]), delinearized(linrgb[2]));
}
function redFromArgb(argb) {
  return argb >> 16 & 255;
}
function greenFromArgb(argb) {
  return argb >> 8 & 255;
}
function blueFromArgb(argb) {
  return 255 & argb;
}
function argbFromXyz(x, y, z) {
  const matrix = XYZ_TO_SRGB, linearR = matrix[0][0] * x + matrix[0][1] * y + matrix[0][2] * z, linearG = matrix[1][0] * x + matrix[1][1] * y + matrix[1][2] * z, linearB = matrix[2][0] * x + matrix[2][1] * y + matrix[2][2] * z;
  return argbFromRgb(delinearized(linearR), delinearized(linearG), delinearized(linearB));
}
function xyzFromArgb(argb) {
  return matrixMultiply([linearized(redFromArgb(argb)), linearized(greenFromArgb(argb)), linearized(blueFromArgb(argb))], SRGB_TO_XYZ);
}
function argbFromLstar(lstar) {
  const component = delinearized(yFromLstar(lstar));
  return argbFromRgb(component, component, component);
}
function lstarFromArgb(argb) {
  return 116 * labF(xyzFromArgb(argb)[1] / 100) - 16;
}
function yFromLstar(lstar) {
  return 100 * labInvf((lstar + 16) / 116);
}
function lstarFromY(y) {
  return 116 * labF(y / 100) - 16;
}
function linearized(rgbComponent) {
  const normalized = rgbComponent / 255;
  return normalized <= 0.040449936 ? normalized / 12.92 * 100 : 100 * Math.pow((normalized + 0.055) / 1.055, 2.4);
}
function delinearized(rgbComponent) {
  const normalized = rgbComponent / 100;
  let delinearized2 = 0;
  return delinearized2 = normalized <= 31308e-7 ? 12.92 * normalized : 1.055 * Math.pow(normalized, 1 / 2.4) - 0.055, clampInt(0, 255, Math.round(255 * delinearized2));
}
function whitePointD65() {
  return WHITE_POINT_D65;
}
function labF(t) {
  return t > 216 / 24389 ? Math.pow(t, 1 / 3) : (903.2962962962963 * t + 16) / 116;
}
function labInvf(ft) {
  const ft3 = ft * ft * ft;
  return ft3 > 216 / 24389 ? ft3 : (116 * ft - 16) / 903.2962962962963;
}
var ViewingConditions = class _ViewingConditions {
  static make(whitePoint, adaptingLuminance, backgroundLstar, surround, discountingIlluminant) {
    if (whitePoint === void 0) {
      whitePoint = whitePointD65();
    }
    if (adaptingLuminance === void 0) {
      adaptingLuminance = 200 / Math.PI * yFromLstar(50) / 100;
    }
    if (backgroundLstar === void 0) {
      backgroundLstar = 50;
    }
    if (surround === void 0) {
      surround = 2;
    }
    if (discountingIlluminant === void 0) {
      discountingIlluminant = false;
    }
    const xyz = whitePoint, rW = 0.401288 * xyz[0] + 0.650173 * xyz[1] + -0.051461 * xyz[2], gW = -0.250268 * xyz[0] + 1.204414 * xyz[1] + 0.045854 * xyz[2], bW = -2079e-6 * xyz[0] + 0.048952 * xyz[1] + 0.953127 * xyz[2], f = 0.8 + surround / 10, c = f >= 0.9 ? lerp(0.59, 0.69, 10 * (f - 0.9)) : lerp(0.525, 0.59, 10 * (f - 0.8));
    let d = discountingIlluminant ? 1 : f * (1 - 1 / 3.6 * Math.exp((-adaptingLuminance - 42) / 92));
    d = d > 1 ? 1 : d < 0 ? 0 : d;
    const nc = f, rgbD = [d * (100 / rW) + 1 - d, d * (100 / gW) + 1 - d, d * (100 / bW) + 1 - d], k = 1 / (5 * adaptingLuminance + 1), k4 = k * k * k * k, k4F = 1 - k4, fl = k4 * adaptingLuminance + 0.1 * k4F * k4F * Math.cbrt(5 * adaptingLuminance), n = yFromLstar(backgroundLstar) / whitePoint[1], z = 1.48 + Math.sqrt(n), nbb = 0.725 / Math.pow(n, 0.2), ncb = nbb, rgbAFactors = [Math.pow(fl * rgbD[0] * rW / 100, 0.42), Math.pow(fl * rgbD[1] * gW / 100, 0.42), Math.pow(fl * rgbD[2] * bW / 100, 0.42)], rgbA = [400 * rgbAFactors[0] / (rgbAFactors[0] + 27.13), 400 * rgbAFactors[1] / (rgbAFactors[1] + 27.13), 400 * rgbAFactors[2] / (rgbAFactors[2] + 27.13)];
    return new _ViewingConditions(n, (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]) * nbb, nbb, ncb, c, nc, rgbD, fl, Math.pow(fl, 0.25), z);
  }
  constructor(n, aw, nbb, ncb, c, nc, rgbD, fl, fLRoot, z) {
    this.n = n, this.aw = aw, this.nbb = nbb, this.ncb = ncb, this.c = c, this.nc = nc, this.rgbD = rgbD, this.fl = fl, this.fLRoot = fLRoot, this.z = z;
  }
};
ViewingConditions.DEFAULT = ViewingConditions.make();
var Cam16 = class _Cam16 {
  constructor(hue, chroma, j, q, m, s, jstar, astar, bstar) {
    this.hue = hue, this.chroma = chroma, this.j = j, this.q = q, this.m = m, this.s = s, this.jstar = jstar, this.astar = astar, this.bstar = bstar;
  }
  distance(other) {
    const dJ = this.jstar - other.jstar, dA = this.astar - other.astar, dB = this.bstar - other.bstar, dEPrime = Math.sqrt(dJ * dJ + dA * dA + dB * dB);
    return 1.41 * Math.pow(dEPrime, 0.63);
  }
  static fromInt(argb) {
    return _Cam16.fromIntInViewingConditions(argb, ViewingConditions.DEFAULT);
  }
  static fromIntInViewingConditions(argb, viewingConditions) {
    const green = (65280 & argb) >> 8, blue = 255 & argb, redL = linearized((16711680 & argb) >> 16), greenL = linearized(green), blueL = linearized(blue), x = 0.41233895 * redL + 0.35762064 * greenL + 0.18051042 * blueL, y = 0.2126 * redL + 0.7152 * greenL + 0.0722 * blueL, z = 0.01932141 * redL + 0.11916382 * greenL + 0.95034478 * blueL, rC = 0.401288 * x + 0.650173 * y - 0.051461 * z, gC = -0.250268 * x + 1.204414 * y + 0.045854 * z, bC = -2079e-6 * x + 0.048952 * y + 0.953127 * z, rD = viewingConditions.rgbD[0] * rC, gD = viewingConditions.rgbD[1] * gC, bD = viewingConditions.rgbD[2] * bC, rAF = Math.pow(viewingConditions.fl * Math.abs(rD) / 100, 0.42), gAF = Math.pow(viewingConditions.fl * Math.abs(gD) / 100, 0.42), bAF = Math.pow(viewingConditions.fl * Math.abs(bD) / 100, 0.42), rA = 400 * signum(rD) * rAF / (rAF + 27.13), gA = 400 * signum(gD) * gAF / (gAF + 27.13), bA = 400 * signum(bD) * bAF / (bAF + 27.13), a = (11 * rA + -12 * gA + bA) / 11, b = (rA + gA - 2 * bA) / 9, u = (20 * rA + 20 * gA + 21 * bA) / 20, p2 = (40 * rA + 20 * gA + bA) / 20, atanDegrees = 180 * Math.atan2(b, a) / Math.PI, hue = atanDegrees < 0 ? atanDegrees + 360 : atanDegrees >= 360 ? atanDegrees - 360 : atanDegrees, hueRadians = hue * Math.PI / 180, ac = p2 * viewingConditions.nbb, j = 100 * Math.pow(ac / viewingConditions.aw, viewingConditions.c * viewingConditions.z), q = 4 / viewingConditions.c * Math.sqrt(j / 100) * (viewingConditions.aw + 4) * viewingConditions.fLRoot, huePrime = hue < 20.14 ? hue + 360 : hue, t = 5e4 / 13 * (0.25 * (Math.cos(huePrime * Math.PI / 180 + 2) + 3.8)) * viewingConditions.nc * viewingConditions.ncb * Math.sqrt(a * a + b * b) / (u + 0.305), alpha = Math.pow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), c = alpha * Math.sqrt(j / 100), m = c * viewingConditions.fLRoot, s = 50 * Math.sqrt(alpha * viewingConditions.c / (viewingConditions.aw + 4)), jstar = (1 + 100 * 7e-3) * j / (1 + 7e-3 * j), mstar = 1 / 0.0228 * Math.log(1 + 0.0228 * m), astar = mstar * Math.cos(hueRadians), bstar = mstar * Math.sin(hueRadians);
    return new _Cam16(hue, c, j, q, m, s, jstar, astar, bstar);
  }
  static fromJch(j, c, h) {
    return _Cam16.fromJchInViewingConditions(j, c, h, ViewingConditions.DEFAULT);
  }
  static fromJchInViewingConditions(j, c, h, viewingConditions) {
    const q = 4 / viewingConditions.c * Math.sqrt(j / 100) * (viewingConditions.aw + 4) * viewingConditions.fLRoot, m = c * viewingConditions.fLRoot, alpha = c / Math.sqrt(j / 100), s = 50 * Math.sqrt(alpha * viewingConditions.c / (viewingConditions.aw + 4)), hueRadians = h * Math.PI / 180, jstar = (1 + 100 * 7e-3) * j / (1 + 7e-3 * j), mstar = 1 / 0.0228 * Math.log(1 + 0.0228 * m), astar = mstar * Math.cos(hueRadians), bstar = mstar * Math.sin(hueRadians);
    return new _Cam16(h, c, j, q, m, s, jstar, astar, bstar);
  }
  static fromUcs(jstar, astar, bstar) {
    return _Cam16.fromUcsInViewingConditions(jstar, astar, bstar, ViewingConditions.DEFAULT);
  }
  static fromUcsInViewingConditions(jstar, astar, bstar, viewingConditions) {
    const a = astar, b = bstar, m = Math.sqrt(a * a + b * b), c = (Math.exp(0.0228 * m) - 1) / 0.0228 / viewingConditions.fLRoot;
    let h = Math.atan2(b, a) * (180 / Math.PI);
    h < 0 && (h += 360);
    const j = jstar / (1 - 7e-3 * (jstar - 100));
    return _Cam16.fromJchInViewingConditions(j, c, h, viewingConditions);
  }
  toInt() {
    return this.viewed(ViewingConditions.DEFAULT);
  }
  viewed(viewingConditions) {
    const alpha = 0 === this.chroma || 0 === this.j ? 0 : this.chroma / Math.sqrt(this.j / 100), t = Math.pow(alpha / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), 1 / 0.9), hRad = this.hue * Math.PI / 180, eHue = 0.25 * (Math.cos(hRad + 2) + 3.8), ac = viewingConditions.aw * Math.pow(this.j / 100, 1 / viewingConditions.c / viewingConditions.z), p1 = eHue * (5e4 / 13) * viewingConditions.nc * viewingConditions.ncb, p2 = ac / viewingConditions.nbb, hSin = Math.sin(hRad), hCos = Math.cos(hRad), gamma = 23 * (p2 + 0.305) * t / (23 * p1 + 11 * t * hCos + 108 * t * hSin), a = gamma * hCos, b = gamma * hSin, rA = (460 * p2 + 451 * a + 288 * b) / 1403, gA = (460 * p2 - 891 * a - 261 * b) / 1403, bA = (460 * p2 - 220 * a - 6300 * b) / 1403, rCBase = Math.max(0, 27.13 * Math.abs(rA) / (400 - Math.abs(rA))), rC = signum(rA) * (100 / viewingConditions.fl) * Math.pow(rCBase, 1 / 0.42), gCBase = Math.max(0, 27.13 * Math.abs(gA) / (400 - Math.abs(gA))), gC = signum(gA) * (100 / viewingConditions.fl) * Math.pow(gCBase, 1 / 0.42), bCBase = Math.max(0, 27.13 * Math.abs(bA) / (400 - Math.abs(bA))), bC = signum(bA) * (100 / viewingConditions.fl) * Math.pow(bCBase, 1 / 0.42), rF = rC / viewingConditions.rgbD[0], gF = gC / viewingConditions.rgbD[1], bF = bC / viewingConditions.rgbD[2];
    return argbFromXyz(1.86206786 * rF - 1.01125463 * gF + 0.14918677 * bF, 0.38752654 * rF + 0.62144744 * gF - 897398e-8 * bF, -0.0158415 * rF - 0.03412294 * gF + 1.04996444 * bF);
  }
  static fromXyzInViewingConditions(x, y, z, viewingConditions) {
    const rC = 0.401288 * x + 0.650173 * y - 0.051461 * z, gC = -0.250268 * x + 1.204414 * y + 0.045854 * z, bC = -2079e-6 * x + 0.048952 * y + 0.953127 * z, rD = viewingConditions.rgbD[0] * rC, gD = viewingConditions.rgbD[1] * gC, bD = viewingConditions.rgbD[2] * bC, rAF = Math.pow(viewingConditions.fl * Math.abs(rD) / 100, 0.42), gAF = Math.pow(viewingConditions.fl * Math.abs(gD) / 100, 0.42), bAF = Math.pow(viewingConditions.fl * Math.abs(bD) / 100, 0.42), rA = 400 * signum(rD) * rAF / (rAF + 27.13), gA = 400 * signum(gD) * gAF / (gAF + 27.13), bA = 400 * signum(bD) * bAF / (bAF + 27.13), a = (11 * rA + -12 * gA + bA) / 11, b = (rA + gA - 2 * bA) / 9, u = (20 * rA + 20 * gA + 21 * bA) / 20, p2 = (40 * rA + 20 * gA + bA) / 20, atanDegrees = 180 * Math.atan2(b, a) / Math.PI, hue = atanDegrees < 0 ? atanDegrees + 360 : atanDegrees >= 360 ? atanDegrees - 360 : atanDegrees, hueRadians = hue * Math.PI / 180, ac = p2 * viewingConditions.nbb, J = 100 * Math.pow(ac / viewingConditions.aw, viewingConditions.c * viewingConditions.z), Q = 4 / viewingConditions.c * Math.sqrt(J / 100) * (viewingConditions.aw + 4) * viewingConditions.fLRoot, huePrime = hue < 20.14 ? hue + 360 : hue, t = 5e4 / 13 * (1 / 4 * (Math.cos(huePrime * Math.PI / 180 + 2) + 3.8)) * viewingConditions.nc * viewingConditions.ncb * Math.sqrt(a * a + b * b) / (u + 0.305), alpha = Math.pow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), C = alpha * Math.sqrt(J / 100), M = C * viewingConditions.fLRoot, s = 50 * Math.sqrt(alpha * viewingConditions.c / (viewingConditions.aw + 4)), jstar = (1 + 100 * 7e-3) * J / (1 + 7e-3 * J), mstar = Math.log(1 + 0.0228 * M) / 0.0228, astar = mstar * Math.cos(hueRadians), bstar = mstar * Math.sin(hueRadians);
    return new _Cam16(hue, C, J, Q, M, s, jstar, astar, bstar);
  }
  xyzInViewingConditions(viewingConditions) {
    const alpha = 0 === this.chroma || 0 === this.j ? 0 : this.chroma / Math.sqrt(this.j / 100), t = Math.pow(alpha / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), 1 / 0.9), hRad = this.hue * Math.PI / 180, eHue = 0.25 * (Math.cos(hRad + 2) + 3.8), ac = viewingConditions.aw * Math.pow(this.j / 100, 1 / viewingConditions.c / viewingConditions.z), p1 = eHue * (5e4 / 13) * viewingConditions.nc * viewingConditions.ncb, p2 = ac / viewingConditions.nbb, hSin = Math.sin(hRad), hCos = Math.cos(hRad), gamma = 23 * (p2 + 0.305) * t / (23 * p1 + 11 * t * hCos + 108 * t * hSin), a = gamma * hCos, b = gamma * hSin, rA = (460 * p2 + 451 * a + 288 * b) / 1403, gA = (460 * p2 - 891 * a - 261 * b) / 1403, bA = (460 * p2 - 220 * a - 6300 * b) / 1403, rCBase = Math.max(0, 27.13 * Math.abs(rA) / (400 - Math.abs(rA))), rC = signum(rA) * (100 / viewingConditions.fl) * Math.pow(rCBase, 1 / 0.42), gCBase = Math.max(0, 27.13 * Math.abs(gA) / (400 - Math.abs(gA))), gC = signum(gA) * (100 / viewingConditions.fl) * Math.pow(gCBase, 1 / 0.42), bCBase = Math.max(0, 27.13 * Math.abs(bA) / (400 - Math.abs(bA))), bC = signum(bA) * (100 / viewingConditions.fl) * Math.pow(bCBase, 1 / 0.42), rF = rC / viewingConditions.rgbD[0], gF = gC / viewingConditions.rgbD[1], bF = bC / viewingConditions.rgbD[2];
    return [1.86206786 * rF - 1.01125463 * gF + 0.14918677 * bF, 0.38752654 * rF + 0.62144744 * gF - 897398e-8 * bF, -0.0158415 * rF - 0.03412294 * gF + 1.04996444 * bF];
  }
};
var HctSolver = class _HctSolver {
  static sanitizeRadians(angle) {
    return (angle + 8 * Math.PI) % (2 * Math.PI);
  }
  static trueDelinearized(rgbComponent) {
    const normalized = rgbComponent / 100;
    let delinearized2 = 0;
    return delinearized2 = normalized <= 31308e-7 ? 12.92 * normalized : 1.055 * Math.pow(normalized, 1 / 2.4) - 0.055, 255 * delinearized2;
  }
  static chromaticAdaptation(component) {
    const af = Math.pow(Math.abs(component), 0.42);
    return 400 * signum(component) * af / (af + 27.13);
  }
  static hueOf(linrgb) {
    const scaledDiscount = matrixMultiply(linrgb, _HctSolver.SCALED_DISCOUNT_FROM_LINRGB), rA = _HctSolver.chromaticAdaptation(scaledDiscount[0]), gA = _HctSolver.chromaticAdaptation(scaledDiscount[1]), bA = _HctSolver.chromaticAdaptation(scaledDiscount[2]), a = (11 * rA + -12 * gA + bA) / 11, b = (rA + gA - 2 * bA) / 9;
    return Math.atan2(b, a);
  }
  static areInCyclicOrder(a, b, c) {
    return _HctSolver.sanitizeRadians(b - a) < _HctSolver.sanitizeRadians(c - a);
  }
  static intercept(source, mid, target) {
    return (mid - source) / (target - source);
  }
  static lerpPoint(source, t, target) {
    return [source[0] + (target[0] - source[0]) * t, source[1] + (target[1] - source[1]) * t, source[2] + (target[2] - source[2]) * t];
  }
  static setCoordinate(source, coordinate, target, axis) {
    const t = _HctSolver.intercept(source[axis], coordinate, target[axis]);
    return _HctSolver.lerpPoint(source, t, target);
  }
  static isBounded(x) {
    return 0 <= x && x <= 100;
  }
  static nthVertex(y, n) {
    const kR = _HctSolver.Y_FROM_LINRGB[0], kG = _HctSolver.Y_FROM_LINRGB[1], kB = _HctSolver.Y_FROM_LINRGB[2], coordA = n % 4 <= 1 ? 0 : 100, coordB = n % 2 == 0 ? 0 : 100;
    if (n < 4) {
      const g = coordA, b = coordB, r = (y - g * kG - b * kB) / kR;
      return _HctSolver.isBounded(r) ? [r, g, b] : [-1, -1, -1];
    }
    if (n < 8) {
      const b = coordA, r = coordB, g = (y - r * kR - b * kB) / kG;
      return _HctSolver.isBounded(g) ? [r, g, b] : [-1, -1, -1];
    }
    {
      const r = coordA, g = coordB, b = (y - r * kR - g * kG) / kB;
      return _HctSolver.isBounded(b) ? [r, g, b] : [-1, -1, -1];
    }
  }
  static bisectToSegment(y, targetHue) {
    let left = [-1, -1, -1], right = left, leftHue = 0, rightHue = 0, initialized = false, uncut = true;
    for (let n = 0; n < 12; n++) {
      const mid = _HctSolver.nthVertex(y, n);
      if (mid[0] < 0) continue;
      const midHue = _HctSolver.hueOf(mid);
      initialized ? (uncut || _HctSolver.areInCyclicOrder(leftHue, midHue, rightHue)) && (uncut = false, _HctSolver.areInCyclicOrder(leftHue, targetHue, midHue) ? (right = mid, rightHue = midHue) : (left = mid, leftHue = midHue)) : (left = mid, right = mid, leftHue = midHue, rightHue = midHue, initialized = true);
    }
    return [left, right];
  }
  static midpoint(a, b) {
    return [(a[0] + b[0]) / 2, (a[1] + b[1]) / 2, (a[2] + b[2]) / 2];
  }
  static criticalPlaneBelow(x) {
    return Math.floor(x - 0.5);
  }
  static criticalPlaneAbove(x) {
    return Math.ceil(x - 0.5);
  }
  static bisectToLimit(y, targetHue) {
    const segment = _HctSolver.bisectToSegment(y, targetHue);
    let left = segment[0], leftHue = _HctSolver.hueOf(left), right = segment[1];
    for (let axis = 0; axis < 3; axis++) if (left[axis] !== right[axis]) {
      let lPlane = -1, rPlane = 255;
      left[axis] < right[axis] ? (lPlane = _HctSolver.criticalPlaneBelow(_HctSolver.trueDelinearized(left[axis])), rPlane = _HctSolver.criticalPlaneAbove(_HctSolver.trueDelinearized(right[axis]))) : (lPlane = _HctSolver.criticalPlaneAbove(_HctSolver.trueDelinearized(left[axis])), rPlane = _HctSolver.criticalPlaneBelow(_HctSolver.trueDelinearized(right[axis])));
      for (let i = 0; i < 8 && !(Math.abs(rPlane - lPlane) <= 1); i++) {
        const mPlane = Math.floor((lPlane + rPlane) / 2), midPlaneCoordinate = _HctSolver.CRITICAL_PLANES[mPlane], mid = _HctSolver.setCoordinate(left, midPlaneCoordinate, right, axis), midHue = _HctSolver.hueOf(mid);
        _HctSolver.areInCyclicOrder(leftHue, targetHue, midHue) ? (right = mid, rPlane = mPlane) : (left = mid, leftHue = midHue, lPlane = mPlane);
      }
    }
    return _HctSolver.midpoint(left, right);
  }
  static inverseChromaticAdaptation(adapted) {
    const adaptedAbs = Math.abs(adapted), base = Math.max(0, 27.13 * adaptedAbs / (400 - adaptedAbs));
    return signum(adapted) * Math.pow(base, 1 / 0.42);
  }
  static findResultByJ(hueRadians, chroma, y) {
    let j = 11 * Math.sqrt(y);
    const viewingConditions = ViewingConditions.DEFAULT, tInnerCoeff = 1 / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), p1 = 0.25 * (Math.cos(hueRadians + 2) + 3.8) * (5e4 / 13) * viewingConditions.nc * viewingConditions.ncb, hSin = Math.sin(hueRadians), hCos = Math.cos(hueRadians);
    for (let iterationRound = 0; iterationRound < 5; iterationRound++) {
      const jNormalized = j / 100, alpha = 0 === chroma || 0 === j ? 0 : chroma / Math.sqrt(jNormalized), t = Math.pow(alpha * tInnerCoeff, 1 / 0.9), p2 = viewingConditions.aw * Math.pow(jNormalized, 1 / viewingConditions.c / viewingConditions.z) / viewingConditions.nbb, gamma = 23 * (p2 + 0.305) * t / (23 * p1 + 11 * t * hCos + 108 * t * hSin), a = gamma * hCos, b = gamma * hSin, rA = (460 * p2 + 451 * a + 288 * b) / 1403, gA = (460 * p2 - 891 * a - 261 * b) / 1403, bA = (460 * p2 - 220 * a - 6300 * b) / 1403, linrgb = matrixMultiply([_HctSolver.inverseChromaticAdaptation(rA), _HctSolver.inverseChromaticAdaptation(gA), _HctSolver.inverseChromaticAdaptation(bA)], _HctSolver.LINRGB_FROM_SCALED_DISCOUNT);
      if (linrgb[0] < 0 || linrgb[1] < 0 || linrgb[2] < 0) return 0;
      const kR = _HctSolver.Y_FROM_LINRGB[0], kG = _HctSolver.Y_FROM_LINRGB[1], kB = _HctSolver.Y_FROM_LINRGB[2], fnj = kR * linrgb[0] + kG * linrgb[1] + kB * linrgb[2];
      if (fnj <= 0) return 0;
      if (4 === iterationRound || Math.abs(fnj - y) < 2e-3) return linrgb[0] > 100.01 || linrgb[1] > 100.01 || linrgb[2] > 100.01 ? 0 : argbFromLinrgb(linrgb);
      j -= (fnj - y) * j / (2 * fnj);
    }
    return 0;
  }
  static solveToInt(hueDegrees, chroma, lstar) {
    if (chroma < 1e-4 || lstar < 1e-4 || lstar > 99.9999) return argbFromLstar(lstar);
    const hueRadians = (hueDegrees = sanitizeDegreesDouble(hueDegrees)) / 180 * Math.PI, y = yFromLstar(lstar), exactAnswer = _HctSolver.findResultByJ(hueRadians, chroma, y);
    if (0 !== exactAnswer) return exactAnswer;
    return argbFromLinrgb(_HctSolver.bisectToLimit(y, hueRadians));
  }
  static solveToCam(hueDegrees, chroma, lstar) {
    return Cam16.fromInt(_HctSolver.solveToInt(hueDegrees, chroma, lstar));
  }
};
HctSolver.SCALED_DISCOUNT_FROM_LINRGB = [[0.001200833568784504, 0.002389694492170889, 2795742885861124e-19], [5891086651375999e-19, 0.0029785502573438758, 3270666104008398e-19], [10146692491640572e-20, 5364214359186694e-19, 0.0032979401770712076]], HctSolver.LINRGB_FROM_SCALED_DISCOUNT = [[1373.2198709594231, -1100.4251190754821, -7.278681089101213], [-271.815969077903, 559.6580465940733, -32.46047482791194], [1.9622899599665666, -57.173814538844006, 308.7233197812385]], HctSolver.Y_FROM_LINRGB = [0.2126, 0.7152, 0.0722], HctSolver.CRITICAL_PLANES = [0.015176349177441876, 0.045529047532325624, 0.07588174588720938, 0.10623444424209313, 0.13658714259697685, 0.16693984095186062, 0.19729253930674434, 0.2276452376616281, 0.2579979360165119, 0.28835063437139563, 0.3188300904430532, 0.350925934958123, 0.3848314933096426, 0.42057480301049466, 0.458183274052838, 0.4976837250274023, 0.5391024159806381, 0.5824650784040898, 0.6277969426914107, 0.6751227633498623, 0.7244668422128921, 0.775853049866786, 0.829304845476233, 0.8848452951698498, 0.942497089126609, 1.0022825574869039, 1.0642236851973577, 1.1283421258858297, 1.1946592148522128, 1.2631959812511864, 1.3339731595349034, 1.407011200216447, 1.4823302800086415, 1.5599503113873272, 1.6398909516233677, 1.7221716113234105, 1.8068114625156377, 1.8938294463134073, 1.9832442801866852, 2.075074464868551, 2.1693382909216234, 2.2660538449872063, 2.36523901573795, 2.4669114995532007, 2.5710888059345764, 2.6777882626779785, 2.7870270208169257, 2.898822059350997, 3.0131901897720907, 3.1301480604002863, 3.2497121605402226, 3.3718988244681087, 3.4967242352587946, 3.624204428461639, 3.754355295633311, 3.887192587735158, 4.022731918402185, 4.160988767090289, 4.301978482107941, 4.445716283538092, 4.592217266055746, 4.741496401646282, 4.893568542229298, 5.048448422192488, 5.20615066083972, 5.3666897647573375, 5.5300801301023865, 5.696336044816294, 5.865471690767354, 6.037501145825082, 6.212438385869475, 6.390297286737924, 6.571091626112461, 6.7548350853498045, 6.941541251256611, 7.131223617812143, 7.323895587840543, 7.5195704746346665, 7.7182615035334345, 7.919981813454504, 8.124744458384042, 8.332562408825165, 8.543448553206703, 8.757415699253682, 8.974476575321063, 9.194643831691977, 9.417930041841839, 9.644347703669503, 9.873909240696694, 10.106627003236781, 10.342513269534024, 10.58158024687427, 10.8238400726681, 11.069304815507364, 11.317986476196008, 11.569896988756009, 11.825048221409341, 12.083451977536606, 12.345119996613247, 12.610063955123938, 12.878295467455942, 13.149826086772048, 13.42466730586372, 13.702830557985108, 13.984327217668513, 14.269168601521828, 14.55736596900856, 14.848930523210871, 15.143873411576273, 15.44220572664832, 15.743938506781891, 16.04908273684337, 16.35764934889634, 16.66964922287304, 16.985093187232053, 17.30399201960269, 17.62635644741625, 17.95219714852476, 18.281524751807332, 18.614349837764564, 18.95068293910138, 19.290534541298456, 19.633915083172692, 19.98083495742689, 20.331304511189067, 20.685334046541502, 21.042933821039977, 21.404114048223256, 21.76888489811322, 22.137256497705877, 22.50923893145328, 22.884842241736916, 23.264076429332462, 23.6469514538663, 24.033477234264016, 24.42366364919083, 24.817520537484558, 25.21505769858089, 25.61628489293138, 26.021211842414342, 26.429848230738664, 26.842203703840827, 27.258287870275353, 27.678110301598522, 28.10168053274597, 28.529008062403893, 28.96010235337422, 29.39497283293396, 29.83362889318845, 30.276079891419332, 30.722335150426627, 31.172403958865512, 31.62629557157785, 32.08401920991837, 32.54558406207592, 33.010999283389665, 33.4802739966603, 33.953417292456834, 34.430438229418264, 34.911345834551085, 35.39614910352207, 35.88485700094671, 36.37747846067349, 36.87402238606382, 37.37449765026789, 37.87891309649659, 38.38727753828926, 38.89959975977785, 39.41588851594697, 39.93615253289054, 40.460400508064545, 40.98864111053629, 41.520882981230194, 42.05713473317016, 42.597404951718396, 43.141702194811224, 43.6900349931913, 44.24241185063697, 44.798841244188324, 45.35933162437017, 45.92389141541209, 46.49252901546552, 47.065252796817916, 47.64207110610409, 48.22299226451468, 48.808024568002054, 49.3971762874833, 49.9904556690408, 50.587870934119984, 51.189430279724725, 51.79514187861014, 52.40501387947288, 53.0190544071392, 53.637271562750364, 54.259673423945976, 54.88626804504493, 55.517063457223934, 56.15206766869424, 56.79128866487574, 57.43473440856916, 58.08241284012621, 58.734331877617365, 59.39049941699807, 60.05092333227251, 60.715611475655585, 61.38457167773311, 62.057811747619894, 62.7353394731159, 63.417162620860914, 64.10328893648692, 64.79372614476921, 65.48848194977529, 66.18756403501224, 66.89098006357258, 67.59873767827808, 68.31084450182222, 69.02730813691093, 69.74813616640164, 70.47333615344107, 71.20291564160104, 71.93688215501312, 72.67524319850172, 73.41800625771542, 74.16517879925733, 74.9167682708136, 75.67278210128072, 76.43322770089146, 77.1981124613393, 77.96744375590167, 78.74122893956174, 79.51947534912904, 80.30219030335869, 81.08938110306934, 81.88105503125999, 82.67721935322541, 83.4778813166706, 84.28304815182372, 85.09272707154808, 85.90692527145302, 86.72564993000343, 87.54890820862819, 88.3767072518277, 89.2090541872801, 90.04595612594655, 90.88742016217518, 91.73345337380438, 92.58406282226491, 93.43925555268066, 94.29903859396902, 95.16341895893969, 96.03240364439274, 96.9059996312159, 97.78421388448044, 98.6670533535366, 99.55452497210776];
var Hct = class _Hct {
  static from(hue, chroma, tone) {
    return new _Hct(HctSolver.solveToInt(hue, chroma, tone));
  }
  static fromInt(argb) {
    return new _Hct(argb);
  }
  toInt() {
    return this.argb;
  }
  get hue() {
    return this.internalHue;
  }
  set hue(newHue) {
    this.setInternalState(HctSolver.solveToInt(newHue, this.internalChroma, this.internalTone));
  }
  get chroma() {
    return this.internalChroma;
  }
  set chroma(newChroma) {
    this.setInternalState(HctSolver.solveToInt(this.internalHue, newChroma, this.internalTone));
  }
  get tone() {
    return this.internalTone;
  }
  set tone(newTone) {
    this.setInternalState(HctSolver.solveToInt(this.internalHue, this.internalChroma, newTone));
  }
  constructor(argb) {
    this.argb = argb;
    const cam = Cam16.fromInt(argb);
    this.internalHue = cam.hue, this.internalChroma = cam.chroma, this.internalTone = lstarFromArgb(argb), this.argb = argb;
  }
  setInternalState(argb) {
    const cam = Cam16.fromInt(argb);
    this.internalHue = cam.hue, this.internalChroma = cam.chroma, this.internalTone = lstarFromArgb(argb), this.argb = argb;
  }
  inViewingConditions(vc) {
    const viewedInVc = Cam16.fromInt(this.toInt()).xyzInViewingConditions(vc), recastInVc = Cam16.fromXyzInViewingConditions(viewedInVc[0], viewedInVc[1], viewedInVc[2], ViewingConditions.make());
    return _Hct.from(recastInVc.hue, recastInVc.chroma, lstarFromY(viewedInVc[1]));
  }
};
var Blend = class _Blend {
  static harmonize(designColor, sourceColor) {
    const fromHct = Hct.fromInt(designColor), toHct = Hct.fromInt(sourceColor), differenceDegrees$1 = differenceDegrees(fromHct.hue, toHct.hue), rotationDegrees = Math.min(0.5 * differenceDegrees$1, 15), outputHue = sanitizeDegreesDouble(fromHct.hue + rotationDegrees * rotationDirection(fromHct.hue, toHct.hue));
    return Hct.from(outputHue, fromHct.chroma, fromHct.tone).toInt();
  }
  static hctHue(from, to, amount) {
    const ucs = _Blend.cam16Ucs(from, to, amount), ucsCam = Cam16.fromInt(ucs), fromCam = Cam16.fromInt(from);
    return Hct.from(ucsCam.hue, fromCam.chroma, lstarFromArgb(from)).toInt();
  }
  static cam16Ucs(from, to, amount) {
    const fromCam = Cam16.fromInt(from), toCam = Cam16.fromInt(to), fromJ = fromCam.jstar, fromA = fromCam.astar, fromB = fromCam.bstar, jstar = fromJ + (toCam.jstar - fromJ) * amount, astar = fromA + (toCam.astar - fromA) * amount, bstar = fromB + (toCam.bstar - fromB) * amount;
    return Cam16.fromUcs(jstar, astar, bstar).toInt();
  }
};
var Contrast = class _Contrast {
  static ratioOfTones(toneA, toneB) {
    return toneA = clampDouble(0, 100, toneA), toneB = clampDouble(0, 100, toneB), _Contrast.ratioOfYs(yFromLstar(toneA), yFromLstar(toneB));
  }
  static ratioOfYs(y1, y2) {
    const lighter = y1 > y2 ? y1 : y2;
    return (lighter + 5) / ((lighter === y2 ? y1 : y2) + 5);
  }
  static lighter(tone, ratio) {
    if (tone < 0 || tone > 100) return -1;
    const darkY = yFromLstar(tone), lightY = ratio * (darkY + 5) - 5, realContrast = _Contrast.ratioOfYs(lightY, darkY), delta = Math.abs(realContrast - ratio);
    if (realContrast < ratio && delta > 0.04) return -1;
    const returnValue = lstarFromY(lightY) + 0.4;
    return returnValue < 0 || returnValue > 100 ? -1 : returnValue;
  }
  static darker(tone, ratio) {
    if (tone < 0 || tone > 100) return -1;
    const lightY = yFromLstar(tone), darkY = (lightY + 5) / ratio - 5, realContrast = _Contrast.ratioOfYs(lightY, darkY), delta = Math.abs(realContrast - ratio);
    if (realContrast < ratio && delta > 0.04) return -1;
    const returnValue = lstarFromY(darkY) - 0.4;
    return returnValue < 0 || returnValue > 100 ? -1 : returnValue;
  }
  static lighterUnsafe(tone, ratio) {
    const lighterSafe = _Contrast.lighter(tone, ratio);
    return lighterSafe < 0 ? 100 : lighterSafe;
  }
  static darkerUnsafe(tone, ratio) {
    const darkerSafe = _Contrast.darker(tone, ratio);
    return darkerSafe < 0 ? 0 : darkerSafe;
  }
};
var DislikeAnalyzer = class _DislikeAnalyzer {
  static isDisliked(hct) {
    const huePasses = Math.round(hct.hue) >= 90 && Math.round(hct.hue) <= 111, chromaPasses = Math.round(hct.chroma) > 16, tonePasses = Math.round(hct.tone) < 65;
    return huePasses && chromaPasses && tonePasses;
  }
  static fixIfDisliked(hct) {
    return _DislikeAnalyzer.isDisliked(hct) ? Hct.from(hct.hue, hct.chroma, 70) : hct;
  }
};
var DynamicColor = class _DynamicColor {
  static fromPalette(args) {
    return new _DynamicColor(args.name ?? "", args.palette, args.tone, args.isBackground ?? false, args.background, args.secondBackground, args.contrastCurve, args.toneDeltaPair);
  }
  constructor(name, palette, tone, isBackground, background, secondBackground, contrastCurve, toneDeltaPair) {
    if (this.name = name, this.palette = palette, this.tone = tone, this.isBackground = isBackground, this.background = background, this.secondBackground = secondBackground, this.contrastCurve = contrastCurve, this.toneDeltaPair = toneDeltaPair, this.hctCache = /* @__PURE__ */ new Map(), !background && secondBackground) throw new Error(`Color ${name} has secondBackgrounddefined, but background is not defined.`);
    if (!background && contrastCurve) throw new Error(`Color ${name} has contrastCurvedefined, but background is not defined.`);
    if (background && !contrastCurve) throw new Error(`Color ${name} has backgrounddefined, but contrastCurve is not defined.`);
  }
  getArgb(scheme) {
    return this.getHct(scheme).toInt();
  }
  getHct(scheme) {
    const cachedAnswer = this.hctCache.get(scheme);
    if (null != cachedAnswer) return cachedAnswer;
    const tone = this.getTone(scheme), answer = this.palette(scheme).getHct(tone);
    return this.hctCache.size > 4 && this.hctCache.clear(), this.hctCache.set(scheme, answer), answer;
  }
  getTone(scheme) {
    const decreasingContrast = scheme.contrastLevel < 0;
    if (this.toneDeltaPair) {
      const toneDeltaPair = this.toneDeltaPair(scheme), roleA = toneDeltaPair.roleA, roleB = toneDeltaPair.roleB, delta = toneDeltaPair.delta, polarity = toneDeltaPair.polarity, stayTogether = toneDeltaPair.stayTogether, bgTone = this.background(scheme).getTone(scheme), aIsNearer = "nearer" === polarity || "lighter" === polarity && !scheme.isDark || "darker" === polarity && scheme.isDark, nearer = aIsNearer ? roleA : roleB, farther = aIsNearer ? roleB : roleA, amNearer = this.name === nearer.name, expansionDir = scheme.isDark ? 1 : -1, nContrast = nearer.contrastCurve.getContrast(scheme.contrastLevel), fContrast = farther.contrastCurve.getContrast(scheme.contrastLevel), nInitialTone = nearer.tone(scheme);
      let nTone = Contrast.ratioOfTones(bgTone, nInitialTone) >= nContrast ? nInitialTone : _DynamicColor.foregroundTone(bgTone, nContrast);
      const fInitialTone = farther.tone(scheme);
      let fTone = Contrast.ratioOfTones(bgTone, fInitialTone) >= fContrast ? fInitialTone : _DynamicColor.foregroundTone(bgTone, fContrast);
      return decreasingContrast && (nTone = _DynamicColor.foregroundTone(bgTone, nContrast), fTone = _DynamicColor.foregroundTone(bgTone, fContrast)), (fTone - nTone) * expansionDir >= delta || (fTone = clampDouble(0, 100, nTone + delta * expansionDir), (fTone - nTone) * expansionDir >= delta || (nTone = clampDouble(0, 100, fTone - delta * expansionDir))), 50 <= nTone && nTone < 60 ? expansionDir > 0 ? (nTone = 60, fTone = Math.max(fTone, nTone + delta * expansionDir)) : (nTone = 49, fTone = Math.min(fTone, nTone + delta * expansionDir)) : 50 <= fTone && fTone < 60 && (stayTogether ? expansionDir > 0 ? (nTone = 60, fTone = Math.max(fTone, nTone + delta * expansionDir)) : (nTone = 49, fTone = Math.min(fTone, nTone + delta * expansionDir)) : fTone = expansionDir > 0 ? 60 : 49), amNearer ? nTone : fTone;
    }
    {
      let answer = this.tone(scheme);
      if (null == this.background) return answer;
      const bgTone = this.background(scheme).getTone(scheme), desiredRatio = this.contrastCurve.getContrast(scheme.contrastLevel);
      if (Contrast.ratioOfTones(bgTone, answer) >= desiredRatio || (answer = _DynamicColor.foregroundTone(bgTone, desiredRatio)), decreasingContrast && (answer = _DynamicColor.foregroundTone(bgTone, desiredRatio)), this.isBackground && 50 <= answer && answer < 60 && (answer = Contrast.ratioOfTones(49, bgTone) >= desiredRatio ? 49 : 60), this.secondBackground) {
        const [bg1, bg2] = [this.background, this.secondBackground], [bgTone1, bgTone2] = [bg1(scheme).getTone(scheme), bg2(scheme).getTone(scheme)], [upper, lower] = [Math.max(bgTone1, bgTone2), Math.min(bgTone1, bgTone2)];
        if (Contrast.ratioOfTones(upper, answer) >= desiredRatio && Contrast.ratioOfTones(lower, answer) >= desiredRatio) return answer;
        const lightOption = Contrast.lighter(upper, desiredRatio), darkOption = Contrast.darker(lower, desiredRatio), availables = [];
        -1 !== lightOption && availables.push(lightOption), -1 !== darkOption && availables.push(darkOption);
        return _DynamicColor.tonePrefersLightForeground(bgTone1) || _DynamicColor.tonePrefersLightForeground(bgTone2) ? lightOption < 0 ? 100 : lightOption : 1 === availables.length ? availables[0] : darkOption < 0 ? 0 : darkOption;
      }
      return answer;
    }
  }
  static foregroundTone(bgTone, ratio) {
    const lighterTone = Contrast.lighterUnsafe(bgTone, ratio), darkerTone = Contrast.darkerUnsafe(bgTone, ratio), lighterRatio = Contrast.ratioOfTones(lighterTone, bgTone), darkerRatio = Contrast.ratioOfTones(darkerTone, bgTone);
    if (_DynamicColor.tonePrefersLightForeground(bgTone)) {
      const negligibleDifference = Math.abs(lighterRatio - darkerRatio) < 0.1 && lighterRatio < ratio && darkerRatio < ratio;
      return lighterRatio >= ratio || lighterRatio >= darkerRatio || negligibleDifference ? lighterTone : darkerTone;
    }
    return darkerRatio >= ratio || darkerRatio >= lighterRatio ? darkerTone : lighterTone;
  }
  static tonePrefersLightForeground(tone) {
    return Math.round(tone) < 60;
  }
  static toneAllowsLightForeground(tone) {
    return Math.round(tone) <= 49;
  }
  static enableLightForeground(tone) {
    return _DynamicColor.tonePrefersLightForeground(tone) && !_DynamicColor.toneAllowsLightForeground(tone) ? 49 : tone;
  }
};
var Variant;
!function(Variant2) {
  Variant2[Variant2.MONOCHROME = 0] = "MONOCHROME", Variant2[Variant2.NEUTRAL = 1] = "NEUTRAL", Variant2[Variant2.TONAL_SPOT = 2] = "TONAL_SPOT", Variant2[Variant2.VIBRANT = 3] = "VIBRANT", Variant2[Variant2.EXPRESSIVE = 4] = "EXPRESSIVE", Variant2[Variant2.FIDELITY = 5] = "FIDELITY", Variant2[Variant2.CONTENT = 6] = "CONTENT", Variant2[Variant2.RAINBOW = 7] = "RAINBOW", Variant2[Variant2.FRUIT_SALAD = 8] = "FRUIT_SALAD";
}(Variant || (Variant = {}));
var ContrastCurve = class {
  constructor(low, normal, medium, high) {
    this.low = low, this.normal = normal, this.medium = medium, this.high = high;
  }
  getContrast(contrastLevel) {
    return contrastLevel <= -1 ? this.low : contrastLevel < 0 ? lerp(this.low, this.normal, (contrastLevel - -1) / 1) : contrastLevel < 0.5 ? lerp(this.normal, this.medium, (contrastLevel - 0) / 0.5) : contrastLevel < 1 ? lerp(this.medium, this.high, (contrastLevel - 0.5) / 0.5) : this.high;
  }
};
var ToneDeltaPair = class {
  constructor(roleA, roleB, delta, polarity, stayTogether) {
    this.roleA = roleA, this.roleB = roleB, this.delta = delta, this.polarity = polarity, this.stayTogether = stayTogether;
  }
};
function isFidelity(scheme) {
  return scheme.variant === Variant.FIDELITY || scheme.variant === Variant.CONTENT;
}
function isMonochrome(scheme) {
  return scheme.variant === Variant.MONOCHROME;
}
function findDesiredChromaByTone(hue, chroma, tone, byDecreasingTone) {
  let answer = tone, closestToChroma = Hct.from(hue, chroma, tone);
  if (closestToChroma.chroma < chroma) {
    let chromaPeak = closestToChroma.chroma;
    for (; closestToChroma.chroma < chroma; ) {
      answer += byDecreasingTone ? -1 : 1;
      const potentialSolution = Hct.from(hue, chroma, answer);
      if (chromaPeak > potentialSolution.chroma) break;
      if (Math.abs(potentialSolution.chroma - chroma) < 0.4) break;
      Math.abs(potentialSolution.chroma - chroma) < Math.abs(closestToChroma.chroma - chroma) && (closestToChroma = potentialSolution), chromaPeak = Math.max(chromaPeak, potentialSolution.chroma);
    }
  }
  return answer;
}
function viewingConditionsForAlbers(scheme) {
  return ViewingConditions.make(void 0, void 0, scheme.isDark ? 30 : 80, void 0, void 0);
}
function performAlbers(prealbers, scheme) {
  const albersd = prealbers.inViewingConditions(viewingConditionsForAlbers(scheme));
  return DynamicColor.tonePrefersLightForeground(prealbers.tone) && !DynamicColor.toneAllowsLightForeground(albersd.tone) ? DynamicColor.enableLightForeground(prealbers.tone) : DynamicColor.enableLightForeground(albersd.tone);
}
var MaterialDynamicColors = class _MaterialDynamicColors {
  static highestSurface(s) {
    return s.isDark ? _MaterialDynamicColors.surfaceBright : _MaterialDynamicColors.surfaceDim;
  }
};
MaterialDynamicColors.contentAccentToneDelta = 15, MaterialDynamicColors.primaryPaletteKeyColor = DynamicColor.fromPalette({
  name: "primary_palette_key_color",
  palette: (s) => s.primaryPalette,
  tone: (s) => s.primaryPalette.keyColor.tone
}), MaterialDynamicColors.secondaryPaletteKeyColor = DynamicColor.fromPalette({
  name: "secondary_palette_key_color",
  palette: (s) => s.secondaryPalette,
  tone: (s) => s.secondaryPalette.keyColor.tone
}), MaterialDynamicColors.tertiaryPaletteKeyColor = DynamicColor.fromPalette({
  name: "tertiary_palette_key_color",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => s.tertiaryPalette.keyColor.tone
}), MaterialDynamicColors.neutralPaletteKeyColor = DynamicColor.fromPalette({
  name: "neutral_palette_key_color",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.neutralPalette.keyColor.tone
}), MaterialDynamicColors.neutralVariantPaletteKeyColor = DynamicColor.fromPalette({
  name: "neutral_variant_palette_key_color",
  palette: (s) => s.neutralVariantPalette,
  tone: (s) => s.neutralVariantPalette.keyColor.tone
}), MaterialDynamicColors.background = DynamicColor.fromPalette({
  name: "background",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 6 : 98,
  isBackground: true
}), MaterialDynamicColors.onBackground = DynamicColor.fromPalette({
  name: "on_background",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.background,
  contrastCurve: new ContrastCurve(3, 3, 4.5, 7)
}), MaterialDynamicColors.surface = DynamicColor.fromPalette({
  name: "surface",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 6 : 98,
  isBackground: true
}), MaterialDynamicColors.surfaceDim = DynamicColor.fromPalette({
  name: "surface_dim",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 6 : 87,
  isBackground: true
}), MaterialDynamicColors.surfaceBright = DynamicColor.fromPalette({
  name: "surface_bright",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 24 : 98,
  isBackground: true
}), MaterialDynamicColors.surfaceContainerLowest = DynamicColor.fromPalette({
  name: "surface_container_lowest",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 4 : 100,
  isBackground: true
}), MaterialDynamicColors.surfaceContainerLow = DynamicColor.fromPalette({
  name: "surface_container_low",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 10 : 96,
  isBackground: true
}), MaterialDynamicColors.surfaceContainer = DynamicColor.fromPalette({
  name: "surface_container",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 12 : 94,
  isBackground: true
}), MaterialDynamicColors.surfaceContainerHigh = DynamicColor.fromPalette({
  name: "surface_container_high",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 17 : 92,
  isBackground: true
}), MaterialDynamicColors.surfaceContainerHighest = DynamicColor.fromPalette({
  name: "surface_container_highest",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 22 : 90,
  isBackground: true
}), MaterialDynamicColors.onSurface = DynamicColor.fromPalette({
  name: "on_surface",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.surfaceVariant = DynamicColor.fromPalette({
  name: "surface_variant",
  palette: (s) => s.neutralVariantPalette,
  tone: (s) => s.isDark ? 30 : 90,
  isBackground: true
}), MaterialDynamicColors.onSurfaceVariant = DynamicColor.fromPalette({
  name: "on_surface_variant",
  palette: (s) => s.neutralVariantPalette,
  tone: (s) => s.isDark ? 80 : 30,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11)
}), MaterialDynamicColors.inverseSurface = DynamicColor.fromPalette({
  name: "inverse_surface",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 90 : 20
}), MaterialDynamicColors.inverseOnSurface = DynamicColor.fromPalette({
  name: "inverse_on_surface",
  palette: (s) => s.neutralPalette,
  tone: (s) => s.isDark ? 20 : 95,
  background: (s) => MaterialDynamicColors.inverseSurface,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.outline = DynamicColor.fromPalette({
  name: "outline",
  palette: (s) => s.neutralVariantPalette,
  tone: (s) => s.isDark ? 60 : 50,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1.5, 3, 4.5, 7)
}), MaterialDynamicColors.outlineVariant = DynamicColor.fromPalette({
  name: "outline_variant",
  palette: (s) => s.neutralVariantPalette,
  tone: (s) => s.isDark ? 30 : 80,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7)
}), MaterialDynamicColors.shadow = DynamicColor.fromPalette({
  name: "shadow",
  palette: (s) => s.neutralPalette,
  tone: (s) => 0
}), MaterialDynamicColors.scrim = DynamicColor.fromPalette({
  name: "scrim",
  palette: (s) => s.neutralPalette,
  tone: (s) => 0
}), MaterialDynamicColors.surfaceTint = DynamicColor.fromPalette({
  name: "surface_tint",
  palette: (s) => s.primaryPalette,
  tone: (s) => s.isDark ? 80 : 40,
  isBackground: true
}), MaterialDynamicColors.primary = DynamicColor.fromPalette({
  name: "primary",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 100 : 0 : s.isDark ? 80 : 40,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.primaryContainer, MaterialDynamicColors.primary, 15, "nearer", false)
}), MaterialDynamicColors.onPrimary = DynamicColor.fromPalette({
  name: "on_primary",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 10 : 90 : s.isDark ? 20 : 100,
  background: (s) => MaterialDynamicColors.primary,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.primaryContainer = DynamicColor.fromPalette({
  name: "primary_container",
  palette: (s) => s.primaryPalette,
  tone: (s) => isFidelity(s) ? performAlbers(s.sourceColorHct, s) : isMonochrome(s) ? s.isDark ? 85 : 25 : s.isDark ? 30 : 90,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.primaryContainer, MaterialDynamicColors.primary, 15, "nearer", false)
}), MaterialDynamicColors.onPrimaryContainer = DynamicColor.fromPalette({
  name: "on_primary_container",
  palette: (s) => s.primaryPalette,
  tone: (s) => isFidelity(s) ? DynamicColor.foregroundTone(MaterialDynamicColors.primaryContainer.tone(s), 4.5) : isMonochrome(s) ? s.isDark ? 0 : 100 : s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.primaryContainer,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.inversePrimary = DynamicColor.fromPalette({
  name: "inverse_primary",
  palette: (s) => s.primaryPalette,
  tone: (s) => s.isDark ? 40 : 80,
  background: (s) => MaterialDynamicColors.inverseSurface,
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11)
}), MaterialDynamicColors.secondary = DynamicColor.fromPalette({
  name: "secondary",
  palette: (s) => s.secondaryPalette,
  tone: (s) => s.isDark ? 80 : 40,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.secondaryContainer, MaterialDynamicColors.secondary, 15, "nearer", false)
}), MaterialDynamicColors.onSecondary = DynamicColor.fromPalette({
  name: "on_secondary",
  palette: (s) => s.secondaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 10 : 100 : s.isDark ? 20 : 100,
  background: (s) => MaterialDynamicColors.secondary,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.secondaryContainer = DynamicColor.fromPalette({
  name: "secondary_container",
  palette: (s) => s.secondaryPalette,
  tone: (s) => {
    const initialTone = s.isDark ? 30 : 90;
    if (isMonochrome(s)) return s.isDark ? 30 : 85;
    if (!isFidelity(s)) return initialTone;
    let answer = findDesiredChromaByTone(s.secondaryPalette.hue, s.secondaryPalette.chroma, initialTone, !s.isDark);
    return answer = performAlbers(s.secondaryPalette.getHct(answer), s), answer;
  },
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.secondaryContainer, MaterialDynamicColors.secondary, 15, "nearer", false)
}), MaterialDynamicColors.onSecondaryContainer = DynamicColor.fromPalette({
  name: "on_secondary_container",
  palette: (s) => s.secondaryPalette,
  tone: (s) => isFidelity(s) ? DynamicColor.foregroundTone(MaterialDynamicColors.secondaryContainer.tone(s), 4.5) : s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.secondaryContainer,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.tertiary = DynamicColor.fromPalette({
  name: "tertiary",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 90 : 25 : s.isDark ? 80 : 40,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.tertiaryContainer, MaterialDynamicColors.tertiary, 15, "nearer", false)
}), MaterialDynamicColors.onTertiary = DynamicColor.fromPalette({
  name: "on_tertiary",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 10 : 90 : s.isDark ? 20 : 100,
  background: (s) => MaterialDynamicColors.tertiary,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.tertiaryContainer = DynamicColor.fromPalette({
  name: "tertiary_container",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => {
    if (isMonochrome(s)) return s.isDark ? 60 : 49;
    if (!isFidelity(s)) return s.isDark ? 30 : 90;
    const albersTone = performAlbers(s.tertiaryPalette.getHct(s.sourceColorHct.tone), s), proposedHct = s.tertiaryPalette.getHct(albersTone);
    return DislikeAnalyzer.fixIfDisliked(proposedHct).tone;
  },
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.tertiaryContainer, MaterialDynamicColors.tertiary, 15, "nearer", false)
}), MaterialDynamicColors.onTertiaryContainer = DynamicColor.fromPalette({
  name: "on_tertiary_container",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? s.isDark ? 0 : 100 : isFidelity(s) ? DynamicColor.foregroundTone(MaterialDynamicColors.tertiaryContainer.tone(s), 4.5) : s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.tertiaryContainer,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.error = DynamicColor.fromPalette({
  name: "error",
  palette: (s) => s.errorPalette,
  tone: (s) => s.isDark ? 80 : 40,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.errorContainer, MaterialDynamicColors.error, 15, "nearer", false)
}), MaterialDynamicColors.onError = DynamicColor.fromPalette({
  name: "on_error",
  palette: (s) => s.errorPalette,
  tone: (s) => s.isDark ? 20 : 100,
  background: (s) => MaterialDynamicColors.error,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.errorContainer = DynamicColor.fromPalette({
  name: "error_container",
  palette: (s) => s.errorPalette,
  tone: (s) => s.isDark ? 30 : 90,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.errorContainer, MaterialDynamicColors.error, 15, "nearer", false)
}), MaterialDynamicColors.onErrorContainer = DynamicColor.fromPalette({
  name: "on_error_container",
  palette: (s) => s.errorPalette,
  tone: (s) => s.isDark ? 90 : 10,
  background: (s) => MaterialDynamicColors.errorContainer,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.primaryFixed = DynamicColor.fromPalette({
  name: "primary_fixed",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? 40 : 90,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.primaryFixed, MaterialDynamicColors.primaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.primaryFixedDim = DynamicColor.fromPalette({
  name: "primary_fixed_dim",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? 30 : 80,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.primaryFixed, MaterialDynamicColors.primaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.onPrimaryFixed = DynamicColor.fromPalette({
  name: "on_primary_fixed",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? 100 : 10,
  background: (s) => MaterialDynamicColors.primaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.primaryFixed,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.onPrimaryFixedVariant = DynamicColor.fromPalette({
  name: "on_primary_fixed_variant",
  palette: (s) => s.primaryPalette,
  tone: (s) => isMonochrome(s) ? 90 : 30,
  background: (s) => MaterialDynamicColors.primaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.primaryFixed,
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11)
}), MaterialDynamicColors.secondaryFixed = DynamicColor.fromPalette({
  name: "secondary_fixed",
  palette: (s) => s.secondaryPalette,
  tone: (s) => isMonochrome(s) ? 80 : 90,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.secondaryFixed, MaterialDynamicColors.secondaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.secondaryFixedDim = DynamicColor.fromPalette({
  name: "secondary_fixed_dim",
  palette: (s) => s.secondaryPalette,
  tone: (s) => isMonochrome(s) ? 70 : 80,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.secondaryFixed, MaterialDynamicColors.secondaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.onSecondaryFixed = DynamicColor.fromPalette({
  name: "on_secondary_fixed",
  palette: (s) => s.secondaryPalette,
  tone: (s) => 10,
  background: (s) => MaterialDynamicColors.secondaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.secondaryFixed,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.onSecondaryFixedVariant = DynamicColor.fromPalette({
  name: "on_secondary_fixed_variant",
  palette: (s) => s.secondaryPalette,
  tone: (s) => isMonochrome(s) ? 25 : 30,
  background: (s) => MaterialDynamicColors.secondaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.secondaryFixed,
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11)
}), MaterialDynamicColors.tertiaryFixed = DynamicColor.fromPalette({
  name: "tertiary_fixed",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? 40 : 90,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.tertiaryFixed, MaterialDynamicColors.tertiaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.tertiaryFixedDim = DynamicColor.fromPalette({
  name: "tertiary_fixed_dim",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? 30 : 80,
  isBackground: true,
  background: (s) => MaterialDynamicColors.highestSurface(s),
  contrastCurve: new ContrastCurve(1, 1, 3, 7),
  toneDeltaPair: (s) => new ToneDeltaPair(MaterialDynamicColors.tertiaryFixed, MaterialDynamicColors.tertiaryFixedDim, 10, "lighter", true)
}), MaterialDynamicColors.onTertiaryFixed = DynamicColor.fromPalette({
  name: "on_tertiary_fixed",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? 100 : 10,
  background: (s) => MaterialDynamicColors.tertiaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.tertiaryFixed,
  contrastCurve: new ContrastCurve(4.5, 7, 11, 21)
}), MaterialDynamicColors.onTertiaryFixedVariant = DynamicColor.fromPalette({
  name: "on_tertiary_fixed_variant",
  palette: (s) => s.tertiaryPalette,
  tone: (s) => isMonochrome(s) ? 90 : 30,
  background: (s) => MaterialDynamicColors.tertiaryFixedDim,
  secondBackground: (s) => MaterialDynamicColors.tertiaryFixed,
  contrastCurve: new ContrastCurve(3, 4.5, 7, 11)
});
var TonalPalette = class _TonalPalette {
  static fromInt(argb) {
    const hct = Hct.fromInt(argb);
    return _TonalPalette.fromHct(hct);
  }
  static fromHct(hct) {
    return new _TonalPalette(hct.hue, hct.chroma, hct);
  }
  static fromHueAndChroma(hue, chroma) {
    return new _TonalPalette(hue, chroma, _TonalPalette.createKeyColor(hue, chroma));
  }
  constructor(hue, chroma, keyColor) {
    this.hue = hue, this.chroma = chroma, this.keyColor = keyColor, this.cache = /* @__PURE__ */ new Map();
  }
  static createKeyColor(hue, chroma) {
    let smallestDeltaHct = Hct.from(hue, chroma, 50), smallestDelta = Math.abs(smallestDeltaHct.chroma - chroma);
    for (let delta = 1; delta < 50; delta += 1) {
      if (Math.round(chroma) === Math.round(smallestDeltaHct.chroma)) return smallestDeltaHct;
      const hctAdd = Hct.from(hue, chroma, 50 + delta), hctAddDelta = Math.abs(hctAdd.chroma - chroma);
      hctAddDelta < smallestDelta && (smallestDelta = hctAddDelta, smallestDeltaHct = hctAdd);
      const hctSubtract = Hct.from(hue, chroma, 50 - delta), hctSubtractDelta = Math.abs(hctSubtract.chroma - chroma);
      hctSubtractDelta < smallestDelta && (smallestDelta = hctSubtractDelta, smallestDeltaHct = hctSubtract);
    }
    return smallestDeltaHct;
  }
  tone(tone) {
    let argb = this.cache.get(tone);
    return void 0 === argb && (argb = Hct.from(this.hue, this.chroma, tone).toInt(), this.cache.set(tone, argb)), argb;
  }
  getHct(tone) {
    return Hct.fromInt(this.tone(tone));
  }
};
var CorePalette = class _CorePalette {
  static of(argb) {
    return new _CorePalette(argb, false);
  }
  static contentOf(argb) {
    return new _CorePalette(argb, true);
  }
  static fromColors(colors) {
    return _CorePalette.createPaletteFromColors(false, colors);
  }
  static contentFromColors(colors) {
    return _CorePalette.createPaletteFromColors(true, colors);
  }
  static createPaletteFromColors(content, colors) {
    const palette = new _CorePalette(colors.primary, content);
    if (colors.secondary) {
      const p = new _CorePalette(colors.secondary, content);
      palette.a2 = p.a1;
    }
    if (colors.tertiary) {
      const p = new _CorePalette(colors.tertiary, content);
      palette.a3 = p.a1;
    }
    if (colors.error) {
      const p = new _CorePalette(colors.error, content);
      palette.error = p.a1;
    }
    if (colors.neutral) {
      const p = new _CorePalette(colors.neutral, content);
      palette.n1 = p.n1;
    }
    if (colors.neutralVariant) {
      const p = new _CorePalette(colors.neutralVariant, content);
      palette.n2 = p.n2;
    }
    return palette;
  }
  constructor(argb, isContent) {
    const hct = Hct.fromInt(argb), hue = hct.hue, chroma = hct.chroma;
    isContent ? (this.a1 = TonalPalette.fromHueAndChroma(hue, chroma), this.a2 = TonalPalette.fromHueAndChroma(hue, chroma / 3), this.a3 = TonalPalette.fromHueAndChroma(hue + 60, chroma / 2), this.n1 = TonalPalette.fromHueAndChroma(hue, Math.min(chroma / 12, 4)), this.n2 = TonalPalette.fromHueAndChroma(hue, Math.min(chroma / 6, 8))) : (this.a1 = TonalPalette.fromHueAndChroma(hue, Math.max(48, chroma)), this.a2 = TonalPalette.fromHueAndChroma(hue, 16), this.a3 = TonalPalette.fromHueAndChroma(hue + 60, 24), this.n1 = TonalPalette.fromHueAndChroma(hue, 4), this.n2 = TonalPalette.fromHueAndChroma(hue, 8)), this.error = TonalPalette.fromHueAndChroma(25, 84);
  }
};
var Scheme = class _Scheme {
  get primary() {
    return this.props.primary;
  }
  get onPrimary() {
    return this.props.onPrimary;
  }
  get primaryContainer() {
    return this.props.primaryContainer;
  }
  get onPrimaryContainer() {
    return this.props.onPrimaryContainer;
  }
  get secondary() {
    return this.props.secondary;
  }
  get onSecondary() {
    return this.props.onSecondary;
  }
  get secondaryContainer() {
    return this.props.secondaryContainer;
  }
  get onSecondaryContainer() {
    return this.props.onSecondaryContainer;
  }
  get tertiary() {
    return this.props.tertiary;
  }
  get onTertiary() {
    return this.props.onTertiary;
  }
  get tertiaryContainer() {
    return this.props.tertiaryContainer;
  }
  get onTertiaryContainer() {
    return this.props.onTertiaryContainer;
  }
  get error() {
    return this.props.error;
  }
  get onError() {
    return this.props.onError;
  }
  get errorContainer() {
    return this.props.errorContainer;
  }
  get onErrorContainer() {
    return this.props.onErrorContainer;
  }
  get background() {
    return this.props.background;
  }
  get onBackground() {
    return this.props.onBackground;
  }
  get surface() {
    return this.props.surface;
  }
  get onSurface() {
    return this.props.onSurface;
  }
  get surfaceVariant() {
    return this.props.surfaceVariant;
  }
  get onSurfaceVariant() {
    return this.props.onSurfaceVariant;
  }
  get outline() {
    return this.props.outline;
  }
  get outlineVariant() {
    return this.props.outlineVariant;
  }
  get shadow() {
    return this.props.shadow;
  }
  get scrim() {
    return this.props.scrim;
  }
  get inverseSurface() {
    return this.props.inverseSurface;
  }
  get inverseOnSurface() {
    return this.props.inverseOnSurface;
  }
  get inversePrimary() {
    return this.props.inversePrimary;
  }
  static light(argb) {
    return _Scheme.lightFromCorePalette(CorePalette.of(argb));
  }
  static dark(argb) {
    return _Scheme.darkFromCorePalette(CorePalette.of(argb));
  }
  static lightContent(argb) {
    return _Scheme.lightFromCorePalette(CorePalette.contentOf(argb));
  }
  static darkContent(argb) {
    return _Scheme.darkFromCorePalette(CorePalette.contentOf(argb));
  }
  static lightFromCorePalette(core) {
    return new _Scheme({
      primary: core.a1.tone(40),
      onPrimary: core.a1.tone(100),
      primaryContainer: core.a1.tone(90),
      onPrimaryContainer: core.a1.tone(10),
      secondary: core.a2.tone(40),
      onSecondary: core.a2.tone(100),
      secondaryContainer: core.a2.tone(90),
      onSecondaryContainer: core.a2.tone(10),
      tertiary: core.a3.tone(40),
      onTertiary: core.a3.tone(100),
      tertiaryContainer: core.a3.tone(90),
      onTertiaryContainer: core.a3.tone(10),
      error: core.error.tone(40),
      onError: core.error.tone(100),
      errorContainer: core.error.tone(90),
      onErrorContainer: core.error.tone(10),
      background: core.n1.tone(99),
      onBackground: core.n1.tone(10),
      surface: core.n1.tone(99),
      onSurface: core.n1.tone(10),
      surfaceVariant: core.n2.tone(90),
      onSurfaceVariant: core.n2.tone(30),
      outline: core.n2.tone(50),
      outlineVariant: core.n2.tone(80),
      shadow: core.n1.tone(0),
      scrim: core.n1.tone(0),
      inverseSurface: core.n1.tone(20),
      inverseOnSurface: core.n1.tone(95),
      inversePrimary: core.a1.tone(80)
    });
  }
  static darkFromCorePalette(core) {
    return new _Scheme({
      primary: core.a1.tone(80),
      onPrimary: core.a1.tone(20),
      primaryContainer: core.a1.tone(30),
      onPrimaryContainer: core.a1.tone(90),
      secondary: core.a2.tone(80),
      onSecondary: core.a2.tone(20),
      secondaryContainer: core.a2.tone(30),
      onSecondaryContainer: core.a2.tone(90),
      tertiary: core.a3.tone(80),
      onTertiary: core.a3.tone(20),
      tertiaryContainer: core.a3.tone(30),
      onTertiaryContainer: core.a3.tone(90),
      error: core.error.tone(80),
      onError: core.error.tone(20),
      errorContainer: core.error.tone(30),
      onErrorContainer: core.error.tone(80),
      background: core.n1.tone(10),
      onBackground: core.n1.tone(90),
      surface: core.n1.tone(10),
      onSurface: core.n1.tone(90),
      surfaceVariant: core.n2.tone(30),
      onSurfaceVariant: core.n2.tone(80),
      outline: core.n2.tone(60),
      outlineVariant: core.n2.tone(30),
      shadow: core.n1.tone(0),
      scrim: core.n1.tone(0),
      inverseSurface: core.n1.tone(90),
      inverseOnSurface: core.n1.tone(20),
      inversePrimary: core.a1.tone(40)
    });
  }
  constructor(props) {
    this.props = props;
  }
  toJSON() {
    return {
      ...this.props
    };
  }
};
function hexFromArgb(argb) {
  const r = redFromArgb(argb), g = greenFromArgb(argb), b = blueFromArgb(argb), outParts = [r.toString(16), g.toString(16), b.toString(16)];
  for (const [i, part] of outParts.entries()) 1 === part.length && (outParts[i] = "0" + part);
  return "#" + outParts.join("");
}
function argbFromHex(hex) {
  const isThree = 3 === (hex = hex.replace("#", "")).length, isSix = 6 === hex.length, isEight = 8 === hex.length;
  if (!isThree && !isSix && !isEight) throw new Error("unexpected hex " + hex);
  let r = 0, g = 0, b = 0;
  return isThree ? (r = parseIntHex(hex.slice(0, 1).repeat(2)), g = parseIntHex(hex.slice(1, 2).repeat(2)), b = parseIntHex(hex.slice(2, 3).repeat(2))) : isSix ? (r = parseIntHex(hex.slice(0, 2)), g = parseIntHex(hex.slice(2, 4)), b = parseIntHex(hex.slice(4, 6))) : isEight && (r = parseIntHex(hex.slice(2, 4)), g = parseIntHex(hex.slice(4, 6)), b = parseIntHex(hex.slice(6, 8))), (255 << 24 | (255 & r) << 16 | (255 & g) << 8 | 255 & b) >>> 0;
}
function parseIntHex(value2) {
  return parseInt(value2, 16);
}
function themeFromSourceColor(source, customColors) {
  if (customColors === void 0) {
    customColors = [];
  }
  const palette = CorePalette.of(source);
  return {
    source,
    schemes: {
      light: Scheme.light(source),
      dark: Scheme.dark(source)
    },
    palettes: {
      primary: palette.a1,
      secondary: palette.a2,
      tertiary: palette.a3,
      neutral: palette.n1,
      neutralVariant: palette.n2,
      error: palette.error
    },
    customColors: customColors.map((c) => customColor(source, c))
  };
}
function customColor(source, color) {
  let value2 = color.value;
  const from = value2, to = source;
  color.blend && (value2 = Blend.harmonize(from, to));
  const tones = CorePalette.of(value2).a1;
  return {
    color,
    value: value2,
    light: {
      color: tones.tone(40),
      onColor: tones.tone(100),
      colorContainer: tones.tone(90),
      onColorContainer: tones.tone(10)
    },
    dark: {
      color: tones.tone(80),
      onColor: tones.tone(20),
      colorContainer: tones.tone(30),
      onColorContainer: tones.tone(90)
    }
  };
}

// node_modules/framework7/shared/material-colors.js
function toRGBA(d) {
  const r = Math.round;
  const l = d.length;
  const rgba = {};
  if (d.slice(0, 3).toLowerCase() === "rgb") {
    d = d.replace(" ", "").split(",");
    rgba[0] = parseInt(d[0].slice(d[3].toLowerCase() === "a" ? 5 : 4), 10);
    rgba[1] = parseInt(d[1], 10);
    rgba[2] = parseInt(d[2], 10);
    rgba[3] = d[3] ? parseFloat(d[3]) : -1;
  } else {
    if (l < 6) d = parseInt(String(d[1]) + d[1] + d[2] + d[2] + d[3] + d[3] + (l > 4 ? String(d[4]) + d[4] : ""), 16);
    else d = parseInt(d.slice(1), 16);
    rgba[0] = d >> 16 & 255;
    rgba[1] = d >> 8 & 255;
    rgba[2] = d & 255;
    rgba[3] = l === 9 || l === 5 ? r((d >> 24 & 255) / 255 * 1e4) / 1e4 : -1;
  }
  return rgba;
}
function blend(from, to, p) {
  if (p === void 0) {
    p = 0.5;
  }
  const r = Math.round;
  from = from.trim();
  to = to.trim();
  const b = p < 0;
  p = b ? p * -1 : p;
  const f = toRGBA(from);
  const t = toRGBA(to);
  if (to[0] === "r") {
    return "rgb" + (to[3] === "a" ? "a(" : "(") + r((t[0] - f[0]) * p + f[0]) + "," + r((t[1] - f[1]) * p + f[1]) + "," + r((t[2] - f[2]) * p + f[2]) + (f[3] < 0 && t[3] < 0 ? "" : "," + (f[3] > -1 && t[3] > -1 ? r(((t[3] - f[3]) * p + f[3]) * 1e4) / 1e4 : t[3] < 0 ? f[3] : t[3])) + ")";
  }
  return "#" + (4294967296 + (f[3] > -1 && t[3] > -1 ? r(((t[3] - f[3]) * p + f[3]) * 255) : t[3] > -1 ? r(t[3] * 255) : f[3] > -1 ? r(f[3] * 255) : 255) * 16777216 + r((t[0] - f[0]) * p + f[0]) * 65536 + r((t[1] - f[1]) * p + f[1]) * 256 + r((t[2] - f[2]) * p + f[2])).toString(16).slice(f[3] > -1 || t[3] > -1 ? 1 : 3);
}
var materialColors = function(hexColor) {
  if (hexColor === void 0) {
    hexColor = "";
  }
  const theme = themeFromSourceColor(argbFromHex(`#${hexColor.replace("#", "")}`));
  [0.05, 0.08, 0.11, 0.12, 0.14].forEach((amount, index2) => {
    theme.schemes.light.props[`surface${index2 + 1}`] = argbFromHex(blend(hexFromArgb(theme.schemes.light.props.surface), hexFromArgb(theme.schemes.light.props.primary), amount));
    theme.schemes.dark.props[`surface${index2 + 1}`] = argbFromHex(blend(hexFromArgb(theme.schemes.dark.props.surface), hexFromArgb(theme.schemes.dark.props.primary), amount));
  });
  const name = (n) => {
    return n.split("").map((char) => char.toUpperCase() === char && char !== "-" && char !== "7" ? `-${char.toLowerCase()}` : char).join("");
  };
  const shouldSkip = (prop2) => {
    const skip = ["tertiary", "shadow", "scrim", "error", "background"];
    return skip.filter((v) => prop2.toLowerCase().includes(v)).length > 0;
  };
  const light = {};
  const dark = {};
  Object.keys(theme.schemes.light.props).forEach((prop2) => {
    if (shouldSkip(prop2)) return;
    light[name(`--f7-md-${prop2}`)] = hexFromArgb(theme.schemes.light.props[prop2]);
  });
  Object.keys(theme.schemes.dark.props).forEach((prop2) => {
    if (shouldSkip(prop2)) return;
    dark[name(`--f7-md-${prop2}`)] = hexFromArgb(theme.schemes.dark.props[prop2]);
  });
  return {
    light,
    dark
  };
};

// node_modules/framework7/shared/utils.js
var uniqueNum = 0;
function uniqueNumber() {
  uniqueNum += 1;
  return uniqueNum;
}
function id(mask, map) {
  if (mask === void 0) {
    mask = "xxxxxxxxxx";
  }
  if (map === void 0) {
    map = "0123456789abcdef";
  }
  const length = map.length;
  return mask.replace(/x/g, () => map[Math.floor(Math.random() * length)]);
}
var mdPreloaderContent = `
  <span class="preloader-inner">
    <svg viewBox="0 0 36 36">
      <circle cx="18" cy="18" r="16"></circle>
    </svg>
  </span>
`.trim();
var iosPreloaderContent = `
  <span class="preloader-inner">
    ${[0, 1, 2, 3, 4, 5, 6, 7].map(() => '<span class="preloader-inner-line"></span>').join("")}
  </span>
`.trim();
function eventNameToColonCase(eventName) {
  let hasColon;
  return eventName.split("").map((char, index2) => {
    if (char.match(/[A-Z]/) && index2 !== 0 && !hasColon) {
      hasColon = true;
      return `:${char.toLowerCase()}`;
    }
    return char.toLowerCase();
  }).join("");
}
function deleteProps(obj) {
  const object = obj;
  Object.keys(object).forEach((key) => {
    try {
      object[key] = null;
    } catch (e) {
    }
    try {
      delete object[key];
    } catch (e) {
    }
  });
}
function requestAnimationFrame(callback) {
  const window2 = getWindow();
  return window2.requestAnimationFrame(callback);
}
function cancelAnimationFrame(frameId) {
  const window2 = getWindow();
  return window2.cancelAnimationFrame(frameId);
}
function nextTick(callback, delay) {
  if (delay === void 0) {
    delay = 0;
  }
  return setTimeout(callback, delay);
}
function nextFrame(callback) {
  return requestAnimationFrame(() => {
    requestAnimationFrame(callback);
  });
}
function now() {
  return Date.now();
}
function parseUrlQuery(url) {
  const window2 = getWindow();
  const query = {};
  let urlToParse = url || window2.location.href;
  let i;
  let params;
  let param;
  let length;
  if (typeof urlToParse === "string" && urlToParse.length) {
    urlToParse = urlToParse.indexOf("?") > -1 ? urlToParse.replace(/\S*\?/, "") : "";
    params = urlToParse.split("&").filter((paramsPart) => paramsPart !== "");
    length = params.length;
    for (i = 0; i < length; i += 1) {
      param = params[i].replace(/#\S+/g, "").split("=");
      query[decodeURIComponent(param[0])] = typeof param[1] === "undefined" ? void 0 : decodeURIComponent(param.slice(1).join("=")) || "";
    }
  }
  return query;
}
function getTranslate(el, axis) {
  if (axis === void 0) {
    axis = "x";
  }
  const window2 = getWindow();
  let matrix;
  let curTransform;
  let transformMatrix;
  const curStyle = window2.getComputedStyle(el, null);
  if (window2.WebKitCSSMatrix) {
    curTransform = curStyle.transform || curStyle.webkitTransform;
    if (curTransform.split(",").length > 6) {
      curTransform = curTransform.split(", ").map((a) => a.replace(",", ".")).join(", ");
    }
    transformMatrix = new window2.WebKitCSSMatrix(curTransform === "none" ? "" : curTransform);
  } else {
    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue("transform").replace("translate(", "matrix(1, 0, 0, 1,");
    matrix = transformMatrix.toString().split(",");
  }
  if (axis === "x") {
    if (window2.WebKitCSSMatrix) curTransform = transformMatrix.m41;
    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);
    else curTransform = parseFloat(matrix[4]);
  }
  if (axis === "y") {
    if (window2.WebKitCSSMatrix) curTransform = transformMatrix.m42;
    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);
    else curTransform = parseFloat(matrix[5]);
  }
  return curTransform || 0;
}
function serializeObject(obj, parents2) {
  if (parents2 === void 0) {
    parents2 = [];
  }
  if (typeof obj === "string") return obj;
  const resultArray = [];
  const separator = "&";
  let newParents;
  function varName(name) {
    if (parents2.length > 0) {
      let parentParts = "";
      for (let j = 0; j < parents2.length; j += 1) {
        if (j === 0) parentParts += parents2[j];
        else parentParts += `[${encodeURIComponent(parents2[j])}]`;
      }
      return `${parentParts}[${encodeURIComponent(name)}]`;
    }
    return encodeURIComponent(name);
  }
  function varValue(value2) {
    return encodeURIComponent(value2);
  }
  Object.keys(obj).forEach((prop2) => {
    let toPush;
    if (Array.isArray(obj[prop2])) {
      toPush = [];
      for (let i = 0; i < obj[prop2].length; i += 1) {
        if (!Array.isArray(obj[prop2][i]) && typeof obj[prop2][i] === "object") {
          newParents = parents2.slice();
          newParents.push(prop2);
          newParents.push(String(i));
          toPush.push(serializeObject(obj[prop2][i], newParents));
        } else {
          toPush.push(`${varName(prop2)}[]=${varValue(obj[prop2][i])}`);
        }
      }
      if (toPush.length > 0) resultArray.push(toPush.join(separator));
    } else if (obj[prop2] === null || obj[prop2] === "") {
      resultArray.push(`${varName(prop2)}=`);
    } else if (typeof obj[prop2] === "object") {
      newParents = parents2.slice();
      newParents.push(prop2);
      toPush = serializeObject(obj[prop2], newParents);
      if (toPush !== "") resultArray.push(toPush);
    } else if (typeof obj[prop2] !== "undefined" && obj[prop2] !== "") {
      resultArray.push(`${varName(prop2)}=${varValue(obj[prop2])}`);
    } else if (obj[prop2] === "") resultArray.push(varName(prop2));
  });
  return resultArray.join(separator);
}
function isObject2(o) {
  return typeof o === "object" && o !== null && o.constructor && o.constructor === Object;
}
function merge() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  const to = args[0];
  args.splice(0, 1);
  const from = args;
  for (let i = 0; i < from.length; i += 1) {
    const nextSource = args[i];
    if (nextSource !== void 0 && nextSource !== null) {
      const keysArray = Object.keys(Object(nextSource));
      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {
        const nextKey = keysArray[nextIndex];
        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);
        if (desc !== void 0 && desc.enumerable) {
          to[nextKey] = nextSource[nextKey];
        }
      }
    }
  }
  return to;
}
function extend2() {
  let deep = true;
  let to;
  let from;
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    args[_key2] = arguments[_key2];
  }
  if (typeof args[0] === "boolean") {
    deep = args[0];
    to = args[1];
    args.splice(0, 2);
    from = args;
  } else {
    to = args[0];
    args.splice(0, 1);
    from = args;
  }
  for (let i = 0; i < from.length; i += 1) {
    const nextSource = args[i];
    if (nextSource !== void 0 && nextSource !== null) {
      const keysArray = Object.keys(Object(nextSource));
      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {
        const nextKey = keysArray[nextIndex];
        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);
        if (desc !== void 0 && desc.enumerable) {
          if (!deep) {
            to[nextKey] = nextSource[nextKey];
          } else if (isObject2(to[nextKey]) && isObject2(nextSource[nextKey])) {
            extend2(to[nextKey], nextSource[nextKey]);
          } else if (!isObject2(to[nextKey]) && isObject2(nextSource[nextKey])) {
            to[nextKey] = {};
            extend2(to[nextKey], nextSource[nextKey]);
          } else {
            to[nextKey] = nextSource[nextKey];
          }
        }
      }
    }
  }
  return to;
}
function colorHexToRgb(hex) {
  const h = hex.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i, (m, r, g, b) => r + r + g + g + b + b);
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(h);
  return result ? result.slice(1).map((n) => parseInt(n, 16)) : null;
}
function colorRgbToHex(r, g, b) {
  const result = [r, g, b].map((n) => {
    const hex = n.toString(16);
    return hex.length === 1 ? `0${hex}` : hex;
  }).join("");
  return `#${result}`;
}
function colorRgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const d = max - min;
  let h;
  if (d === 0) h = 0;
  else if (max === r) h = (g - b) / d % 6;
  else if (max === g) h = (b - r) / d + 2;
  else if (max === b) h = (r - g) / d + 4;
  const l = (min + max) / 2;
  const s = d === 0 ? 0 : d / (1 - Math.abs(2 * l - 1));
  if (h < 0) h = 360 / 60 + h;
  return [h * 60, s, l];
}
function colorHslToRgb(h, s, l) {
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const hp = h / 60;
  const x = c * (1 - Math.abs(hp % 2 - 1));
  let rgb1;
  if (Number.isNaN(h) || typeof h === "undefined") {
    rgb1 = [0, 0, 0];
  } else if (hp <= 1) rgb1 = [c, x, 0];
  else if (hp <= 2) rgb1 = [x, c, 0];
  else if (hp <= 3) rgb1 = [0, c, x];
  else if (hp <= 4) rgb1 = [0, x, c];
  else if (hp <= 5) rgb1 = [x, 0, c];
  else if (hp <= 6) rgb1 = [c, 0, x];
  const m = l - c / 2;
  return rgb1.map((n) => Math.max(0, Math.min(255, Math.round(255 * (n + m)))));
}
function colorHsbToHsl(h, s, b) {
  const HSL = {
    h,
    s: 0,
    l: 0
  };
  const HSB = {
    h,
    s,
    b
  };
  HSL.l = (2 - HSB.s) * HSB.b / 2;
  HSL.s = HSL.l && HSL.l < 1 ? HSB.s * HSB.b / (HSL.l < 0.5 ? HSL.l * 2 : 2 - HSL.l * 2) : HSL.s;
  return [HSL.h, HSL.s, HSL.l];
}
function colorHslToHsb(h, s, l) {
  const HSB = {
    h,
    s: 0,
    b: 0
  };
  const HSL = {
    h,
    s,
    l
  };
  const t = HSL.s * (HSL.l < 0.5 ? HSL.l : 1 - HSL.l);
  HSB.b = HSL.l + t;
  HSB.s = HSL.l > 0 ? 2 * t / HSB.b : HSB.s;
  return [HSB.h, HSB.s, HSB.b];
}
var getShadeTintColors = (rgb) => {
  const hsl = colorRgbToHsl(...rgb);
  const hslShade = [hsl[0], hsl[1], Math.max(0, hsl[2] - 0.08)];
  const hslTint = [hsl[0], hsl[1], Math.max(0, hsl[2] + 0.08)];
  const shade = colorRgbToHex(...colorHslToRgb(...hslShade));
  const tint = colorRgbToHex(...colorHslToRgb(...hslTint));
  return {
    shade,
    tint
  };
};
function colorThemeCSSProperties() {
  let hex;
  let rgb;
  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
    args[_key3] = arguments[_key3];
  }
  if (args.length === 1) {
    hex = args[0];
    rgb = colorHexToRgb(hex);
  } else if (args.length === 3) {
    rgb = args;
    hex = colorRgbToHex(...rgb);
  }
  if (!rgb) return {};
  const {
    light,
    dark
  } = materialColors(hex);
  const shadeTintIos = getShadeTintColors(rgb);
  const shadeTintMdLight = getShadeTintColors(colorHexToRgb(light["--f7-md-primary"]));
  const shadeTintMdDark = getShadeTintColors(colorHexToRgb(dark["--f7-md-primary"]));
  Object.keys(light).forEach((key) => {
    if (key.includes("surface-")) {
      light[`${key}-rgb`] = colorHexToRgb(light[key]);
    }
  });
  Object.keys(dark).forEach((key) => {
    if (key.includes("surface-")) {
      dark[`${key}-rgb`] = colorHexToRgb(dark[key]);
    }
  });
  return {
    ios: {
      "--f7-theme-color": "var(--f7-ios-primary)",
      "--f7-theme-color-rgb": "var(--f7-ios-primary-rgb)",
      "--f7-theme-color-shade": "var(--f7-ios-primary-shade)",
      "--f7-theme-color-tint": "var(--f7-ios-primary-tint)"
    },
    md: {
      "--f7-theme-color": "var(--f7-md-primary)",
      "--f7-theme-color-rgb": "var(--f7-md-primary-rgb)",
      "--f7-theme-color-shade": "var(--f7-md-primary-shade)",
      "--f7-theme-color-tint": "var(--f7-md-primary-tint)"
    },
    light: {
      "--f7-ios-primary": hex,
      "--f7-ios-primary-shade": shadeTintIos.shade,
      "--f7-ios-primary-tint": shadeTintIos.tint,
      "--f7-ios-primary-rgb": rgb.join(", "),
      "--f7-md-primary-shade": shadeTintMdLight.shade,
      "--f7-md-primary-tint": shadeTintMdLight.tint,
      "--f7-md-primary-rgb": colorHexToRgb(light["--f7-md-primary"]).join(", "),
      ...light
    },
    dark: {
      "--f7-md-primary-shade": shadeTintMdDark.shade,
      "--f7-md-primary-tint": shadeTintMdDark.tint,
      "--f7-md-primary-rgb": colorHexToRgb(dark["--f7-md-primary"]).join(", "),
      ...dark
    }
  };
}
function bindMethods(instance, obj) {
  Object.keys(obj).forEach((key) => {
    if (isObject2(obj[key])) {
      Object.keys(obj[key]).forEach((subKey) => {
        if (typeof obj[key][subKey] === "function") {
          obj[key][subKey] = obj[key][subKey].bind(instance);
        }
      });
    }
    instance[key] = obj[key];
  });
}
function flattenArray() {
  const arr = [];
  for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
    args[_key4] = arguments[_key4];
  }
  args.forEach((arg) => {
    if (Array.isArray(arg)) arr.push(...flattenArray(...arg));
    else arr.push(arg);
  });
  return arr;
}
function colorThemeCSSStyles(colors) {
  if (colors === void 0) {
    colors = {};
  }
  const stringifyObject = (obj) => {
    let res = "";
    Object.keys(obj).forEach((key) => {
      res += `${key}:${obj[key]};`;
    });
    return res;
  };
  const colorVars = colorThemeCSSProperties(colors.primary);
  const primary = [`:root{`, stringifyObject(colorVars.light), `--swiper-theme-color:var(--f7-theme-color);`, ...Object.keys(colors).map((colorName) => `--f7-color-${colorName}: ${colors[colorName]};`), `}`, `.dark{`, stringifyObject(colorVars.dark), `}`, `.ios, .ios .dark{`, stringifyObject(colorVars.ios), "}", `.md, .md .dark{`, stringifyObject(colorVars.md), "}"].join("");
  const restVars = {};
  Object.keys(colors).forEach((colorName) => {
    const colorValue = colors[colorName];
    restVars[colorName] = colorThemeCSSProperties(colorValue);
  });
  let rest = "";
  Object.keys(colors).forEach((colorName) => {
    const {
      light,
      dark,
      ios,
      md
    } = restVars[colorName];
    const whiteColorVars = `
    --f7-ios-primary: #ffffff;
    --f7-ios-primary-shade: #ebebeb;
    --f7-ios-primary-tint: #ffffff;
    --f7-ios-primary-rgb: 255, 255, 255;
    --f7-md-primary-shade: #eee;
    --f7-md-primary-tint: #fff;
    --f7-md-primary-rgb: 255, 255, 255;
    --f7-md-primary: #fff;
    --f7-md-on-primary: #000;
    --f7-md-primary-container: #fff;
    --f7-md-on-primary-container: #000;
    --f7-md-secondary: #fff;
    --f7-md-on-secondary: #000;
    --f7-md-secondary-container: #555;
    --f7-md-on-secondary-container: #fff;
    --f7-md-surface: #fff;
    --f7-md-on-surface: #000;
    --f7-md-surface-variant: #333;
    --f7-md-on-surface-variant: #fff;
    --f7-md-outline: #fff;
    --f7-md-outline-variant: #fff;
    --f7-md-inverse-surface: #000;
    --f7-md-inverse-on-surface: #fff;
    --f7-md-inverse-primary: #000;
    --f7-md-surface-1: #f8f8f8;
    --f7-md-surface-2: #f1f1f1;
    --f7-md-surface-3: #e7e7e7;
    --f7-md-surface-4: #e1e1e1;
    --f7-md-surface-5: #d7d7d7;
    --f7-md-surface-variant-rgb: 51, 51, 51;
    --f7-md-on-surface-variant-rgb: 255, 255, 255;
    --f7-md-surface-1-rgb: 248, 248, 248;
    --f7-md-surface-2-rgb: 241, 241, 241;
    --f7-md-surface-3-rgb: 231, 231, 231;
    --f7-md-surface-4-rgb: 225, 225, 225;
    --f7-md-surface-5-rgb: 215, 215, 215;
    `;
    const blackColorVars = `
    --f7-ios-primary: #000;
    --f7-ios-primary-shade: #000;
    --f7-ios-primary-tint: #232323;
    --f7-ios-primary-rgb: 0, 0, 0;
    --f7-md-primary-shade: #000;
    --f7-md-primary-tint: #232323;
    --f7-md-primary-rgb: 0, 0, 0;
    --f7-md-primary: #000;
    --f7-md-on-primary: #fff;
    --f7-md-primary-container: #000;
    --f7-md-on-primary-container: #fff;
    --f7-md-secondary: #000;
    --f7-md-on-secondary: #fff;
    --f7-md-secondary-container: #aaa;
    --f7-md-on-secondary-container: #000;
    --f7-md-surface: #000;
    --f7-md-on-surface: #fff;
    --f7-md-surface-variant: #ccc;
    --f7-md-on-surface-variant: #000;
    --f7-md-outline: #000;
    --f7-md-outline-variant: #000;
    --f7-md-inverse-surface: #fff;
    --f7-md-inverse-on-surface: #000;
    --f7-md-inverse-primary: #fff;
    --f7-md-surface-1: #070707;
    --f7-md-surface-2: #161616;
    --f7-md-surface-3: #232323;
    --f7-md-surface-4: #303030;
    --f7-md-surface-5: #373737;
    --f7-md-surface-variant-rgb: 204, 204, 204;
    --f7-md-on-surface-variant-rgb: 0, 0, 0;
    --f7-md-surface-1-rgb: 7, 7, 7;
    --f7-md-surface-2-rgb: 22, 22, 22;
    --f7-md-surface-3-rgb: 35, 35, 35;
    --f7-md-surface-4-rgb: 48, 48, 48;
    --f7-md-surface-5-rgb: 55, 55, 55;
    `;
    const lightString = colorName === "white" ? whiteColorVars : colorName === "black" ? blackColorVars : stringifyObject(light);
    const darkString = colorName === "white" ? whiteColorVars : colorName === "black" ? blackColorVars : stringifyObject(dark);
    rest += [
      `.color-${colorName} {`,
      lightString,
      `--swiper-theme-color: var(--f7-theme-color);`,
      `}`,
      `.color-${colorName}.dark, .color-${colorName} .dark, .dark .color-${colorName} {`,
      darkString,
      `--swiper-theme-color: var(--f7-theme-color);`,
      `}`,
      `.ios .color-${colorName}, .ios.color-${colorName}, .ios .dark .color-${colorName}, .ios .dark.color-${colorName} {`,
      stringifyObject(ios),
      `}`,
      `.md .color-${colorName}, .md.color-${colorName}, .md .dark .color-${colorName}, .md .dark.color-${colorName} {`,
      stringifyObject(md),
      `}`,
      // text color
      `.text-color-${colorName} {`,
      `--f7-theme-color-text-color: ${colors[colorName]};`,
      `}`,
      // bg color
      `.bg-color-${colorName} {`,
      `--f7-theme-color-bg-color: ${colors[colorName]};`,
      `}`,
      // border color
      `.border-color-${colorName} {`,
      `--f7-theme-color-border-color: ${colors[colorName]};`,
      `}`,
      // ripple color
      `.ripple-color-${colorName} {`,
      `--f7-theme-color-ripple-color: rgba(${light["--f7-ios-primary-rgb"]}, 0.3);`,
      `}`
    ].join("");
  });
  return `${primary}${rest}`;
}

// node_modules/framework7/shared/get-support.js
var support;
function calcSupport() {
  const window2 = getWindow();
  const document2 = getDocument();
  return {
    touch: !!("ontouchstart" in window2 || window2.DocumentTouch && document2 instanceof window2.DocumentTouch),
    pointerEvents: !!window2.PointerEvent && "maxTouchPoints" in window2.navigator && window2.navigator.maxTouchPoints >= 0,
    passiveListener: function checkPassiveListener() {
      let supportsPassive = false;
      try {
        const opts = Object.defineProperty({}, "passive", {
          // eslint-disable-next-line
          get() {
            supportsPassive = true;
          }
        });
        window2.addEventListener("testPassiveListener", null, opts);
      } catch (e) {
      }
      return supportsPassive;
    }(),
    intersectionObserver: function checkObserver() {
      return "IntersectionObserver" in window2;
    }()
  };
}
function getSupport() {
  if (!support) {
    support = calcSupport();
  }
  return support;
}

// node_modules/framework7/shared/get-device.js
var deviceCalculated;
function calcDevice(_temp) {
  let {
    userAgent
  } = _temp === void 0 ? {} : _temp;
  const support2 = getSupport();
  const window2 = getWindow();
  const platform = window2.navigator.platform;
  const ua = userAgent || window2.navigator.userAgent;
  const device = {
    ios: false,
    android: false,
    androidChrome: false,
    desktop: false,
    iphone: false,
    ipod: false,
    ipad: false,
    edge: false,
    ie: false,
    firefox: false,
    macos: false,
    windows: false,
    cordova: !!window2.cordova,
    electron: false,
    capacitor: !!window2.Capacitor,
    nwjs: false
  };
  const screenWidth = window2.screen.width;
  const screenHeight = window2.screen.height;
  const android = ua.match(/(Android);?[\s\/]+([\d.]+)?/);
  let ipad = ua.match(/(iPad).*OS\s([\d_]+)/);
  const ipod = ua.match(/(iPod)(.*OS\s([\d_]+))?/);
  const iphone = !ipad && ua.match(/(iPhone\sOS|iOS|iPhone;\sCPU\sOS)\s([\d_]+)/);
  const ie = ua.indexOf("MSIE ") >= 0 || ua.indexOf("Trident/") >= 0;
  const edge = ua.indexOf("Edge/") >= 0;
  const firefox = ua.indexOf("Gecko/") >= 0 && ua.indexOf("Firefox/") >= 0;
  const windows = platform === "Win32";
  const electron = ua.toLowerCase().indexOf("electron") >= 0;
  const nwjs = typeof nw !== "undefined" && typeof process !== "undefined" && typeof process.versions !== "undefined" && typeof process.versions.nw !== "undefined";
  let macos = platform === "MacIntel";
  const iPadScreens = ["1024x1366", "1366x1024", "834x1194", "1194x834", "834x1112", "1112x834", "768x1024", "1024x768", "820x1180", "1180x820", "810x1080", "1080x810"];
  if (!ipad && macos && support2.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {
    ipad = ua.match(/(Version)\/([\d.]+)/);
    if (!ipad) ipad = [0, 1, "13_0_0"];
    macos = false;
  }
  device.ie = ie;
  device.edge = edge;
  device.firefox = firefox;
  if (android) {
    device.os = "android";
    device.osVersion = android[2];
    device.android = true;
    device.androidChrome = ua.toLowerCase().indexOf("chrome") >= 0;
  }
  if (ipad || iphone || ipod) {
    device.os = "ios";
    device.ios = true;
  }
  if (iphone && !ipod) {
    device.osVersion = iphone[2].replace(/_/g, ".");
    device.iphone = true;
  }
  if (ipad) {
    device.osVersion = ipad[2].replace(/_/g, ".");
    device.ipad = true;
  }
  if (ipod) {
    device.osVersion = ipod[3] ? ipod[3].replace(/_/g, ".") : null;
    device.ipod = true;
  }
  if (device.ios && device.osVersion && ua.indexOf("Version/") >= 0) {
    if (device.osVersion.split(".")[0] === "10") {
      device.osVersion = ua.toLowerCase().split("version/")[1].split(" ")[0];
    }
  }
  device.webView = !!((iphone || ipad || ipod) && (ua.match(/.*AppleWebKit(?!.*Safari)/i) || window2.navigator.standalone)) || window2.matchMedia && window2.matchMedia("(display-mode: standalone)").matches;
  device.webview = device.webView;
  device.standalone = device.webView;
  device.desktop = !(device.ios || device.android) || electron || nwjs;
  if (device.desktop) {
    device.electron = electron;
    device.nwjs = nwjs;
    device.macos = macos;
    device.windows = windows;
    if (device.macos) {
      device.os = "macos";
    }
    if (device.windows) {
      device.os = "windows";
    }
  }
  device.pixelRatio = window2.devicePixelRatio || 1;
  const DARK = "(prefers-color-scheme: dark)";
  const LIGHT = "(prefers-color-scheme: light)";
  device.prefersColorScheme = function prefersColorTheme() {
    let theme;
    if (window2.matchMedia && window2.matchMedia(LIGHT).matches) {
      theme = "light";
    }
    if (window2.matchMedia && window2.matchMedia(DARK).matches) {
      theme = "dark";
    }
    return theme;
  };
  return device;
}
var IS_BROWSER = (() => {
  const document2 = getDocument();
  try {
    return Boolean(document2 && document2.body && document2.body.getBoundingClientRect && document2.body.getBoundingClientRect().width > 0);
  } catch (e) {
    return false;
  }
})();
function getDevice(overrides, reset) {
  if (overrides === void 0) {
    overrides = {};
  }
  if (reset === void 0) {
    reset = IS_BROWSER;
  }
  if (!deviceCalculated || reset) {
    deviceCalculated = calcDevice(overrides);
  }
  return deviceCalculated;
}

// node_modules/framework7/shared/events-class.js
var EventsClass = class {
  constructor(parents2) {
    if (parents2 === void 0) {
      parents2 = [];
    }
    const self = this;
    self.eventsParents = parents2;
    self.eventsListeners = {};
  }
  on(events, handler, priority) {
    const self = this;
    if (typeof handler !== "function") return self;
    const method = priority ? "unshift" : "push";
    events.split(" ").forEach((event) => {
      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];
      self.eventsListeners[event][method](handler);
    });
    return self;
  }
  once(events, handler, priority) {
    const self = this;
    if (typeof handler !== "function") return self;
    function onceHandler() {
      self.off(events, onceHandler);
      if (onceHandler.f7proxy) {
        delete onceHandler.f7proxy;
      }
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      handler.apply(self, args);
    }
    onceHandler.f7proxy = handler;
    return self.on(events, onceHandler, priority);
  }
  off(events, handler) {
    const self = this;
    if (!self.eventsListeners) return self;
    events.split(" ").forEach((event) => {
      if (typeof handler === "undefined") {
        self.eventsListeners[event] = [];
      } else if (self.eventsListeners[event]) {
        self.eventsListeners[event].forEach((eventHandler, index2) => {
          if (eventHandler === handler || eventHandler.f7proxy && eventHandler.f7proxy === handler) {
            self.eventsListeners[event].splice(index2, 1);
          }
        });
      }
    });
    return self;
  }
  emit() {
    const self = this;
    if (!self.eventsListeners) return self;
    let events;
    let data2;
    let context;
    let eventsParents;
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    if (typeof args[0] === "string" || Array.isArray(args[0])) {
      events = args[0];
      data2 = args.slice(1, args.length);
      context = self;
      eventsParents = self.eventsParents;
    } else {
      events = args[0].events;
      data2 = args[0].data;
      context = args[0].context || self;
      eventsParents = args[0].local ? [] : args[0].parents || self.eventsParents;
    }
    const eventsArray = Array.isArray(events) ? events : events.split(" ");
    const localEvents = eventsArray.map((eventName) => eventName.replace("local::", ""));
    const parentEvents = eventsArray.filter((eventName) => eventName.indexOf("local::") < 0);
    localEvents.forEach((event) => {
      if (self.eventsListeners && self.eventsListeners[event]) {
        const handlers = [];
        self.eventsListeners[event].forEach((eventHandler) => {
          handlers.push(eventHandler);
        });
        handlers.forEach((eventHandler) => {
          eventHandler.apply(context, data2);
        });
      }
    });
    if (eventsParents && eventsParents.length > 0) {
      eventsParents.forEach((eventsParent) => {
        eventsParent.emit(parentEvents, ...data2);
      });
    }
    return self;
  }
};
var events_class_default = EventsClass;

// node_modules/framework7/shared/class.js
var Framework7Class = class extends events_class_default {
  constructor(params, parents2) {
    if (params === void 0) {
      params = {};
    }
    if (parents2 === void 0) {
      parents2 = [];
    }
    super(parents2);
    const self = this;
    self.params = params;
    if (self.params && self.params.on) {
      Object.keys(self.params.on).forEach((eventName) => {
        self.on(eventName, self.params.on[eventName]);
      });
    }
  }
  // eslint-disable-next-line
  useModuleParams(module, instanceParams) {
    if (module.params) {
      const originalParams = {};
      Object.keys(module.params).forEach((paramKey) => {
        if (typeof instanceParams[paramKey] === "undefined") return;
        originalParams[paramKey] = extend2({}, instanceParams[paramKey]);
      });
      extend2(instanceParams, module.params);
      Object.keys(originalParams).forEach((paramKey) => {
        extend2(instanceParams[paramKey], originalParams[paramKey]);
      });
    }
  }
  useModulesParams(instanceParams) {
    const instance = this;
    if (!instance.modules) return;
    Object.keys(instance.modules).forEach((moduleName) => {
      const module = instance.modules[moduleName];
      if (module.params) {
        extend2(instanceParams, module.params);
      }
    });
  }
  useModule(moduleName, moduleParams) {
    if (moduleName === void 0) {
      moduleName = "";
    }
    if (moduleParams === void 0) {
      moduleParams = {};
    }
    const instance = this;
    if (!instance.modules) return;
    const module = typeof moduleName === "string" ? instance.modules[moduleName] : moduleName;
    if (!module) return;
    if (module.instance) {
      Object.keys(module.instance).forEach((modulePropName) => {
        const moduleProp = module.instance[modulePropName];
        if (typeof moduleProp === "function") {
          instance[modulePropName] = moduleProp.bind(instance);
        } else {
          instance[modulePropName] = moduleProp;
        }
      });
    }
    if (module.on && instance.on) {
      Object.keys(module.on).forEach((moduleEventName) => {
        instance.on(moduleEventName, module.on[moduleEventName]);
      });
    }
    if (module.vnode) {
      if (!instance.vnodeHooks) instance.vnodeHooks = {};
      Object.keys(module.vnode).forEach((vnodeId) => {
        Object.keys(module.vnode[vnodeId]).forEach((hookName) => {
          const handler = module.vnode[vnodeId][hookName];
          if (!instance.vnodeHooks[hookName]) instance.vnodeHooks[hookName] = {};
          if (!instance.vnodeHooks[hookName][vnodeId]) instance.vnodeHooks[hookName][vnodeId] = [];
          instance.vnodeHooks[hookName][vnodeId].push(handler.bind(instance));
        });
      });
    }
    if (module.create) {
      module.create.bind(instance)(moduleParams);
    }
  }
  useModules(modulesParams) {
    if (modulesParams === void 0) {
      modulesParams = {};
    }
    const instance = this;
    if (!instance.modules) return;
    Object.keys(instance.modules).forEach((moduleName) => {
      const moduleParams = modulesParams[moduleName] || {};
      instance.useModule(moduleName, moduleParams);
    });
  }
  static set components(components) {
    const Class = this;
    if (!Class.use) return;
    Class.use(components);
  }
  static installModule(module) {
    const Class = this;
    if (!Class.prototype.modules) Class.prototype.modules = {};
    const name = module.name || `${Object.keys(Class.prototype.modules).length}_${now()}`;
    Class.prototype.modules[name] = module;
    if (module.proto) {
      Object.keys(module.proto).forEach((key) => {
        Class.prototype[key] = module.proto[key];
      });
    }
    if (module.static) {
      Object.keys(module.static).forEach((key) => {
        Class[key] = module.static[key];
      });
    }
    if (module.install) {
      for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        params[_key - 1] = arguments[_key];
      }
      module.install.apply(Class, params);
    }
    return Class;
  }
  static use(module) {
    const Class = this;
    if (Array.isArray(module)) {
      module.forEach((m) => Class.installModule(m));
      return Class;
    }
    for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      params[_key2 - 1] = arguments[_key2];
    }
    return Class.installModule(module, ...params);
  }
};
var class_default = Framework7Class;

// node_modules/framework7/shared/constructor-methods.js
function ConstructorMethods(parameters) {
  if (parameters === void 0) {
    parameters = {};
  }
  const {
    defaultSelector,
    constructor: Constructor,
    domProp,
    app,
    addMethods
  } = parameters;
  const methods = {
    create() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      if (app) return new Constructor(app, ...args);
      return new Constructor(...args);
    },
    get(el) {
      if (el === void 0) {
        el = defaultSelector;
      }
      if (el instanceof Constructor) return el;
      const $el = dom7_default(el);
      if ($el.length === 0) return void 0;
      return $el[0][domProp];
    },
    destroy(el) {
      const instance = methods.get(el);
      if (instance && instance.destroy) return instance.destroy();
      return void 0;
    }
  };
  if (addMethods && Array.isArray(addMethods)) {
    addMethods.forEach((methodName) => {
      methods[methodName] = function(el) {
        if (el === void 0) {
          el = defaultSelector;
        }
        const instance = methods.get(el);
        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
          args[_key2 - 1] = arguments[_key2];
        }
        if (instance && instance[methodName]) return instance[methodName](...args);
        return void 0;
      };
    });
  }
  return methods;
}

// node_modules/framework7/shared/modal-methods.js
function ModalMethods(parameters) {
  if (parameters === void 0) {
    parameters = {};
  }
  const {
    defaultSelector,
    constructor: Constructor,
    app
  } = parameters;
  const methods = extend2(ConstructorMethods({
    defaultSelector,
    constructor: Constructor,
    app,
    domProp: "f7Modal"
  }), {
    open(el, animate2, targetEl) {
      let $el = dom7_default(el);
      if ($el.length > 1 && targetEl) {
        const $targetPage = dom7_default(targetEl).parents(".page");
        if ($targetPage.length) {
          $el.each((modalEl) => {
            const $modalEl = dom7_default(modalEl);
            if ($modalEl.parents($targetPage)[0] === $targetPage[0]) {
              $el = $modalEl;
            }
          });
        }
      }
      if ($el.length > 1) {
        $el = $el.eq($el.length - 1);
      }
      if (!$el.length) return void 0;
      let instance = $el[0].f7Modal;
      if (!instance) {
        const params = $el.dataset();
        instance = new Constructor(app, {
          el: $el,
          ...params
        });
      }
      return instance.open(animate2);
    },
    close(el, animate2, targetEl) {
      if (el === void 0) {
        el = defaultSelector;
      }
      let $el = dom7_default(el);
      if (!$el.length) return void 0;
      if ($el.length > 1) {
        let $parentEl;
        if (targetEl) {
          const $targetEl = dom7_default(targetEl);
          if ($targetEl.length) {
            $parentEl = $targetEl.parents($el);
          }
        }
        if ($parentEl && $parentEl.length > 0) {
          $el = $parentEl;
        } else {
          $el = $el.eq($el.length - 1);
        }
      }
      let instance = $el[0].f7Modal;
      if (!instance) {
        const params = $el.dataset();
        instance = new Constructor(app, {
          el: $el,
          ...params
        });
      }
      return instance.close(animate2);
    }
  });
  return methods;
}

// node_modules/framework7/components/app/load-module.js
var fetchedModules = [];
function loadModule(moduleToLoad) {
  const Framework72 = this;
  const window2 = getWindow();
  const document2 = getDocument();
  return new Promise((resolve, reject) => {
    const app = Framework72.instance;
    let modulePath;
    let moduleObj;
    let moduleFunc;
    if (!moduleToLoad) {
      reject(new Error("Framework7: Lazy module must be specified"));
      return;
    }
    function install(module) {
      Framework72.use(module);
      if (app) {
        app.useModuleParams(module, app.params);
        app.useModule(module);
      }
    }
    if (typeof moduleToLoad === "string") {
      const matchNamePattern = moduleToLoad.match(/([a-z0-9-]*)/i);
      if (moduleToLoad.indexOf(".") < 0 && matchNamePattern && matchNamePattern[0].length === moduleToLoad.length) {
        if (!app || app && !app.params.lazyModulesPath) {
          reject(new Error('Framework7: "lazyModulesPath" app parameter must be specified to fetch module by name'));
          return;
        }
        modulePath = `${app.params.lazyModulesPath}/${moduleToLoad}/${moduleToLoad}.lazy.js`;
      } else {
        modulePath = moduleToLoad;
      }
    } else if (typeof moduleToLoad === "function") {
      moduleFunc = moduleToLoad;
    } else {
      moduleObj = moduleToLoad;
    }
    if (moduleFunc) {
      const module = moduleFunc(Framework72, false);
      if (!module) {
        reject(new Error("Framework7: Can't find Framework7 component in specified component function"));
        return;
      }
      if (Framework72.prototype.modules && Framework72.prototype.modules[module.name]) {
        resolve();
        return;
      }
      install(module);
      resolve();
    }
    if (moduleObj) {
      const module = moduleObj;
      if (!module) {
        reject(new Error("Framework7: Can't find Framework7 component in specified component"));
        return;
      }
      if (Framework72.prototype.modules && Framework72.prototype.modules[module.name]) {
        resolve();
        return;
      }
      install(module);
      resolve();
    }
    if (modulePath) {
      if (fetchedModules.indexOf(modulePath) >= 0) {
        resolve();
        return;
      }
      fetchedModules.push(modulePath);
      const scriptLoad = new Promise((resolveScript, rejectScript) => {
        fetch(modulePath).then((res) => res.text()).then((scriptContent) => {
          const callbackId = id();
          const callbackLoadName = `f7_component_loader_callback_${callbackId}`;
          const scriptEl = document2.createElement("script");
          scriptEl.innerHTML = `window.${callbackLoadName} = function (Framework7, Framework7AutoInstallComponent) {return ${scriptContent.trim()}}`;
          dom7_default("head").append(scriptEl);
          const componentLoader = window2[callbackLoadName];
          delete window2[callbackLoadName];
          dom7_default(scriptEl).remove();
          const module = componentLoader(Framework72, false);
          if (!module) {
            rejectScript(new Error(`Framework7: Can't find Framework7 component in ${modulePath} file`));
            return;
          }
          if (Framework72.prototype.modules && Framework72.prototype.modules[module.name]) {
            resolveScript();
            return;
          }
          install(module);
          resolveScript();
        }).catch((err) => {
          rejectScript(err);
        });
      });
      const styleLoad = new Promise((resolveStyle) => {
        fetch(modulePath.replace(".lazy.js", app.rtl ? ".rtl.css" : ".css").replace(".js", app.rtl ? ".rtl.css" : ".css")).then((res) => res.text()).then((styleContent) => {
          const styleEl = document2.createElement("style");
          styleEl.innerHTML = styleContent;
          dom7_default("head").append(styleEl);
          resolveStyle();
        }).catch(() => {
          resolveStyle();
        });
      });
      Promise.all([scriptLoad, styleLoad]).then(() => {
        resolve();
      }).catch((err) => {
        reject(err);
      });
    }
  });
}
var load_module_default = loadModule;

// node_modules/framework7/shared/$jsx.js
var $jsx = function(tag, props) {
  const attrs = props || {};
  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
    args[_key - 2] = arguments[_key];
  }
  const children2 = args || [];
  const attrsString = Object.keys(attrs).map((attr2) => {
    if (attr2[0] === "_") {
      if (attrs[attr2]) return attr2.replace("_", "");
      return "";
    }
    return `${attr2}="${attrs[attr2]}"`;
  }).filter((attr2) => !!attr2).join(" ");
  if (["path", "img", "circle", "polygon", "line", "input"].indexOf(tag) >= 0) {
    return `<${tag} ${attrsString} />`.trim();
  }
  const childrenContent = children2.filter((c) => !!c).map((c) => Array.isArray(c) ? c.join("") : c).join("");
  return `<${tag} ${attrsString}>${childrenContent}</${tag}>`.trim();
};
var jsx_default = $jsx;

// node_modules/framework7/components/app/app-class.js
var Framework7 = class _Framework7 extends class_default {
  constructor(params) {
    if (params === void 0) {
      params = {};
    }
    super(params);
    if (_Framework7.instance && typeof window !== "undefined") {
      throw new Error("Framework7 is already initialized and can't be initialized more than once");
    }
    const device = getDevice({
      userAgent: params.userAgent || void 0
    });
    const support2 = getSupport();
    const passedParams = extend2({}, params);
    const app = this;
    app.device = device;
    app.support = support2;
    const w = getWindow();
    const d = getDocument();
    _Framework7.instance = app;
    const defaults = {
      el: "body",
      theme: "auto",
      routes: [],
      name: "Framework7",
      lazyModulesPath: null,
      initOnDeviceReady: true,
      init: true,
      darkMode: void 0,
      iosTranslucentBars: true,
      iosTranslucentModals: true,
      component: void 0,
      componentUrl: void 0,
      userAgent: null,
      url: null,
      colors: {
        primary: "#007aff",
        red: "#ff3b30",
        green: "#4cd964",
        blue: "#2196f3",
        pink: "#ff2d55",
        yellow: "#ffcc00",
        orange: "#ff9500",
        purple: "#9c27b0",
        deeppurple: "#673ab7",
        lightblue: "#5ac8fa",
        teal: "#009688",
        lime: "#cddc39",
        deeporange: "#ff6b22",
        white: "#ffffff",
        black: "#000000"
      }
    };
    app.useModulesParams(defaults);
    app.params = extend2(defaults, params);
    extend2(app, {
      // App Name
      name: app.params.name,
      // Routes
      routes: app.params.routes,
      // Theme
      theme: function getTheme() {
        if (app.params.theme === "auto") {
          if (device.ios) return "ios";
          return "md";
        }
        return app.params.theme;
      }(),
      // Initially passed parameters
      passedParams,
      online: w.navigator.onLine,
      colors: app.params.colors,
      darkMode: app.params.darkMode
    });
    if (params.store) app.params.store = params.store;
    if (app.$el && app.$el[0]) {
      app.$el[0].f7 = app;
    }
    app.useModules();
    app.initStore();
    if (app.params.init) {
      if (device.cordova && app.params.initOnDeviceReady) {
        dom7_default(d).on("deviceready", () => {
          app.init();
        });
      } else {
        app.init();
      }
    }
    return app;
  }
  setColorTheme(color) {
    if (!color) return;
    const app = this;
    app.colors.primary = color;
    app.setColors();
  }
  setColors() {
    const app = this;
    const document2 = getDocument();
    if (!app.colorsStyleEl) {
      app.colorsStyleEl = document2.createElement("style");
      document2.head.prepend(app.colorsStyleEl);
    }
    app.colorsStyleEl.textContent = app.utils.colorThemeCSSStyles(app.colors);
  }
  mount(rootEl) {
    const app = this;
    const window2 = getWindow();
    const document2 = getDocument();
    const $rootEl = dom7_default(rootEl || app.params.el).eq(0);
    app.$el = $rootEl;
    if (app.$el && app.$el[0]) {
      app.el = app.$el[0];
      app.el.f7 = app;
      app.rtl = $rootEl.css("direction") === "rtl";
    }
    const DARK = "(prefers-color-scheme: dark)";
    const LIGHT = "(prefers-color-scheme: light)";
    app.mq = {};
    if (window2.matchMedia) {
      app.mq.dark = window2.matchMedia(DARK);
      app.mq.light = window2.matchMedia(LIGHT);
    }
    app.colorSchemeListener = function colorSchemeListener(_ref) {
      let {
        matches,
        media
      } = _ref;
      if (!matches) {
        return;
      }
      const html2 = document2.querySelector("html");
      if (media === DARK) {
        html2.classList.add("dark");
        app.darkMode = true;
        app.emit("darkModeChange", true);
      } else if (media === LIGHT) {
        html2.classList.remove("dark");
        app.darkMode = false;
        app.emit("darkModeChange", false);
      }
    };
    app.emit("mount");
  }
  initStore() {
    const app = this;
    if (typeof app.params.store !== "undefined" && app.params.store.__store) {
      app.store = app.params.store;
    } else {
      app.store = app.createStore(app.params.store);
    }
  }
  enableAutoDarkMode() {
    const window2 = getWindow();
    const document2 = getDocument();
    if (!window2.matchMedia) return;
    const app = this;
    const html2 = document2.querySelector("html");
    if (app.mq.dark && app.mq.light) {
      app.mq.dark.addEventListener("change", app.colorSchemeListener);
      app.mq.light.addEventListener("change", app.colorSchemeListener);
    }
    if (app.mq.dark && app.mq.dark.matches) {
      html2.classList.add("dark");
      app.darkMode = true;
      app.emit("darkModeChange", true);
    } else if (app.mq.light && app.mq.light.matches) {
      html2.classList.remove("dark");
      app.darkMode = false;
      app.emit("darkModeChange", false);
    }
  }
  disableAutoDarkMode() {
    const window2 = getWindow();
    if (!window2.matchMedia) return;
    const app = this;
    if (app.mq.dark) app.mq.dark.removeEventListener("change", app.colorSchemeListener);
    if (app.mq.light) app.mq.light.removeEventListener("change", app.colorSchemeListener);
  }
  setDarkMode(mode) {
    const app = this;
    if (mode === "auto") {
      app.enableAutoDarkMode();
    } else {
      app.disableAutoDarkMode();
      dom7_default("html")[mode ? "addClass" : "removeClass"]("dark");
      app.darkMode = mode;
    }
  }
  initAppComponent(callback) {
    const app = this;
    app.router.componentLoader(app.params.component, app.params.componentUrl, {
      componentOptions: {
        el: app.$el[0]
      }
    }, (el) => {
      app.$el = dom7_default(el);
      app.$el[0].f7 = app;
      app.$elComponent = el.f7Component;
      app.el = app.$el[0];
      if (callback) callback();
    }, () => {
    });
  }
  init(rootEl) {
    const app = this;
    app.setColors();
    app.mount(rootEl);
    const init = () => {
      if (app.initialized) return;
      app.$el.addClass("framework7-initializing");
      if (app.rtl) {
        dom7_default("html").attr("dir", "rtl");
      }
      if (typeof app.params.darkMode === "undefined") {
        app.darkMode = dom7_default("html").hasClass("dark");
      } else {
        app.setDarkMode(app.params.darkMode);
      }
      const window2 = getWindow();
      window2.addEventListener("offline", () => {
        app.online = false;
        app.emit("offline");
        app.emit("connection", false);
      });
      window2.addEventListener("online", () => {
        app.online = true;
        app.emit("online");
        app.emit("connection", true);
      });
      app.$el.addClass("framework7-root");
      dom7_default("html").removeClass("ios md").addClass(app.theme);
      if (app.params.iosTranslucentBars && app.theme === "ios") {
        dom7_default("html").addClass("ios-translucent-bars");
      }
      if (app.params.iosTranslucentModals && app.theme === "ios") {
        dom7_default("html").addClass("ios-translucent-modals");
      }
      nextFrame(() => {
        app.$el.removeClass("framework7-initializing");
      });
      app.initialized = true;
      app.emit("init");
    };
    if (app.params.component || app.params.componentUrl) {
      app.initAppComponent(() => {
        init();
      });
    } else {
      init();
    }
    return app;
  }
  // eslint-disable-next-line
  loadModule() {
    return _Framework7.loadModule(...arguments);
  }
  // eslint-disable-next-line
  loadModules() {
    return _Framework7.loadModules(...arguments);
  }
  getVnodeHooks(hook, id2) {
    const app = this;
    if (!app.vnodeHooks || !app.vnodeHooks[hook]) return [];
    return app.vnodeHooks[hook][id2] || [];
  }
  // eslint-disable-next-line
  get $() {
    return dom7_default;
  }
  static get Dom7() {
    return dom7_default;
  }
  static get $() {
    return dom7_default;
  }
  static get device() {
    return getDevice();
  }
  static get support() {
    return getSupport();
  }
  static get Class() {
    return class_default;
  }
  static get Events() {
    return events_class_default;
  }
};
Framework7.$jsx = jsx_default;
Framework7.ModalMethods = ModalMethods;
Framework7.ConstructorMethods = ConstructorMethods;
Framework7.loadModule = load_module_default;
Framework7.loadModules = function loadModules(modules) {
  return Promise.all(modules.map((module) => Framework7.loadModule(module)));
};
var app_class_default = Framework7;

// node_modules/framework7/modules/device/device.js
var device_default = {
  name: "device",
  static: {
    getDevice
  },
  on: {
    init() {
      const document2 = getDocument();
      const device = getDevice();
      const classNames = [];
      const html2 = document2.querySelector("html");
      const metaStatusbar = document2.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
      if (!html2) return;
      if (device.standalone && device.ios && metaStatusbar && metaStatusbar.content === "black-translucent") {
        classNames.push("device-full-viewport");
      }
      classNames.push(`device-pixel-ratio-${Math.floor(device.pixelRatio)}`);
      if (device.os && !device.desktop) {
        classNames.push(`device-${device.os}`);
      } else if (device.desktop) {
        classNames.push("device-desktop");
        if (device.os) {
          classNames.push(`device-${device.os}`);
        }
      }
      if (device.cordova) {
        classNames.push("device-cordova");
      }
      if (device.capacitor) {
        classNames.push("device-capacitor");
      }
      classNames.forEach((className) => {
        html2.classList.add(className);
      });
    }
  }
};

// node_modules/framework7/modules/support/support.js
var support_default = {
  name: "support",
  static: {
    getSupport
  }
};

// node_modules/framework7/modules/utils/utils.js
var utils_default = {
  name: "utils",
  proto: {
    utils: utils_exports
  },
  static: {
    utils: utils_exports
  }
};

// node_modules/framework7/modules/resize/resize.js
var resize_default = {
  name: "resize",
  create() {
    const app = this;
    app.getSize = () => {
      if (!app.el) return {
        width: 0,
        height: 0,
        left: 0,
        top: 0
      };
      const offset2 = app.$el.offset();
      const [width2, height2, left, top] = [app.el.offsetWidth, app.el.offsetHeight, offset2.left, offset2.top];
      app.width = width2;
      app.height = height2;
      app.left = left;
      app.top = top;
      return {
        width: width2,
        height: height2,
        left,
        top
      };
    };
  },
  on: {
    init() {
      const app = this;
      const window2 = getWindow();
      app.getSize();
      window2.addEventListener("resize", () => {
        app.emit("resize");
      }, false);
      window2.addEventListener("orientationchange", () => {
        app.emit("orientationchange");
      });
    },
    orientationchange() {
      const document2 = getDocument();
      const device = getDevice();
      if (device.ipad) {
        document2.body.scrollLeft = 0;
        setTimeout(() => {
          document2.body.scrollLeft = 0;
        }, 0);
      }
    },
    resize() {
      const app = this;
      app.getSize();
    }
  }
};

// node_modules/framework7/modules/touch/touch.js
function initTouch() {
  const app = this;
  const device = getDevice();
  const support2 = getSupport();
  const window2 = getWindow();
  const document2 = getDocument();
  const params = app.params.touch;
  const useRipple = params[`${app.theme}TouchRipple`];
  if (device.ios && device.webView) {
    window2.addEventListener("touchstart", () => {
    });
  }
  let touchStartX;
  let touchStartY;
  let targetElement;
  let isMoved;
  let tapHoldFired;
  let tapHoldTimeout;
  let preventClick;
  let activableElement;
  let activeTimeout;
  let rippleWave;
  let rippleTarget;
  let rippleTimeout;
  function findActivableElement(el) {
    const target = dom7_default(el);
    const parents2 = target.parents(params.activeStateElements);
    if (target.closest(".no-active-state").length) {
      return null;
    }
    let activable;
    if (target.is(params.activeStateElements)) {
      activable = target;
    }
    if (parents2.length > 0) {
      activable = activable ? activable.add(parents2) : parents2;
    }
    if (activable && activable.length > 1) {
      const newActivable = [];
      let preventPropagation;
      for (let i = 0; i < activable.length; i += 1) {
        if (!preventPropagation) {
          newActivable.push(activable[i]);
          if (activable.eq(i).hasClass("prevent-active-state-propagation") || activable.eq(i).hasClass("no-active-state-propagation")) {
            preventPropagation = true;
          }
        }
      }
      activable = dom7_default(newActivable);
    }
    return activable || target;
  }
  function isInsideScrollableView(el) {
    const pageContent = el.parents(".page-content");
    return pageContent.length > 0;
  }
  function addActive() {
    if (!activableElement) return;
    activableElement.addClass("active-state");
  }
  function removeActive() {
    if (!activableElement) return;
    activableElement.removeClass("active-state");
    activableElement = null;
  }
  function findRippleElement(el) {
    const rippleElements = params.touchRippleElements;
    const $el = dom7_default(el);
    if ($el.is(rippleElements)) {
      if ($el.hasClass("no-ripple")) {
        return false;
      }
      return $el;
    }
    if ($el.parents(rippleElements).length > 0) {
      const rippleParent = $el.parents(rippleElements).eq(0);
      if (rippleParent.hasClass("no-ripple")) {
        return false;
      }
      return rippleParent;
    }
    return false;
  }
  function createRipple($el, x, y) {
    if (!$el) return;
    rippleWave = app.touchRipple.create(app, $el, x, y);
  }
  function removeRipple() {
    if (!rippleWave) return;
    rippleWave.remove();
    rippleWave = void 0;
    rippleTarget = void 0;
  }
  function rippleTouchStart(el) {
    rippleTarget = findRippleElement(el);
    if (!rippleTarget || rippleTarget.length === 0) {
      rippleTarget = void 0;
      return;
    }
    const inScrollable = isInsideScrollableView(rippleTarget);
    if (!inScrollable) {
      removeRipple();
      createRipple(rippleTarget, touchStartX, touchStartY);
    } else {
      clearTimeout(rippleTimeout);
      rippleTimeout = setTimeout(() => {
        removeRipple();
        createRipple(rippleTarget, touchStartX, touchStartY);
      }, 80);
    }
  }
  function rippleTouchMove() {
    clearTimeout(rippleTimeout);
    removeRipple();
  }
  function rippleTouchEnd() {
    if (!rippleWave && rippleTarget && !isMoved) {
      clearTimeout(rippleTimeout);
      createRipple(rippleTarget, touchStartX, touchStartY);
      setTimeout(removeRipple, 0);
    } else {
      removeRipple();
    }
  }
  function handleMouseDown(e) {
    const $activableEl = findActivableElement(e.target);
    if ($activableEl) {
      $activableEl.addClass("active-state");
      if ("which" in e && e.which === 3) {
        setTimeout(() => {
          dom7_default(".active-state").removeClass("active-state");
        }, 0);
      }
    }
    if (useRipple) {
      touchStartX = e.pageX;
      touchStartY = e.pageY;
      rippleTouchStart(e.target, e.pageX, e.pageY);
    }
  }
  function handleMouseMove() {
    if (!params.activeStateOnMouseMove) {
      dom7_default(".active-state").removeClass("active-state");
    }
    if (useRipple) {
      rippleTouchMove();
    }
  }
  function handleMouseUp() {
    dom7_default(".active-state").removeClass("active-state");
    if (useRipple) {
      rippleTouchEnd();
    }
  }
  function handleTouchCancel() {
    targetElement = null;
    clearTimeout(activeTimeout);
    clearTimeout(tapHoldTimeout);
    if (params.activeState) {
      removeActive();
    }
    if (useRipple) {
      rippleTouchEnd();
    }
  }
  let isScrolling;
  let isSegmentedStrong = false;
  let segmentedStrongEl = null;
  const touchMoveActivableIos = ".dialog-button, .actions-button";
  let isTouchMoveActivable = false;
  let touchmoveActivableEl = null;
  function handleTouchStart(e) {
    if (!e.isTrusted) return true;
    isMoved = false;
    tapHoldFired = false;
    preventClick = false;
    isScrolling = void 0;
    if (e.targetTouches.length > 1) {
      if (activableElement) removeActive();
      return true;
    }
    if (e.touches.length > 1 && activableElement) {
      removeActive();
    }
    if (params.tapHold) {
      if (tapHoldTimeout) clearTimeout(tapHoldTimeout);
      tapHoldTimeout = setTimeout(() => {
        if (e && e.touches && e.touches.length > 1) return;
        tapHoldFired = true;
        e.preventDefault();
        preventClick = true;
        dom7_default(e.target).trigger("taphold", e);
        app.emit("taphold", e);
      }, params.tapHoldDelay);
    }
    targetElement = e.target;
    touchStartX = e.targetTouches[0].pageX;
    touchStartY = e.targetTouches[0].pageY;
    isSegmentedStrong = e.target.closest(".segmented-strong .button-active, .segmented-strong .tab-link-active");
    isTouchMoveActivable = app.theme === "ios" && e.target.closest(touchMoveActivableIos);
    if (isSegmentedStrong) {
      segmentedStrongEl = isSegmentedStrong.closest(".segmented-strong");
    }
    if (params.activeState) {
      activableElement = findActivableElement(targetElement);
      if (activableElement && !isInsideScrollableView(activableElement)) {
        addActive();
      } else if (activableElement) {
        activeTimeout = setTimeout(addActive, 80);
      }
    }
    if (useRipple) {
      rippleTouchStart(targetElement, touchStartX, touchStartY);
    }
    return true;
  }
  function handleTouchMove(e) {
    if (!e.isTrusted) return;
    let touch;
    let distance;
    let shouldRemoveActive = true;
    if (e.type === "touchmove") {
      touch = e.targetTouches[0];
      distance = params.touchClicksDistanceThreshold;
    }
    const touchCurrentX = e.targetTouches[0].pageX;
    const touchCurrentY = e.targetTouches[0].pageY;
    if (typeof isScrolling === "undefined") {
      isScrolling = !!(isScrolling || Math.abs(touchCurrentY - touchStartY) > Math.abs(touchCurrentX - touchStartX));
    }
    if (isTouchMoveActivable || !isScrolling && isSegmentedStrong && segmentedStrongEl) {
      if (e.cancelable) e.preventDefault();
    }
    if (!isScrolling && isSegmentedStrong && segmentedStrongEl) {
      const elementFromPoint = document2.elementFromPoint(e.targetTouches[0].clientX, e.targetTouches[0].clientY);
      const buttonEl = elementFromPoint.closest(".segmented-strong .button:not(.button-active):not(.tab-link-active)");
      if (buttonEl && segmentedStrongEl.contains(buttonEl)) {
        dom7_default(buttonEl).trigger("click", "f7Segmented");
        targetElement = buttonEl;
      }
    }
    if (distance && touch) {
      const pageX = touch.pageX;
      const pageY = touch.pageY;
      if (Math.abs(pageX - touchStartX) > distance || Math.abs(pageY - touchStartY) > distance) {
        isMoved = true;
      }
    } else {
      isMoved = true;
    }
    if (isMoved) {
      preventClick = true;
      if (isTouchMoveActivable) {
        const elementFromPoint = document2.elementFromPoint(e.targetTouches[0].clientX, e.targetTouches[0].clientY);
        touchmoveActivableEl = elementFromPoint.closest(touchMoveActivableIos);
        if (touchmoveActivableEl && activableElement && activableElement[0] === touchmoveActivableEl) {
          shouldRemoveActive = false;
        } else if (touchmoveActivableEl) {
          setTimeout(() => {
            activableElement = findActivableElement(touchmoveActivableEl);
            addActive();
          });
        }
      }
      if (params.tapHold) {
        clearTimeout(tapHoldTimeout);
      }
      if (params.activeState && shouldRemoveActive) {
        clearTimeout(activeTimeout);
        removeActive();
      }
      if (useRipple) {
        rippleTouchMove();
      }
    }
  }
  function handleTouchEnd(e) {
    if (!e.isTrusted) return true;
    isScrolling = void 0;
    isSegmentedStrong = false;
    segmentedStrongEl = null;
    isTouchMoveActivable = false;
    clearTimeout(activeTimeout);
    clearTimeout(tapHoldTimeout);
    if (touchmoveActivableEl) {
      dom7_default(touchmoveActivableEl).trigger("click", "f7TouchMoveActivable");
      touchmoveActivableEl = null;
    }
    if (document2.activeElement === e.target) {
      if (params.activeState) removeActive();
      if (useRipple) {
        rippleTouchEnd();
      }
      return true;
    }
    if (params.activeState) {
      addActive();
      setTimeout(removeActive, 0);
    }
    if (useRipple) {
      rippleTouchEnd();
    }
    if (params.tapHoldPreventClicks && tapHoldFired || preventClick) {
      if (e.cancelable) e.preventDefault();
      preventClick = true;
      return false;
    }
    return true;
  }
  function handleClick(e) {
    const isOverswipe = e && e.detail && e.detail === "f7Overswipe";
    const isSegmented = e && e.detail && e.detail === "f7Segmented";
    const isTouchMoveActivable2 = e && e.detail && e.detail === "f7TouchMoveActivable";
    let localPreventClick = preventClick;
    if (targetElement && e.target !== targetElement) {
      if (isOverswipe || isSegmented || isTouchMoveActivable2) {
        localPreventClick = false;
      } else {
        localPreventClick = true;
      }
    } else if (isTouchMoveActivable2) {
      localPreventClick = false;
    }
    if (params.tapHold && params.tapHoldPreventClicks && tapHoldFired) {
      localPreventClick = true;
    }
    if (localPreventClick) {
      e.stopImmediatePropagation();
      e.stopPropagation();
      e.preventDefault();
    }
    if (params.tapHold) {
      tapHoldTimeout = setTimeout(() => {
        tapHoldFired = false;
      }, device.ios || device.androidChrome ? 100 : 400);
    }
    preventClick = false;
    targetElement = null;
    return !localPreventClick;
  }
  function emitAppTouchEvent(name, e) {
    app.emit({
      events: name,
      data: [e]
    });
  }
  function appClick(e) {
    emitAppTouchEvent("click", e);
  }
  function appTouchStartActive(e) {
    emitAppTouchEvent("touchstart touchstart:active", e);
  }
  function appTouchMoveActive(e) {
    emitAppTouchEvent("touchmove touchmove:active", e);
  }
  function appTouchEndActive(e) {
    emitAppTouchEvent("touchend touchend:active", e);
  }
  function appTouchStartPassive(e) {
    emitAppTouchEvent("touchstart:passive", e);
  }
  function appTouchMovePassive(e) {
    emitAppTouchEvent("touchmove:passive", e);
  }
  function appTouchEndPassive(e) {
    emitAppTouchEvent("touchend:passive", e);
  }
  const passiveListener = support2.passiveListener ? {
    passive: true
  } : false;
  const passiveListenerCapture = support2.passiveListener ? {
    passive: true,
    capture: true
  } : true;
  const activeListener = support2.passiveListener ? {
    passive: false
  } : false;
  const activeListenerCapture = support2.passiveListener ? {
    passive: false,
    capture: true
  } : true;
  document2.addEventListener("click", appClick, true);
  if (support2.passiveListener) {
    document2.addEventListener(app.touchEvents.start, appTouchStartActive, activeListenerCapture);
    document2.addEventListener(app.touchEvents.move, appTouchMoveActive, activeListener);
    document2.addEventListener(app.touchEvents.end, appTouchEndActive, activeListener);
    document2.addEventListener(app.touchEvents.start, appTouchStartPassive, passiveListenerCapture);
    document2.addEventListener(app.touchEvents.move, appTouchMovePassive, passiveListener);
    document2.addEventListener(app.touchEvents.end, appTouchEndPassive, passiveListener);
  } else {
    document2.addEventListener(app.touchEvents.start, (e) => {
      appTouchStartActive(e);
      appTouchStartPassive(e);
    }, true);
    document2.addEventListener(app.touchEvents.move, (e) => {
      appTouchMoveActive(e);
      appTouchMovePassive(e);
    }, false);
    document2.addEventListener(app.touchEvents.end, (e) => {
      appTouchEndActive(e);
      appTouchEndPassive(e);
    }, false);
  }
  if (support2.touch) {
    app.on("click", handleClick);
    app.on("touchstart", handleTouchStart);
    app.on("touchmove", handleTouchMove);
    app.on("touchend", handleTouchEnd);
    document2.addEventListener("touchcancel", handleTouchCancel, {
      passive: true
    });
  } else if (params.activeState) {
    app.on("touchstart", handleMouseDown);
    app.on("touchmove", handleMouseMove);
    app.on("touchend", handleMouseUp);
    document2.addEventListener("pointercancel", handleMouseUp, {
      passive: true
    });
  }
  document2.addEventListener("contextmenu", (e) => {
    if (params.disableContextMenu && (device.ios || device.android || device.cordova || window2.Capacitor && window2.Capacitor.isNative)) {
      e.preventDefault();
    }
    if (useRipple) {
      if (activableElement) removeActive();
      rippleTouchEnd();
    }
  });
}
var touch_default = {
  name: "touch",
  params: {
    touch: {
      // Clicks
      touchClicksDistanceThreshold: 5,
      // ContextMenu
      disableContextMenu: false,
      // Tap Hold
      tapHold: false,
      tapHoldDelay: 750,
      tapHoldPreventClicks: true,
      // Active State
      activeState: true,
      activeStateElements: "a, button, label, span, .actions-button, .stepper-button, .stepper-button-plus, .stepper-button-minus, .card-expandable, .link, .item-link, .accordion-item-toggle",
      activeStateOnMouseMove: false,
      mdTouchRipple: true,
      iosTouchRipple: false,
      touchRippleElements: ".ripple, .link, .item-link, .list label.item-content, .list-button, .links-list a, .button, button, .input-clear-button, .dialog-button, .tab-link, .item-radio, .item-checkbox, .actions-button, .searchbar-disable-button, .fab a, .checkbox, .radio, .data-table .sortable-cell:not(.input-cell), .notification-close-button, .stepper-button, .stepper-button-minus, .stepper-button-plus, .list.accordion-list .accordion-item-toggle",
      touchRippleInsetElements: ".ripple-inset, .icon-only, .searchbar-disable-button, .input-clear-button, .notification-close-button, .md .navbar .link.back"
    }
  },
  create() {
    const app = this;
    const support2 = getSupport();
    extend2(app, {
      touchEvents: {
        start: support2.touch ? "touchstart" : support2.pointerEvents ? "pointerdown" : "mousedown",
        move: support2.touch ? "touchmove" : support2.pointerEvents ? "pointermove" : "mousemove",
        end: support2.touch ? "touchend" : support2.pointerEvents ? "pointerup" : "mouseup"
      }
    });
  },
  on: {
    init: initTouch
  }
};

// node_modules/path-to-regexp/dist.es2015/index.js
function lexer(str) {
  var tokens = [];
  var i = 0;
  while (i < str.length) {
    var char = str[i];
    if (char === "*" || char === "+" || char === "?") {
      tokens.push({ type: "MODIFIER", index: i, value: str[i++] });
      continue;
    }
    if (char === "\\") {
      tokens.push({ type: "ESCAPED_CHAR", index: i++, value: str[i++] });
      continue;
    }
    if (char === "{") {
      tokens.push({ type: "OPEN", index: i, value: str[i++] });
      continue;
    }
    if (char === "}") {
      tokens.push({ type: "CLOSE", index: i, value: str[i++] });
      continue;
    }
    if (char === ":") {
      var name = "";
      var j = i + 1;
      while (j < str.length) {
        var code = str.charCodeAt(j);
        if (
          // `0-9`
          code >= 48 && code <= 57 || // `A-Z`
          code >= 65 && code <= 90 || // `a-z`
          code >= 97 && code <= 122 || // `_`
          code === 95
        ) {
          name += str[j++];
          continue;
        }
        break;
      }
      if (!name)
        throw new TypeError("Missing parameter name at ".concat(i));
      tokens.push({ type: "NAME", index: i, value: name });
      i = j;
      continue;
    }
    if (char === "(") {
      var count = 1;
      var pattern = "";
      var j = i + 1;
      if (str[j] === "?") {
        throw new TypeError('Pattern cannot start with "?" at '.concat(j));
      }
      while (j < str.length) {
        if (str[j] === "\\") {
          pattern += str[j++] + str[j++];
          continue;
        }
        if (str[j] === ")") {
          count--;
          if (count === 0) {
            j++;
            break;
          }
        } else if (str[j] === "(") {
          count++;
          if (str[j + 1] !== "?") {
            throw new TypeError("Capturing groups are not allowed at ".concat(j));
          }
        }
        pattern += str[j++];
      }
      if (count)
        throw new TypeError("Unbalanced pattern at ".concat(i));
      if (!pattern)
        throw new TypeError("Missing pattern at ".concat(i));
      tokens.push({ type: "PATTERN", index: i, value: pattern });
      i = j;
      continue;
    }
    tokens.push({ type: "CHAR", index: i, value: str[i++] });
  }
  tokens.push({ type: "END", index: i, value: "" });
  return tokens;
}
function parse(str, options) {
  if (options === void 0) {
    options = {};
  }
  var tokens = lexer(str);
  var _a = options.prefixes, prefixes = _a === void 0 ? "./" : _a, _b = options.delimiter, delimiter = _b === void 0 ? "/#?" : _b;
  var result = [];
  var key = 0;
  var i = 0;
  var path = "";
  var tryConsume = function(type) {
    if (i < tokens.length && tokens[i].type === type)
      return tokens[i++].value;
  };
  var mustConsume = function(type) {
    var value3 = tryConsume(type);
    if (value3 !== void 0)
      return value3;
    var _a2 = tokens[i], nextType = _a2.type, index2 = _a2.index;
    throw new TypeError("Unexpected ".concat(nextType, " at ").concat(index2, ", expected ").concat(type));
  };
  var consumeText = function() {
    var result2 = "";
    var value3;
    while (value3 = tryConsume("CHAR") || tryConsume("ESCAPED_CHAR")) {
      result2 += value3;
    }
    return result2;
  };
  var isSafe = function(value3) {
    for (var _i = 0, delimiter_1 = delimiter; _i < delimiter_1.length; _i++) {
      var char2 = delimiter_1[_i];
      if (value3.indexOf(char2) > -1)
        return true;
    }
    return false;
  };
  var safePattern = function(prefix2) {
    var prev2 = result[result.length - 1];
    var prevText = prefix2 || (prev2 && typeof prev2 === "string" ? prev2 : "");
    if (prev2 && !prevText) {
      throw new TypeError('Must have text between two parameters, missing text after "'.concat(prev2.name, '"'));
    }
    if (!prevText || isSafe(prevText))
      return "[^".concat(escapeString(delimiter), "]+?");
    return "(?:(?!".concat(escapeString(prevText), ")[^").concat(escapeString(delimiter), "])+?");
  };
  while (i < tokens.length) {
    var char = tryConsume("CHAR");
    var name = tryConsume("NAME");
    var pattern = tryConsume("PATTERN");
    if (name || pattern) {
      var prefix = char || "";
      if (prefixes.indexOf(prefix) === -1) {
        path += prefix;
        prefix = "";
      }
      if (path) {
        result.push(path);
        path = "";
      }
      result.push({
        name: name || key++,
        prefix,
        suffix: "",
        pattern: pattern || safePattern(prefix),
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    var value2 = char || tryConsume("ESCAPED_CHAR");
    if (value2) {
      path += value2;
      continue;
    }
    if (path) {
      result.push(path);
      path = "";
    }
    var open = tryConsume("OPEN");
    if (open) {
      var prefix = consumeText();
      var name_1 = tryConsume("NAME") || "";
      var pattern_1 = tryConsume("PATTERN") || "";
      var suffix = consumeText();
      mustConsume("CLOSE");
      result.push({
        name: name_1 || (pattern_1 ? key++ : ""),
        pattern: name_1 && !pattern_1 ? safePattern(prefix) : pattern_1,
        prefix,
        suffix,
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    mustConsume("END");
  }
  return result;
}
function compile(str, options) {
  return tokensToFunction(parse(str, options), options);
}
function tokensToFunction(tokens, options) {
  if (options === void 0) {
    options = {};
  }
  var reFlags = flags(options);
  var _a = options.encode, encode = _a === void 0 ? function(x) {
    return x;
  } : _a, _b = options.validate, validate = _b === void 0 ? true : _b;
  var matches = tokens.map(function(token) {
    if (typeof token === "object") {
      return new RegExp("^(?:".concat(token.pattern, ")$"), reFlags);
    }
  });
  return function(data2) {
    var path = "";
    for (var i = 0; i < tokens.length; i++) {
      var token = tokens[i];
      if (typeof token === "string") {
        path += token;
        continue;
      }
      var value2 = data2 ? data2[token.name] : void 0;
      var optional = token.modifier === "?" || token.modifier === "*";
      var repeat = token.modifier === "*" || token.modifier === "+";
      if (Array.isArray(value2)) {
        if (!repeat) {
          throw new TypeError('Expected "'.concat(token.name, '" to not repeat, but got an array'));
        }
        if (value2.length === 0) {
          if (optional)
            continue;
          throw new TypeError('Expected "'.concat(token.name, '" to not be empty'));
        }
        for (var j = 0; j < value2.length; j++) {
          var segment = encode(value2[j], token);
          if (validate && !matches[i].test(segment)) {
            throw new TypeError('Expected all "'.concat(token.name, '" to match "').concat(token.pattern, '", but got "').concat(segment, '"'));
          }
          path += token.prefix + segment + token.suffix;
        }
        continue;
      }
      if (typeof value2 === "string" || typeof value2 === "number") {
        var segment = encode(String(value2), token);
        if (validate && !matches[i].test(segment)) {
          throw new TypeError('Expected "'.concat(token.name, '" to match "').concat(token.pattern, '", but got "').concat(segment, '"'));
        }
        path += token.prefix + segment + token.suffix;
        continue;
      }
      if (optional)
        continue;
      var typeOfMessage = repeat ? "an array" : "a string";
      throw new TypeError('Expected "'.concat(token.name, '" to be ').concat(typeOfMessage));
    }
    return path;
  };
}
function escapeString(str) {
  return str.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
}
function flags(options) {
  return options && options.sensitive ? "" : "i";
}
function regexpToRegexp(path, keys) {
  if (!keys)
    return path;
  var groupsRegex = /\((?:\?<(.*?)>)?(?!\?)/g;
  var index2 = 0;
  var execResult = groupsRegex.exec(path.source);
  while (execResult) {
    keys.push({
      // Use parenthesized substring match if available, index otherwise
      name: execResult[1] || index2++,
      prefix: "",
      suffix: "",
      modifier: "",
      pattern: ""
    });
    execResult = groupsRegex.exec(path.source);
  }
  return path;
}
function arrayToRegexp(paths, keys, options) {
  var parts = paths.map(function(path) {
    return pathToRegexp(path, keys, options).source;
  });
  return new RegExp("(?:".concat(parts.join("|"), ")"), flags(options));
}
function stringToRegexp(path, keys, options) {
  return tokensToRegexp(parse(path, options), keys, options);
}
function tokensToRegexp(tokens, keys, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.strict, strict = _a === void 0 ? false : _a, _b = options.start, start = _b === void 0 ? true : _b, _c = options.end, end = _c === void 0 ? true : _c, _d = options.encode, encode = _d === void 0 ? function(x) {
    return x;
  } : _d, _e = options.delimiter, delimiter = _e === void 0 ? "/#?" : _e, _f = options.endsWith, endsWith = _f === void 0 ? "" : _f;
  var endsWithRe = "[".concat(escapeString(endsWith), "]|$");
  var delimiterRe = "[".concat(escapeString(delimiter), "]");
  var route = start ? "^" : "";
  for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {
    var token = tokens_1[_i];
    if (typeof token === "string") {
      route += escapeString(encode(token));
    } else {
      var prefix = escapeString(encode(token.prefix));
      var suffix = escapeString(encode(token.suffix));
      if (token.pattern) {
        if (keys)
          keys.push(token);
        if (prefix || suffix) {
          if (token.modifier === "+" || token.modifier === "*") {
            var mod = token.modifier === "*" ? "?" : "";
            route += "(?:".concat(prefix, "((?:").concat(token.pattern, ")(?:").concat(suffix).concat(prefix, "(?:").concat(token.pattern, "))*)").concat(suffix, ")").concat(mod);
          } else {
            route += "(?:".concat(prefix, "(").concat(token.pattern, ")").concat(suffix, ")").concat(token.modifier);
          }
        } else {
          if (token.modifier === "+" || token.modifier === "*") {
            throw new TypeError('Can not repeat "'.concat(token.name, '" without a prefix and suffix'));
          }
          route += "(".concat(token.pattern, ")").concat(token.modifier);
        }
      } else {
        route += "(?:".concat(prefix).concat(suffix, ")").concat(token.modifier);
      }
    }
  }
  if (end) {
    if (!strict)
      route += "".concat(delimiterRe, "?");
    route += !options.endsWith ? "$" : "(?=".concat(endsWithRe, ")");
  } else {
    var endToken = tokens[tokens.length - 1];
    var isEndDelimited = typeof endToken === "string" ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1 : endToken === void 0;
    if (!strict) {
      route += "(?:".concat(delimiterRe, "(?=").concat(endsWithRe, "))?");
    }
    if (!isEndDelimited) {
      route += "(?=".concat(delimiterRe, "|").concat(endsWithRe, ")");
    }
  }
  return new RegExp(route, flags(options));
}
function pathToRegexp(path, keys, options) {
  if (path instanceof RegExp)
    return regexpToRegexp(path, keys);
  if (Array.isArray(path))
    return arrayToRegexp(path, keys, options);
  return stringToRegexp(path, keys, options);
}

// node_modules/framework7/shared/history.js
var History = {
  queue: [],
  clearQueue() {
    if (History.queue.length === 0) return;
    const currentQueue = History.queue.shift();
    currentQueue();
  },
  routerQueue: [],
  clearRouterQueue() {
    if (History.routerQueue.length === 0) return;
    const currentQueue = History.routerQueue.pop();
    const {
      router,
      stateUrl,
      action
    } = currentQueue;
    let animate2 = router.params.animate;
    if (router.params.browserHistoryAnimate === false) animate2 = false;
    if (action === "back") {
      router.back({
        animate: animate2,
        browserHistory: false
      });
    }
    if (action === "load") {
      router.navigate(stateUrl, {
        animate: animate2,
        browserHistory: false
      });
    }
  },
  handle(e) {
    if (History.blockPopstate) return;
    const app = this;
    let state = e.state;
    History.previousState = History.state;
    History.state = state;
    History.allowChange = true;
    History.clearQueue();
    state = History.state;
    if (!state) state = {};
    app.views.forEach((view) => {
      const router = view.router;
      let viewState = state[view.id];
      if (!viewState && view.params.browserHistory) {
        viewState = {
          url: view.router.history[0]
        };
      }
      if (!viewState) return;
      const stateUrl = viewState.url || void 0;
      let animate2 = router.params.animate;
      if (router.params.browserHistoryAnimate === false) animate2 = false;
      if (stateUrl !== router.url) {
        if (router.history.indexOf(stateUrl) >= 0) {
          if (router.allowPageChange) {
            router.back({
              animate: animate2,
              browserHistory: false
            });
          } else {
            History.routerQueue.push({
              action: "back",
              router
            });
          }
        } else if (router.allowPageChange) {
          router.navigate(stateUrl, {
            animate: animate2,
            browserHistory: false
          });
        } else {
          History.routerQueue.unshift({
            action: "load",
            stateUrl,
            router
          });
        }
      }
    });
  },
  initViewState(viewId, viewState) {
    const window2 = getWindow();
    const newState = extend2({}, History.state || {}, {
      [viewId]: viewState
    });
    History.state = newState;
    window2.history.replaceState(newState, "");
  },
  push(viewId, viewState, url) {
    const window2 = getWindow();
    const document2 = getDocument();
    if (url.substr(-3) === "#!/") {
      url = url.replace("#!/", "");
      if (url === "") {
        url = document2.location.href;
        if (url.includes("#!/")) {
          url = document2.location.href.split("#!/")[0];
        }
      }
    }
    if (!History.allowChange) {
      History.queue.push(() => {
        History.push(viewId, viewState, url);
      });
      return;
    }
    History.previousState = History.state;
    const newState = extend2({}, History.previousState || {}, {
      [viewId]: viewState
    });
    History.state = newState;
    window2.history.pushState(newState, "", url);
  },
  replace(viewId, viewState, url) {
    const window2 = getWindow();
    if (url.substr(-3) === "#!/") {
      url = url.replace("#!/", "");
    }
    if (!History.allowChange) {
      History.queue.push(() => {
        History.replace(viewId, viewState, url);
      });
      return;
    }
    History.previousState = History.state;
    const newState = extend2({}, History.previousState || {}, {
      [viewId]: viewState
    });
    History.state = newState;
    window2.history.replaceState(newState, "", url);
  },
  go(index2) {
    const window2 = getWindow();
    History.allowChange = false;
    window2.history.go(index2);
  },
  back() {
    const window2 = getWindow();
    History.allowChange = false;
    window2.history.back();
  },
  allowChange: true,
  previousState: {},
  state: {},
  blockPopstate: true,
  init(app) {
    const window2 = getWindow();
    const document2 = getDocument();
    History.state = window2.history.state;
    dom7_default(window2).on("load", () => {
      setTimeout(() => {
        History.blockPopstate = false;
      }, 0);
    });
    if (document2.readyState && document2.readyState === "complete") {
      History.blockPopstate = false;
    }
    dom7_default(window2).on("popstate", History.handle.bind(app));
  }
};
var history_default = History;

// node_modules/framework7/modules/router/swipe-back.js
function SwipeBack(r) {
  const router = r;
  const {
    $el,
    $navbarsEl,
    app,
    params
  } = router;
  const support2 = getSupport();
  const device = getDevice();
  let isTouched = false;
  let isMoved = false;
  const touchesStart = {};
  let isScrolling;
  let $currentPageEl = [];
  let $previousPageEl = [];
  let viewContainerWidth;
  let touchesDiff;
  let allowViewTouchMove = true;
  let touchStartTime;
  let $currentNavbarEl = [];
  let $previousNavbarEl = [];
  let dynamicNavbar;
  let $pageShadowEl;
  let $pageOpacityEl;
  let animatableNavEls;
  const paramsSwipeBackAnimateShadow = params[`${app.theme}SwipeBackAnimateShadow`];
  const paramsSwipeBackAnimateOpacity = params[`${app.theme}SwipeBackAnimateOpacity`];
  const paramsSwipeBackActiveArea = params[`${app.theme}SwipeBackActiveArea`];
  const paramsSwipeBackThreshold = params[`${app.theme}SwipeBackThreshold`];
  const transformOrigin = app.rtl ? "right center" : "left center";
  const transformOriginTitleLarge = app.rtl ? "calc(100% - var(--f7-navbar-large-title-padding-left) - var(--f7-safe-area-left)) center" : "calc(var(--f7-navbar-large-title-padding-left) + var(--f7-safe-area-left)) center";
  function animatableNavElements() {
    const els = [];
    const inverter = app.rtl ? -1 : 1;
    const currentNavIsTransparent = $currentNavbarEl.hasClass("navbar-transparent") && !$currentNavbarEl.hasClass("navbar-large") && !$currentNavbarEl.hasClass("navbar-transparent-visible");
    const currentNavIsLarge = $currentNavbarEl.hasClass("navbar-large");
    const currentNavIsCollapsed = $currentNavbarEl.hasClass("navbar-large-collapsed");
    const currentNavIsLargeTransparent = $currentNavbarEl.hasClass("navbar-large-transparent") || $currentNavbarEl.hasClass("navbar-large") && $currentNavbarEl.hasClass("navbar-transparent");
    const previousNavIsTransparent = $previousNavbarEl.hasClass("navbar-transparent") && !$previousNavbarEl.hasClass("navbar-large") && !$previousNavbarEl.hasClass("navbar-transparent-visible");
    const previousNavIsLarge = $previousNavbarEl.hasClass("navbar-large");
    const previousNavIsCollapsed = $previousNavbarEl.hasClass("navbar-large-collapsed");
    const previousNavIsLargeTransparent = $previousNavbarEl.hasClass("navbar-large-transparent") || $previousNavbarEl.hasClass("navbar-large") && $previousNavbarEl.hasClass("navbar-transparent");
    const fromLarge = currentNavIsLarge && !currentNavIsCollapsed;
    const toLarge = previousNavIsLarge && !previousNavIsCollapsed;
    const $currentNavElements = $currentNavbarEl.find(".left, .title, .right, .subnavbar, .fading, .title-large, .navbar-bg");
    const $previousNavElements = $previousNavbarEl.find(".left, .title, .right, .subnavbar, .fading, .title-large, .navbar-bg");
    let activeNavBackIconText;
    let previousNavBackIconText;
    if (params.iosAnimateNavbarBackIcon) {
      if ($currentNavbarEl.hasClass("sliding") || $currentNavbarEl.find(".navbar-inner.sliding").length) {
        activeNavBackIconText = $currentNavbarEl.find(".left").find(".back .icon + span").eq(0);
      } else {
        activeNavBackIconText = $currentNavbarEl.find(".left.sliding").find(".back .icon + span").eq(0);
      }
      if ($previousNavbarEl.hasClass("sliding") || $previousNavbarEl.find(".navbar-inner.sliding").length) {
        previousNavBackIconText = $previousNavbarEl.find(".left").find(".back .icon + span").eq(0);
      } else {
        previousNavBackIconText = $previousNavbarEl.find(".left.sliding").find(".back .icon + span").eq(0);
      }
      if (activeNavBackIconText.length) {
        $previousNavElements.each((el) => {
          if (!dom7_default(el).hasClass("title")) return;
          el.f7NavbarLeftOffset += activeNavBackIconText.prev(".icon")[0].offsetWidth;
        });
      }
    }
    $currentNavElements.each((navEl) => {
      const $navEl = dom7_default(navEl);
      const isSubnavbar = $navEl.hasClass("subnavbar");
      const isLeft = $navEl.hasClass("left");
      const isTitle = $navEl.hasClass("title");
      const isBg = $navEl.hasClass("navbar-bg");
      if ((isTitle || isBg) && currentNavIsTransparent) return;
      if (!fromLarge && $navEl.hasClass(".title-large")) return;
      const el = {
        el: navEl
      };
      if (fromLarge) {
        if (isTitle) return;
        if ($navEl.hasClass("title-large")) {
          if (els.indexOf(el) < 0) els.push(el);
          el.overflow = "visible";
          $navEl.find(".title-large-text").each((subNavEl) => {
            els.push({
              el: subNavEl,
              transform: (progress) => `translateX(${progress * 100 * inverter}%)`
            });
          });
          return;
        }
      }
      if (toLarge) {
        if (!fromLarge) {
          if ($navEl.hasClass("title-large")) {
            if (els.indexOf(el) < 0) els.push(el);
            el.opacity = 0;
          }
        }
        if (isLeft) {
          if (els.indexOf(el) < 0) els.push(el);
          el.opacity = (progress) => 1 - progress ** 0.33;
          $navEl.find(".back span").each((subNavEl) => {
            els.push({
              el: subNavEl,
              "transform-origin": transformOrigin,
              transform: (progress) => `translateX(calc(${progress} * (var(--f7-navbarTitleLargeOffset) - var(--f7-navbarLeftTextOffset)))) translateY(calc(${progress} * (var(--f7-navbar-large-title-height) - var(--f7-navbar-large-title-padding-vertical) / 2))) scale(${1 + 1 * progress})`
            });
          });
          return;
        }
      }
      if (isBg) {
        if (els.indexOf(el) < 0) els.push(el);
        if (!fromLarge && !toLarge) {
          if (currentNavIsCollapsed) {
            if (currentNavIsLargeTransparent) {
              el.className = "ios-swipeback-navbar-bg-large";
            }
            el.transform = (progress) => `translateX(${100 * progress * inverter}%) translateY(calc(-1 * var(--f7-navbar-large-title-height)))`;
          } else {
            el.transform = (progress) => `translateX(${100 * progress * inverter}%)`;
          }
        }
        if (!fromLarge && toLarge) {
          el.className = "ios-swipeback-navbar-bg-large";
          el.transform = (progress) => `translateX(${100 * progress * inverter}%) translateY(calc(-1 * ${1 - progress} * var(--f7-navbar-large-title-height)))`;
        }
        if (fromLarge && toLarge) {
          el.transform = (progress) => `translateX(${100 * progress * inverter}%)`;
        }
        if (fromLarge && !toLarge) {
          el.transform = (progress) => `translateX(${100 * progress * inverter}%) translateY(calc(-${progress} * var(--f7-navbar-large-title-height)))`;
        }
        return;
      }
      if ($navEl.hasClass("title-large")) return;
      const isSliding = $navEl.hasClass("sliding") || $navEl.parents(".navbar-inner.sliding").length;
      if (els.indexOf(el) < 0) els.push(el);
      if (!isSubnavbar || isSubnavbar && !isSliding) {
        el.opacity = (progress) => 1 - progress ** 0.33;
      }
      if (isSliding) {
        let transformTarget = el;
        if (isLeft && activeNavBackIconText.length && params.iosAnimateNavbarBackIcon) {
          const textEl = {
            el: activeNavBackIconText[0]
          };
          transformTarget = textEl;
          els.push(textEl);
        }
        transformTarget.transform = (progress) => {
          let activeNavTranslate = progress * transformTarget.el.f7NavbarRightOffset;
          if (device.pixelRatio === 1) activeNavTranslate = Math.round(activeNavTranslate);
          if (isSubnavbar && currentNavIsLarge) {
            return `translate3d(${activeNavTranslate}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`;
          }
          return `translate3d(${activeNavTranslate}px,0,0)`;
        };
      }
    });
    $previousNavElements.each((navEl) => {
      const $navEl = dom7_default(navEl);
      const isSubnavbar = $navEl.hasClass("subnavbar");
      const isLeft = $navEl.hasClass("left");
      const isTitle = $navEl.hasClass("title");
      const isBg = $navEl.hasClass("navbar-bg");
      if ((isTitle || isBg) && previousNavIsTransparent) return;
      const el = {
        el: navEl
      };
      if (toLarge) {
        if (isTitle) return;
        if (els.indexOf(el) < 0) els.push(el);
        if ($navEl.hasClass("title-large")) {
          el.opacity = 1;
          el.overflow = "visible";
          $navEl.find(".title-large-text").each((subNavEl) => {
            els.push({
              el: subNavEl,
              "transform-origin": transformOriginTitleLarge,
              opacity: (progress) => progress ** 3,
              transform: (progress) => `translateX(calc(${1 - progress} * (var(--f7-navbarLeftTextOffset) - var(--f7-navbarTitleLargeOffset)))) translateY(calc(${progress - 1} * var(--f7-navbar-large-title-height) + ${1 - progress} * var(--f7-navbar-large-title-padding-vertical))) scale(${0.5 + progress * 0.5})`
            });
          });
          return;
        }
      }
      if (isBg) {
        if (els.indexOf(el) < 0) els.push(el);
        if (!fromLarge && !toLarge) {
          if (previousNavIsCollapsed) {
            if (previousNavIsLargeTransparent) {
              el.className = "ios-swipeback-navbar-bg-large";
            }
            el.transform = (progress) => `translateX(${(-100 + 100 * progress) * inverter}%) translateY(calc(-1 * var(--f7-navbar-large-title-height)))`;
          } else {
            el.transform = (progress) => `translateX(${(-100 + 100 * progress) * inverter}%)`;
          }
        }
        if (!fromLarge && toLarge) {
          el.transform = (progress) => `translateX(${(-100 + 100 * progress) * inverter}%) translateY(calc(-1 * ${1 - progress} * var(--f7-navbar-large-title-height)))`;
        }
        if (fromLarge && !toLarge) {
          el.className = "ios-swipeback-navbar-bg-large";
          el.transform = (progress) => `translateX(${(-100 + 100 * progress) * inverter}%) translateY(calc(-${progress} * var(--f7-navbar-large-title-height)))`;
        }
        if (fromLarge && toLarge) {
          el.transform = (progress) => `translateX(${(-100 + 100 * progress) * inverter}%)`;
        }
        return;
      }
      if ($navEl.hasClass("title-large")) return;
      const isSliding = $navEl.hasClass("sliding") || $previousNavbarEl.children(".navbar-inner.sliding").length;
      if (els.indexOf(el) < 0) els.push(el);
      if (!isSubnavbar || isSubnavbar && !isSliding) {
        el.opacity = (progress) => progress ** 3;
      }
      if (isSliding) {
        let transformTarget = el;
        if (isLeft && previousNavBackIconText.length && params.iosAnimateNavbarBackIcon) {
          const textEl = {
            el: previousNavBackIconText[0]
          };
          transformTarget = textEl;
          els.push(textEl);
        }
        transformTarget.transform = (progress) => {
          let previousNavTranslate = transformTarget.el.f7NavbarLeftOffset * (1 - progress);
          if (device.pixelRatio === 1) previousNavTranslate = Math.round(previousNavTranslate);
          if (isSubnavbar && previousNavIsLarge) {
            return `translate3d(${previousNavTranslate}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`;
          }
          return `translate3d(${previousNavTranslate}px,0,0)`;
        };
      }
    });
    return els;
  }
  function setAnimatableNavElements(_temp) {
    let {
      progress,
      reset,
      transition: transition2,
      reflow
    } = _temp === void 0 ? {} : _temp;
    const styles2 = ["overflow", "transform", "transform-origin", "opacity"];
    if (transition2 === true || transition2 === false) {
      for (let i = 0; i < animatableNavEls.length; i += 1) {
        const el = animatableNavEls[i];
        if (el && el.el) {
          if (transition2 === true) el.el.classList.add("navbar-page-transitioning");
          if (transition2 === false) el.el.classList.remove("navbar-page-transitioning");
        }
      }
    }
    if (reflow && animatableNavEls.length && animatableNavEls[0] && animatableNavEls[0].el) {
      animatableNavEls[0].el._clientLeft = animatableNavEls[0].el.clientLeft;
    }
    for (let i = 0; i < animatableNavEls.length; i += 1) {
      const el = animatableNavEls[i];
      if (el && el.el) {
        if (el.className && !el.classNameSet && !reset) {
          el.el.classList.add(el.className);
          el.classNameSet = true;
        }
        if (el.className && reset) {
          el.el.classList.remove(el.className);
        }
        for (let j = 0; j < styles2.length; j += 1) {
          const styleProp = styles2[j];
          if (el[styleProp]) {
            if (reset) {
              el.el.style[styleProp] = "";
            } else if (typeof el[styleProp] === "function") {
              el.el.style[styleProp] = el[styleProp](progress);
            } else {
              el.el.style[styleProp] = el[styleProp];
            }
          }
        }
      }
    }
  }
  function handleTouchStart(e) {
    if (!e.isTrusted) return;
    const swipeBackEnabled = params[`${app.theme}SwipeBack`];
    if (!allowViewTouchMove || !swipeBackEnabled || isTouched || app.swipeout && app.swipeout.el || !router.allowPageChange) return;
    if (dom7_default(e.target).closest(".range-slider, .calendar-months").length > 0) return;
    if (dom7_default(e.target).closest(".page-master, .page-master-detail").length > 0 && params.masterDetailBreakpoint > 0 && app.width >= params.masterDetailBreakpoint) return;
    isMoved = false;
    isTouched = true;
    isScrolling = void 0;
    touchesStart.x = e.type === "touchstart" ? e.targetTouches[0].pageX : e.pageX;
    touchesStart.y = e.type === "touchstart" ? e.targetTouches[0].pageY : e.pageY;
    touchStartTime = now();
    dynamicNavbar = router.dynamicNavbar;
  }
  function handleTouchMove(e) {
    if (!e.isTrusted) return;
    if (!isTouched) return;
    const pageX = e.type === "touchmove" ? e.targetTouches[0].pageX : e.pageX;
    const pageY = e.type === "touchmove" ? e.targetTouches[0].pageY : e.pageY;
    if (typeof isScrolling === "undefined") {
      isScrolling = !!(isScrolling || Math.abs(pageY - touchesStart.y) > Math.abs(pageX - touchesStart.x)) || pageX < touchesStart.x && !app.rtl || pageX > touchesStart.x && app.rtl;
    }
    if (isScrolling || e.f7PreventSwipeBack || app.preventSwipeBack) {
      isTouched = false;
      return;
    }
    if (!isMoved) {
      let cancel = false;
      const target = dom7_default(e.target);
      const swipeout = target.closest(".swipeout");
      if (swipeout.length > 0) {
        if (!app.rtl && swipeout.find(".swipeout-actions-left").length > 0) cancel = true;
        if (app.rtl && swipeout.find(".swipeout-actions-right").length > 0) cancel = true;
      }
      $currentPageEl = target.closest(".page");
      if ($currentPageEl.hasClass("no-swipeback") || target.closest(".no-swipeback, .card-opened").length > 0) cancel = true;
      $previousPageEl = $el.find(".page-previous");
      if ($previousPageEl.length > 1) {
        $previousPageEl = $previousPageEl.eq($previousPageEl.length - 1);
      }
      let notFromBorder = touchesStart.x - $el.offset().left > paramsSwipeBackActiveArea;
      viewContainerWidth = $el.width();
      if (app.rtl) {
        notFromBorder = touchesStart.x < $el.offset().left - $el[0].scrollLeft + (viewContainerWidth - paramsSwipeBackActiveArea);
      } else {
        notFromBorder = touchesStart.x - $el.offset().left > paramsSwipeBackActiveArea;
      }
      if (notFromBorder) cancel = true;
      if ($previousPageEl.length === 0 || $currentPageEl.length === 0) cancel = true;
      if (cancel) {
        isTouched = false;
        return;
      }
      if (paramsSwipeBackAnimateShadow) {
        $pageShadowEl = $currentPageEl.find(".page-shadow-effect");
        if ($pageShadowEl.length === 0) {
          $pageShadowEl = dom7_default('<div class="page-shadow-effect"></div>');
          $currentPageEl.append($pageShadowEl);
        }
      }
      if (paramsSwipeBackAnimateOpacity) {
        $pageOpacityEl = $previousPageEl.find(".page-opacity-effect");
        if ($pageOpacityEl.length === 0) {
          $pageOpacityEl = dom7_default('<div class="page-opacity-effect"></div>');
          $previousPageEl.append($pageOpacityEl);
        }
      }
      if (dynamicNavbar) {
        $currentNavbarEl = $navbarsEl.find(".navbar-current");
        $previousNavbarEl = $navbarsEl.find(".navbar-previous");
        if ($previousNavbarEl.length > 1) {
          $previousNavbarEl = $previousNavbarEl.eq($previousNavbarEl.length - 1);
        }
        animatableNavEls = animatableNavElements($previousNavbarEl, $currentNavbarEl);
      }
      if (dom7_default(".sheet.modal-in").length > 0 && app.sheet) {
        app.sheet.close(dom7_default(".sheet.modal-in"));
      }
    }
    e.f7PreventSwipePanel = true;
    isMoved = true;
    app.preventSwipePanelBySwipeBack = true;
    e.preventDefault();
    const inverter = app.rtl ? -1 : 1;
    touchesDiff = (pageX - touchesStart.x - paramsSwipeBackThreshold) * inverter;
    if (touchesDiff < 0) touchesDiff = 0;
    const percentage = Math.min(Math.max(touchesDiff / viewContainerWidth, 0), 1);
    const callbackData = {
      percentage,
      progress: percentage,
      currentPageEl: $currentPageEl[0],
      previousPageEl: $previousPageEl[0],
      currentNavbarEl: $currentNavbarEl[0],
      previousNavbarEl: $previousNavbarEl[0]
    };
    $el.trigger("swipeback:move", callbackData);
    router.emit("swipebackMove", callbackData);
    let currentPageTranslate = touchesDiff * inverter;
    let previousPageTranslate = (touchesDiff / 5 - viewContainerWidth / 5) * inverter;
    if (!app.rtl) {
      currentPageTranslate = Math.min(currentPageTranslate, viewContainerWidth);
      previousPageTranslate = Math.min(previousPageTranslate, 0);
    } else {
      currentPageTranslate = Math.max(currentPageTranslate, -viewContainerWidth);
      previousPageTranslate = Math.max(previousPageTranslate, 0);
    }
    if (device.pixelRatio === 1) {
      currentPageTranslate = Math.round(currentPageTranslate);
      previousPageTranslate = Math.round(previousPageTranslate);
    }
    router.swipeBackActive = true;
    dom7_default([$currentPageEl[0], $previousPageEl[0]]).addClass("page-swipeback-active");
    $currentPageEl.transform(`translate3d(${currentPageTranslate}px,0,0)`);
    if (paramsSwipeBackAnimateShadow) $pageShadowEl[0].style.opacity = 1 - 1 * percentage;
    if (app.theme === "ios") {
      $previousPageEl.transform(`translate3d(${previousPageTranslate}px,0,0)`);
    }
    if (paramsSwipeBackAnimateOpacity) $pageOpacityEl[0].style.opacity = 1 - 1 * percentage;
    if (!dynamicNavbar) return;
    setAnimatableNavElements({
      progress: percentage
    });
  }
  function handleTouchEnd(e) {
    if (!e.isTrusted) return;
    app.preventSwipePanelBySwipeBack = false;
    if (!isTouched || !isMoved) {
      isTouched = false;
      isMoved = false;
      return;
    }
    isTouched = false;
    isMoved = false;
    router.swipeBackActive = false;
    const $pages = dom7_default([$currentPageEl[0], $previousPageEl[0]]);
    $pages.removeClass("page-swipeback-active");
    if (touchesDiff === 0) {
      $pages.transform("");
      if ($pageShadowEl && $pageShadowEl.length > 0) $pageShadowEl.remove();
      if ($pageOpacityEl && $pageOpacityEl.length > 0) $pageOpacityEl.remove();
      if (dynamicNavbar) {
        setAnimatableNavElements({
          reset: true
        });
      }
      return;
    }
    const timeDiff = now() - touchStartTime;
    let pageChanged = false;
    if (timeDiff < 300 && touchesDiff > 10 || timeDiff >= 300 && touchesDiff > viewContainerWidth / 2) {
      $currentPageEl.removeClass("page-current").addClass(`page-next${app.theme !== "ios" ? " page-next-on-right" : ""}`);
      $previousPageEl.removeClass("page-previous").addClass("page-current").removeAttr("aria-hidden");
      if ($pageShadowEl) $pageShadowEl[0].style.opacity = "";
      if ($pageOpacityEl) $pageOpacityEl[0].style.opacity = "";
      if (dynamicNavbar) {
        router.setNavbarPosition($currentNavbarEl, "next");
        router.setNavbarPosition($previousNavbarEl, "current", false);
      }
      pageChanged = true;
    }
    $pages.addClass("page-transitioning page-transitioning-swipeback");
    if (device.ios) {
      $currentPageEl[0]._clientLeft = $currentPageEl[0].clientLeft;
    }
    $pages.transform("");
    if (dynamicNavbar) {
      setAnimatableNavElements({
        progress: pageChanged ? 1 : 0,
        transition: true,
        reflow: !!device.ios
      });
    }
    allowViewTouchMove = false;
    router.allowPageChange = false;
    const callbackData = {
      currentPageEl: $currentPageEl[0],
      previousPageEl: $previousPageEl[0],
      currentNavbarEl: $currentNavbarEl[0],
      previousNavbarEl: $previousNavbarEl[0]
    };
    if (pageChanged) {
      router.currentRoute = $previousPageEl[0].f7Page.route;
      router.currentPage = $previousPageEl[0];
      router.pageCallback("beforeOut", $currentPageEl, $currentNavbarEl, "current", "next", {
        route: $currentPageEl[0].f7Page.route,
        swipeBack: true
      });
      router.pageCallback("beforeIn", $previousPageEl, $previousNavbarEl, "previous", "current", {
        route: $previousPageEl[0].f7Page.route,
        swipeBack: true
      }, $currentPageEl[0]);
      $el.trigger("swipeback:beforechange", callbackData);
      router.emit("swipebackBeforeChange", callbackData);
    } else {
      $el.trigger("swipeback:beforereset", callbackData);
      router.emit("swipebackBeforeReset", callbackData);
    }
    $currentPageEl.transitionEnd(() => {
      $pages.removeClass("page-transitioning page-transitioning-swipeback");
      if (dynamicNavbar) {
        setAnimatableNavElements({
          reset: true,
          transition: false
        });
      }
      allowViewTouchMove = true;
      router.allowPageChange = true;
      if (pageChanged) {
        if (router.history.length === 1) {
          router.history.unshift(router.url);
        }
        router.history.pop();
        router.saveHistory();
        if (params.browserHistory) {
          history_default.back();
        }
        router.pageCallback("afterOut", $currentPageEl, $currentNavbarEl, "current", "next", {
          route: $currentPageEl[0].f7Page.route,
          swipeBack: true
        });
        router.pageCallback("afterIn", $previousPageEl, $previousNavbarEl, "previous", "current", {
          route: $previousPageEl[0].f7Page.route,
          swipeBack: true
        });
        router.pageCallback("beforeRemove", $currentPageEl, $currentNavbarEl, "next", {
          swipeBack: true
        });
        router.removePage($currentPageEl);
        if (dynamicNavbar) {
          router.removeNavbar($currentNavbarEl);
        }
        $el.trigger("swipeback:afterchange", callbackData);
        router.emit("swipebackAfterChange", callbackData);
        router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
        if (params.preloadPreviousPage) {
          router.back(router.history[router.history.length - 2], {
            preload: true
          });
        }
      } else {
        $el.trigger("swipeback:afterreset", callbackData);
        router.emit("swipebackAfterReset", callbackData);
      }
      if ($pageShadowEl && $pageShadowEl.length > 0) $pageShadowEl.remove();
      if ($pageOpacityEl && $pageOpacityEl.length > 0) $pageOpacityEl.remove();
    });
  }
  function attachEvents() {
    const passiveListener = app.touchEvents.start === "touchstart" && support2.passiveListener ? {
      passive: true,
      capture: false
    } : false;
    $el.on(app.touchEvents.start, handleTouchStart, passiveListener);
    app.on("touchmove:active", handleTouchMove);
    app.on("touchend:passive", handleTouchEnd);
  }
  function detachEvents() {
    const passiveListener = app.touchEvents.start === "touchstart" && support2.passiveListener ? {
      passive: true,
      capture: false
    } : false;
    $el.off(app.touchEvents.start, handleTouchStart, passiveListener);
    app.off("touchmove:active", handleTouchMove);
    app.off("touchend:passive", handleTouchEnd);
  }
  attachEvents();
  router.on("routerDestroy", detachEvents);
}
var swipe_back_default = SwipeBack;

// node_modules/framework7/modules/router/redirect.js
function redirect(direction, route, options) {
  const router = this;
  const r = route.route.redirect;
  const method = direction === "forward" ? "navigate" : "back";
  if (options.initial && router.params.browserHistory) {
    options.replaceState = true;
    options.history = true;
  }
  function redirectResolve(redirectUrl, redirectOptions) {
    if (redirectOptions === void 0) {
      redirectOptions = {};
    }
    router.allowPageChange = true;
    router[method](redirectUrl, extend2({}, options, redirectOptions));
  }
  function redirectReject() {
    router.allowPageChange = true;
  }
  if (typeof r === "function") {
    router.allowPageChange = false;
    const redirectUrl = r.call(router, {
      router,
      to: route,
      resolve: redirectResolve,
      reject: redirectReject,
      direction,
      app: router.app
    });
    if (redirectUrl && typeof redirectUrl === "string") {
      router.allowPageChange = true;
      return router[method](redirectUrl, options);
    }
    return router;
  }
  return router[method](r, options);
}

// node_modules/framework7/modules/router/process-route-queue.js
function processQueue(router, routerQueue, routeQueue, to, from, resolve, reject, direction) {
  const queue = [];
  if (Array.isArray(routeQueue)) {
    queue.push(...routeQueue);
  } else if (routeQueue && typeof routeQueue === "function") {
    queue.push(routeQueue);
  }
  if (routerQueue) {
    if (Array.isArray(routerQueue)) {
      queue.push(...routerQueue);
    } else {
      queue.push(routerQueue);
    }
  }
  function next2() {
    if (queue.length === 0) {
      resolve();
      return;
    }
    const queueItem = queue.shift();
    queueItem.call(router, {
      router,
      to,
      from,
      resolve() {
        next2();
      },
      reject() {
        reject();
      },
      direction,
      app: router.app
    });
  }
  next2();
}
function processRouteQueue(to, from, resolve, reject, direction) {
  const router = this;
  function enterNextRoute() {
    if (to && to.route && (router.params.routesBeforeEnter || to.route.beforeEnter)) {
      router.allowPageChange = false;
      processQueue(router, router.params.routesBeforeEnter, to.route.beforeEnter, to, from, () => {
        router.allowPageChange = true;
        resolve();
      }, () => {
        reject();
      }, direction);
    } else {
      resolve();
    }
  }
  function leaveCurrentRoute() {
    if (from && from.route && (router.params.routesBeforeLeave || from.route.beforeLeave)) {
      router.allowPageChange = false;
      processQueue(router, router.params.routesBeforeLeave, from.route.beforeLeave, to, from, () => {
        router.allowPageChange = true;
        enterNextRoute();
      }, () => {
        reject();
      }, direction);
    } else {
      enterNextRoute();
    }
  }
  leaveCurrentRoute();
}

// node_modules/framework7/modules/router/app-router-check.js
function appRouterCheck(router, method) {
  if (!router.view) {
    throw new Error(`Framework7: it is not allowed to use router methods on global app router. Use router methods only on related View, e.g. app.views.main.router.${method}(...)`);
  }
}

// node_modules/framework7/modules/router/async-component.js
function asyncComponent(router, component, resolve, reject) {
  function resolvePromise(componentPromise) {
    componentPromise.then((c) => {
      resolve({
        component: c.default || c._default || c
      });
    }).catch((err) => {
      reject();
      throw new Error(err, {
        cause: err
      });
    });
  }
  if (component instanceof Promise) {
    resolvePromise(component);
    return;
  }
  const asyncComponentResult = component.call(router);
  if (asyncComponentResult instanceof Promise) {
    resolvePromise(asyncComponentResult);
  } else {
    resolve({
      component: asyncComponentResult
    });
  }
}

// node_modules/framework7/modules/router/navigate.js
function refreshPage(props) {
  if (props === void 0) {
    props = {};
  }
  const router = this;
  appRouterCheck(router, "refreshPage");
  return router.navigate(router.currentRoute.url, {
    ignoreCache: true,
    reloadCurrent: true,
    props
  });
}
function forward(router, el, forwardOptions) {
  if (forwardOptions === void 0) {
    forwardOptions = {};
  }
  const document2 = getDocument();
  const $el = dom7_default(el);
  const app = router.app;
  const view = router.view;
  const options = extend2(false, {
    animate: router.params.animate,
    browserHistory: true,
    replaceState: false,
    history: true,
    reloadCurrent: router.params.reloadPages,
    reloadPrevious: false,
    reloadAll: false,
    clearPreviousHistory: false,
    reloadDetail: router.params.reloadDetail,
    on: {}
  }, forwardOptions);
  const masterDetailEnabled = router.params.masterDetailBreakpoint > 0;
  const isMaster = masterDetailEnabled && options.route && options.route.route && (options.route.route.master === true || typeof options.route.route.master === "function" && options.route.route.master(app, router));
  let masterPageEl;
  let otherDetailPageEl;
  let detailsInBetweenRemoved = 0;
  let currentRouteIsModal = router.currentRoute.modal;
  let modalType;
  if (!currentRouteIsModal) {
    "popup popover sheet loginScreen actions customModal panel".split(" ").forEach((modalLoadProp) => {
      if (router.currentRoute && router.currentRoute.route && router.currentRoute.route[modalLoadProp]) {
        currentRouteIsModal = true;
        modalType = modalLoadProp;
      }
    });
  }
  if (currentRouteIsModal) {
    const modalToClose = router.currentRoute.modal || router.currentRoute.route.modalInstance || app[modalType].get();
    const previousUrl = router.history[router.history.length - 2];
    let previousRoute = router.findMatchingRoute(previousUrl);
    if (!previousRoute && previousUrl) {
      previousRoute = {
        url: previousUrl,
        path: previousUrl.split("?")[0],
        query: parseUrlQuery(previousUrl),
        route: {
          path: previousUrl.split("?")[0],
          url: previousUrl
        }
      };
    }
    router.modalRemove(modalToClose);
  }
  const dynamicNavbar = router.dynamicNavbar;
  const $viewEl = router.$el;
  const $newPage = $el;
  const reload = options.reloadPrevious || options.reloadCurrent || options.reloadAll;
  let $oldPage;
  let $navbarsEl;
  let $newNavbarEl;
  let $oldNavbarEl;
  router.allowPageChange = false;
  if ($newPage.length === 0) {
    router.allowPageChange = true;
    return router;
  }
  if ($newPage.length) {
    router.removeThemeElements($newPage);
  }
  if (dynamicNavbar) {
    $newNavbarEl = $newPage.children(".navbar");
    $navbarsEl = router.$navbarsEl;
    if ($newNavbarEl.length === 0 && $newPage[0] && $newPage[0].f7Page) {
      $newNavbarEl = $newPage[0].f7Page.$navbarEl;
    }
  }
  if (options.route && options.route.route && options.route.route.keepAlive && !options.route.route.keepAliveData) {
    options.route.route.keepAliveData = {
      pageEl: $el[0]
    };
  }
  const $pagesInView = $viewEl.children(".page").filter((pageInView) => pageInView !== $newPage[0]);
  let $navbarsInView;
  if (dynamicNavbar) {
    $navbarsInView = $navbarsEl.children(".navbar").filter((navbarInView) => navbarInView !== $newNavbarEl[0]);
  }
  if (options.reloadPrevious && $pagesInView.length < 2) {
    router.allowPageChange = true;
    return router;
  }
  let isDetail;
  let reloadDetail;
  let isDetailRoot;
  if (masterDetailEnabled && !options.reloadAll) {
    for (let i = 0; i < $pagesInView.length; i += 1) {
      if (!masterPageEl && $pagesInView[i].classList.contains("page-master")) {
        masterPageEl = $pagesInView[i];
        continue;
      }
    }
    isDetail = !isMaster && masterPageEl;
    if (isDetail) {
      if (masterPageEl) {
        for (let i = 0; i < $pagesInView.length; i += 1) {
          if ($pagesInView[i].classList.contains("page-master-detail")) {
            otherDetailPageEl = $pagesInView[i];
            continue;
          }
        }
      }
    }
    reloadDetail = isDetail && options.reloadDetail && app.width >= router.params.masterDetailBreakpoint && masterPageEl;
  }
  if (isDetail) {
    isDetailRoot = !otherDetailPageEl || reloadDetail || options.reloadAll || options.reloadCurrent;
  }
  let newPagePosition = "next";
  if (options.reloadCurrent || options.reloadAll || reloadDetail) {
    newPagePosition = "current";
  } else if (options.reloadPrevious) {
    newPagePosition = "previous";
  }
  $newPage.removeClass("page-previous page-current page-next").addClass(`page-${newPagePosition}${isMaster ? " page-master" : ""}${isDetail ? " page-master-detail" : ""}${isDetailRoot ? " page-master-detail-root" : ""}`).trigger("page:unstack").trigger("page:position", {
    position: newPagePosition
  });
  router.emit("pageUnstack", $newPage[0]);
  router.emit("pagePosition", $newPage[0], newPagePosition);
  if (isMaster || isDetail) {
    $newPage.trigger("page:role", {
      role: isMaster ? "master" : "detail",
      root: !!isDetailRoot
    });
    router.emit("pageRole", $newPage[0], {
      role: isMaster ? "master" : "detail",
      detailRoot: !!isDetailRoot
    });
  }
  if (dynamicNavbar && $newNavbarEl.length) {
    $newNavbarEl.removeClass("navbar-previous navbar-current navbar-next").addClass(`navbar-${newPagePosition}${isMaster ? " navbar-master" : ""}${isDetail ? " navbar-master-detail" : ""}${isDetailRoot ? " navbar-master-detail-root" : ""}`);
    $newNavbarEl.trigger("navbar:position", {
      position: newPagePosition
    });
    router.emit("navbarPosition", $newNavbarEl[0], newPagePosition);
    if (isMaster || isDetail) {
      router.emit("navbarRole", $newNavbarEl[0], {
        role: isMaster ? "master" : "detail",
        detailRoot: !!isDetailRoot
      });
    }
  }
  if (options.reloadCurrent || reloadDetail) {
    if (reloadDetail) {
      $oldPage = $pagesInView.filter((pageEl) => !pageEl.classList.contains("page-master"));
      if (dynamicNavbar) {
        $oldNavbarEl = dom7_default($oldPage.map((pageEl) => app.navbar.getElByPage(pageEl)));
      }
      if ($oldPage.length > 1 && masterPageEl) {
        detailsInBetweenRemoved = $oldPage.length - 1;
        dom7_default(masterPageEl).removeClass("page-master-stacked").trigger("page:masterunstack");
        router.emit("pageMasterUnstack", masterPageEl);
        if (dynamicNavbar) {
          dom7_default(app.navbar.getElByPage(masterPageEl)).removeClass("navbar-master-stacked");
          router.emit("navbarMasterUnstack", app.navbar.getElByPage(masterPageEl));
        }
      }
    } else {
      $oldPage = $pagesInView.eq($pagesInView.length - 1);
      if (dynamicNavbar) {
        $oldNavbarEl = dom7_default(app.navbar.getElByPage($oldPage));
      }
    }
  } else if (options.reloadPrevious) {
    $oldPage = $pagesInView.eq($pagesInView.length - 2);
    if (dynamicNavbar) {
      $oldNavbarEl = dom7_default(app.navbar.getElByPage($oldPage));
    }
  } else if (options.reloadAll) {
    $oldPage = $pagesInView.filter((pageEl) => pageEl !== $newPage[0]);
    if (dynamicNavbar) {
      $oldNavbarEl = $navbarsInView.filter((navbarEl) => navbarEl !== $newNavbarEl[0]);
    }
  } else {
    let removedPageEls = [];
    let removedNavbarEls = [];
    if ($pagesInView.length > 1) {
      let i = 0;
      for (i = 0; i < $pagesInView.length - 1; i += 1) {
        if (masterPageEl && $pagesInView[i] === masterPageEl) {
          $pagesInView.eq(i).addClass("page-master-stacked");
          $pagesInView.eq(i).trigger("page:masterstack");
          router.emit("pageMasterStack", $pagesInView[i]);
          if (dynamicNavbar) {
            dom7_default(app.navbar.getElByPage(masterPageEl)).addClass("navbar-master-stacked");
            router.emit("navbarMasterStack", app.navbar.getElByPage(masterPageEl));
          }
          continue;
        }
        const oldNavbarEl = app.navbar.getElByPage($pagesInView.eq(i));
        removedPageEls.push($pagesInView[i]);
        router.pageCallback("beforeRemove", $pagesInView[i], $navbarsInView && $navbarsInView[i], "previous", void 0, options);
        router.removePage($pagesInView[i]);
        if (dynamicNavbar && oldNavbarEl) {
          removedNavbarEls.push(oldNavbarEl);
          router.removeNavbar(oldNavbarEl);
        }
      }
    }
    $oldPage = $viewEl.children(".page").filter((pageEl) => pageEl !== $newPage[0] && removedPageEls.indexOf(pageEl) < 0);
    if (dynamicNavbar) {
      $oldNavbarEl = $navbarsEl.children(".navbar").filter((navbarEl) => navbarEl !== $newNavbarEl[0] && removedNavbarEls.indexOf(removedNavbarEls) < 0);
    }
    removedPageEls = [];
    removedNavbarEls = [];
  }
  if (isDetail && !options.reloadAll) {
    if ($oldPage.length > 1 || reloadDetail) {
      $oldPage = $oldPage.filter((pageEl) => !pageEl.classList.contains("page-master"));
    }
    if ($oldNavbarEl && ($oldNavbarEl.length > 1 || reloadDetail)) {
      $oldNavbarEl = $oldNavbarEl.filter((navbarEl) => !navbarEl.classList.contains("navbar-master"));
    }
  }
  if (router.params.browserHistory && (options.browserHistory || options.replaceState) && !options.reloadPrevious) {
    const browserHistoryRoot = router.params.browserHistoryRoot || "";
    history_default[options.reloadCurrent || reloadDetail && otherDetailPageEl || options.reloadAll || options.replaceState ? "replace" : "push"](view.id, {
      url: options.route.url
    }, browserHistoryRoot + router.params.browserHistorySeparator + options.route.url);
  }
  if (!options.reloadPrevious) {
    router.currentPageEl = $newPage[0];
    if (dynamicNavbar && $newNavbarEl.length) {
      router.currentNavbarEl = $newNavbarEl[0];
    } else {
      delete router.currentNavbarEl;
    }
    router.currentRoute = options.route;
  }
  const url = options.route.url;
  if (options.history) {
    if (((options.reloadCurrent || reloadDetail && otherDetailPageEl) && router.history.length) > 0 || options.replaceState) {
      if (reloadDetail && detailsInBetweenRemoved > 0) {
        router.history = router.history.slice(0, router.history.length - detailsInBetweenRemoved);
        router.propsHistory = router.propsHistory.slice(0, router.propsHistory.length - detailsInBetweenRemoved);
      }
      router.history[router.history.length - (options.reloadPrevious ? 2 : 1)] = url;
      router.propsHistory[router.propsHistory.length - (options.reloadPrevious ? 2 : 1)] = options.props || {};
    } else if (options.reloadPrevious) {
      router.history[router.history.length - 2] = url;
      router.propsHistory[router.propsHistory.length - 2] = options.props || {};
    } else if (options.reloadAll) {
      router.history = [url];
      router.propsHistory = [options.props || {}];
    } else {
      router.history.push(url);
      router.propsHistory.push(options.props || {});
    }
  }
  router.saveHistory();
  const newPageInDom = $newPage.parents(document2).length > 0;
  const f7Component = $newPage[0].f7Component;
  if (options.reloadPrevious) {
    if (f7Component && !newPageInDom) {
      f7Component.mount((componentEl) => {
        dom7_default(componentEl).insertBefore($oldPage);
      });
    } else {
      $newPage.insertBefore($oldPage);
    }
    if (dynamicNavbar && $newNavbarEl.length) {
      if ($newNavbarEl.find(".title-large").length) {
        $newNavbarEl.addClass("navbar-large");
      }
      if ($oldNavbarEl.length) {
        $newNavbarEl.insertBefore($oldNavbarEl);
      } else {
        if (!router.$navbarsEl.parents(document2).length) {
          router.$el.prepend(router.$navbarsEl);
        }
        $navbarsEl.append($newNavbarEl);
      }
    }
  } else {
    if ($oldPage.next(".page")[0] !== $newPage[0]) {
      if (f7Component && !newPageInDom) {
        f7Component.mount((componentEl) => {
          $viewEl.append(componentEl);
        });
      } else {
        $viewEl.append($newPage[0]);
      }
    }
    if (dynamicNavbar && $newNavbarEl.length) {
      if ($newNavbarEl.find(".title-large").length) {
        $newNavbarEl.addClass("navbar-large");
      }
      if (!router.$navbarsEl.parents(document2).length) {
        router.$el.prepend(router.$navbarsEl);
      }
      $navbarsEl.append($newNavbarEl[0]);
    }
  }
  if (!newPageInDom) {
    router.pageCallback("mounted", $newPage, $newNavbarEl, newPagePosition, reload ? newPagePosition : "current", options, $oldPage);
  } else if (options.route && options.route.route && options.route.route.keepAlive && !$newPage[0].f7PageMounted) {
    $newPage[0].f7PageMounted = true;
    router.pageCallback("mounted", $newPage, $newNavbarEl, newPagePosition, reload ? newPagePosition : "current", options, $oldPage);
  }
  if ((options.reloadCurrent || reloadDetail) && $oldPage.length > 0) {
    router.pageCallback("beforeOut", $oldPage, $oldNavbarEl, "current", void 0, options);
    router.pageCallback("afterOut", $oldPage, $oldNavbarEl, "current", void 0, options);
    router.pageCallback("beforeRemove", $oldPage, $oldNavbarEl, "current", void 0, options);
    router.removePage($oldPage);
    if (dynamicNavbar && $oldNavbarEl && $oldNavbarEl.length) {
      router.removeNavbar($oldNavbarEl);
    }
  } else if (options.reloadAll) {
    $oldPage.each((pageEl, index2) => {
      const $oldPageEl = dom7_default(pageEl);
      const $oldNavbarElEl = dom7_default(app.navbar.getElByPage($oldPageEl));
      if ($oldPageEl.hasClass("page-current")) {
        router.pageCallback("beforeOut", $oldPage, $oldNavbarEl, "current", void 0, options);
        router.pageCallback("afterOut", $oldPage, $oldNavbarEl, "current", void 0, options);
      }
      router.pageCallback("beforeRemove", $oldPageEl, $oldNavbarEl && $oldNavbarEl.eq(index2), "previous", void 0, options);
      router.removePage($oldPageEl);
      if (dynamicNavbar && $oldNavbarElEl.length) {
        router.removeNavbar($oldNavbarElEl);
      }
    });
  } else if (options.reloadPrevious) {
    router.pageCallback("beforeRemove", $oldPage, $oldNavbarEl, "previous", void 0, options);
    router.removePage($oldPage);
    if (dynamicNavbar && $oldNavbarEl && $oldNavbarEl.length) {
      router.removeNavbar($oldNavbarEl);
    }
  }
  if (options.route.route.tab) {
    router.tabLoad(options.route.route.tab, extend2({}, options, {
      history: false,
      browserHistory: false
    }));
  }
  if (masterDetailEnabled) {
    view.checkMasterDetailBreakpoint();
  }
  router.pageCallback("init", $newPage, $newNavbarEl, newPagePosition, reload ? newPagePosition : "current", options, $oldPage);
  if (options.reloadCurrent || options.reloadAll || reloadDetail) {
    router.allowPageChange = true;
    router.pageCallback("beforeIn", $newPage, $newNavbarEl, newPagePosition, "current", options);
    $newPage.removeAttr("aria-hidden");
    if (dynamicNavbar && $newNavbarEl) {
      $newNavbarEl.removeAttr("aria-hidden");
    }
    router.pageCallback("afterIn", $newPage, $newNavbarEl, newPagePosition, "current", options);
    if (options.reloadCurrent && options.clearPreviousHistory) router.clearPreviousHistory();
    if (reloadDetail) {
      router.setPagePosition(dom7_default(masterPageEl), "previous");
      if (masterPageEl.f7Page && masterPageEl.f7Page.navbarEl) {
        router.setNavbarPosition(dom7_default(masterPageEl.f7Page.navbarEl), "previous");
      }
    }
    return router;
  }
  if (options.reloadPrevious) {
    router.allowPageChange = true;
    return router;
  }
  router.pageCallback("beforeOut", $oldPage, $oldNavbarEl, "current", "previous", options);
  router.pageCallback("beforeIn", $newPage, $newNavbarEl, "next", "current", options);
  function afterAnimation() {
    router.setPagePosition($newPage, "current", false);
    router.setPagePosition($oldPage, "previous", !$oldPage.hasClass("page-master"));
    if (dynamicNavbar) {
      router.setNavbarPosition($newNavbarEl, "current", false);
      router.setNavbarPosition($oldNavbarEl, "previous", !$oldNavbarEl.hasClass("navbar-master"));
    }
    router.allowPageChange = true;
    router.pageCallback("afterOut", $oldPage, $oldNavbarEl, "current", "previous", options);
    router.pageCallback("afterIn", $newPage, $newNavbarEl, "next", "current", options);
    let keepOldPage = (router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`]) && !isMaster;
    if (!keepOldPage) {
      if ($newPage.hasClass("smart-select-page") || $newPage.hasClass("photo-browser-page") || $newPage.hasClass("autocomplete-page") || $newPage.hasClass("color-picker-page")) {
        keepOldPage = true;
      }
    }
    if (!keepOldPage) {
      if (!($newPage.attr("data-name") && $newPage.attr("data-name") === "smart-select-page")) {
        router.pageCallback("beforeRemove", $oldPage, $oldNavbarEl, "previous", void 0, options);
        router.removePage($oldPage);
        if (dynamicNavbar && $oldNavbarEl.length) {
          router.removeNavbar($oldNavbarEl);
        }
      }
    }
    if (options.clearPreviousHistory) router.clearPreviousHistory();
    router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
    if (router.params.browserHistory) {
      history_default.clearRouterQueue();
    }
  }
  function setPositionClasses() {
    router.setPagePosition($oldPage, "current", false);
    router.setPagePosition($newPage, "next", false);
    if (dynamicNavbar) {
      router.setNavbarPosition($oldNavbarEl, "current", false);
      router.setNavbarPosition($newNavbarEl, "next", false);
    }
  }
  if (options.animate && !(isMaster && app.width >= router.params.masterDetailBreakpoint)) {
    const delay = router.params[`${router.app.theme}PageLoadDelay`];
    let transition2 = router.params.transition;
    if (options.transition) transition2 = options.transition;
    if (!transition2 && router.currentRoute && router.currentRoute.route) {
      transition2 = router.currentRoute.route.transition;
    }
    if (!transition2 && router.currentRoute && router.currentRoute.route.options) {
      transition2 = router.currentRoute.route.options.transition;
    }
    if (transition2) {
      $newPage[0].f7PageTransition = transition2;
    }
    if (delay) {
      setTimeout(() => {
        setPositionClasses();
        router.animate($oldPage, $newPage, $oldNavbarEl, $newNavbarEl, "forward", transition2, () => {
          afterAnimation();
        });
      }, delay);
    } else {
      setPositionClasses();
      router.animate($oldPage, $newPage, $oldNavbarEl, $newNavbarEl, "forward", transition2, () => {
        afterAnimation();
      });
    }
  } else {
    afterAnimation();
  }
  return router;
}
function load(router, loadParams, loadOptions, ignorePageChange) {
  if (loadParams === void 0) {
    loadParams = {};
  }
  if (loadOptions === void 0) {
    loadOptions = {};
  }
  if (!router.allowPageChange && !ignorePageChange) return router;
  const params = loadParams;
  const options = loadOptions;
  const {
    url,
    content,
    el,
    pageName,
    component,
    componentUrl
  } = params;
  if (!options.reloadCurrent && options.route && options.route.route && options.route.route.parentPath && router.currentRoute.route && router.currentRoute.route.parentPath === options.route.route.parentPath) {
    if (options.route.url === router.url) {
      router.allowPageChange = true;
      return false;
    }
    let sameParams = Object.keys(options.route.params).length === Object.keys(router.currentRoute.params).length;
    if (sameParams) {
      Object.keys(options.route.params).forEach((paramName) => {
        if (!(paramName in router.currentRoute.params) || router.currentRoute.params[paramName] !== options.route.params[paramName]) {
          sameParams = false;
        }
      });
    }
    if (sameParams) {
      if (options.route.route.tab) {
        return router.tabLoad(options.route.route.tab, options);
      }
      return false;
    }
    if (!sameParams && options.route.route.tab && router.currentRoute.route.tab && router.currentRoute.parentPath === options.route.parentPath) {
      return router.tabLoad(options.route.route.tab, options);
    }
  }
  if (options.route && options.route.url && router.url === options.route.url && !(options.reloadCurrent || options.reloadPrevious) && !router.params.allowDuplicateUrls) {
    router.allowPageChange = true;
    return false;
  }
  if (!options.route && url) {
    options.route = router.parseRouteUrl(url);
    extend2(options.route, {
      route: {
        url,
        path: url
      }
    });
  }
  function resolve(pageEl, newOptions) {
    return forward(router, pageEl, extend2(options, newOptions));
  }
  function reject() {
    router.allowPageChange = true;
    return router;
  }
  if (url || componentUrl || component) {
    router.allowPageChange = false;
  }
  if (content) {
    forward(router, router.getPageEl(content), options);
  } else if (el) {
    forward(router, router.getPageEl(el), options);
  } else if (pageName) {
    forward(router, router.$el.children(`.page[data-name="${pageName}"]`).eq(0), options);
  } else if (component || componentUrl) {
    try {
      router.pageComponentLoader({
        routerEl: router.el,
        component,
        componentUrl,
        options,
        resolve,
        reject
      });
    } catch (err) {
      router.allowPageChange = true;
      throw err;
    }
  } else if (url) {
    if (router.xhrAbortController) {
      router.xhrAbortController.abort();
      router.xhrAbortController = false;
    }
    router.xhrRequest(url, options).then((pageContent) => {
      forward(router, router.getPageEl(pageContent), options);
    }).catch(() => {
      router.allowPageChange = true;
    });
  }
  return router;
}
function navigate(navigateParams, navigateOptions) {
  if (navigateOptions === void 0) {
    navigateOptions = {};
  }
  const router = this;
  if (router.swipeBackActive) return router;
  let url;
  let createRoute;
  let name;
  let path;
  let query;
  let params;
  let route;
  if (typeof navigateParams === "string") {
    url = navigateParams;
  } else {
    url = navigateParams.url;
    createRoute = navigateParams.route;
    name = navigateParams.name;
    path = navigateParams.path;
    query = navigateParams.query;
    params = navigateParams.params;
  }
  if (name || path) {
    url = router.generateUrl({
      path,
      name,
      params,
      query
    });
    if (url) {
      return router.navigate(url, navigateOptions);
    }
    return router;
  }
  const app = router.app;
  appRouterCheck(router, "navigate");
  if (url === "#" || url === "") {
    return router;
  }
  let navigateUrl = url.replace("./", "");
  if (navigateUrl[0] !== "/" && navigateUrl.indexOf("#") !== 0) {
    const currentPath = router.currentRoute.parentPath || router.currentRoute.path;
    navigateUrl = ((currentPath ? `${currentPath}/` : "/") + navigateUrl).replace("///", "/").replace("//", "/");
  }
  if (createRoute) {
    route = extend2(router.parseRouteUrl(navigateUrl), {
      route: extend2({}, createRoute)
    });
  } else {
    route = router.findMatchingRoute(navigateUrl);
  }
  if (!route) {
    return router;
  }
  if (route.route && route.route.viewName) {
    const anotherViewName = route.route.viewName;
    const anotherView = app.views[anotherViewName];
    if (!anotherView) {
      throw new Error(`Framework7: There is no View with "${anotherViewName}" name that was specified in this route`);
    }
    if (anotherView !== router.view) {
      return anotherView.router.navigate(navigateParams, navigateOptions);
    }
  }
  if (route.route.redirect) {
    return redirect.call(router, "forward", route, navigateOptions);
  }
  const options = {};
  if (route.route.options) {
    extend2(options, route.route.options, navigateOptions);
  } else {
    extend2(options, navigateOptions);
  }
  if (options.openIn && (!router.params.ignoreOpenIn || router.params.ignoreOpenIn && router.history.length > 0)) {
    return router.openIn(router, navigateUrl, options);
  }
  options.route = route;
  function resolve() {
    let routerLoaded = false;
    "popup popover sheet loginScreen actions customModal panel".split(" ").forEach((modalLoadProp) => {
      if (route.route[modalLoadProp] && !routerLoaded) {
        routerLoaded = true;
        router.modalLoad(modalLoadProp, route, options, "forward");
      }
    });
    if (route.route.keepAlive && route.route.keepAliveData) {
      load(router, {
        el: route.route.keepAliveData.pageEl
      }, options, false);
      routerLoaded = true;
    }
    "url content component pageName el componentUrl".split(" ").forEach((pageLoadProp) => {
      if (route.route[pageLoadProp] && !routerLoaded) {
        routerLoaded = true;
        load(router, {
          [pageLoadProp]: route.route[pageLoadProp]
        }, options, false);
      }
    });
    if (routerLoaded) return;
    function asyncResolve(resolveParams, resolveOptions) {
      router.allowPageChange = false;
      let resolvedAsModal = false;
      "popup popover sheet loginScreen actions customModal panel".split(" ").forEach((modalLoadProp) => {
        if (resolveParams[modalLoadProp]) {
          resolvedAsModal = true;
          const modalRoute = extend2({}, route, {
            route: resolveParams
          });
          router.allowPageChange = true;
          router.modalLoad(modalLoadProp, modalRoute, extend2(options, resolveOptions), "forward");
        }
      });
      if (resolvedAsModal) return;
      load(router, resolveParams, extend2(options, resolveOptions), true);
    }
    function asyncReject() {
      router.allowPageChange = true;
    }
    if (route.route.async) {
      router.allowPageChange = false;
      route.route.async.call(router, {
        router,
        to: options.route,
        from: router.currentRoute,
        resolve: asyncResolve,
        reject: asyncReject,
        direction: "forward",
        app
      });
    }
    if (route.route.asyncComponent) {
      asyncComponent(router, route.route.asyncComponent, asyncResolve, asyncReject);
    }
  }
  function reject() {
    router.allowPageChange = true;
  }
  if (router.params.masterDetailBreakpoint > 0 && route.route.masterRoute) {
    let preloadMaster = true;
    let masterLoaded = false;
    if (router.currentRoute && router.currentRoute.route) {
      if ((router.currentRoute.route.master === true || typeof router.currentRoute.route.master === "function" && router.currentRoute.route.master(app, router)) && (router.currentRoute.route === route.route.masterRoute || router.currentRoute.route.path === route.route.masterRoute.path)) {
        preloadMaster = false;
      }
      if (router.currentRoute.route.masterRoute && (router.currentRoute.route.masterRoute === route.route.masterRoute || router.currentRoute.route.masterRoute.path === route.route.masterRoute.path)) {
        preloadMaster = false;
        masterLoaded = true;
      }
    }
    if (preloadMaster || masterLoaded && navigateOptions.reloadAll) {
      router.navigate({
        path: route.route.masterRoute.path,
        params: route.params || {}
      }, {
        animate: false,
        reloadAll: navigateOptions.reloadAll,
        reloadCurrent: navigateOptions.reloadCurrent,
        reloadPrevious: navigateOptions.reloadPrevious,
        browserHistory: !navigateOptions.initial,
        history: !navigateOptions.initial,
        once: {
          pageAfterIn() {
            router.navigate(navigateParams, extend2({}, navigateOptions, {
              animate: false,
              reloadAll: false,
              reloadCurrent: false,
              reloadPrevious: false,
              history: !navigateOptions.initial,
              browserHistory: !navigateOptions.initial
            }));
          }
        }
      });
      return router;
    }
  }
  processRouteQueue.call(router, route, router.currentRoute, () => {
    if (route.route.modules) {
      app.loadModules(Array.isArray(route.route.modules) ? route.route.modules : [route.route.modules]).then(() => {
        resolve();
      }).catch(() => {
        reject();
      });
    } else {
      resolve();
    }
  }, () => {
    reject();
  }, "forward");
  return router;
}

// node_modules/framework7/modules/router/tab.js
function tabLoad(tabRoute, loadOptions) {
  if (loadOptions === void 0) {
    loadOptions = {};
  }
  const router = this;
  const options = extend2({
    animate: router.params.animate,
    browserHistory: true,
    history: true,
    parentPageEl: null,
    preload: false,
    on: {}
  }, loadOptions);
  let currentRoute;
  let previousRoute;
  if (options.route) {
    if (!options.preload && options.route !== router.currentRoute) {
      previousRoute = router.previousRoute;
      router.currentRoute = options.route;
    }
    if (options.preload) {
      currentRoute = options.route;
      previousRoute = router.currentRoute;
    } else {
      currentRoute = router.currentRoute;
      if (!previousRoute) previousRoute = router.previousRoute;
    }
    if (router.params.browserHistory && options.browserHistory && !options.reloadPrevious) {
      history_default[router.params.browserHistoryTabs](router.view.id, {
        url: options.route.url
      }, (router.params.browserHistoryRoot || "") + router.params.browserHistorySeparator + options.route.url);
    }
    if (options.history) {
      router.history[Math.max(router.history.length - 1, 0)] = options.route.url;
      router.saveHistory();
    }
  }
  const $parentPageEl = dom7_default(options.parentPageEl || router.currentPageEl);
  let tabEl;
  if ($parentPageEl.length && $parentPageEl.find(`#${tabRoute.id}`).length) {
    tabEl = $parentPageEl.find(`#${tabRoute.id}`).eq(0);
  } else if (router.view.selector) {
    tabEl = `${router.view.selector} #${tabRoute.id}`;
  } else {
    tabEl = `#${tabRoute.id}`;
  }
  const tabShowResult = router.app.tab.show({
    tabEl,
    animate: options.animate,
    tabRoute: options.route
  });
  const {
    $newTabEl,
    $oldTabEl,
    animated,
    onTabsChanged
  } = tabShowResult;
  if ($newTabEl && $newTabEl.parents(".page").length > 0 && options.route) {
    const tabParentPageData = $newTabEl.parents(".page")[0].f7Page;
    if (tabParentPageData && options.route) {
      tabParentPageData.route = options.route;
    }
  }
  function onTabLoaded(contentEl) {
    router.removeThemeElements($newTabEl);
    let tabEventTarget = $newTabEl;
    if (typeof contentEl !== "string") tabEventTarget = dom7_default(contentEl);
    tabEventTarget.trigger("tab:init tab:mounted", tabRoute);
    router.emit("tabInit tabMounted", $newTabEl[0], tabRoute);
    if ($oldTabEl && $oldTabEl.length) {
      if (animated) {
        onTabsChanged(() => {
          router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
          if (router.params.unloadTabContent) {
            router.tabRemove($oldTabEl, $newTabEl, tabRoute);
          }
        });
      } else {
        router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
        if (router.params.unloadTabContent) {
          router.tabRemove($oldTabEl, $newTabEl, tabRoute);
        }
      }
    }
  }
  if ($newTabEl[0].f7RouterTabLoaded) {
    if (!$oldTabEl || !$oldTabEl.length) return router;
    if (animated) {
      onTabsChanged(() => {
        router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
      });
    } else {
      router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
    }
    return router;
  }
  function loadTab(loadTabParams, loadTabOptions) {
    const {
      url,
      content,
      el,
      component,
      componentUrl
    } = loadTabParams;
    function resolve(contentEl) {
      router.allowPageChange = true;
      if (!contentEl) return;
      if (typeof contentEl === "string") {
        $newTabEl.html(contentEl);
      } else {
        $newTabEl.html("");
        if (contentEl.f7Component) {
          contentEl.f7Component.mount((componentEl) => {
            $newTabEl.append(componentEl);
          });
        } else {
          $newTabEl.append(contentEl);
        }
      }
      $newTabEl[0].f7RouterTabLoaded = true;
      onTabLoaded(contentEl);
    }
    function reject() {
      router.allowPageChange = true;
      return router;
    }
    if (content) {
      resolve(content);
    } else if (el) {
      resolve(el);
    } else if (component || componentUrl) {
      try {
        router.tabComponentLoader({
          tabEl: $newTabEl[0],
          component,
          componentUrl,
          options: loadTabOptions,
          resolve,
          reject
        });
      } catch (err) {
        router.allowPageChange = true;
        throw err;
      }
    } else if (url) {
      if (router.xhrAbortController) {
        router.xhrAbortController.abort();
        router.xhrAbortController = false;
      }
      router.xhrRequest(url, loadTabOptions).then((tabContent) => {
        resolve(tabContent);
      }).catch(() => {
        router.allowPageChange = true;
      });
    }
  }
  let hasContentLoadProp;
  "url content component el componentUrl".split(" ").forEach((tabLoadProp) => {
    if (tabRoute[tabLoadProp]) {
      hasContentLoadProp = true;
      loadTab({
        [tabLoadProp]: tabRoute[tabLoadProp]
      }, options);
    }
  });
  function asyncResolve(resolveParams, resolveOptions) {
    loadTab(resolveParams, extend2(options, resolveOptions));
  }
  function asyncReject() {
    router.allowPageChange = true;
  }
  if (tabRoute.async) {
    tabRoute.async.call(router, {
      router,
      to: currentRoute,
      from: previousRoute,
      resolve: asyncResolve,
      reject: asyncReject,
      app: router.app
    });
  } else if (tabRoute.asyncComponent) {
    asyncComponent(router, tabRoute.asyncComponent, asyncResolve, asyncReject);
  } else if (!hasContentLoadProp) {
    router.allowPageChange = true;
  }
  return router;
}
function tabRemove($oldTabEl, $newTabEl, tabRoute) {
  const router = this;
  let hasTabComponentChild;
  if ($oldTabEl[0]) {
    $oldTabEl[0].f7RouterTabLoaded = false;
    delete $oldTabEl[0].f7RouterTabLoaded;
  }
  $oldTabEl.children().each((tabChild) => {
    if (tabChild.f7Component) {
      hasTabComponentChild = true;
      dom7_default(tabChild).trigger("tab:beforeremove", tabRoute);
      tabChild.f7Component.destroy();
    }
  });
  if (!hasTabComponentChild) {
    $oldTabEl.trigger("tab:beforeremove", tabRoute);
  }
  router.emit("tabBeforeRemove", $oldTabEl[0], $newTabEl[0], tabRoute);
  router.removeTabContent($oldTabEl[0], tabRoute);
}

// node_modules/framework7/modules/router/modal.js
function modalLoad(modalType, route, loadOptions, direction) {
  if (loadOptions === void 0) {
    loadOptions = {};
  }
  const router = this;
  const app = router.app;
  const isPanel = modalType === "panel";
  const modalOrPanel = isPanel ? "panel" : "modal";
  const options = extend2({
    animate: router.params.animate,
    browserHistory: true,
    history: true,
    on: {},
    once: {}
  }, loadOptions);
  const modalParams = extend2({}, route.route[modalType]);
  const modalRoute = route.route;
  const routeCallback = (modal, name) => {
    const {
      on: on2,
      once: once2
    } = options;
    let callback;
    if (name === "open") {
      callback = on2.modalOpen || once2.modalOpen || on2.panelOpen || once2.panelOpen;
    }
    if (name === "close") {
      callback = on2.modalClose || once2.modalClose || on2.panelClose || once2.panelClose;
    }
    if (name === "closed") {
      callback = on2.modalClosed || once2.modalClosed || on2.panelClosed || once2.panelClosed;
    }
    if (callback) callback(modal);
  };
  function onModalLoaded() {
    const modal = app[modalType].create(modalParams);
    modalRoute.modalInstance = modal;
    const hasEl = modal.el;
    function closeOnSwipeBack() {
      modal.close();
    }
    modal.on(`${modalOrPanel}Open`, () => {
      if (!hasEl) {
        router.removeThemeElements(modal.el);
        modal.$el.trigger(`${modalType.toLowerCase()}:init ${modalType.toLowerCase()}:mounted`, route, modal);
        router.emit(`${!isPanel ? "modalInit" : ""} ${modalType}Init ${modalType}Mounted`, modal.el, route, modal);
      }
      router.once("swipeBackMove", closeOnSwipeBack);
      routeCallback(modal, "open");
    });
    modal.on(`${modalOrPanel}Close`, () => {
      router.off("swipeBackMove", closeOnSwipeBack);
      if (!modal.closeByRouter) {
        router.back();
      }
      routeCallback(modal, "close");
    });
    modal.on(`${modalOrPanel}Closed`, () => {
      modal.$el.trigger(`${modalType.toLowerCase()}:beforeremove`, route, modal);
      modal.emit(`${!isPanel ? "modalBeforeRemove " : ""}${modalType}BeforeRemove`, modal.el, route, modal);
      const modalComponent = modal.el.f7Component;
      routeCallback(modal, "closed");
      if (modalComponent) {
        modalComponent.destroy();
      }
      nextTick(() => {
        if (modalComponent || modalParams.component || modalParams.asyncComponent || modalParams.async) {
          router.removeModal(modal.el);
        }
        modal.destroy();
        delete modal.route;
        delete modalRoute.modalInstance;
      });
    });
    if (options.route) {
      if (router.params.browserHistory && options.browserHistory) {
        history_default.push(router.view.id, {
          url: options.route.url,
          modal: modalType
        }, (router.params.browserHistoryRoot || "") + router.params.browserHistorySeparator + options.route.url);
      }
      if (options.route !== router.currentRoute) {
        modal.route = extend2(options.route, {
          modal
        });
        router.currentRoute = modal.route;
      }
      if (options.history && !options.reloadCurrent) {
        router.history.push(options.route.url);
        router.saveHistory();
      }
    }
    if (hasEl) {
      router.removeThemeElements(modal.el);
      modal.$el.trigger(`${modalType.toLowerCase()}:init ${modalType.toLowerCase()}:mounted`, route, modal);
      router.emit(`${modalOrPanel}Init ${modalType}Init ${modalType}Mounted`, modal.el, route, modal);
    }
    modal.open(options.animate === false || options.animate === true ? options.animate : void 0);
  }
  function loadModal(loadModalParams, loadModalOptions) {
    const {
      url,
      content,
      component,
      componentUrl
    } = loadModalParams;
    function resolve(contentEl) {
      if (contentEl) {
        if (typeof contentEl === "string") {
          modalParams.content = contentEl;
        } else if (contentEl.f7Component) {
          contentEl.f7Component.mount((componentEl) => {
            modalParams.el = componentEl;
            app.$el.append(componentEl);
          });
        } else {
          modalParams.el = contentEl;
        }
        onModalLoaded();
      }
    }
    function reject() {
      router.allowPageChange = true;
      return router;
    }
    if (content) {
      resolve(content);
    } else if (component || componentUrl) {
      try {
        router.modalComponentLoader({
          rootEl: app.el,
          component,
          componentUrl,
          options: loadModalOptions,
          resolve,
          reject
        });
      } catch (err) {
        router.allowPageChange = true;
        throw err;
      }
    } else if (url) {
      if (router.xhrAbortController) {
        router.xhrAbortController.abort();
        router.xhrAbortController = false;
      }
      router.xhrRequest(url, loadModalOptions).then((modalContent) => {
        modalParams.content = modalContent;
        onModalLoaded();
      }).catch(() => {
        router.allowPageChange = true;
      });
    } else {
      onModalLoaded();
    }
  }
  let foundLoadProp;
  "url content component el componentUrl template".split(" ").forEach((modalLoadProp) => {
    if (modalParams[modalLoadProp] && !foundLoadProp) {
      foundLoadProp = true;
      loadModal({
        [modalLoadProp]: modalParams[modalLoadProp]
      }, options);
    }
  });
  if (!foundLoadProp && modalType === "actions") {
    onModalLoaded();
  }
  function asyncResolve(resolveParams, resolveOptions) {
    loadModal(resolveParams, extend2(options, resolveOptions));
  }
  function asyncReject() {
    router.allowPageChange = true;
  }
  if (modalParams.async) {
    modalParams.async.call(router, {
      router,
      to: options.route,
      from: router.currentRoute,
      resolve: asyncResolve,
      reject: asyncReject,
      direction,
      app
    });
  }
  if (modalParams.asyncComponent) {
    asyncComponent(router, modalParams.asyncComponent, asyncResolve, asyncReject);
  }
  return router;
}
function modalRemove(modal) {
  extend2(modal, {
    closeByRouter: true
  });
  modal.close();
}

// node_modules/framework7/modules/router/back.js
function backward(router, el, backwardOptions) {
  const device = getDevice();
  const document2 = getDocument();
  const $el = dom7_default(el);
  const app = router.app;
  const view = router.view;
  const options = extend2(false, {
    animate: router.params.animate,
    browserHistory: true,
    replaceState: false
  }, backwardOptions);
  const masterDetailEnabled = router.params.masterDetailBreakpoint > 0;
  const isMaster = masterDetailEnabled && options.route && options.route.route && (options.route.route.master === true || typeof options.route.route.master === "function" && options.route.route.master(app, router));
  let masterPageEl;
  let masterPageRemoved;
  const dynamicNavbar = router.dynamicNavbar;
  const $newPage = $el;
  const $oldPage = router.$el.children(".page-current");
  const initialPreload = $oldPage.length === 0 && options.preload;
  const currentIsMaster = masterDetailEnabled && $oldPage.hasClass("page-master");
  if ($newPage.length) {
    router.removeThemeElements($newPage);
  }
  let $navbarsEl;
  let $newNavbarEl;
  let $oldNavbarEl;
  if (dynamicNavbar) {
    $newNavbarEl = $newPage.children(".navbar");
    $navbarsEl = router.$navbarsEl;
    if ($newNavbarEl.length === 0 && $newPage[0] && $newPage[0].f7Page) {
      $newNavbarEl = $newPage[0].f7Page.$navbarEl;
    }
    $oldNavbarEl = $navbarsEl.find(".navbar-current");
  }
  router.allowPageChange = false;
  if ($newPage.length === 0 || $oldPage.length === 0 && !options.preload) {
    router.allowPageChange = true;
    return router;
  }
  router.removeThemeElements($newPage);
  if (options.route && options.route.route && options.route.route.keepAlive && !options.route.route.keepAliveData) {
    options.route.route.keepAliveData = {
      pageEl: $el[0]
    };
  }
  let isDetail;
  let isDetailRoot;
  if (masterDetailEnabled) {
    const $pagesInView = router.$el.children(".page").filter((pageInView) => pageInView !== $newPage[0]);
    for (let i = 0; i < $pagesInView.length; i += 1) {
      if (!masterPageEl && $pagesInView[i].classList.contains("page-master")) {
        masterPageEl = $pagesInView[i];
        continue;
      }
    }
    isDetail = !isMaster && masterPageEl && router.history.indexOf(options.route.url) > router.history.indexOf(masterPageEl.f7Page.route.url);
    if (!isDetail && !isMaster && masterPageEl && masterPageEl.f7Page && options.route.route.masterRoute) {
      isDetail = options.route.route.masterRoute.path === masterPageEl.f7Page.route.route.path;
    }
  }
  if (isDetail && masterPageEl && masterPageEl.f7Page) {
    isDetailRoot = router.history.indexOf(options.route.url) - router.history.indexOf(masterPageEl.f7Page.route.url) === 1;
  }
  $newPage.addClass(`page-${initialPreload ? "current" : "previous"}${isMaster ? " page-master" : ""}${isDetail ? " page-master-detail" : ""}${isDetailRoot ? " page-master-detail-root" : ""}`).removeAttr("aria-hidden").trigger("page:unstack").trigger("page:position", {
    position: initialPreload ? "current" : "previous"
  });
  router.emit("pageUnstack", $newPage[0]);
  router.emit("pagePosition", $newPage[0], initialPreload ? "current" : "previous");
  if (isMaster || isDetail) {
    $newPage.trigger("page:role", {
      role: isMaster ? "master" : "detail",
      root: !!isDetailRoot
    });
    router.emit("pageRole", $newPage[0], {
      role: isMaster ? "master" : "detail",
      detailRoot: !!isDetailRoot
    });
  }
  if (dynamicNavbar && $newNavbarEl.length > 0) {
    $newNavbarEl.addClass(`navbar-${initialPreload ? "current" : "previous"}${isMaster ? " navbar-master" : ""}${isDetail ? " navbar-master-detail" : ""}${isDetailRoot ? " navbar-master-detail-root" : ""}`).removeAttr("aria-hidden");
    $newNavbarEl.trigger("navbar:position", {
      position: initialPreload ? "current" : "previous"
    });
    router.emit("navbarPosition", $newNavbarEl[0], initialPreload ? "current" : "previous");
    if (isMaster || isDetailRoot) {
      router.emit("navbarRole", $newNavbarEl[0], {
        role: isMaster ? "master" : "detail",
        detailRoot: !!isDetailRoot
      });
    }
  }
  let backIndex;
  if (options.force) {
    if ($oldPage.prev(".page-previous").length >= 0) {
      if (router.history.indexOf(options.route.url) >= 0) {
        backIndex = router.history.length - router.history.indexOf(options.route.url) - 1;
        router.history = router.history.slice(0, router.history.indexOf(options.route.url) + 2);
        router.propsHistory = router.propsHistory.slice(0, router.history.indexOf(options.route.url) + 2);
        view.history = router.history;
      } else if (router.history[[router.history.length - 2]]) {
        router.propsHistory[router.propsHistory.length - 2] = options.props || {};
      } else {
        router.history.unshift(router.url);
        router.propsHistory.unshift(options.props || {});
      }
      const $pageToRemove = $oldPage.prev(".page-previous");
      let $navbarToRemove;
      if (dynamicNavbar) {
        $navbarToRemove = dom7_default(app.navbar.getElByPage($pageToRemove));
      }
      if ($pageToRemove.length > 0) {
        router.pageCallback("beforeRemove", $pageToRemove, $navbarToRemove, "previous", void 0, options);
        if ($pageToRemove[0] === masterPageEl) {
          masterPageRemoved = true;
        }
        router.removePage($pageToRemove);
        if (dynamicNavbar && $navbarToRemove.length) {
          router.removeNavbar($navbarToRemove);
        }
      }
    }
  }
  const newPageInDom = $newPage.parents(document2).length > 0;
  const f7Component = $newPage[0].f7Component;
  function insertPage() {
    if (initialPreload) {
      if (!newPageInDom && f7Component) {
        f7Component.mount((componentEl) => {
          router.$el.append(componentEl);
        });
      } else {
        router.$el.append($newPage);
      }
    }
    if ($newPage.next($oldPage).length === 0) {
      if (!newPageInDom && f7Component) {
        f7Component.mount((componentEl) => {
          dom7_default(componentEl).insertBefore($oldPage);
        });
      } else {
        $newPage.insertBefore($oldPage);
      }
    }
    if (dynamicNavbar && $newNavbarEl.length) {
      if ($newNavbarEl.find(".title-large").length) {
        $newNavbarEl.addClass("navbar-large");
      }
      $newNavbarEl.insertBefore($oldNavbarEl);
      if ($oldNavbarEl.length > 0) {
        $newNavbarEl.insertBefore($oldNavbarEl);
      } else {
        if (!router.$navbarsEl.parents(document2).length) {
          router.$el.prepend(router.$navbarsEl);
        }
        $navbarsEl.append($newNavbarEl);
      }
    }
    if (!newPageInDom) {
      router.pageCallback("mounted", $newPage, $newNavbarEl, "previous", "current", options, $oldPage);
    } else if (options.route && options.route.route && options.route.route.keepAlive && !$newPage[0].f7PageMounted) {
      $newPage[0].f7PageMounted = true;
      router.pageCallback("mounted", $newPage, $newNavbarEl, "previous", "current", options, $oldPage);
    }
  }
  if (options.preload) {
    insertPage();
    if (options.route.route.tab) {
      router.tabLoad(options.route.route.tab, extend2({}, options, {
        history: false,
        browserHistory: false,
        preload: true
      }));
    }
    if (isMaster) {
      $newPage.removeClass("page-master-stacked").trigger("page:masterunstack");
      router.emit("pageMasterUnstack", $newPage[0]);
      if (dynamicNavbar) {
        dom7_default(app.navbar.getElByPage($newPage)).removeClass("navbar-master-stacked");
        router.emit("navbarMasterUnstack", app.navbar.getElByPage($newPage));
      }
    }
    router.pageCallback("init", $newPage, $newNavbarEl, "previous", "current", options, $oldPage);
    if (initialPreload) {
      router.pageCallback("beforeIn", $newPage, $newNavbarEl, "current", void 0, options);
      router.pageCallback("afterIn", $newPage, $newNavbarEl, "current", void 0, options);
    }
    const $previousPages = $newPage.prevAll(".page-previous:not(.page-master)");
    if ($previousPages.length > 0) {
      $previousPages.each((pageToRemove) => {
        const $pageToRemove = dom7_default(pageToRemove);
        let $navbarToRemove;
        if (dynamicNavbar) {
          $navbarToRemove = dom7_default(app.navbar.getElByPage($pageToRemove));
        }
        router.pageCallback("beforeRemove", $pageToRemove, $navbarToRemove, "previous", void 0);
        router.removePage($pageToRemove);
        if (dynamicNavbar && $navbarToRemove.length) {
          router.removeNavbar($navbarToRemove);
        }
      });
    }
    router.allowPageChange = true;
    return router;
  }
  if (!(device.ie || device.edge || device.firefox && !device.ios)) {
    if (router.params.browserHistory && options.browserHistory) {
      if (options.replaceState) {
        const browserHistoryRoot = router.params.browserHistoryRoot || "";
        history_default.replace(view.id, {
          url: options.route.url
        }, browserHistoryRoot + router.params.browserHistorySeparator + options.route.url);
      } else if (backIndex) {
        history_default.go(-backIndex);
      } else {
        history_default.back();
      }
    }
  }
  if (options.replaceState) {
    router.history[router.history.length - 1] = options.route.url;
    router.propsHistory[router.propsHistory.length - 1] = options.props || {};
  } else {
    if (router.history.length === 1) {
      router.history.unshift(router.url);
      router.propsHistory.unshift(options.props || {});
    }
    router.history.pop();
    router.propsHistory.pop();
  }
  router.saveHistory();
  router.currentPageEl = $newPage[0];
  if (dynamicNavbar && $newNavbarEl.length) {
    router.currentNavbarEl = $newNavbarEl[0];
  } else {
    delete router.currentNavbarEl;
  }
  router.currentRoute = options.route;
  if (device.ie || device.edge || device.firefox && !device.ios) {
    if (router.params.browserHistory && options.browserHistory) {
      if (options.replaceState) {
        const browserHistoryRoot = router.params.browserHistoryRoot || "";
        history_default.replace(view.id, {
          url: options.route.url
        }, browserHistoryRoot + router.params.browserHistorySeparator + options.route.url);
      } else if (backIndex) {
        history_default.go(-backIndex);
      } else {
        history_default.back();
      }
    }
  }
  insertPage();
  if (options.route.route.tab) {
    router.tabLoad(options.route.route.tab, extend2({}, options, {
      history: false,
      browserHistory: false
    }));
  }
  if (masterDetailEnabled && (currentIsMaster || masterPageRemoved)) {
    view.checkMasterDetailBreakpoint(false);
  }
  router.pageCallback("init", $newPage, $newNavbarEl, "previous", "current", options, $oldPage);
  router.pageCallback("beforeOut", $oldPage, $oldNavbarEl, "current", "next", options);
  router.pageCallback("beforeIn", $newPage, $newNavbarEl, "previous", "current", options);
  function afterAnimation() {
    router.setPagePosition($newPage, "current", false);
    router.setPagePosition($oldPage, "next", true);
    if (dynamicNavbar) {
      router.setNavbarPosition($newNavbarEl, "current", false);
      router.setNavbarPosition($oldNavbarEl, "next", true);
    }
    router.pageCallback("afterOut", $oldPage, $oldNavbarEl, "current", "next", options);
    router.pageCallback("afterIn", $newPage, $newNavbarEl, "previous", "current", options);
    router.pageCallback("beforeRemove", $oldPage, $oldNavbarEl, "next", void 0, options);
    router.removePage($oldPage);
    if (dynamicNavbar && $oldNavbarEl.length) {
      router.removeNavbar($oldNavbarEl);
    }
    router.allowPageChange = true;
    router.emit("routeChanged", router.currentRoute, router.previousRoute, router);
    const preloadPreviousPage = router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`];
    if (preloadPreviousPage && router.history[router.history.length - 2] && !isMaster) {
      router.back(router.history[router.history.length - 2], {
        preload: true,
        props: router.propsHistory[router.propsHistory.length - 2] || {}
      });
    }
    if (router.params.browserHistory) {
      history_default.clearRouterQueue();
    }
  }
  function setPositionClasses() {
    router.setPagePosition($oldPage, "current");
    router.setPagePosition($newPage, "previous", false);
    if (dynamicNavbar) {
      router.setNavbarPosition($oldNavbarEl, "current");
      router.setNavbarPosition($newNavbarEl, "previous", false);
    }
  }
  if (options.animate && !(currentIsMaster && app.width >= router.params.masterDetailBreakpoint)) {
    let transition2 = router.params.transition;
    if ($oldPage[0] && $oldPage[0].f7PageTransition) {
      transition2 = $oldPage[0].f7PageTransition;
      delete $oldPage[0].f7PageTransition;
    }
    if (options.transition) transition2 = options.transition;
    if (!transition2 && router.previousRoute && router.previousRoute.route) {
      transition2 = router.previousRoute.route.transition;
    }
    if (!transition2 && router.previousRoute && router.previousRoute.route && router.previousRoute.route.options) {
      transition2 = router.previousRoute.route.options.transition;
    }
    setPositionClasses();
    router.animate($oldPage, $newPage, $oldNavbarEl, $newNavbarEl, "backward", transition2, () => {
      afterAnimation();
    });
  } else {
    afterAnimation();
  }
  return router;
}
function loadBack(router, backParams, backOptions, ignorePageChange) {
  if (!router.allowPageChange && !ignorePageChange) return router;
  const params = backParams;
  const options = backOptions;
  const {
    url,
    content,
    el,
    pageName,
    component,
    componentUrl
  } = params;
  if (options.route.url && router.url === options.route.url && !(options.reloadCurrent || options.reloadPrevious) && !router.params.allowDuplicateUrls) {
    router.allowPageChange = true;
    return false;
  }
  if (!options.route && url) {
    options.route = router.parseRouteUrl(url);
  }
  function resolve(pageEl, newOptions) {
    return backward(router, pageEl, extend2(options, newOptions));
  }
  function reject() {
    router.allowPageChange = true;
    return router;
  }
  if (url || componentUrl || component) {
    router.allowPageChange = false;
  }
  if (content) {
    backward(router, router.getPageEl(content), options);
  } else if (el) {
    backward(router, router.getPageEl(el), options);
  } else if (pageName) {
    backward(router, router.$el.children(`.page[data-name="${pageName}"]`).eq(0), options);
  } else if (component || componentUrl) {
    try {
      router.pageComponentLoader({
        routerEl: router.el,
        component,
        componentUrl,
        options,
        resolve,
        reject
      });
    } catch (err) {
      router.allowPageChange = true;
      throw err;
    }
  } else if (url) {
    if (router.xhrAbortController) {
      router.xhrAbortController.abort();
      router.xhrAbortController = false;
    }
    router.xhrRequest(url, options).then((pageContent) => {
      backward(router, router.getPageEl(pageContent), options);
    }).catch(() => {
      router.allowPageChange = true;
    });
  }
  return router;
}
function back() {
  const router = this;
  const device = getDevice();
  if (router.swipeBackActive) return router;
  let navigateUrl;
  let navigateOptions;
  let navigateProps;
  let route;
  if (typeof (arguments.length <= 0 ? void 0 : arguments[0]) === "object") {
    navigateOptions = (arguments.length <= 0 ? void 0 : arguments[0]) || {};
  } else {
    navigateUrl = arguments.length <= 0 ? void 0 : arguments[0];
    navigateOptions = (arguments.length <= 1 ? void 0 : arguments[1]) || {};
  }
  const {
    name,
    params,
    query
  } = navigateOptions;
  if (name) {
    navigateUrl = router.generateUrl({
      name,
      params,
      query
    });
    if (navigateUrl) {
      return router.back(navigateUrl, extend2({}, navigateOptions, {
        name: null,
        params: null,
        query: null
      }));
    }
    return router;
  }
  const app = router.app;
  appRouterCheck(router, "back");
  let currentRouteIsModal = router.currentRoute.modal;
  let modalType;
  if (!currentRouteIsModal) {
    "popup popover sheet loginScreen actions customModal panel".split(" ").forEach((modalLoadProp) => {
      if (router.currentRoute.route[modalLoadProp]) {
        currentRouteIsModal = true;
        modalType = modalLoadProp;
      }
    });
  }
  if (currentRouteIsModal && !navigateOptions.preload) {
    const modalToClose = router.currentRoute.modal || router.currentRoute.route.modalInstance || app[modalType].get();
    const previousUrl = router.history[router.history.length - 2];
    let previousRoute;
    if (modalToClose && modalToClose.$el) {
      const prevOpenedModals = modalToClose.$el.prevAll(".modal-in");
      if (prevOpenedModals.length && prevOpenedModals[0].f7Modal) {
        const modalEl = prevOpenedModals[0];
        if (!router.$el.parents(modalEl).length) {
          previousRoute = modalEl.f7Modal.route;
        }
      }
    }
    if (!previousRoute) {
      previousRoute = router.findMatchingRoute(previousUrl);
    }
    if (!previousRoute && previousUrl) {
      previousRoute = {
        url: previousUrl,
        path: previousUrl.split("?")[0],
        query: parseUrlQuery(previousUrl),
        route: {
          path: previousUrl.split("?")[0],
          url: previousUrl
        }
      };
    }
    if (!navigateUrl || navigateUrl.replace(/[# ]/g, "").trim().length === 0) {
      if (!previousRoute || !modalToClose) {
        return router;
      }
    }
    const forceOtherUrl = navigateOptions.force && previousRoute && navigateUrl;
    if (previousRoute && modalToClose) {
      const isBrokenBrowserHistory = device.ie || device.edge || device.firefox && !device.ios;
      const needHistoryBack = router.params.browserHistory && navigateOptions.browserHistory !== false;
      const currentRouteWithoutBrowserHistory = router.currentRoute && router.currentRoute.route && router.currentRoute.route.options && router.currentRoute.route.options.browserHistory === false;
      if (needHistoryBack && !isBrokenBrowserHistory && !currentRouteWithoutBrowserHistory) {
        history_default.back();
      }
      router.currentRoute = previousRoute;
      router.history.pop();
      router.propsHistory.pop();
      router.saveHistory();
      if (needHistoryBack && isBrokenBrowserHistory && !currentRouteWithoutBrowserHistory) {
        history_default.back();
      }
      router.modalRemove(modalToClose);
      if (forceOtherUrl) {
        router.navigate(navigateUrl, {
          reloadCurrent: true
        });
      }
    } else if (modalToClose) {
      router.modalRemove(modalToClose);
      if (navigateUrl) {
        router.navigate(navigateUrl, {
          reloadCurrent: true
        });
      }
    }
    return router;
  }
  let $previousPage = router.$el.children(".page-current").prevAll(".page-previous:not(.page-master)").eq(0);
  let skipMaster;
  if (router.params.masterDetailBreakpoint > 0) {
    const classes = [];
    router.$el.children(".page").each((pageEl) => {
      classes.push(pageEl.className);
    });
    const $previousMaster = router.$el.children(".page-current").prevAll(".page-master").eq(0);
    if ($previousMaster.length) {
      const expectedPreviousPageUrl = router.history[router.history.length - 2];
      const expectedPreviousPageRoute = router.findMatchingRoute(expectedPreviousPageUrl);
      if (expectedPreviousPageRoute && $previousMaster[0].f7Page && expectedPreviousPageRoute.route === $previousMaster[0].f7Page.route.route) {
        $previousPage = $previousMaster;
        if (!navigateOptions.preload) {
          skipMaster = app.width >= router.params.masterDetailBreakpoint;
        }
      }
    }
  }
  if (!navigateOptions.force && $previousPage.length && !skipMaster) {
    const previousPageObj = $previousPage[0].f7Page;
    if (router.params.browserHistory && previousPageObj && router.history[router.history.length - 2] !== previousPageObj.route.url) {
      router.back(router.history[router.history.length - 2], extend2(navigateOptions, {
        force: true,
        props: router.propsHistory[router.propsHistory.length - 2] || {}
      }));
      return router;
    }
    if (previousPageObj) {
      const previousPageRoute = previousPageObj.route;
      processRouteQueue.call(router, previousPageRoute, router.currentRoute, () => {
        loadBack(router, {
          el: $previousPage
        }, extend2(navigateOptions, {
          route: previousPageRoute
        }));
      }, () => {
      }, "backward");
      return router;
    }
  }
  if (navigateUrl === "#") {
    navigateUrl = void 0;
  }
  if (navigateUrl && navigateUrl[0] !== "/" && navigateUrl.indexOf("#") !== 0) {
    navigateUrl = ((router.path || "/") + navigateUrl).replace("//", "/");
  }
  if (!navigateUrl && router.history.length > 1) {
    navigateUrl = router.history[router.history.length - 2];
    navigateProps = router.propsHistory[router.propsHistory.length - 2] || {};
  }
  if (skipMaster && !navigateOptions.force && router.history[router.history.length - 3]) {
    return router.back(router.history[router.history.length - 3], extend2({}, navigateOptions || {}, {
      force: true,
      animate: false,
      props: router.propsHistory[router.propsHistory.length - 3] || {}
    }));
  }
  if (skipMaster && !navigateOptions.force) {
    return router;
  }
  route = router.findMatchingRoute(navigateUrl);
  if (!route) {
    if (navigateUrl) {
      route = {
        url: navigateUrl,
        path: navigateUrl.split("?")[0],
        query: parseUrlQuery(navigateUrl),
        route: {
          path: navigateUrl.split("?")[0],
          url: navigateUrl
        }
      };
    }
  }
  if (!route) {
    return router;
  }
  if (route.route.redirect) {
    return redirect.call(router, "backward", route, navigateOptions);
  }
  const options = {};
  if (route.route.options) {
    extend2(options, route.route.options, navigateOptions, {
      props: navigateProps || {}
    });
  } else {
    extend2(options, navigateOptions, {
      props: navigateProps || {}
    });
  }
  options.route = route;
  function resolve() {
    let routerLoaded = false;
    if (route.route.keepAlive && route.route.keepAliveData) {
      loadBack(router, {
        el: route.route.keepAliveData.pageEl
      }, options);
      routerLoaded = true;
    }
    "url content component pageName el componentUrl".split(" ").forEach((pageLoadProp) => {
      if (route.route[pageLoadProp] && !routerLoaded) {
        routerLoaded = true;
        loadBack(router, {
          [pageLoadProp]: route.route[pageLoadProp]
        }, options);
      }
    });
    if (routerLoaded) return;
    function asyncResolve(resolveParams, resolveOptions) {
      router.allowPageChange = false;
      loadBack(router, resolveParams, extend2(options, resolveOptions), true);
    }
    function asyncReject() {
      router.allowPageChange = true;
    }
    if (route.route.async) {
      router.allowPageChange = false;
      route.route.async.call(router, {
        router,
        to: route,
        from: router.currentRoute,
        resolve: asyncResolve,
        reject: asyncReject,
        direction: "backward",
        app
      });
    }
    if (route.route.asyncComponent) {
      asyncComponent(router, route.route.asyncComponent, asyncResolve, asyncReject);
    }
  }
  function reject() {
    router.allowPageChange = true;
  }
  if (options.preload) {
    resolve();
  } else {
    processRouteQueue.call(router, route, router.currentRoute, () => {
      if (route.route.modules) {
        app.loadModules(Array.isArray(route.route.modules) ? route.route.modules : [route.route.modules]).then(() => {
          resolve();
        }).catch(() => {
          reject();
        });
      } else {
        resolve();
      }
    }, () => {
      reject();
    }, "backward");
  }
  return router;
}

// node_modules/framework7/modules/router/clear-previous-history.js
function clearPreviousPages(router) {
  appRouterCheck(router, "clearPreviousPages");
  const app = router.app;
  const dynamicNavbar = router.dynamicNavbar;
  const $pagesToRemove = router.$el.children(".page").filter((pageInView) => {
    if (router.currentRoute && (router.currentRoute.modal || router.currentRoute.panel)) return true;
    return pageInView !== router.currentPageEl;
  });
  $pagesToRemove.each((pageEl) => {
    const $oldPageEl = dom7_default(pageEl);
    const $oldNavbarEl = dom7_default(app.navbar.getElByPage($oldPageEl));
    router.pageCallback("beforeRemove", $oldPageEl, $oldNavbarEl, "previous", void 0, {});
    router.removePage($oldPageEl);
    if (dynamicNavbar && $oldNavbarEl.length) {
      router.removeNavbar($oldNavbarEl);
    }
  });
}
function clearPreviousHistory() {
  const router = this;
  appRouterCheck(router, "clearPreviousHistory");
  const url = router.history[router.history.length - 1];
  clearPreviousPages(router);
  router.history = [url];
  router.view.history = [url];
  router.saveHistory();
}

// node_modules/framework7/modules/router/router-class.js
var Router = class extends class_default {
  constructor(app, view) {
    super({}, [typeof view === "undefined" ? app : view]);
    const router = this;
    router.isAppRouter = typeof view === "undefined";
    if (router.isAppRouter) {
      extend2(false, router, {
        app,
        params: app.params.view,
        routes: app.routes || [],
        cache: app.cache
      });
    } else {
      extend2(false, router, {
        app,
        view,
        viewId: view.id,
        id: view.params.routerId,
        params: view.params,
        routes: view.routes,
        history: view.history,
        propsHistory: [],
        scrollHistory: view.scrollHistory,
        cache: app.cache,
        dynamicNavbar: app.theme === "ios" && view.params.iosDynamicNavbar,
        initialPages: [],
        initialNavbars: []
      });
    }
    router.useModules();
    router.allowPageChange = true;
    let currentRoute = {};
    let previousRoute = {};
    Object.defineProperty(router, "currentRoute", {
      enumerable: true,
      configurable: true,
      set(newRoute) {
        if (newRoute === void 0) {
          newRoute = {};
        }
        previousRoute = extend2({}, currentRoute);
        currentRoute = newRoute;
        if (!currentRoute) return;
        router.url = currentRoute.url;
        router.emit("routeChange", newRoute, previousRoute, router);
      },
      get() {
        return currentRoute;
      }
    });
    Object.defineProperty(router, "previousRoute", {
      enumerable: true,
      configurable: true,
      get() {
        return previousRoute;
      },
      set(newRoute) {
        previousRoute = newRoute;
      }
    });
    return router;
  }
  mount() {
    const router = this;
    const view = router.view;
    const document2 = getDocument();
    extend2(false, router, {
      tempDom: document2.createElement("div"),
      $el: view.$el,
      el: view.el,
      $navbarsEl: view.$navbarsEl,
      navbarsEl: view.navbarsEl
    });
    router.emit("local::mount routerMount", router);
  }
  animatableNavElements($newNavbarEl, $oldNavbarEl, toLarge, fromLarge, direction) {
    const router = this;
    const dynamicNavbar = router.dynamicNavbar;
    const animateIcon = router.params.iosAnimateNavbarBackIcon;
    let newNavEls;
    let oldNavEls;
    function animatableNavEl($el, $navbarInner) {
      const isSliding = $el.hasClass("sliding") || $navbarInner.hasClass("sliding");
      const isSubnavbar = $el.hasClass("subnavbar");
      const needsOpacityTransition = isSliding ? !isSubnavbar : true;
      const $iconEl = $el.find(".back .icon");
      let isIconLabel;
      if (isSliding && animateIcon && $el.hasClass("left") && $iconEl.length > 0 && $iconEl.next("span").length) {
        $el = $iconEl.next("span");
        isIconLabel = true;
      }
      return {
        $el,
        isIconLabel,
        leftOffset: $el[0].f7NavbarLeftOffset,
        rightOffset: $el[0].f7NavbarRightOffset,
        isSliding,
        isSubnavbar,
        needsOpacityTransition
      };
    }
    if (dynamicNavbar) {
      newNavEls = [];
      oldNavEls = [];
      $newNavbarEl.children(".navbar-inner").children(".left, .right, .title, .subnavbar").each((navEl) => {
        const $navEl = dom7_default(navEl);
        if ($navEl.hasClass("left") && fromLarge && direction === "forward") return;
        if ($navEl.hasClass("title") && toLarge) return;
        newNavEls.push(animatableNavEl($navEl, $newNavbarEl.children(".navbar-inner")));
      });
      if (!($oldNavbarEl.hasClass("navbar-master") && router.params.masterDetailBreakpoint > 0 && router.app.width >= router.params.masterDetailBreakpoint)) {
        $oldNavbarEl.children(".navbar-inner").children(".left, .right, .title, .subnavbar").each((navEl) => {
          const $navEl = dom7_default(navEl);
          if ($navEl.hasClass("left") && toLarge && !fromLarge && direction === "forward") return;
          if ($navEl.hasClass("left") && toLarge && direction === "backward") return;
          if ($navEl.hasClass("title") && fromLarge) {
            return;
          }
          oldNavEls.push(animatableNavEl($navEl, $oldNavbarEl.children(".navbar-inner")));
        });
      }
      [oldNavEls, newNavEls].forEach((navEls) => {
        navEls.forEach((navEl) => {
          const n = navEl;
          const {
            isSliding,
            $el
          } = navEl;
          const otherEls = navEls === oldNavEls ? newNavEls : oldNavEls;
          if (!(isSliding && $el.hasClass("title") && otherEls)) return;
          otherEls.forEach((otherNavEl) => {
            if (otherNavEl.isIconLabel) {
              const iconTextEl = otherNavEl.$el[0];
              n.leftOffset += iconTextEl ? iconTextEl.offsetLeft || 0 : 0;
            }
          });
        });
      });
    }
    return {
      newNavEls,
      oldNavEls
    };
  }
  animate($oldPageEl, $newPageEl, $oldNavbarEl, $newNavbarEl, direction, transition2, callback) {
    const router = this;
    if (router.params.animateCustom) {
      router.params.animateCustom.apply(router, [$oldPageEl, $newPageEl, $oldNavbarEl, $newNavbarEl, direction, callback]);
      return;
    }
    const dynamicNavbar = router.dynamicNavbar;
    const ios = router.app.theme === "ios";
    if (transition2) {
      const routerCustomTransitionClass = `router-transition-custom router-transition-${transition2}-${direction}`;
      const onCustomTransitionDone = () => {
        router.$el.removeClass(routerCustomTransitionClass);
        if (dynamicNavbar && router.$navbarsEl.length) {
          if ($newNavbarEl) {
            router.$navbarsEl.prepend($newNavbarEl);
          }
          if ($oldNavbarEl) {
            router.$navbarsEl.prepend($oldNavbarEl);
          }
        }
        if (callback) callback();
      };
      (direction === "forward" ? $newPageEl : $oldPageEl).animationEnd(onCustomTransitionDone);
      if (dynamicNavbar) {
        if ($newNavbarEl && $newPageEl) {
          router.setNavbarPosition($newNavbarEl, "");
          $newNavbarEl.removeClass("navbar-next navbar-previous navbar-current");
          $newPageEl.prepend($newNavbarEl);
        }
        if ($oldNavbarEl && $oldPageEl) {
          router.setNavbarPosition($oldNavbarEl, "");
          $oldNavbarEl.removeClass("navbar-next navbar-previous navbar-current");
          $oldPageEl.prepend($oldNavbarEl);
        }
      }
      router.$el.addClass(routerCustomTransitionClass);
      return;
    }
    const routerTransitionClass = `router-transition-${direction} router-transition`;
    let newNavEls;
    let oldNavEls;
    let fromLarge;
    let toLarge;
    let toDifferent;
    let oldIsLarge;
    let newIsLarge;
    if (ios && dynamicNavbar) {
      const betweenMasterAndDetail = router.params.masterDetailBreakpoint > 0 && router.app.width >= router.params.masterDetailBreakpoint && ($oldNavbarEl.hasClass("navbar-master") && $newNavbarEl.hasClass("navbar-master-detail") || $oldNavbarEl.hasClass("navbar-master-detail") && $newNavbarEl.hasClass("navbar-master"));
      if (!betweenMasterAndDetail) {
        oldIsLarge = $oldNavbarEl && $oldNavbarEl.hasClass("navbar-large");
        newIsLarge = $newNavbarEl && $newNavbarEl.hasClass("navbar-large");
        fromLarge = oldIsLarge && !$oldNavbarEl.hasClass("navbar-large-collapsed");
        toLarge = newIsLarge && !$newNavbarEl.hasClass("navbar-large-collapsed");
        toDifferent = fromLarge && !toLarge || toLarge && !fromLarge;
      }
      const navEls = router.animatableNavElements($newNavbarEl, $oldNavbarEl, toLarge, fromLarge, direction);
      newNavEls = navEls.newNavEls;
      oldNavEls = navEls.oldNavEls;
    }
    function animateNavbars(progress) {
      if (!(ios && dynamicNavbar)) return;
      if (progress === 1) {
        if (toLarge) {
          $newNavbarEl.addClass("router-navbar-transition-to-large");
          $oldNavbarEl.addClass("router-navbar-transition-to-large");
        }
        if (fromLarge) {
          $newNavbarEl.addClass("router-navbar-transition-from-large");
          $oldNavbarEl.addClass("router-navbar-transition-from-large");
        }
      }
      newNavEls.forEach((navEl) => {
        const $el = navEl.$el;
        const offset2 = direction === "forward" ? navEl.rightOffset : navEl.leftOffset;
        if (navEl.isSliding) {
          if (navEl.isSubnavbar && newIsLarge) {
            $el[0].style.setProperty("transform", `translate3d(${offset2 * (1 - progress)}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`, "important");
          } else {
            $el.transform(`translate3d(${offset2 * (1 - progress)}px,0,0)`);
          }
        }
      });
      oldNavEls.forEach((navEl) => {
        const $el = navEl.$el;
        const offset2 = direction === "forward" ? navEl.leftOffset : navEl.rightOffset;
        if (navEl.isSliding) {
          if (navEl.isSubnavbar && oldIsLarge) {
            $el.transform(`translate3d(${offset2 * progress}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`);
          } else {
            $el.transform(`translate3d(${offset2 * progress}px,0,0)`);
          }
        }
      });
    }
    function onDone() {
      if (router.dynamicNavbar) {
        if ($newNavbarEl) {
          $newNavbarEl.removeClass("router-navbar-transition-to-large router-navbar-transition-from-large");
          $newNavbarEl.addClass("navbar-no-title-large-transition");
          nextFrame(() => {
            $newNavbarEl.removeClass("navbar-no-title-large-transition");
          });
        }
        if ($oldNavbarEl) {
          $oldNavbarEl.removeClass("router-navbar-transition-to-large router-navbar-transition-from-large");
        }
        if ($newNavbarEl.hasClass("sliding") || $newNavbarEl.children(".navbar-inner.sliding").length) {
          $newNavbarEl.find(".title, .left, .right, .left .icon, .subnavbar").transform("");
        } else {
          $newNavbarEl.find(".sliding").transform("");
        }
        if ($oldNavbarEl.hasClass("sliding") || $oldNavbarEl.children(".navbar-inner.sliding").length) {
          $oldNavbarEl.find(".title, .left, .right, .left .icon, .subnavbar").transform("");
        } else {
          $oldNavbarEl.find(".sliding").transform("");
        }
      }
      router.$el.removeClass(routerTransitionClass);
      if (callback) callback();
    }
    (direction === "forward" ? $newPageEl : ios ? $oldPageEl : $newPageEl).animationEnd(() => {
      onDone();
    });
    if (dynamicNavbar) {
      animateNavbars(0);
      nextFrame(() => {
        router.$el.addClass(routerTransitionClass);
        if (toDifferent) {
          router.el._clientLeft = router.el.clientLeft;
        }
        animateNavbars(1);
      });
    } else {
      router.$el.addClass(routerTransitionClass);
    }
  }
  removeModal(modalEl) {
    const router = this;
    router.removeEl(modalEl);
  }
  // eslint-disable-next-line
  removeTabContent(tabEl) {
    const $tabEl = dom7_default(tabEl);
    $tabEl.html("");
  }
  removeNavbar(el) {
    const router = this;
    router.removeEl(el);
  }
  removePage(el) {
    const $el = dom7_default(el);
    const f7Page = $el && $el[0] && $el[0].f7Page;
    const router = this;
    if (f7Page && f7Page.route && f7Page.route.route && f7Page.route.route.keepAlive) {
      $el.remove();
      return;
    }
    router.removeEl(el);
  }
  removeEl(el) {
    if (!el) return;
    const router = this;
    const $el = dom7_default(el);
    if ($el.length === 0) return;
    $el.find(".tab").each((tabEl) => {
      dom7_default(tabEl).children().each((tabChild) => {
        if (tabChild.f7Component) {
          dom7_default(tabChild).trigger("tab:beforeremove");
          tabChild.f7Component.destroy();
        }
      });
    });
    if ($el[0].f7Component && $el[0].f7Component.destroy) {
      $el[0].f7Component.destroy();
    }
    if (!router.params.removeElements) {
      return;
    }
    if (router.params.removeElementsWithTimeout) {
      setTimeout(() => {
        $el.remove();
      }, router.params.removeElementsTimeout);
    } else {
      $el.remove();
    }
  }
  getPageEl(content) {
    const router = this;
    if (typeof content === "string") {
      router.tempDom.innerHTML = content;
    } else {
      if (dom7_default(content).hasClass("page")) {
        return content;
      }
      router.tempDom.innerHTML = "";
      dom7_default(router.tempDom).append(content);
    }
    return router.findElement(".page", router.tempDom);
  }
  findElement(stringSelector, container) {
    const router = this;
    const view = router.view;
    const app = router.app;
    const modalsSelector = ".popup, .dialog, .popover, .actions-modal, .sheet-modal, .login-screen, .page";
    const $container = dom7_default(container);
    const selector = stringSelector;
    let found = $container.find(selector).filter((el) => dom7_default(el).parents(modalsSelector).length === 0);
    if (found.length > 1) {
      if (typeof view.selector === "string") {
        found = $container.find(`${view.selector} ${selector}`);
      }
      if (found.length > 1) {
        found = $container.find(`.${app.params.viewMainClass} ${selector}`);
      }
    }
    if (found.length === 1) return found;
    found = router.findElement(selector, $container);
    if (found && found.length === 1) return found;
    if (found && found.length > 1) return dom7_default(found[0]);
    return void 0;
  }
  flattenRoutes(routes) {
    if (routes === void 0) {
      routes = this.routes;
    }
    const router = this;
    let flattenedRoutes = [];
    routes.forEach((route) => {
      let hasTabRoutes = false;
      if ("tabs" in route && route.tabs) {
        const mergedPathsRoutes = route.tabs.map((tabRoute) => {
          const tRoute = extend2({}, route, {
            path: `${route.path}/${tabRoute.path}`.replace("///", "/").replace("//", "/"),
            parentPath: route.path,
            tab: tabRoute
          });
          delete tRoute.tabs;
          delete tRoute.routes;
          return tRoute;
        });
        hasTabRoutes = true;
        flattenedRoutes = flattenedRoutes.concat(router.flattenRoutes(mergedPathsRoutes));
      }
      if ("detailRoutes" in route) {
        const mergedPathsRoutes = route.detailRoutes.map((detailRoute) => {
          const dRoute = extend2({}, detailRoute);
          dRoute.masterRoute = route;
          dRoute.masterRoutePath = route.path;
          return dRoute;
        });
        flattenedRoutes = flattenedRoutes.concat(route, router.flattenRoutes(mergedPathsRoutes));
      }
      if ("routes" in route) {
        const mergedPathsRoutes = route.routes.map((childRoute) => {
          const cRoute = extend2({}, childRoute);
          cRoute.path = `${route.path}/${cRoute.path}`.replace("///", "/").replace("//", "/");
          return cRoute;
        });
        if (hasTabRoutes) {
          flattenedRoutes = flattenedRoutes.concat(router.flattenRoutes(mergedPathsRoutes));
        } else {
          flattenedRoutes = flattenedRoutes.concat(route, router.flattenRoutes(mergedPathsRoutes));
        }
      }
      if (!("routes" in route) && !("tabs" in route && route.tabs) && !("detailRoutes" in route)) {
        flattenedRoutes.push(route);
      }
    });
    return flattenedRoutes;
  }
  // eslint-disable-next-line
  parseRouteUrl(url) {
    if (!url) return {};
    const query = parseUrlQuery(url);
    const hash = url.split("#")[1];
    const params = {};
    const path = url.split("#")[0].split("?")[0];
    return {
      query,
      hash,
      params,
      url,
      path
    };
  }
  generateUrl(parameters) {
    if (parameters === void 0) {
      parameters = {};
    }
    if (typeof parameters === "string") {
      return parameters;
    }
    const {
      name,
      path,
      params,
      query
    } = parameters;
    if (!name && !path) {
      throw new Error('Framework7: "name" or "path" parameter is required');
    }
    const router = this;
    const route = name ? router.findRouteByKey("name", name) : router.findRouteByKey("path", path);
    if (!route) {
      if (name) {
        throw new Error(`Framework7: route with name "${name}" not found`);
      } else {
        throw new Error(`Framework7: route with path "${path}" not found`);
      }
    }
    const url = router.constructRouteUrl(route, {
      params,
      query
    });
    if (url === "") {
      return "/";
    }
    if (!url) {
      throw new Error(`Framework7: can't construct URL for route with name "${name}"`);
    }
    return url;
  }
  // eslint-disable-next-line
  constructRouteUrl(route, _temp) {
    let {
      params,
      query
    } = _temp === void 0 ? {} : _temp;
    const {
      path
    } = route;
    const toUrl = compile(path);
    let url;
    try {
      url = toUrl(params || {});
    } catch (error) {
      throw new Error(`Framework7: error constructing route URL from passed params:
Route: ${path}
${error.toString()}`);
    }
    if (query) {
      if (typeof query === "string") url += `?${query}`;
      else if (Object.keys(query).length) url += `?${serializeObject(query)}`;
    }
    return url;
  }
  findTabRouteUrl(tabEl) {
    const router = this;
    const $tabEl = dom7_default(tabEl);
    const parentPath = router.currentRoute.route.parentPath;
    const tabId = $tabEl.attr("id");
    const flattenedRoutes = router.flattenRoutes(router.routes);
    let foundTabRouteUrl;
    flattenedRoutes.forEach((route) => {
      if (route.parentPath === parentPath && route.tab && route.tab.id === tabId) {
        if (router.currentRoute.params && Object.keys(router.currentRoute.params).length > 0) {
          foundTabRouteUrl = router.constructRouteUrl(route, {
            params: router.currentRoute.params,
            query: router.currentRoute.query
          });
        } else {
          foundTabRouteUrl = route.path;
        }
      }
    });
    return foundTabRouteUrl;
  }
  findRouteByKey(key, value2) {
    const router = this;
    const routes = router.routes;
    const flattenedRoutes = router.flattenRoutes(routes);
    let matchingRoute;
    flattenedRoutes.forEach((route) => {
      if (matchingRoute) return;
      if (route[key] === value2) {
        matchingRoute = route;
      }
    });
    return matchingRoute;
  }
  findMatchingRoute(url) {
    if (!url) return void 0;
    const router = this;
    const routes = router.routes;
    const flattenedRoutes = router.flattenRoutes(routes);
    const {
      path,
      query,
      hash,
      params
    } = router.parseRouteUrl(url);
    let matchingRoute;
    flattenedRoutes.forEach((route) => {
      if (matchingRoute) return;
      const keys = [];
      const pathsToMatch = [route.path || "/"];
      if (route.alias) {
        if (typeof route.alias === "string") pathsToMatch.push(route.alias);
        else if (Array.isArray(route.alias)) {
          route.alias.forEach((aliasPath) => {
            pathsToMatch.push(aliasPath);
          });
        }
      }
      let matched;
      pathsToMatch.forEach((pathToMatch) => {
        if (matched) return;
        matched = pathToRegexp(pathToMatch, keys).exec(path || "/");
      });
      if (matched) {
        keys.forEach((keyObj, index2) => {
          if (typeof keyObj.name === "number") return;
          const paramValue = matched[index2 + 1];
          if (typeof paramValue === "undefined" || paramValue === null) {
            params[keyObj.name] = paramValue;
          } else {
            params[keyObj.name] = decodeURIComponent(paramValue);
          }
        });
        let parentPath;
        if (route.parentPath) {
          parentPath = (path || "/").split("/").slice(0, route.parentPath.split("/").length - 1).join("/");
        }
        matchingRoute = {
          query,
          hash,
          params,
          url,
          path: path || "/",
          parentPath,
          route,
          name: route.name
        };
      }
    });
    return matchingRoute;
  }
  // eslint-disable-next-line
  replaceRequestUrlParams(url, options) {
    if (url === void 0) {
      url = "";
    }
    if (options === void 0) {
      options = {};
    }
    let compiledUrl = url;
    if (typeof compiledUrl === "string" && compiledUrl.indexOf("{{") >= 0 && options && options.route && options.route.params && Object.keys(options.route.params).length) {
      Object.keys(options.route.params).forEach((paramName) => {
        const regExp = new RegExp(`{{${paramName}}}`, "g");
        compiledUrl = compiledUrl.replace(regExp, options.route.params[paramName] || "");
      });
    }
    return compiledUrl;
  }
  removeFromXhrCache(url) {
    const router = this;
    const xhrCache = router.cache.xhr;
    let index2 = false;
    for (let i = 0; i < xhrCache.length; i += 1) {
      if (xhrCache[i].url === url) index2 = i;
    }
    if (index2 !== false) xhrCache.splice(index2, 1);
  }
  xhrRequest(requestUrl, options) {
    const router = this;
    const params = router.params;
    const {
      ignoreCache
    } = options;
    let url = requestUrl;
    let hasQuery = url.indexOf("?") >= 0;
    if (params.passRouteQueryToRequest && options && options.route && options.route.query && Object.keys(options.route.query).length) {
      url += `${hasQuery ? "&" : "?"}${serializeObject(options.route.query)}`;
      hasQuery = true;
    }
    if (params.passRouteParamsToRequest && options && options.route && options.route.params && Object.keys(options.route.params).length) {
      url += `${hasQuery ? "&" : "?"}${serializeObject(options.route.params)}`;
      hasQuery = true;
    }
    if (url.indexOf("{{") >= 0) {
      url = router.replaceRequestUrlParams(url, options);
    }
    if (params.xhrCacheIgnoreGetParameters && url.indexOf("?") >= 0) {
      url = url.split("?")[0];
    }
    return new Promise((resolve, reject) => {
      if (params.xhrCache && !ignoreCache && url.indexOf("nocache") < 0 && params.xhrCacheIgnore.indexOf(url) < 0) {
        for (let i = 0; i < router.cache.xhr.length; i += 1) {
          const cachedUrl = router.cache.xhr[i];
          if (cachedUrl.url === url) {
            if (now() - cachedUrl.time < params.xhrCacheDuration) {
              resolve(cachedUrl.content);
              return;
            }
          }
        }
      }
      router.xhrAbortController = new AbortController();
      let fetchRes;
      fetch(url, {
        signal: router.xhrAbortController.signal,
        method: "GET"
      }).then((res) => {
        fetchRes = res;
        return res.text();
      }).then((responseText) => {
        const {
          status
        } = fetchRes;
        router.emit("routerAjaxComplete", fetchRes);
        if (status !== "error" && status !== "timeout" && status >= 200 && status < 300 || status === 0) {
          if (params.xhrCache && responseText !== "") {
            router.removeFromXhrCache(url);
            router.cache.xhr.push({
              url,
              time: now(),
              content: responseText
            });
          }
          router.emit("routerAjaxSuccess", fetchRes, options);
          resolve(responseText);
        } else {
          router.emit("routerAjaxError", fetchRes, options);
          reject(fetchRes);
        }
      }).catch((err) => {
        reject(err);
      });
    });
  }
  setNavbarPosition($el, position, ariaHidden) {
    const router = this;
    $el.removeClass("navbar-previous navbar-current navbar-next");
    if (position) {
      $el.addClass(`navbar-${position}`);
    }
    if (ariaHidden === false) {
      $el.removeAttr("aria-hidden");
    } else if (ariaHidden === true) {
      $el.attr("aria-hidden", "true");
    }
    $el.trigger("navbar:position", {
      position
    });
    router.emit("navbarPosition", $el[0], position);
  }
  setPagePosition($el, position, ariaHidden) {
    const router = this;
    $el.removeClass("page-previous page-current page-next");
    $el.addClass(`page-${position}`);
    if (ariaHidden === false) {
      $el.removeAttr("aria-hidden");
    } else if (ariaHidden === true) {
      $el.attr("aria-hidden", "true");
    }
    $el.trigger("page:position", {
      position
    });
    router.emit("pagePosition", $el[0], position);
  }
  // Remove theme elements
  removeThemeElements(el) {
    const router = this;
    const theme = router.app.theme;
    let toRemove;
    if (theme === "ios") {
      toRemove = ".md-only, .if-md, .if-not-ios, .not-ios";
    } else if (theme === "md") {
      toRemove = ".ios-only, .if-ios, .if-not-md, .not-md";
    }
    dom7_default(el).find(toRemove).remove();
  }
  getPageData(pageEl, navbarEl, from, to, route, pageFromEl) {
    if (route === void 0) {
      route = {};
    }
    const router = this;
    const $pageEl = dom7_default(pageEl).eq(0);
    const $navbarEl = dom7_default(navbarEl).eq(0);
    const currentPage = $pageEl[0].f7Page || {};
    let direction;
    let pageFrom;
    if (from === "next" && to === "current" || from === "current" && to === "previous") direction = "forward";
    if (from === "current" && to === "next" || from === "previous" && to === "current") direction = "backward";
    if (currentPage && !currentPage.fromPage) {
      const $pageFromEl = dom7_default(pageFromEl);
      if ($pageFromEl.length) {
        pageFrom = $pageFromEl[0].f7Page;
      }
    }
    pageFrom = currentPage.pageFrom || pageFrom;
    if (pageFrom && pageFrom.pageFrom) {
      pageFrom.pageFrom = null;
    }
    const page = {
      app: router.app,
      view: router.view,
      router,
      $el: $pageEl,
      el: $pageEl[0],
      $pageEl,
      pageEl: $pageEl[0],
      $navbarEl,
      navbarEl: $navbarEl[0],
      name: $pageEl.attr("data-name"),
      position: from,
      from,
      to,
      direction,
      route: currentPage.route ? currentPage.route : route,
      pageFrom
    };
    $pageEl[0].f7Page = page;
    return page;
  }
  // Callbacks
  pageCallback(callback, pageEl, navbarEl, from, to, options, pageFromEl) {
    if (options === void 0) {
      options = {};
    }
    if (!pageEl) return;
    const router = this;
    const $pageEl = dom7_default(pageEl);
    if (!$pageEl.length) return;
    const $navbarEl = dom7_default(navbarEl);
    const {
      route
    } = options;
    const restoreScrollTopOnBack = router.params.restoreScrollTopOnBack && !(router.params.masterDetailBreakpoint > 0 && $pageEl.hasClass("page-master") && router.app.width >= router.params.masterDetailBreakpoint);
    const keepAlive = $pageEl[0].f7Page && $pageEl[0].f7Page.route && $pageEl[0].f7Page.route.route && $pageEl[0].f7Page.route.route.keepAlive;
    if (callback === "beforeRemove" && keepAlive) {
      callback = "beforeUnmount";
    }
    const camelName = `page${callback[0].toUpperCase() + callback.slice(1, callback.length)}`;
    const colonName = `page:${callback.toLowerCase()}`;
    let page = {};
    if (callback === "beforeRemove" && $pageEl[0].f7Page) {
      page = extend2($pageEl[0].f7Page, {
        from,
        to,
        position: from
      });
    } else {
      page = router.getPageData($pageEl[0], $navbarEl[0], from, to, route, pageFromEl);
    }
    page.swipeBack = !!options.swipeBack;
    const {
      on: on2 = {},
      once: once2 = {}
    } = options.route ? options.route.route : {};
    if (options.on) {
      extend2(on2, options.on);
    }
    if (options.once) {
      extend2(once2, options.once);
    }
    function attachEvents() {
      if ($pageEl[0].f7RouteEventsAttached) return;
      $pageEl[0].f7RouteEventsAttached = true;
      if (on2 && Object.keys(on2).length > 0) {
        $pageEl[0].f7RouteEventsOn = on2;
        Object.keys(on2).forEach((eventName) => {
          on2[eventName] = on2[eventName].bind(router);
          $pageEl.on(eventNameToColonCase(eventName), on2[eventName]);
        });
      }
      if (once2 && Object.keys(once2).length > 0) {
        $pageEl[0].f7RouteEventsOnce = once2;
        Object.keys(once2).forEach((eventName) => {
          once2[eventName] = once2[eventName].bind(router);
          $pageEl.once(eventNameToColonCase(eventName), once2[eventName]);
        });
      }
    }
    function detachEvents() {
      if (!$pageEl[0].f7RouteEventsAttached) return;
      if ($pageEl[0].f7RouteEventsOn) {
        Object.keys($pageEl[0].f7RouteEventsOn).forEach((eventName) => {
          $pageEl.off(eventNameToColonCase(eventName), $pageEl[0].f7RouteEventsOn[eventName]);
        });
      }
      if ($pageEl[0].f7RouteEventsOnce) {
        Object.keys($pageEl[0].f7RouteEventsOnce).forEach((eventName) => {
          $pageEl.off(eventNameToColonCase(eventName), $pageEl[0].f7RouteEventsOnce[eventName]);
        });
      }
      $pageEl[0].f7RouteEventsAttached = null;
      $pageEl[0].f7RouteEventsOn = null;
      $pageEl[0].f7RouteEventsOnce = null;
      delete $pageEl[0].f7RouteEventsAttached;
      delete $pageEl[0].f7RouteEventsOn;
      delete $pageEl[0].f7RouteEventsOnce;
    }
    if (callback === "mounted") {
      attachEvents();
    }
    if (callback === "init") {
      if (restoreScrollTopOnBack && (from === "previous" || !from) && to === "current" && router.scrollHistory[page.route.url] && !$pageEl.hasClass("no-restore-scroll")) {
        let $pageContent = $pageEl.find(".page-content");
        if ($pageContent.length > 0) {
          $pageContent = $pageContent.filter((pageContentEl) => {
            return dom7_default(pageContentEl).parents(".tab:not(.tab-active)").length === 0 && !dom7_default(pageContentEl).is(".tab:not(.tab-active)");
          });
        }
        $pageContent.scrollTop(router.scrollHistory[page.route.url]);
      }
      attachEvents();
      if ($pageEl[0].f7PageInitialized) {
        $pageEl.trigger("page:reinit", page);
        router.emit("pageReinit", page);
        return;
      }
      $pageEl[0].f7PageInitialized = true;
    }
    if (restoreScrollTopOnBack && callback === "beforeOut" && from === "current" && to === "previous") {
      let $pageContent = $pageEl.find(".page-content");
      if ($pageContent.length > 0) {
        $pageContent = $pageContent.filter((pageContentEl) => {
          return dom7_default(pageContentEl).parents(".tab:not(.tab-active)").length === 0 && !dom7_default(pageContentEl).is(".tab:not(.tab-active)");
        });
      }
      router.scrollHistory[page.route.url] = $pageContent.scrollTop();
    }
    if (restoreScrollTopOnBack && callback === "beforeOut" && from === "current" && to === "next") {
      delete router.scrollHistory[page.route.url];
    }
    $pageEl.trigger(colonName, page);
    router.emit(camelName, page);
    if (callback === "beforeRemove" || callback === "beforeUnmount") {
      detachEvents();
      if (!keepAlive) {
        if ($pageEl[0].f7Page && $pageEl[0].f7Page.navbarEl) {
          delete $pageEl[0].f7Page.navbarEl.f7Page;
        }
        $pageEl[0].f7Page = null;
      }
    }
  }
  saveHistory() {
    const router = this;
    const window2 = getWindow();
    router.view.history = router.history;
    if (router.params.browserHistory && router.params.browserHistoryStoreHistory && window2.localStorage) {
      window2.localStorage[`f7router-${router.view.id}-history`] = JSON.stringify(router.history);
    }
  }
  restoreHistory() {
    const router = this;
    const window2 = getWindow();
    if (router.params.browserHistory && router.params.browserHistoryStoreHistory && window2.localStorage && window2.localStorage[`f7router-${router.view.id}-history`]) {
      router.history = JSON.parse(window2.localStorage[`f7router-${router.view.id}-history`]);
      router.view.history = router.history;
    }
  }
  clearHistory() {
    const router = this;
    router.history = [];
    if (router.view) router.view.history = [];
    router.saveHistory();
  }
  updateCurrentUrl(newUrl) {
    const router = this;
    appRouterCheck(router, "updateCurrentUrl");
    if (router.history.length) {
      router.history[router.history.length - 1] = newUrl;
    } else {
      router.history.push(newUrl);
    }
    const {
      query,
      hash,
      params,
      url,
      path
    } = router.parseRouteUrl(newUrl);
    if (router.currentRoute) {
      extend2(router.currentRoute, {
        query,
        hash,
        params,
        url,
        path
      });
    }
    if (router.params.browserHistory) {
      const browserHistoryRoot = router.params.browserHistoryRoot || "";
      history_default.replace(router.view.id, {
        url: newUrl
      }, browserHistoryRoot + router.params.browserHistorySeparator + newUrl);
    }
    router.saveHistory();
    router.emit("routeUrlUpdate", router.currentRoute, router);
  }
  getInitialUrl() {
    const router = this;
    if (router.initialUrl) {
      return {
        initialUrl: router.initialUrl,
        historyRestored: router.historyRestored
      };
    }
    const {
      app,
      view
    } = router;
    const document2 = getDocument();
    const window2 = getWindow();
    const location = app.params.url && typeof app.params.url === "string" && typeof URL !== "undefined" ? new URL(app.params.url) : document2.location;
    let initialUrl = router.params.url;
    let documentUrl = location.href.split(location.origin)[1];
    let historyRestored;
    const {
      browserHistory,
      browserHistoryOnLoad,
      browserHistorySeparator
    } = router.params;
    let {
      browserHistoryRoot
    } = router.params;
    if ((window2.cordova || window2.Capacitor && window2.Capacitor.isNative) && browserHistory && !browserHistorySeparator && !browserHistoryRoot && location.pathname.indexOf("index.html")) {
      console.warn("Framework7: wrong or not complete browserHistory configuration, trying to guess browserHistoryRoot");
      browserHistoryRoot = location.pathname.split("index.html")[0];
    }
    if (!browserHistory || !browserHistoryOnLoad) {
      if (!initialUrl) {
        initialUrl = documentUrl;
      }
      if (location.search && initialUrl.indexOf("?") < 0) {
        initialUrl += location.search;
      }
      if (location.hash && initialUrl.indexOf("#") < 0) {
        initialUrl += location.hash;
      }
    } else {
      if (browserHistoryRoot && documentUrl.indexOf(browserHistoryRoot) >= 0) {
        documentUrl = documentUrl.substring(documentUrl.indexOf(browserHistoryRoot) + browserHistoryRoot.length);
        if (documentUrl === "") documentUrl = "/";
      }
      if (browserHistorySeparator.length > 0 && documentUrl.indexOf(browserHistorySeparator) >= 0) {
        initialUrl = documentUrl.substring(documentUrl.indexOf(browserHistorySeparator) + browserHistorySeparator.length);
      } else {
        initialUrl = documentUrl;
      }
      router.restoreHistory();
      if (router.history.indexOf(initialUrl) >= 0) {
        router.history = router.history.slice(0, router.history.indexOf(initialUrl) + 1);
      } else if (router.params.url === initialUrl) {
        router.history = [initialUrl];
      } else if (history_default.state && history_default.state[view.id] && history_default.state[view.id].url === router.history[router.history.length - 1]) {
        initialUrl = router.history[router.history.length - 1];
      } else {
        router.history = [documentUrl.split(browserHistorySeparator)[0] || "/", initialUrl];
      }
      if (router.history.length > 1) {
        historyRestored = true;
      } else {
        router.history = [];
      }
      router.saveHistory();
    }
    router.initialUrl = initialUrl;
    router.historyRestored = historyRestored;
    return {
      initialUrl,
      historyRestored
    };
  }
  init() {
    const router = this;
    const {
      app,
      view
    } = router;
    const document2 = getDocument();
    router.mount();
    const {
      initialUrl,
      historyRestored
    } = router.getInitialUrl();
    if (view && router.params.iosSwipeBack && app.theme === "ios" || view && router.params.mdSwipeBack && app.theme === "md") {
      swipe_back_default(router);
    }
    const {
      browserHistory,
      browserHistoryOnLoad,
      browserHistoryAnimateOnLoad,
      browserHistoryInitialMatch
    } = router.params;
    let currentRoute;
    if (router.history.length > 1) {
      const initUrl = browserHistoryInitialMatch ? initialUrl : router.history[0];
      currentRoute = router.findMatchingRoute(initUrl);
      if (!currentRoute) {
        currentRoute = extend2(router.parseRouteUrl(initUrl), {
          route: {
            url: initUrl,
            path: initUrl.split("?")[0]
          }
        });
      }
    } else {
      currentRoute = router.findMatchingRoute(initialUrl);
      if (!currentRoute) {
        currentRoute = extend2(router.parseRouteUrl(initialUrl), {
          route: {
            url: initialUrl,
            path: initialUrl.split("?")[0]
          }
        });
      }
    }
    if (router.$el.children(".page").length === 0 && initialUrl && router.params.loadInitialPage) {
      router.navigate(initialUrl, {
        initial: true,
        reloadCurrent: true,
        browserHistory: false,
        animate: false,
        once: {
          modalOpen() {
            if (!historyRestored) return;
            const preloadPreviousPage = router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`];
            if (preloadPreviousPage && router.history.length > 1) {
              router.back({
                preload: true
              });
            }
          },
          pageAfterIn() {
            if (!historyRestored) return;
            const preloadPreviousPage = router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`];
            if (preloadPreviousPage && router.history.length > 1) {
              router.back({
                preload: true
              });
            }
          }
        }
      });
    } else if (router.$el.children(".page").length) {
      let hasTabRoute;
      router.currentRoute = currentRoute;
      router.$el.children(".page").each((pageEl) => {
        const $pageEl = dom7_default(pageEl);
        let $navbarEl;
        router.setPagePosition($pageEl, "current");
        if (router.dynamicNavbar) {
          $navbarEl = $pageEl.children(".navbar");
          if ($navbarEl.length > 0) {
            if (!router.$navbarsEl.parents(document2).length) {
              router.$el.prepend(router.$navbarsEl);
            }
            router.setNavbarPosition($navbarEl, "current");
            router.$navbarsEl.append($navbarEl);
            if ($navbarEl.children(".title-large").length) {
              $navbarEl.addClass("navbar-large");
            }
            $pageEl.children(".navbar").remove();
          } else {
            router.$navbarsEl.addClass("navbar-hidden");
            if ($navbarEl.children(".title-large").length) {
              router.$navbarsEl.addClass("navbar-hidden navbar-large-hidden");
            }
          }
        }
        if (router.currentRoute && router.currentRoute.route && (router.currentRoute.route.master === true || typeof router.currentRoute.route.master === "function" && router.currentRoute.route.master(app, router)) && router.params.masterDetailBreakpoint > 0) {
          $pageEl.addClass("page-master");
          $pageEl.trigger("page:role", {
            role: "master"
          });
          if ($navbarEl && $navbarEl.length) {
            $navbarEl.addClass("navbar-master");
          }
          view.checkMasterDetailBreakpoint();
        }
        const initOptions = {
          route: router.currentRoute
        };
        if (router.currentRoute && router.currentRoute.route && router.currentRoute.route.options) {
          extend2(initOptions, router.currentRoute.route.options);
        }
        router.currentPageEl = $pageEl[0];
        if (router.dynamicNavbar && $navbarEl.length) {
          router.currentNavbarEl = $navbarEl[0];
        }
        router.removeThemeElements($pageEl);
        if (router.dynamicNavbar && $navbarEl.length) {
          router.removeThemeElements($navbarEl);
        }
        if (initOptions.route.route.tab) {
          hasTabRoute = true;
          router.tabLoad(initOptions.route.route.tab, extend2({}, initOptions));
        }
        router.pageCallback("init", $pageEl, $navbarEl, "current", void 0, initOptions);
        router.pageCallback("beforeIn", $pageEl, $navbarEl, "current", void 0, initOptions);
        router.pageCallback("afterIn", $pageEl, $navbarEl, "current", void 0, initOptions);
      });
      if (historyRestored) {
        if (browserHistoryInitialMatch) {
          const preloadPreviousPage = router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`];
          if (preloadPreviousPage && router.history.length > 1) {
            router.back({
              preload: true
            });
          }
        } else {
          router.navigate(initialUrl, {
            initial: true,
            browserHistory: false,
            history: false,
            animate: browserHistoryAnimateOnLoad,
            once: {
              pageAfterIn() {
                const preloadPreviousPage = router.params.preloadPreviousPage || router.params[`${app.theme}SwipeBack`];
                if (preloadPreviousPage && router.history.length > 2) {
                  router.back({
                    preload: true
                  });
                }
              }
            }
          });
        }
      }
      if (!historyRestored && !hasTabRoute) {
        router.history.push(initialUrl);
        router.saveHistory();
      }
    }
    if (initialUrl && browserHistory && browserHistoryOnLoad && (!history_default.state || !history_default.state[view.id])) {
      history_default.initViewState(view.id, {
        url: initialUrl
      });
    }
    router.emit("local::init routerInit", router);
  }
  destroy() {
    let router = this;
    router.emit("local::destroy routerDestroy", router);
    Object.keys(router).forEach((routerProp) => {
      router[routerProp] = null;
      delete router[routerProp];
    });
    router = null;
  }
};
Router.prototype.navigate = navigate;
Router.prototype.refreshPage = refreshPage;
Router.prototype.tabLoad = tabLoad;
Router.prototype.tabRemove = tabRemove;
Router.prototype.modalLoad = modalLoad;
Router.prototype.modalRemove = modalRemove;
Router.prototype.back = back;
Router.prototype.clearPreviousHistory = clearPreviousHistory;
var router_class_default = Router;

// node_modules/framework7/modules/router/router.js
var router_default = {
  name: "router",
  static: {
    Router: router_class_default
  },
  instance: {
    cache: {
      xhr: [],
      templates: [],
      components: []
    }
  },
  create() {
    const instance = this;
    if (instance.app) {
      if (instance.params.router) {
        instance.router = new router_class_default(instance.app, instance);
      }
    } else {
      instance.router = new router_class_default(instance);
    }
  }
};

// node_modules/framework7/components/view/resizable-view.js
function resizableView(view) {
  const app = view.app;
  const support2 = getSupport();
  if (view.resizableInitialized) return;
  extend2(view, {
    resizable: true,
    resizableWidth: null,
    resizableInitialized: true
  });
  const $htmlEl = dom7_default("html");
  const {
    $el
  } = view;
  if (!$el) return;
  let $resizeHandlerEl;
  let isTouched;
  let isMoved;
  const touchesStart = {};
  let touchesDiff;
  let width2;
  let minWidth;
  let maxWidth;
  function transformCSSWidth(v) {
    if (!v) return null;
    if (v.indexOf("%") >= 0 || v.indexOf("vw") >= 0) {
      return parseInt(v, 10) / 100 * app.width;
    }
    const newV = parseInt(v, 10);
    if (Number.isNaN(newV)) return null;
    return newV;
  }
  function isResizable() {
    return view.resizable && $el.hasClass("view-resizable") && $el.hasClass("view-master-detail");
  }
  function handleTouchStart(e) {
    if (!isResizable()) return;
    touchesStart.x = e.type === "touchstart" ? e.targetTouches[0].pageX : e.pageX;
    touchesStart.y = e.type === "touchstart" ? e.targetTouches[0].pageY : e.pageY;
    isMoved = false;
    isTouched = true;
    const $pageMasterEl = $el.children(".page-master");
    minWidth = transformCSSWidth($pageMasterEl.css("min-width"));
    maxWidth = transformCSSWidth($pageMasterEl.css("max-width"));
  }
  function handleTouchMove(e) {
    if (!isTouched) return;
    e.f7PreventSwipePanel = true;
    const pageX = e.type === "touchmove" ? e.targetTouches[0].pageX : e.pageX;
    if (!isMoved) {
      width2 = $resizeHandlerEl[0].offsetLeft + $resizeHandlerEl[0].offsetWidth;
      $el.addClass("view-resizing");
      $htmlEl.css("cursor", "col-resize");
    }
    isMoved = true;
    e.preventDefault();
    touchesDiff = pageX - touchesStart.x;
    let newWidth = width2 + touchesDiff;
    if (minWidth && !Number.isNaN(minWidth)) {
      newWidth = Math.max(newWidth, minWidth);
    }
    if (maxWidth && !Number.isNaN(maxWidth)) {
      newWidth = Math.min(newWidth, maxWidth);
    }
    newWidth = Math.min(Math.max(newWidth, 0), app.width);
    view.resizableWidth = newWidth;
    $htmlEl[0].style.setProperty("--f7-page-master-width", `${newWidth}px`);
    $el.trigger("view:resize", newWidth);
    view.emit("local::resize viewResize", view, newWidth);
  }
  function handleTouchEnd() {
    dom7_default("html").css("cursor", "");
    if (!isTouched || !isMoved) {
      isTouched = false;
      isMoved = false;
      return;
    }
    isTouched = false;
    isMoved = false;
    $htmlEl[0].style.setProperty("--f7-page-master-width", `${view.resizableWidth}px`);
    $el.removeClass("view-resizing");
  }
  function handleResize() {
    if (!view.resizableWidth) return;
    minWidth = transformCSSWidth($resizeHandlerEl.css("min-width"));
    maxWidth = transformCSSWidth($resizeHandlerEl.css("max-width"));
    if (minWidth && !Number.isNaN(minWidth) && view.resizableWidth < minWidth) {
      view.resizableWidth = Math.max(view.resizableWidth, minWidth);
    }
    if (maxWidth && !Number.isNaN(maxWidth) && view.resizableWidth > maxWidth) {
      view.resizableWidth = Math.min(view.resizableWidth, maxWidth);
    }
    view.resizableWidth = Math.min(Math.max(view.resizableWidth, 0), app.width);
    $htmlEl[0].style.setProperty("--f7-page-master-width", `${view.resizableWidth}px`);
  }
  $resizeHandlerEl = view.$el.children(".view-resize-handler");
  if (!$resizeHandlerEl.length) {
    view.$el.append('<div class="view-resize-handler"></div>');
    $resizeHandlerEl = view.$el.children(".view-resize-handler");
  }
  view.$resizeHandlerEl = $resizeHandlerEl;
  $el.addClass("view-resizable");
  const passive = support2.passiveListener ? {
    passive: true
  } : false;
  view.$el.on(app.touchEvents.start, ".view-resize-handler", handleTouchStart, passive);
  app.on("touchmove:active", handleTouchMove);
  app.on("touchend:passive", handleTouchEnd);
  app.on("resize", handleResize);
  view.on("beforeOpen", handleResize);
  view.once("viewDestroy", () => {
    $el.removeClass("view-resizable");
    view.$resizeHandlerEl.remove();
    view.$el.off(app.touchEvents.start, ".view-resize-handler", handleTouchStart, passive);
    app.off("touchmove:active", handleTouchMove);
    app.off("touchend:passive", handleTouchEnd);
    app.off("resize", handleResize);
    view.off("beforeOpen", handleResize);
  });
}
var resizable_view_default = resizableView;

// node_modules/framework7/components/view/view-class.js
var View = class extends class_default {
  constructor(app, el, viewParams) {
    if (viewParams === void 0) {
      viewParams = {};
    }
    super(viewParams, [app]);
    const view = this;
    const ssr = view.params.routerId;
    const defaults = {
      routes: [],
      routesAdd: []
    };
    if (!ssr) {
      const $el = dom7_default(el);
      if (!$el.length) {
        let message = "Framework7: can't create a View instance because ";
        message += typeof el === "string" ? `the selector "${el}" didn't match any element` : "el must be an HTMLElement or Dom7 object";
        throw new Error(message);
      }
    }
    view.params = extend2({
      el
    }, defaults, app.params.view, viewParams);
    if (view.params.routes.length > 0) {
      view.routes = view.params.routes;
    } else {
      view.routes = [].concat(app.routes, view.params.routesAdd);
    }
    extend2(false, view, {
      app,
      name: view.params.name,
      main: view.params.main,
      history: [],
      scrollHistory: {}
    });
    view.useModules();
    app.views.push(view);
    if (view.main) {
      app.views.main = view;
    }
    if (view.name) {
      app.views[view.name] = view;
    }
    view.index = app.views.indexOf(view);
    let viewId;
    if (view.name) {
      viewId = `view_${view.name}`;
    } else if (view.main) {
      viewId = "view_main";
    } else {
      viewId = `view_${view.index}`;
    }
    view.id = viewId;
    if (!view.params.init) {
      return view;
    }
    if (app.initialized) {
      view.init();
    } else {
      app.on("init", () => {
        view.init();
      });
    }
    return view;
  }
  destroy() {
    let view = this;
    const app = view.app;
    view.$el.trigger("view:beforedestroy");
    view.emit("local::beforeDestroy viewBeforeDestroy", view);
    app.off("resize", view.checkMasterDetailBreakpoint);
    if (view.main) {
      app.views.main = null;
      delete app.views.main;
    } else if (view.name) {
      app.views[view.name] = null;
      delete app.views[view.name];
    }
    view.$el[0].f7View = null;
    delete view.$el[0].f7View;
    app.views.splice(app.views.indexOf(view), 1);
    if (view.params.router && view.router) {
      view.router.destroy();
    }
    view.emit("local::destroy viewDestroy", view);
    Object.keys(view).forEach((viewProp) => {
      view[viewProp] = null;
      delete view[viewProp];
    });
    view = null;
  }
  checkMasterDetailBreakpoint(force) {
    const view = this;
    const app = view.app;
    const wasMasterDetail = view.$el.hasClass("view-master-detail");
    const isMasterDetail = app.width >= view.params.masterDetailBreakpoint && view.$el.children(".page-master").length;
    if (typeof force === "undefined" && isMasterDetail || force === true) {
      view.$el.addClass("view-master-detail");
      if (!wasMasterDetail) {
        view.emit("local::masterDetailBreakpoint viewMasterDetailBreakpoint", view);
        view.$el.trigger("view:masterDetailBreakpoint");
      }
    } else {
      view.$el.removeClass("view-master-detail");
      if (wasMasterDetail) {
        view.emit("local::masterDetailBreakpoint viewMasterDetailBreakpoint", view);
        view.$el.trigger("view:masterDetailBreakpoint");
      }
    }
  }
  initMasterDetail() {
    const view = this;
    const app = view.app;
    view.checkMasterDetailBreakpoint = view.checkMasterDetailBreakpoint.bind(view);
    view.checkMasterDetailBreakpoint();
    if (view.params.masterDetailResizable) {
      resizable_view_default(view);
    }
    app.on("resize", view.checkMasterDetailBreakpoint);
  }
  mount(viewEl) {
    const view = this;
    const app = view.app;
    const el = view.params.el || viewEl;
    const $el = dom7_default(el);
    let selector;
    if (typeof el === "string") selector = el;
    else {
      selector = ($el.attr("id") ? `#${$el.attr("id")}` : "") + ($el.attr("class") ? `.${$el.attr("class").replace(/ /g, ".").replace(".active", "")}` : "");
    }
    let $navbarsEl;
    if (app.theme === "ios" && view.params.iosDynamicNavbar) {
      $navbarsEl = $el.children(".navbars").eq(0);
      if ($navbarsEl.length === 0) {
        $navbarsEl = dom7_default('<div class="navbars"></div>');
      }
    }
    extend2(view, {
      $el,
      el: $el[0],
      main: view.main || $el.hasClass("view-main"),
      $navbarsEl,
      navbarsEl: $navbarsEl ? $navbarsEl[0] : void 0,
      selector
    });
    if (view.main) {
      app.views.main = view;
    }
    if ($el && $el[0]) {
      $el[0].f7View = view;
    }
    view.emit("local::mount viewMount", view);
  }
  init(viewEl) {
    const view = this;
    view.mount(viewEl);
    if (view.params.router) {
      if (view.params.masterDetailBreakpoint > 0) {
        view.initMasterDetail();
      }
      if (view.params.initRouterOnTabShow && view.$el.hasClass("tab") && !view.$el.hasClass("tab-active")) {
        view.$el.once("tab:show", () => {
          view.router.init();
        });
      } else {
        view.router.init();
      }
      view.$el.trigger("view:init");
      view.emit("local::init viewInit", view);
    }
  }
};
View.use(router_default);
var view_class_default = View;

// node_modules/framework7/modules/clicks/clicks.js
function initClicks(app) {
  function handleClicks(e) {
    const window2 = getWindow();
    const $clickedEl = dom7_default(e.target);
    const $clickedLinkEl = $clickedEl.closest("a");
    const isLink = $clickedLinkEl.length > 0;
    const url = isLink && $clickedLinkEl.attr("href");
    if (isLink) {
      if ($clickedLinkEl.is(app.params.clicks.externalLinks) || // eslint-disable-next-line
      url && url.indexOf("javascript:") >= 0) {
        const target = $clickedLinkEl.attr("target");
        if (url && window2.cordova && window2.cordova.InAppBrowser && (target === "_system" || target === "_blank")) {
          e.preventDefault();
          window2.cordova.InAppBrowser.open(url, target);
        } else if (url && window2.Capacitor && window2.Capacitor.Plugins && window2.Capacitor.Plugins.Browser && (target === "_system" || target === "_blank")) {
          e.preventDefault();
          window2.Capacitor.Plugins.Browser.open({
            url
          });
        }
        return;
      }
    }
    Object.keys(app.modules).forEach((moduleName) => {
      const moduleClicks = app.modules[moduleName].clicks;
      if (!moduleClicks) return;
      if (e.preventF7Router) return;
      Object.keys(moduleClicks).forEach((clickSelector) => {
        const matchingClickedElement = $clickedEl.closest(clickSelector).eq(0);
        if (matchingClickedElement.length > 0) {
          moduleClicks[clickSelector].call(app, matchingClickedElement, matchingClickedElement.dataset(), e);
        }
      });
    });
    let clickedLinkData = {};
    if (isLink) {
      e.preventDefault();
      clickedLinkData = $clickedLinkEl.dataset();
    }
    clickedLinkData.clickedEl = $clickedLinkEl[0];
    if (e.preventF7Router) return;
    if ($clickedLinkEl.hasClass("prevent-router") || $clickedLinkEl.hasClass("router-prevent")) return;
    const validUrl = url && url.length > 0 && url[0] !== "#";
    if (validUrl || $clickedLinkEl.hasClass("back")) {
      let view;
      if (clickedLinkData.view && clickedLinkData.view === "current") {
        view = app.views.current;
      } else if (clickedLinkData.view) {
        view = dom7_default(clickedLinkData.view)[0].f7View;
      } else {
        view = $clickedEl.parents(".view")[0] && $clickedEl.parents(".view")[0].f7View;
        if (view && view.params.linksView && (!$clickedLinkEl.hasClass("back") || $clickedLinkEl.hasClass("back") && view.router.history.length === 1)) {
          if (typeof view.params.linksView === "string") view = dom7_default(view.params.linksView)[0].f7View;
          else if (view.params.linksView instanceof view_class_default) view = view.params.linksView;
        }
      }
      if (!view) {
        if (app.views.main) view = app.views.main;
      }
      if (!view || !view.router) return;
      if ($clickedLinkEl[0].f7RouteProps) {
        clickedLinkData.props = $clickedLinkEl[0].f7RouteProps;
      }
      if ($clickedLinkEl.hasClass("back")) view.router.back(url, clickedLinkData);
      else view.router.navigate(url, clickedLinkData);
    }
  }
  app.on("click", handleClicks);
}
var clicks_default = {
  name: "clicks",
  params: {
    clicks: {
      // External Links
      externalLinks: ".external"
    }
  },
  on: {
    init() {
      const app = this;
      initClicks(app);
    }
  }
};

// node_modules/framework7/modules/history/history.js
var history_default2 = {
  name: "history",
  static: {
    history: history_default
  },
  on: {
    init() {
      history_default.init(this);
    }
  }
};

// node_modules/framework7/modules/service-worker/service-worker.js
var SW = {
  registrations: [],
  register(path, scope) {
    const app = this;
    const window2 = getWindow();
    if (!("serviceWorker" in window2.navigator) || !app.serviceWorker.container) {
      return new Promise((resolve, reject) => {
        reject(new Error("Service worker is not supported"));
      });
    }
    return new Promise((resolve, reject) => {
      app.serviceWorker.container.register(path, scope ? {
        scope
      } : {}).then((reg) => {
        SW.registrations.push(reg);
        app.emit("serviceWorkerRegisterSuccess", reg);
        resolve(reg);
      }).catch((error) => {
        app.emit("serviceWorkerRegisterError", error);
        reject(error);
      });
    });
  },
  unregister(registration) {
    const app = this;
    const window2 = getWindow();
    if (!("serviceWorker" in window2.navigator) || !app.serviceWorker.container) {
      return new Promise((resolve, reject) => {
        reject(new Error("Service worker is not supported"));
      });
    }
    let registrations;
    if (!registration) registrations = SW.registrations;
    else if (Array.isArray(registration)) registrations = registration;
    else registrations = [registration];
    return Promise.all(registrations.map((reg) => new Promise((resolve, reject) => {
      reg.unregister().then(() => {
        if (SW.registrations.indexOf(reg) >= 0) {
          SW.registrations.splice(SW.registrations.indexOf(reg), 1);
        }
        app.emit("serviceWorkerUnregisterSuccess", reg);
        resolve();
      }).catch((error) => {
        app.emit("serviceWorkerUnregisterError", reg, error);
        reject(error);
      });
    })));
  }
};
var service_worker_default = {
  name: "sw",
  params: {
    serviceWorker: {
      path: void 0,
      scope: void 0
    }
  },
  create() {
    const app = this;
    const window2 = getWindow();
    extend2(app, {
      serviceWorker: {
        container: "serviceWorker" in window2.navigator ? window2.navigator.serviceWorker : void 0,
        registrations: SW.registrations,
        register: SW.register.bind(app),
        unregister: SW.unregister.bind(app)
      }
    });
  },
  on: {
    init() {
      const window2 = getWindow();
      if (!("serviceWorker" in window2.navigator)) return;
      const app = this;
      if (app.device.cordova || window2.Capacitor && window2.Capacitor.isNative) return;
      if (!app.serviceWorker.container) return;
      const paths = app.params.serviceWorker.path;
      const scope = app.params.serviceWorker.scope;
      if (!paths || Array.isArray(paths) && !paths.length) return;
      const toRegister = Array.isArray(paths) ? paths : [paths];
      toRegister.forEach((path) => {
        app.serviceWorker.register(path, scope);
      });
    }
  }
};

// node_modules/framework7/modules/store/create-store.js
function createStore(storeParams) {
  if (storeParams === void 0) {
    storeParams = {};
  }
  const store = {
    __store: true
  };
  const originalState = {
    ...storeParams.state || {}
  };
  const actions = {
    ...storeParams.actions || {}
  };
  const getters = {
    ...storeParams.getters || {}
  };
  const state = extend2({}, originalState);
  let propsQueue = [];
  const gettersDependencies = {};
  const gettersCallbacks = {};
  Object.keys(getters).forEach((getterKey) => {
    gettersDependencies[getterKey] = [];
    gettersCallbacks[getterKey] = [];
  });
  const getGetterValue = (getterKey) => {
    return getters[getterKey]({
      state: store.state
    });
  };
  const addGetterDependencies = (getterKey, deps) => {
    if (!gettersDependencies[getterKey]) gettersDependencies[getterKey] = [];
    deps.forEach((dep) => {
      if (gettersDependencies[getterKey].indexOf(dep) < 0) {
        gettersDependencies[getterKey].push(dep);
      }
    });
  };
  const addGetterCallback = (getterKey, callback) => {
    if (!gettersCallbacks[getterKey]) gettersCallbacks[getterKey] = [];
    gettersCallbacks[getterKey].push(callback);
  };
  const runGetterCallbacks = (stateKey) => {
    const keys = Object.keys(gettersDependencies).filter((getterKey) => {
      return gettersDependencies[getterKey].indexOf(stateKey) >= 0;
    });
    keys.forEach((getterKey) => {
      if (!gettersCallbacks[getterKey] || !gettersCallbacks[getterKey].length) return;
      gettersCallbacks[getterKey].forEach((callback) => {
        callback(getGetterValue(getterKey));
      });
    });
  };
  const removeGetterCallback = (callback) => {
    Object.keys(gettersCallbacks).forEach((stateKey) => {
      const callbacks = gettersCallbacks[stateKey];
      if (callbacks.indexOf(callback) >= 0) {
        callbacks.splice(callbacks.indexOf(callback), 1);
      }
    });
  };
  store.__removeCallback = (callback) => {
    removeGetterCallback(callback);
  };
  const getterValue = function(getterKey, addCallback) {
    if (addCallback === void 0) {
      addCallback = true;
    }
    if (getterKey === "constructor") return void 0;
    propsQueue = [];
    const value2 = getGetterValue(getterKey);
    addGetterDependencies(getterKey, propsQueue);
    const onUpdated = (callback2) => {
      addGetterCallback(getterKey, callback2);
    };
    const obj = {
      value: value2,
      onUpdated
    };
    if (!addCallback) {
      return obj;
    }
    const callback = (v) => {
      obj.value = v;
    };
    obj.__callback = callback;
    addGetterCallback(getterKey, callback);
    return obj;
  };
  store.state = new Proxy(state, {
    set: (target, prop2, value2) => {
      target[prop2] = value2;
      runGetterCallbacks(prop2);
      return true;
    },
    get: (target, prop2) => {
      propsQueue.push(prop2);
      return target[prop2];
    }
  });
  store.getters = new Proxy(getters, {
    set: () => false,
    get: (target, prop2) => {
      if (!target[prop2]) {
        return void 0;
      }
      return getterValue(prop2, true);
    }
  });
  store._gettersPlain = new Proxy(getters, {
    set: () => false,
    get: (target, prop2) => {
      if (!target[prop2]) {
        return void 0;
      }
      return getterValue(prop2, false);
    }
  });
  store.dispatch = (actionName, data2) => {
    return new Promise((resolve, reject) => {
      if (!actions[actionName]) {
        reject();
        throw new Error(`Framework7: Store action "${actionName}" is not found`);
      }
      const result = actions[actionName]({
        state: store.state,
        dispatch: store.dispatch
      }, data2);
      resolve(result);
    });
  };
  return store;
}
var create_store_default = createStore;

// node_modules/framework7/modules/store/store.js
var store_default = {
  name: "store",
  static: {
    createStore: create_store_default
  },
  proto: {
    createStore: create_store_default
  }
};

// node_modules/framework7/components/statusbar/statusbar.js
var isCapacitor = () => {
  const window2 = getWindow();
  return window2.Capacitor && window2.Capacitor.isNative && window2.Capacitor.Plugins && window2.Capacitor.Plugins.StatusBar;
};
var Statusbar = {
  hide() {
    const window2 = getWindow();
    const device = getDevice();
    if (device.cordova && window2.StatusBar) {
      window2.StatusBar.hide();
    }
    if (isCapacitor()) {
      window2.Capacitor.Plugins.StatusBar.hide();
    }
  },
  show() {
    const window2 = getWindow();
    const device = getDevice();
    if (device.cordova && window2.StatusBar) {
      window2.StatusBar.show();
    }
    if (isCapacitor()) {
      window2.Capacitor.Plugins.StatusBar.show();
    }
  },
  onClick() {
    const app = this;
    let pageContent;
    if (dom7_default(".popup.modal-in").length > 0) {
      pageContent = dom7_default(".popup.modal-in").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content");
    } else if (dom7_default(".panel.panel-in").length > 0) {
      pageContent = dom7_default(".panel.panel-in").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content");
    } else if (dom7_default(".views > .view.tab-active").length > 0) {
      pageContent = dom7_default(".views > .view.tab-active").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content");
    } else if (dom7_default(".views").length > 0) {
      pageContent = dom7_default(".views").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content");
    } else {
      pageContent = app.$el.children(".view").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content");
    }
    if (pageContent && pageContent.length > 0) {
      if (pageContent.hasClass("tab")) {
        pageContent = pageContent.parent(".tabs").children(".page-content.tab-active");
      }
      if (pageContent.length > 0) pageContent.scrollTop(0, 300);
    }
  },
  setTextColor(color) {
    const window2 = getWindow();
    const device = getDevice();
    if (device.cordova && window2.StatusBar) {
      if (color === "white") {
        window2.StatusBar.styleLightContent();
      } else {
        window2.StatusBar.styleDefault();
      }
    }
    if (isCapacitor()) {
      if (color === "white") {
        window2.Capacitor.Plugins.StatusBar.setStyle({
          style: "DARK"
        });
      } else {
        window2.Capacitor.Plugins.StatusBar.setStyle({
          style: "LIGHT"
        });
      }
    }
  },
  setBackgroundColor(color) {
    const window2 = getWindow();
    const device = getDevice();
    if (device.cordova && window2.StatusBar) {
      window2.StatusBar.backgroundColorByHexString(color);
    }
    if (isCapacitor()) {
      window2.Capacitor.Plugins.StatusBar.setBackgroundColor({
        color
      });
    }
  },
  isVisible() {
    const window2 = getWindow();
    const device = getDevice();
    return new Promise((resolve) => {
      if (device.cordova && window2.StatusBar) {
        resolve(window2.StatusBar.isVisible);
      }
      if (isCapacitor()) {
        window2.Capacitor.Plugins.StatusBar.getInfo().then((info) => {
          resolve(info.visible);
        });
      }
      resolve(false);
    });
  },
  overlaysWebView(overlays) {
    if (overlays === void 0) {
      overlays = true;
    }
    const window2 = getWindow();
    const device = getDevice();
    if (device.cordova && window2.StatusBar) {
      window2.StatusBar.overlaysWebView(overlays);
    }
    if (isCapacitor()) {
      window2.Capacitor.Plugins.StatusBar.setOverlaysWebView({
        overlay: overlays
      });
    }
  },
  init() {
    const app = this;
    const window2 = getWindow();
    const device = getDevice();
    const params = app.params.statusbar;
    if (!params.enabled) return;
    const isCordova = device.cordova && window2.StatusBar;
    const isCap = isCapacitor();
    if (isCordova || isCap) {
      if (params.scrollTopOnClick) {
        dom7_default(window2).on("statusTap", Statusbar.onClick.bind(app));
      }
      if (device.ios) {
        if (params.iosOverlaysWebView) {
          Statusbar.overlaysWebView(true);
        } else {
          Statusbar.overlaysWebView(false);
        }
        if (params.iosTextColor === "white") {
          Statusbar.setTextColor("white");
        } else {
          Statusbar.setTextColor("black");
        }
      }
      if (device.android) {
        if (params.androidOverlaysWebView) {
          Statusbar.overlaysWebView(true);
        } else {
          Statusbar.overlaysWebView(false);
        }
        if (params.androidTextColor === "white") {
          Statusbar.setTextColor("white");
        } else {
          Statusbar.setTextColor("black");
        }
      }
    }
    if (params.iosBackgroundColor && device.ios) {
      Statusbar.setBackgroundColor(params.iosBackgroundColor);
    }
    if (params.androidBackgroundColor && device.android) {
      Statusbar.setBackgroundColor(params.androidBackgroundColor);
    }
  }
};
var statusbar_default = {
  name: "statusbar",
  params: {
    statusbar: {
      enabled: true,
      scrollTopOnClick: true,
      iosOverlaysWebView: true,
      iosTextColor: "black",
      iosBackgroundColor: null,
      androidOverlaysWebView: false,
      androidTextColor: "black",
      androidBackgroundColor: null
    }
  },
  create() {
    const app = this;
    bindMethods(app, {
      statusbar: Statusbar
    });
  },
  on: {
    init() {
      const app = this;
      Statusbar.init.call(app);
    }
  }
};

// node_modules/framework7/components/view/view.js
function getCurrentView(app) {
  const $popoverView = dom7_default(".popover.modal-in .view");
  const $popupView = dom7_default(".popup.modal-in .view");
  const $panelView = dom7_default(".panel.panel-in .view");
  let $viewsEl = dom7_default(".views");
  if ($viewsEl.length === 0) $viewsEl = app.$el;
  let $viewEl = $viewsEl.children(".view");
  if ($viewEl.length === 0) {
    $viewEl = $viewsEl.children(".tabs").children(".view");
  }
  if ($viewEl.length > 1) {
    if ($viewEl.hasClass("tab")) {
      $viewEl = $viewsEl.children(".view.tab-active");
      if ($viewEl.length === 0) {
        $viewEl = $viewsEl.children(".tabs").children(".view.tab-active");
      }
    } else {
    }
  }
  if ($popoverView.length > 0 && $popoverView[0].f7View) return $popoverView[0].f7View;
  if ($popupView.length > 0 && $popupView[0].f7View) return $popupView[0].f7View;
  if ($panelView.length > 0 && $panelView[0].f7View) return $panelView[0].f7View;
  if ($viewEl.length > 0) {
    if ($viewEl.length === 1 && $viewEl[0].f7View) return $viewEl[0].f7View;
    if ($viewEl.length > 1) {
      return app.views.main;
    }
  }
  return void 0;
}
var view_default = {
  name: "view",
  params: {
    view: {
      init: true,
      initRouterOnTabShow: false,
      name: void 0,
      main: false,
      router: true,
      linksView: null,
      xhrCache: true,
      xhrCacheIgnore: [],
      xhrCacheIgnoreGetParameters: false,
      xhrCacheDuration: 1e3 * 60 * 10,
      // Ten minutes
      componentCache: true,
      preloadPreviousPage: true,
      allowDuplicateUrls: false,
      reloadPages: false,
      reloadDetail: false,
      masterDetailBreakpoint: 0,
      masterDetailResizable: false,
      removeElements: true,
      removeElementsWithTimeout: false,
      removeElementsTimeout: 0,
      restoreScrollTopOnBack: true,
      unloadTabContent: true,
      passRouteQueryToRequest: true,
      passRouteParamsToRequest: false,
      loadInitialPage: true,
      // Swipe Back
      iosSwipeBack: true,
      iosSwipeBackAnimateShadow: true,
      iosSwipeBackAnimateOpacity: true,
      iosSwipeBackActiveArea: 30,
      iosSwipeBackThreshold: 0,
      mdSwipeBack: false,
      mdSwipeBackAnimateShadow: true,
      mdSwipeBackAnimateOpacity: false,
      mdSwipeBackActiveArea: 30,
      mdSwipeBackThreshold: 0,
      // Push State
      browserHistory: false,
      browserHistoryRoot: void 0,
      browserHistoryAnimate: true,
      browserHistoryAnimateOnLoad: false,
      browserHistorySeparator: "#!",
      browserHistoryOnLoad: true,
      browserHistoryInitialMatch: false,
      browserHistoryStoreHistory: true,
      browserHistoryTabs: "replace",
      // Animate Pages
      animate: true,
      // iOS Dynamic Navbar
      iosDynamicNavbar: true,
      // Animate iOS Navbar Back Icon
      iosAnimateNavbarBackIcon: true,
      // Delays
      iosPageLoadDelay: 0,
      mdPageLoadDelay: 0,
      // Routes hooks
      routesBeforeEnter: null,
      routesBeforeLeave: null
    }
  },
  static: {
    View: view_class_default
  },
  create() {
    const app = this;
    extend2(app, {
      views: extend2([], {
        create(el, params) {
          return new view_class_default(app, el, params);
        },
        get(viewEl) {
          const $viewEl = dom7_default(viewEl);
          if ($viewEl.length && $viewEl[0].f7View) return $viewEl[0].f7View;
          return void 0;
        }
      })
    });
    Object.defineProperty(app.views, "current", {
      enumerable: true,
      configurable: true,
      get() {
        return getCurrentView(app);
      }
    });
    app.view = app.views;
  },
  on: {
    init() {
      const app = this;
      dom7_default(".view-init").each((viewEl) => {
        if (viewEl.f7View) return;
        const viewParams = dom7_default(viewEl).dataset();
        app.views.create(viewEl, viewParams);
      });
    },
    "modalOpen panelOpen": function onOpen(instance) {
      const app = this;
      instance.$el.find(".view-init").each((viewEl) => {
        if (viewEl.f7View) return;
        const viewParams = dom7_default(viewEl).dataset();
        app.views.create(viewEl, viewParams);
      });
    },
    "modalBeforeDestroy panelBeforeDestroy": function onClose(instance) {
      if (!instance || !instance.$el) return;
      instance.$el.find(".view-init").each((viewEl) => {
        const view = viewEl.f7View;
        if (!view) return;
        view.destroy();
      });
    }
  },
  vnode: {
    "view-init": {
      insert(vnode) {
        const app = this;
        const viewEl = vnode.elm;
        if (viewEl.f7View) return;
        const viewParams = dom7_default(viewEl).dataset();
        app.views.create(viewEl, viewParams);
      },
      destroy(vnode) {
        const viewEl = vnode.elm;
        const view = viewEl.f7View;
        if (!view) return;
        view.destroy();
      }
    }
  }
};

// node_modules/framework7/components/navbar/navbar.js
var Navbar = {
  size(el) {
    const app = this;
    let $el = dom7_default(el);
    if ($el.hasClass("navbars")) {
      $el = $el.children(".navbar").each((navbarEl) => {
        app.navbar.size(navbarEl);
      });
      return;
    }
    const $innerEl = $el.children(".navbar-inner");
    if (!$innerEl.length) return;
    const needCenterTitle = $innerEl.hasClass("navbar-inner-centered-title") || app.params.navbar[`${app.theme}CenterTitle`];
    const needLeftTitle = app.theme === "ios" && !app.params.navbar[`${app.theme}CenterTitle`];
    if (!needCenterTitle && !needLeftTitle) return;
    if ($el.parents(".tab:not(.tab-active)").length > 0 || $el.parents(".popup:not(.modal-in)").length > 0) {
      return;
    }
    if (app.theme !== "ios" && app.params.navbar[`${app.theme}CenterTitle`]) {
      $innerEl.addClass("navbar-inner-centered-title");
    }
    if (app.theme === "ios" && !app.params.navbar.iosCenterTitle) {
      $innerEl.addClass("navbar-inner-left-title");
    }
    const $viewEl = $el.parents(".view").eq(0);
    const left = app.rtl ? $innerEl.children(".right") : $innerEl.children(".left");
    const right = app.rtl ? $innerEl.children(".left") : $innerEl.children(".right");
    const title = $innerEl.children(".title");
    const subnavbar = $innerEl.children(".subnavbar");
    const noLeft = left.length === 0;
    const noRight = right.length === 0;
    const leftWidth = noLeft ? 0 : left.outerWidth(true);
    const rightWidth = noRight ? 0 : right.outerWidth(true);
    const titleWidth = title.outerWidth(true);
    const navbarStyles = $innerEl.styles();
    const navbarWidth = $innerEl[0].offsetWidth;
    const navbarInnerWidth = navbarWidth - parseInt(navbarStyles.paddingLeft, 10) - parseInt(navbarStyles.paddingRight, 10);
    const isPrevious = $el.hasClass("navbar-previous");
    const sliding = $innerEl.hasClass("sliding");
    let router;
    let dynamicNavbar;
    if ($viewEl.length > 0 && $viewEl[0].f7View) {
      router = $viewEl[0].f7View.router;
      dynamicNavbar = router && router.dynamicNavbar;
    }
    let currLeft;
    let diff;
    if (noRight) {
      currLeft = navbarInnerWidth - titleWidth;
    }
    if (noLeft) {
      currLeft = 0;
    }
    if (!noLeft && !noRight) {
      currLeft = (navbarInnerWidth - rightWidth - titleWidth + leftWidth) / 2;
    }
    let requiredLeft = (navbarInnerWidth - titleWidth) / 2;
    if (navbarInnerWidth - leftWidth - rightWidth > titleWidth) {
      if (requiredLeft < leftWidth) {
        requiredLeft = leftWidth;
      }
      if (requiredLeft + titleWidth > navbarInnerWidth - rightWidth) {
        requiredLeft = navbarInnerWidth - rightWidth - titleWidth;
      }
      diff = requiredLeft - currLeft;
    } else {
      diff = 0;
    }
    const inverter = app.rtl ? -1 : 1;
    if (dynamicNavbar && app.theme === "ios") {
      if (title.hasClass("sliding") || title.length > 0 && sliding) {
        let titleLeftOffset = -(currLeft + diff) * inverter;
        const titleRightOffset = (navbarInnerWidth - currLeft - diff - titleWidth) * inverter;
        if (isPrevious) {
          if (router && router.params.iosAnimateNavbarBackIcon) {
            const activeNavbarBackLink = $el.parent().find(".navbar-current").children(".left.sliding").find(".back .icon ~ span");
            if (activeNavbarBackLink.length > 0) {
              titleLeftOffset += activeNavbarBackLink[0].offsetLeft;
            }
          }
        }
        title[0].f7NavbarLeftOffset = titleLeftOffset;
        title[0].f7NavbarRightOffset = titleRightOffset;
      }
      if (!noLeft && (left.hasClass("sliding") || sliding)) {
        if (app.rtl) {
          left[0].f7NavbarLeftOffset = -(navbarInnerWidth - left[0].offsetWidth) / 2 * inverter;
          left[0].f7NavbarRightOffset = leftWidth * inverter;
        } else {
          left[0].f7NavbarLeftOffset = -leftWidth;
          left[0].f7NavbarRightOffset = (navbarInnerWidth - left[0].offsetWidth) / 2;
          if (router && router.params.iosAnimateNavbarBackIcon && left.find(".back .icon").length > 0) {
            if (left.find(".back .icon ~ span").length) {
              const leftOffset = left[0].f7NavbarLeftOffset;
              const rightOffset = left[0].f7NavbarRightOffset;
              left[0].f7NavbarLeftOffset = 0;
              left[0].f7NavbarRightOffset = 0;
              left.find(".back .icon ~ span")[0].f7NavbarLeftOffset = leftOffset;
              left.find(".back .icon ~ span")[0].f7NavbarRightOffset = rightOffset - left.find(".back .icon")[0].offsetWidth;
            }
          }
        }
      }
      if (!noRight && (right.hasClass("sliding") || sliding)) {
        if (app.rtl) {
          right[0].f7NavbarLeftOffset = -rightWidth * inverter;
          right[0].f7NavbarRightOffset = (navbarInnerWidth - right[0].offsetWidth) / 2 * inverter;
        } else {
          right[0].f7NavbarLeftOffset = -(navbarInnerWidth - right[0].offsetWidth) / 2;
          right[0].f7NavbarRightOffset = rightWidth;
        }
      }
      if (subnavbar.length && (subnavbar.hasClass("sliding") || sliding)) {
        subnavbar[0].f7NavbarLeftOffset = app.rtl ? subnavbar[0].offsetWidth : -subnavbar[0].offsetWidth;
        subnavbar[0].f7NavbarRightOffset = -subnavbar[0].f7NavbarLeftOffset;
      }
    }
    if (needCenterTitle) {
      let titleLeft = diff;
      if (app.rtl && noLeft && noRight && title.length > 0) titleLeft = -titleLeft;
      title.css({
        left: `${titleLeft}px`
      });
    }
  },
  hide(el, animate2, hideStatusbar, hideOnlyCurrent) {
    if (animate2 === void 0) {
      animate2 = true;
    }
    if (hideStatusbar === void 0) {
      hideStatusbar = false;
    }
    if (hideOnlyCurrent === void 0) {
      hideOnlyCurrent = false;
    }
    const app = this;
    let $el = dom7_default(el);
    const isDynamic = $el.hasClass("navbar") && $el.parent(".navbars").length && !hideOnlyCurrent;
    if (isDynamic) $el = $el.parents(".navbars");
    if (!$el.length) return;
    if ($el.hasClass("navbar-hidden")) return;
    let className = `navbar-hidden${animate2 ? " navbar-transitioning" : ""}`;
    const currentIsLarge = isDynamic ? $el.find(".navbar-current .title-large").length : $el.find(".title-large").length;
    if (currentIsLarge) {
      className += " navbar-large-hidden";
    }
    if (hideStatusbar) {
      className += " navbar-hidden-statusbar";
    }
    $el.transitionEnd(() => {
      $el.removeClass("navbar-transitioning");
    });
    $el.addClass(className);
    if (isDynamic) {
      $el.children(".navbar").each((subEl) => {
        dom7_default(subEl).trigger("navbar:hide");
        app.emit("navbarHide", subEl);
      });
    } else {
      $el.trigger("navbar:hide");
      app.emit("navbarHide", $el[0]);
    }
  },
  show(el, animate2, hideOnlyCurrent) {
    if (el === void 0) {
      el = ".navbar-hidden";
    }
    if (animate2 === void 0) {
      animate2 = true;
    }
    if (hideOnlyCurrent === void 0) {
      hideOnlyCurrent = false;
    }
    const app = this;
    let $el = dom7_default(el);
    const isDynamic = $el.hasClass("navbar") && $el.parent(".navbars").length && !hideOnlyCurrent;
    if (isDynamic) $el = $el.parents(".navbars");
    if (!$el.length) return;
    if (!$el.hasClass("navbar-hidden")) return;
    if (animate2) {
      $el.addClass("navbar-transitioning");
      $el.transitionEnd(() => {
        $el.removeClass("navbar-transitioning");
      });
    }
    $el.removeClass("navbar-hidden navbar-large-hidden navbar-hidden-statusbar");
    if (isDynamic) {
      $el.children(".navbar").each((subEl) => {
        dom7_default(subEl).trigger("navbar:show");
        app.emit("navbarShow", subEl);
      });
    } else {
      $el.trigger("navbar:show");
      app.emit("navbarShow", $el[0]);
    }
  },
  getElByPage(page) {
    let $pageEl;
    let $navbarEl;
    let pageData;
    if (page.$navbarEl || page.$el) {
      pageData = page;
      $pageEl = page.$el;
    } else {
      $pageEl = dom7_default(page);
      if ($pageEl.length > 0) pageData = $pageEl[0].f7Page;
    }
    if (pageData && pageData.$navbarEl && pageData.$navbarEl.length > 0) {
      $navbarEl = pageData.$navbarEl;
    } else if ($pageEl) {
      $navbarEl = $pageEl.children(".navbar");
    }
    if (!$navbarEl || $navbarEl && $navbarEl.length === 0) return void 0;
    return $navbarEl[0];
  },
  getPageByEl(navbarEl) {
    const $navbarEl = dom7_default(navbarEl);
    if ($navbarEl.parents(".page").length) {
      return $navbarEl.parents(".page")[0];
    }
    let pageEl;
    $navbarEl.parents(".view").find(".page").each((el) => {
      if (el && el.f7Page && el.f7Page.navbarEl && $navbarEl[0] === el.f7Page.navbarEl) {
        pageEl = el;
      }
    });
    return pageEl;
  },
  collapseLargeTitle(navbarEl) {
    const app = this;
    let $navbarEl = dom7_default(navbarEl);
    if ($navbarEl.hasClass("navbars")) {
      $navbarEl = $navbarEl.find(".navbar");
      if ($navbarEl.length > 1) {
        $navbarEl = dom7_default(navbarEl).find(".navbar-large.navbar-current");
      }
      if ($navbarEl.length > 1 || !$navbarEl.length) {
        return;
      }
    }
    const $pageEl = dom7_default(app.navbar.getPageByEl($navbarEl));
    $navbarEl.addClass("navbar-large-collapsed");
    $pageEl.eq(0).addClass("page-with-navbar-large-collapsed").trigger("page:navbarlargecollapsed");
    app.emit("pageNavbarLargeCollapsed", $pageEl[0]);
    $navbarEl.trigger("navbar:collapse");
    app.emit("navbarCollapse", $navbarEl[0]);
  },
  expandLargeTitle(navbarEl) {
    const app = this;
    let $navbarEl = dom7_default(navbarEl);
    if ($navbarEl.hasClass("navbars")) {
      $navbarEl = $navbarEl.find(".navbar-large");
      if ($navbarEl.length > 1) {
        $navbarEl = dom7_default(navbarEl).find(".navbar-large.navbar-current");
      }
      if ($navbarEl.length > 1 || !$navbarEl.length) {
        return;
      }
    }
    const $pageEl = dom7_default(app.navbar.getPageByEl($navbarEl));
    $navbarEl.removeClass("navbar-large-collapsed");
    $pageEl.eq(0).removeClass("page-with-navbar-large-collapsed").trigger("page:navbarlargeexpanded");
    app.emit("pageNavbarLargeExpanded", $pageEl[0]);
    $navbarEl.trigger("navbar:expand");
    app.emit("navbarExpand", $navbarEl[0]);
  },
  toggleLargeTitle(navbarEl) {
    const app = this;
    let $navbarEl = dom7_default(navbarEl);
    if ($navbarEl.hasClass("navbars")) {
      $navbarEl = $navbarEl.find(".navbar-large");
      if ($navbarEl.length > 1) {
        $navbarEl = dom7_default(navbarEl).find(".navbar-large.navbar-current");
      }
      if ($navbarEl.length > 1 || !$navbarEl.length) {
        return;
      }
    }
    if ($navbarEl.hasClass("navbar-large-collapsed")) {
      app.navbar.expandLargeTitle($navbarEl);
    } else {
      app.navbar.collapseLargeTitle($navbarEl);
    }
  },
  initNavbarOnScroll(pageEl, navbarEl, needHide, needCollapse, needTransparent) {
    const app = this;
    const support2 = getSupport();
    const $pageEl = dom7_default(pageEl);
    const $navbarEl = dom7_default(navbarEl);
    const $titleLargeEl = $navbarEl.find(".title-large");
    const isLarge = $titleLargeEl.length || $navbarEl.hasClass(".navbar-large");
    let navbarHideHeight = 44;
    const snapPageScrollToLargeTitle = app.params.navbar.snapPageScrollToLargeTitle;
    const snapPageScrollToTransparentNavbar = app.params.navbar.snapPageScrollToTransparentNavbar;
    let previousScrollTop;
    let currentScrollTop;
    let scrollHeight;
    let offsetHeight;
    let reachEnd;
    let action;
    let navbarHidden;
    let navbarCollapsed;
    let navbarTitleLargeHeight;
    let navbarOffsetHeight;
    if (needCollapse || needHide && isLarge) {
      navbarTitleLargeHeight = $navbarEl.css("--f7-navbar-large-title-height");
      if (navbarTitleLargeHeight && navbarTitleLargeHeight.indexOf("px") >= 0) {
        navbarTitleLargeHeight = parseInt(navbarTitleLargeHeight, 10);
        if (Number.isNaN(navbarTitleLargeHeight) && $titleLargeEl.length) {
          navbarTitleLargeHeight = $titleLargeEl[0].offsetHeight;
        } else if (Number.isNaN(navbarTitleLargeHeight)) {
          if (app.theme === "ios") navbarTitleLargeHeight = 52;
          else if (app.theme === "md") navbarTitleLargeHeight = 88;
        }
      } else if ($titleLargeEl.length) {
        navbarTitleLargeHeight = $titleLargeEl[0].offsetHeight;
      } else {
        if (app.theme === "ios") navbarTitleLargeHeight = 52;
        else if (app.theme === "md") navbarTitleLargeHeight = 88;
      }
    }
    if (needHide && isLarge) {
      navbarHideHeight += navbarTitleLargeHeight;
    }
    let scrollChanged;
    let scrollContent;
    let scrollTimeoutId;
    let touchEndTimeoutId;
    const touchSnapTimeout = 70;
    const desktopSnapTimeout = 300;
    function calcScrollableDistance() {
      $pageEl.find(".page-content").each((pageContentEl) => {
        pageContentEl.f7ScrollableDistance = pageContentEl.scrollHeight - pageContentEl.offsetHeight;
      });
    }
    function snapLargeNavbar() {
      const inSearchbarExpanded = $navbarEl.hasClass("with-searchbar-expandable-enabled");
      if (inSearchbarExpanded) return;
      if (!scrollContent || currentScrollTop < 0) return;
      if (currentScrollTop >= navbarTitleLargeHeight / 2 && currentScrollTop < navbarTitleLargeHeight) {
        dom7_default(scrollContent).scrollTop(navbarTitleLargeHeight, 100);
      } else if (currentScrollTop < navbarTitleLargeHeight) {
        dom7_default(scrollContent).scrollTop(0, 200);
      }
    }
    function snapTransparentNavbar() {
      const inSearchbarExpanded = $navbarEl.hasClass("with-searchbar-expandable-enabled");
      if (inSearchbarExpanded) return;
      if (!scrollContent || currentScrollTop < 0) return;
      if (currentScrollTop >= navbarOffsetHeight / 2 && currentScrollTop < navbarOffsetHeight) {
        dom7_default(scrollContent).scrollTop(navbarOffsetHeight, 100);
      } else if (currentScrollTop < navbarOffsetHeight) {
        dom7_default(scrollContent).scrollTop(0, 200);
      }
    }
    function handleNavbarTransparent() {
      const isHidden = $navbarEl.hasClass("navbar-hidden") || $navbarEl.parent(".navbars").hasClass("navbar-hidden");
      const inSearchbarExpanded = $navbarEl.hasClass("with-searchbar-expandable-enabled");
      if (inSearchbarExpanded || isHidden) return;
      if (!navbarOffsetHeight) {
        navbarOffsetHeight = navbarEl.offsetHeight;
      }
      let opacity = currentScrollTop / navbarOffsetHeight;
      const notTransparent = $navbarEl.hasClass("navbar-transparent-visible");
      opacity = Math.max(Math.min(opacity, 1), 0);
      if (notTransparent && opacity === 1 || !notTransparent && opacity === 0) {
        $navbarEl.find(".navbar-bg, .title").css("opacity", "");
        return;
      }
      if (notTransparent && opacity === 0) {
        $navbarEl.trigger("navbar:transparenthide");
        app.emit("navbarTransparentHide", $navbarEl[0]);
        $navbarEl.removeClass("navbar-transparent-visible");
        $navbarEl.find(".navbar-bg, .title").css("opacity", "");
        return;
      }
      if (!notTransparent && opacity === 1) {
        $navbarEl.trigger("navbar:transparentshow");
        app.emit("navbarTransparentShow", $navbarEl[0]);
        $navbarEl.addClass("navbar-transparent-visible");
        $navbarEl.find(".navbar-bg, .title").css("opacity", "");
        return;
      }
      $navbarEl.find(".navbar-bg, .title").css("opacity", opacity);
      if (snapPageScrollToTransparentNavbar) {
        if (!support2.touch) {
          clearTimeout(scrollTimeoutId);
          scrollTimeoutId = setTimeout(() => {
            snapTransparentNavbar();
          }, desktopSnapTimeout);
        } else if (touchEndTimeoutId) {
          clearTimeout(touchEndTimeoutId);
          touchEndTimeoutId = null;
          touchEndTimeoutId = setTimeout(() => {
            snapTransparentNavbar();
            clearTimeout(touchEndTimeoutId);
            touchEndTimeoutId = null;
          }, touchSnapTimeout);
        }
      }
    }
    let previousCollapseProgress = null;
    let collapseProgress = null;
    function handleLargeNavbarCollapse(pageContentEl) {
      const isHidden = $navbarEl.hasClass("navbar-hidden") || $navbarEl.parent(".navbars").hasClass("navbar-hidden");
      if (isHidden) return;
      const isLargeTransparent = $navbarEl.hasClass("navbar-large-transparent") || $navbarEl.hasClass("navbar-large") && $navbarEl.hasClass("navbar-transparent");
      previousCollapseProgress = collapseProgress;
      const scrollableDistance = Math.min(navbarTitleLargeHeight, pageContentEl.f7ScrollableDistance || navbarTitleLargeHeight);
      collapseProgress = Math.min(Math.max(currentScrollTop / scrollableDistance, 0), 1);
      const previousCollapseWasInMiddle = previousCollapseProgress > 0 && previousCollapseProgress < 1;
      const inSearchbarExpanded = $navbarEl.hasClass("with-searchbar-expandable-enabled");
      if (inSearchbarExpanded) return;
      navbarCollapsed = $navbarEl.hasClass("navbar-large-collapsed");
      const $bgEl = $navbarEl.find(".navbar-bg");
      if (collapseProgress === 0 && navbarCollapsed) {
        app.navbar.expandLargeTitle($navbarEl[0]);
      } else if (collapseProgress === 1 && !navbarCollapsed) {
        app.navbar.collapseLargeTitle($navbarEl[0]);
      }
      if (collapseProgress === 0 && navbarCollapsed || collapseProgress === 0 && previousCollapseWasInMiddle || collapseProgress === 1 && !navbarCollapsed || collapseProgress === 1 && previousCollapseWasInMiddle) {
        if (app.theme === "md") {
          $navbarEl.find(".navbar-inner").css("overflow", "");
        }
        $navbarEl.find(".title").css("opacity", "");
        $navbarEl.find(".title-large-text, .subnavbar").css("transform", "");
        $navbarEl.find(".title-large-text").css("opacity", "");
        if (isLargeTransparent) {
          $bgEl.css("opacity", "");
        }
        $bgEl.css("transform", "");
      } else if (collapseProgress > 0 && collapseProgress < 1) {
        if (app.theme === "md") {
          $navbarEl.find(".navbar-inner").css("overflow", "visible");
        }
        $navbarEl.find(".title").css("opacity", -0.5 + collapseProgress * 1.5);
        $navbarEl.find(".title-large-text, .subnavbar").css("transform", `translate3d(0px, ${-1 * collapseProgress * navbarTitleLargeHeight}px, 0)`);
        $navbarEl.find(".title-large-text").css("opacity", 1 - collapseProgress * 2);
        if (isLargeTransparent) {
          $bgEl.css("opacity", collapseProgress);
        }
        $bgEl.css("transform", `translate3d(0px, ${-1 * collapseProgress * navbarTitleLargeHeight}px, 0)`);
      }
      if (snapPageScrollToLargeTitle) {
        if (!support2.touch) {
          clearTimeout(scrollTimeoutId);
          scrollTimeoutId = setTimeout(() => {
            snapLargeNavbar();
          }, desktopSnapTimeout);
        } else if (touchEndTimeoutId) {
          clearTimeout(touchEndTimeoutId);
          touchEndTimeoutId = null;
          touchEndTimeoutId = setTimeout(() => {
            snapLargeNavbar();
            clearTimeout(touchEndTimeoutId);
            touchEndTimeoutId = null;
          }, touchSnapTimeout);
        }
      }
    }
    function handleTitleHideShow() {
      if ($pageEl.hasClass("page-with-card-opened")) return;
      scrollHeight = scrollContent.scrollHeight;
      offsetHeight = scrollContent.offsetHeight;
      reachEnd = currentScrollTop + offsetHeight >= scrollHeight;
      navbarHidden = $navbarEl.hasClass("navbar-hidden") || $navbarEl.parent(".navbars").hasClass("navbar-hidden");
      if (reachEnd) {
        if (app.params.navbar.showOnPageScrollEnd) {
          action = "show";
        }
      } else if (previousScrollTop > currentScrollTop) {
        if (app.params.navbar.showOnPageScrollTop || currentScrollTop <= navbarHideHeight) {
          action = "show";
        } else {
          action = "hide";
        }
      } else if (currentScrollTop > navbarHideHeight) {
        action = "hide";
      } else {
        action = "show";
      }
      if (action === "show" && navbarHidden) {
        app.navbar.show($navbarEl, true, true);
        navbarHidden = false;
      } else if (action === "hide" && !navbarHidden) {
        app.navbar.hide($navbarEl, true, false, true);
        navbarHidden = true;
      }
      previousScrollTop = currentScrollTop;
    }
    function handleScroll(e) {
      scrollContent = this;
      if (e && e.target && e.target !== scrollContent) {
        return;
      }
      currentScrollTop = scrollContent.scrollTop;
      scrollChanged = currentScrollTop;
      if (needCollapse) {
        handleLargeNavbarCollapse(scrollContent);
      } else if (needTransparent) {
        handleNavbarTransparent();
      }
      if ($pageEl.hasClass("page-previous")) return;
      if (needHide) {
        handleTitleHideShow();
      }
    }
    function handeTouchStart() {
      scrollChanged = false;
    }
    function handleTouchEnd() {
      clearTimeout(touchEndTimeoutId);
      touchEndTimeoutId = null;
      touchEndTimeoutId = setTimeout(() => {
        if (scrollChanged !== false) {
          if (needTransparent && !needCollapse) {
            snapTransparentNavbar();
          } else {
            snapLargeNavbar();
          }
          clearTimeout(touchEndTimeoutId);
          touchEndTimeoutId = null;
        }
      }, touchSnapTimeout);
    }
    $pageEl.on("scroll", ".page-content", handleScroll, true);
    if (support2.touch && (needCollapse && snapPageScrollToLargeTitle || needTransparent && snapPageScrollToTransparentNavbar)) {
      app.on("touchstart:passive", handeTouchStart);
      app.on("touchend:passive", handleTouchEnd);
    }
    calcScrollableDistance();
    if (needCollapse || needTransparent) {
      $pageEl.find(".page-content").each((pageContentEl) => {
        if (pageContentEl.scrollTop > 0) handleScroll.call(pageContentEl);
      });
    }
    app.on("resize", calcScrollableDistance);
    $pageEl[0].f7DetachNavbarScrollHandlers = function f7DetachNavbarScrollHandlers() {
      app.off("resize", calcScrollableDistance);
      delete $pageEl[0].f7DetachNavbarScrollHandlers;
      $pageEl.off("scroll", ".page-content", handleScroll, true);
      if (support2.touch && (needCollapse && snapPageScrollToLargeTitle || needTransparent && snapPageScrollToTransparentNavbar)) {
        app.off("touchstart:passive", handeTouchStart);
        app.off("touchend:passive", handleTouchEnd);
      }
    };
  }
};
var navbar_default = {
  name: "navbar",
  create() {
    const app = this;
    bindMethods(app, {
      navbar: Navbar
    });
  },
  params: {
    navbar: {
      scrollTopOnTitleClick: true,
      iosCenterTitle: true,
      mdCenterTitle: false,
      hideOnPageScroll: false,
      showOnPageScrollEnd: true,
      showOnPageScrollTop: true,
      collapseLargeTitleOnScroll: true,
      snapPageScrollToLargeTitle: true,
      snapPageScrollToTransparentNavbar: true
    }
  },
  on: {
    "panelBreakpoint panelCollapsedBreakpoint panelResize viewResize resize viewMasterDetailBreakpoint": function onPanelResize() {
      const app = this;
      dom7_default(".navbar").each((navbarEl) => {
        app.navbar.size(navbarEl);
      });
    },
    pageBeforeRemove(page) {
      if (page.$el[0].f7DetachNavbarScrollHandlers) {
        page.$el[0].f7DetachNavbarScrollHandlers();
      }
    },
    pageBeforeIn(page) {
      const app = this;
      if (app.theme !== "ios") return;
      let $navbarsEl;
      const view = page.$el.parents(".view")[0].f7View;
      const navbarEl = app.navbar.getElByPage(page);
      if (!navbarEl) {
        $navbarsEl = page.$el.parents(".view").children(".navbars");
      } else {
        $navbarsEl = dom7_default(navbarEl).parents(".navbars");
      }
      if (page.$el.hasClass("no-navbar") || view.router.dynamicNavbar && !navbarEl) {
        const animate2 = !!(page.pageFrom && page.router.history.length > 0);
        app.navbar.hide($navbarsEl, animate2);
      } else {
        app.navbar.show($navbarsEl);
      }
    },
    pageReinit(page) {
      const app = this;
      const $navbarEl = dom7_default(app.navbar.getElByPage(page));
      if (!$navbarEl || $navbarEl.length === 0) return;
      app.navbar.size($navbarEl);
    },
    pageInit(page) {
      const app = this;
      const $navbarEl = dom7_default(app.navbar.getElByPage(page));
      if (!$navbarEl || $navbarEl.length === 0) return;
      app.navbar.size($navbarEl);
      let needCollapseOnScrollHandler;
      if ($navbarEl.find(".title-large").length > 0) {
        $navbarEl.addClass("navbar-large");
      }
      if ($navbarEl.hasClass("navbar-large")) {
        if (app.params.navbar.collapseLargeTitleOnScroll) needCollapseOnScrollHandler = true;
        page.$el.addClass("page-with-navbar-large");
      }
      let needTransparentOnScroll;
      if (!needCollapseOnScrollHandler && $navbarEl.hasClass("navbar-transparent")) {
        needTransparentOnScroll = true;
      }
      let needHideOnScrollHandler;
      if (app.params.navbar.hideOnPageScroll || page.$el.find(".hide-navbar-on-scroll").length || page.$el.hasClass("hide-navbar-on-scroll") || page.$el.find(".hide-bars-on-scroll").length || page.$el.hasClass("hide-bars-on-scroll")) {
        if (page.$el.find(".keep-navbar-on-scroll").length || page.$el.hasClass("keep-navbar-on-scroll") || page.$el.find(".keep-bars-on-scroll").length || page.$el.hasClass("keep-bars-on-scroll")) {
          needHideOnScrollHandler = false;
        } else {
          needHideOnScrollHandler = true;
        }
      }
      if (needCollapseOnScrollHandler || needHideOnScrollHandler || needTransparentOnScroll) {
        app.navbar.initNavbarOnScroll(page.el, $navbarEl[0], needHideOnScrollHandler, needCollapseOnScrollHandler, needTransparentOnScroll);
      }
    },
    "panelOpen panelSwipeOpen modalOpen": function onPanelModalOpen(instance) {
      const app = this;
      instance.$el.find(".navbar:not(.navbar-previous)").each((navbarEl) => {
        app.navbar.size(navbarEl);
      });
    },
    tabShow(tabEl) {
      const app = this;
      dom7_default(tabEl).find(".navbar:not(.navbar-previous)").each((navbarEl) => {
        app.navbar.size(navbarEl);
      });
    }
  },
  clicks: {
    ".navbar .title": function onTitleClick($clickedEl, clickedData, e) {
      const app = this;
      if (!app.params.navbar.scrollTopOnTitleClick) return;
      if (dom7_default(e.target).closest("a, button").length > 0) {
        return;
      }
      let $pageContentEl;
      const $navbarEl = $clickedEl.parents(".navbar");
      const $navbarsEl = $navbarEl.parents(".navbars");
      $pageContentEl = $navbarEl.parents(".page-content");
      if ($pageContentEl.length === 0) {
        if ($navbarEl.parents(".page").length > 0) {
          $pageContentEl = $navbarEl.parents(".page").find(".page-content");
        }
        if ($pageContentEl.length === 0 && $navbarsEl.length) {
          if ($navbarsEl.nextAll(".page-current").length > 0) {
            $pageContentEl = $navbarsEl.nextAll(".page-current").find(".page-content");
          }
        }
        if ($pageContentEl.length === 0) {
          if ($navbarEl.nextAll(".page-current").length > 0) {
            $pageContentEl = $navbarEl.nextAll(".page-current").find(".page-content");
          }
        }
      }
      if ($pageContentEl && $pageContentEl.length > 0) {
        if ($pageContentEl.hasClass("tab")) {
          $pageContentEl = $pageContentEl.parent(".tabs").children(".page-content.tab-active");
        }
        if ($pageContentEl.length > 0) $pageContentEl.scrollTop(0, 300);
      }
    }
  },
  vnode: {
    navbar: {
      postpatch(vnode) {
        const app = this;
        app.navbar.size(vnode.elm);
      }
    }
  }
};

// node_modules/framework7/components/toolbar/toolbar.js
var Toolbar = {
  setHighlight(tabbarEl) {
    const app = this;
    const $tabbarEl = dom7_default(tabbarEl);
    if (app.theme === "ios" && !$tabbarEl.hasClass("tabbar-highlight")) return;
    if ($tabbarEl.length === 0 || !($tabbarEl.hasClass("tabbar") || $tabbarEl.hasClass("tabbar-icons"))) return;
    let $highlightEl = $tabbarEl.find(".tab-link-highlight");
    const tabLinksCount = $tabbarEl.find(".tab-link").length;
    if (tabLinksCount === 0) {
      $highlightEl.remove();
      return;
    }
    if ($highlightEl.length === 0) {
      $tabbarEl.children(".toolbar-inner").append('<span class="tab-link-highlight"></span>');
      $highlightEl = $tabbarEl.find(".tab-link-highlight");
    } else if ($highlightEl.next().length) {
      $tabbarEl.children(".toolbar-inner").append($highlightEl);
    }
    const $activeLink = $tabbarEl.find(".tab-link-active");
    let highlightWidth;
    let highlightTranslate;
    if ($tabbarEl.hasClass("tabbar-scrollable") && $activeLink && $activeLink[0]) {
      highlightWidth = `${$activeLink[0].offsetWidth}px`;
      highlightTranslate = `${$activeLink[0].offsetLeft}px`;
    } else {
      const activeIndex = $activeLink.index();
      highlightWidth = `${100 / tabLinksCount}%`;
      highlightTranslate = `${(app.rtl ? -activeIndex : activeIndex) * 100}%`;
    }
    nextFrame(() => {
      $highlightEl.css("width", highlightWidth).transform(`translate3d(${highlightTranslate},0,0)`);
    });
  },
  init(tabbarEl) {
    const app = this;
    app.toolbar.setHighlight(tabbarEl);
  },
  hide(el, animate2) {
    if (animate2 === void 0) {
      animate2 = true;
    }
    const app = this;
    const $el = dom7_default(el);
    if ($el.hasClass("toolbar-hidden")) return;
    const className = `toolbar-hidden${animate2 ? " toolbar-transitioning" : ""}`;
    $el.transitionEnd(() => {
      $el.removeClass("toolbar-transitioning");
    });
    $el.addClass(className);
    $el.trigger("toolbar:hide");
    app.emit("toolbarHide", $el[0]);
  },
  show(el, animate2) {
    if (animate2 === void 0) {
      animate2 = true;
    }
    const app = this;
    const $el = dom7_default(el);
    if (!$el.hasClass("toolbar-hidden")) return;
    if (animate2) {
      $el.addClass("toolbar-transitioning");
      $el.transitionEnd(() => {
        $el.removeClass("toolbar-transitioning");
      });
    }
    $el.removeClass("toolbar-hidden");
    $el.trigger("toolbar:show");
    app.emit("toolbarShow", $el[0]);
  },
  initToolbarOnScroll(pageEl) {
    const app = this;
    const $pageEl = dom7_default(pageEl);
    let $toolbarEl = $pageEl.parents(".view").children(".toolbar");
    if ($toolbarEl.length === 0) {
      $toolbarEl = $pageEl.find(".toolbar");
    }
    if ($toolbarEl.length === 0) {
      $toolbarEl = $pageEl.parents(".views").children(".tabbar, .tabbar-icons");
    }
    if ($toolbarEl.length === 0) {
      return;
    }
    let previousScrollTop;
    let currentScrollTop;
    let scrollHeight;
    let offsetHeight;
    let reachEnd;
    let action;
    let toolbarHidden;
    function handleScroll(e) {
      if ($pageEl.hasClass("page-with-card-opened")) return;
      if ($pageEl.hasClass("page-previous")) return;
      const scrollContent = this;
      if (e && e.target && e.target !== scrollContent) {
        return;
      }
      currentScrollTop = scrollContent.scrollTop;
      scrollHeight = scrollContent.scrollHeight;
      offsetHeight = scrollContent.offsetHeight;
      reachEnd = currentScrollTop + offsetHeight >= scrollHeight;
      toolbarHidden = $toolbarEl.hasClass("toolbar-hidden");
      if (reachEnd) {
        if (app.params.toolbar.showOnPageScrollEnd) {
          action = "show";
        }
      } else if (previousScrollTop > currentScrollTop) {
        if (app.params.toolbar.showOnPageScrollTop || currentScrollTop <= 44) {
          action = "show";
        } else {
          action = "hide";
        }
      } else if (currentScrollTop > 44) {
        action = "hide";
      } else {
        action = "show";
      }
      if (action === "show" && toolbarHidden) {
        app.toolbar.show($toolbarEl);
        toolbarHidden = false;
      } else if (action === "hide" && !toolbarHidden) {
        app.toolbar.hide($toolbarEl);
        toolbarHidden = true;
      }
      previousScrollTop = currentScrollTop;
    }
    $pageEl.on("scroll", ".page-content", handleScroll, true);
    $pageEl[0].f7ScrollToolbarHandler = handleScroll;
  }
};
var toolbar_default = {
  name: "toolbar",
  create() {
    const app = this;
    bindMethods(app, {
      toolbar: Toolbar
    });
  },
  params: {
    toolbar: {
      hideOnPageScroll: false,
      showOnPageScrollEnd: true,
      showOnPageScrollTop: true
    }
  },
  on: {
    pageBeforeRemove(page) {
      if (page.$el[0].f7ScrollToolbarHandler) {
        page.$el.off("scroll", ".page-content", page.$el[0].f7ScrollToolbarHandler, true);
      }
    },
    pageBeforeIn(page) {
      const app = this;
      let $toolbarEl = page.$el.parents(".view").children(".toolbar");
      if ($toolbarEl.length === 0) {
        $toolbarEl = page.$el.parents(".views").children(".tabbar, .tabbar-icons");
      }
      if ($toolbarEl.length === 0) {
        $toolbarEl = page.$el.find(".toolbar");
      }
      if ($toolbarEl.length === 0) {
        return;
      }
      if (page.$el.hasClass("no-toolbar")) {
        app.toolbar.hide($toolbarEl);
      } else {
        app.toolbar.show($toolbarEl);
      }
    },
    pageInit(page) {
      const app = this;
      page.$el.find(".tabbar, .tabbar-icons").each((tabbarEl) => {
        app.toolbar.init(tabbarEl);
      });
      if (app.params.toolbar.hideOnPageScroll || page.$el.find(".hide-toolbar-on-scroll").length || page.$el.hasClass("hide-toolbar-on-scroll") || page.$el.find(".hide-bars-on-scroll").length || page.$el.hasClass("hide-bars-on-scroll")) {
        if (page.$el.find(".keep-toolbar-on-scroll").length || page.$el.hasClass("keep-toolbar-on-scroll") || page.$el.find(".keep-bars-on-scroll").length || page.$el.hasClass("keep-bars-on-scroll")) {
          return;
        }
        app.toolbar.initToolbarOnScroll(page.el);
      }
    },
    init() {
      const app = this;
      app.$el.find(".tabbar, .tabbar-icons").each((tabbarEl) => {
        app.toolbar.init(tabbarEl);
      });
    }
  },
  vnode: {
    tabbar: {
      insert(vnode) {
        const app = this;
        app.toolbar.init(vnode.elm);
      }
    }
  }
};

// node_modules/framework7/components/subnavbar/subnavbar.js
var subnavbar_default = {
  name: "subnavbar",
  on: {
    pageInit(page) {
      if (page.$navbarEl && page.$navbarEl.length && page.$navbarEl.find(".subnavbar").length) {
        page.$el.addClass("page-with-subnavbar");
      }
      const $innerSubnavbars = page.$el.find(".subnavbar").filter((subnavbarEl) => {
        return dom7_default(subnavbarEl).parents(".page")[0] === page.$el[0];
      });
      if ($innerSubnavbars.length) {
        page.$el.addClass("page-with-subnavbar");
      }
    }
  }
};

// node_modules/framework7/components/touch-ripple/touch-ripple-class.js
var TouchRipple = class {
  constructor(app, $el, x, y) {
    const ripple = this;
    if (!$el) return void 0;
    const {
      left,
      top,
      width: width2,
      height: height2
    } = $el[0].getBoundingClientRect();
    const center = {
      x: x - left,
      y: y - top
    };
    let diameter = Math.max((height2 ** 2 + width2 ** 2) ** 0.5, 48);
    let isInset = false;
    const insetElements = app.params.touch.touchRippleInsetElements || "";
    if (insetElements && $el.is(insetElements)) {
      isInset = true;
    }
    if (isInset) {
      diameter = Math.max(Math.min(width2, height2), 48);
    }
    if (!isInset && $el.css("overflow") === "hidden") {
      const distanceFromCenter = ((center.x - width2 / 2) ** 2 + (center.y - height2 / 2) ** 2) ** 0.5;
      const scale = (diameter / 2 + distanceFromCenter) / (diameter / 2);
      ripple.rippleTransform = `translate3d(0px, 0px, 0) scale(${scale * 2})`;
    } else {
      ripple.rippleTransform = `translate3d(${-center.x + width2 / 2}px, ${-center.y + height2 / 2}px, 0) scale(1)`;
    }
    if (isInset) {
      $el.addClass("ripple-inset");
    }
    ripple.$rippleWaveEl = dom7_default(`<div class="ripple-wave${isInset ? " ripple-wave-inset" : ""}" style="width: ${diameter}px; height: ${diameter}px; margin-top:-${diameter / 2}px; margin-left:-${diameter / 2}px; left:${center.x}px; top:${center.y}px; --f7-ripple-transform: ${ripple.rippleTransform}"></div>`);
    $el.prepend(ripple.$rippleWaveEl);
    ripple.$rippleWaveEl.animationEnd(() => {
      if (!ripple.$rippleWaveEl) return;
      if (ripple.$rippleWaveEl.hasClass("ripple-wave-out")) return;
      ripple.$rippleWaveEl.addClass("ripple-wave-in");
      if (ripple.shouldBeRemoved) {
        ripple.out();
      }
    });
    return ripple;
  }
  destroy() {
    let ripple = this;
    if (ripple.$rippleWaveEl) {
      ripple.$rippleWaveEl.remove();
    }
    Object.keys(ripple).forEach((key) => {
      ripple[key] = null;
      delete ripple[key];
    });
    ripple = null;
  }
  out() {
    const ripple = this;
    const {
      $rippleWaveEl
    } = this;
    clearTimeout(ripple.removeTimeout);
    $rippleWaveEl.addClass("ripple-wave-out");
    ripple.removeTimeout = setTimeout(() => {
      ripple.destroy();
    }, 300);
    $rippleWaveEl.animationEnd(() => {
      clearTimeout(ripple.removeTimeout);
      ripple.destroy();
    });
  }
  remove() {
    const ripple = this;
    if (ripple.shouldBeRemoved) return;
    ripple.removeTimeout = setTimeout(() => {
      ripple.destroy();
    }, 400);
    ripple.shouldBeRemoved = true;
    if (ripple.$rippleWaveEl.hasClass("ripple-wave-in")) {
      ripple.out();
    }
  }
};

// node_modules/framework7/components/touch-ripple/touch-ripple.js
var touch_ripple_default = {
  name: "touch-ripple",
  static: {
    TouchRipple
  },
  create() {
    const app = this;
    app.touchRipple = {
      create() {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        return new TouchRipple(...args);
      }
    };
  }
};

// node_modules/framework7/components/modal/modal-class.js
var openedModals = [];
var dialogsQueue = [];
function clearDialogsQueue() {
  if (dialogsQueue.length === 0) return;
  const dialog = dialogsQueue.shift();
  dialog.open();
}
var Modal = class extends class_default {
  constructor(app, params) {
    super(params, [app]);
    const modal = this;
    const defaults = {};
    modal.useModulesParams(defaults);
    modal.params = extend2(defaults, params);
    modal.opened = false;
    let $containerEl = modal.params.containerEl ? dom7_default(modal.params.containerEl).eq(0) : app.$el;
    if (!$containerEl.length) $containerEl = app.$el;
    modal.$containerEl = $containerEl;
    modal.containerEl = $containerEl[0];
    modal.useModules();
    return this;
  }
  onOpen() {
    const modal = this;
    modal.opened = true;
    openedModals.push(modal);
    dom7_default("html").addClass(`with-modal-${modal.type.toLowerCase()}`);
    modal.$el.trigger(`modal:open ${modal.type.toLowerCase()}:open`);
    modal.emit(`local::open modalOpen ${modal.type}Open`, modal);
  }
  onOpened() {
    const modal = this;
    modal.$el.trigger(`modal:opened ${modal.type.toLowerCase()}:opened`);
    modal.emit(`local::opened modalOpened ${modal.type}Opened`, modal);
  }
  onClose() {
    const modal = this;
    modal.opened = false;
    if (!modal.type || !modal.$el) return;
    openedModals.splice(openedModals.indexOf(modal), 1);
    dom7_default("html").removeClass(`with-modal-${modal.type.toLowerCase()}`);
    modal.$el.trigger(`modal:close ${modal.type.toLowerCase()}:close`);
    modal.emit(`local::close modalClose ${modal.type}Close`, modal);
  }
  onClosed() {
    const modal = this;
    if (!modal.type || !modal.$el) return;
    modal.$el.removeClass("modal-out");
    modal.$el.hide();
    if (modal.params.backdrop && (modal.params.backdropUnique || modal.forceBackdropUnique) && modal.$backdropEl) {
      modal.$backdropEl.remove();
    }
    modal.$el.trigger(`modal:closed ${modal.type.toLowerCase()}:closed`);
    modal.emit(`local::closed modalClosed ${modal.type}Closed`, modal);
  }
  open(animateModal, force) {
    const modal = this;
    const document2 = getDocument();
    const app = modal.app;
    const $el = modal.$el;
    const $backdropEl = modal.$backdropEl;
    const type = modal.type;
    let animate2 = true;
    if (typeof animateModal !== "undefined") animate2 = animateModal;
    else if (typeof modal.params.animate !== "undefined") {
      animate2 = modal.params.animate;
    }
    if (!$el || $el.hasClass("modal-in")) {
      if (animateModal === false && $el[0] && type !== "dialog") {
        $el[0].style.display = "block";
      }
      if (!force) return modal;
    }
    if (type === "dialog" && app.params.modal.queueDialogs) {
      let pushToQueue;
      if (dom7_default(".dialog.modal-in").length > 0) {
        pushToQueue = true;
      } else if (openedModals.length > 0) {
        openedModals.forEach((openedModal) => {
          if (openedModal.type === "dialog") pushToQueue = true;
        });
      }
      if (pushToQueue) {
        dialogsQueue.push(modal);
        return modal;
      }
    }
    const $modalParentEl = $el.parent();
    const wasInDom = $el.parents(document2).length > 0;
    if (!$modalParentEl.is(modal.$containerEl)) {
      modal.$containerEl.append($el);
      modal.once(`${type}Closed`, () => {
        if (wasInDom) {
          $modalParentEl.append($el);
        } else {
          $el.remove();
        }
      });
    }
    $el.show();
    if (modal.params.backdrop && (modal.params.backdropUnique || modal.forceBackdropUnique) && modal.$backdropEl) {
      modal.$backdropEl.insertBefore($el);
    }
    modal._clientLeft = $el[0].clientLeft;
    function transitionEnd2() {
      if ($el.hasClass("modal-out")) {
        modal.onClosed();
      } else if ($el.hasClass("modal-in")) {
        modal.onOpened();
      }
    }
    if (animate2) {
      if ($backdropEl) {
        $backdropEl.removeClass("not-animated");
        $backdropEl.addClass("backdrop-in");
      }
      $el.animationEnd(() => {
        transitionEnd2();
      });
      $el.transitionEnd(() => {
        transitionEnd2();
      });
      $el.removeClass("modal-out not-animated").addClass("modal-in");
      modal.onOpen();
    } else {
      if ($backdropEl) {
        $backdropEl.addClass("backdrop-in not-animated");
      }
      $el.removeClass("modal-out").addClass("modal-in not-animated");
      modal.onOpen();
      modal.onOpened();
    }
    return modal;
  }
  close(animateModal) {
    const modal = this;
    const $el = modal.$el;
    const $backdropEl = modal.$backdropEl;
    let animate2 = true;
    if (typeof animateModal !== "undefined") animate2 = animateModal;
    else if (typeof modal.params.animate !== "undefined") {
      animate2 = modal.params.animate;
    }
    if (!$el || !$el.hasClass("modal-in")) {
      if (dialogsQueue.indexOf(modal) >= 0) {
        dialogsQueue.splice(dialogsQueue.indexOf(modal), 1);
      }
      return modal;
    }
    if ($backdropEl) {
      let needToHideBackdrop = true;
      if (modal.type === "popup") {
        modal.$el.prevAll(".popup.modal-in").add(modal.$el.nextAll(".popup.modal-in")).each((popupEl) => {
          const popupInstance = popupEl.f7Modal;
          if (!popupInstance) return;
          if (popupInstance.params.closeByBackdropClick && popupInstance.params.backdrop && popupInstance.backdropEl === modal.backdropEl) {
            needToHideBackdrop = false;
          }
        });
      }
      if (needToHideBackdrop) {
        $backdropEl[animate2 ? "removeClass" : "addClass"]("not-animated");
        $backdropEl.removeClass("backdrop-in");
      }
    }
    $el[animate2 ? "removeClass" : "addClass"]("not-animated");
    function transitionEnd2() {
      if ($el.hasClass("modal-out")) {
        modal.onClosed();
      } else if ($el.hasClass("modal-in")) {
        modal.onOpened();
      }
    }
    if (animate2) {
      $el.animationEnd(() => {
        transitionEnd2();
      });
      $el.transitionEnd(() => {
        transitionEnd2();
      });
      $el.removeClass("modal-in").addClass("modal-out");
      modal.onClose();
    } else {
      $el.addClass("not-animated").removeClass("modal-in").addClass("modal-out");
      modal.onClose();
      modal.onClosed();
    }
    if (modal.type === "dialog") {
      clearDialogsQueue();
    }
    return modal;
  }
  destroy() {
    const modal = this;
    if (modal.destroyed) return;
    modal.emit(`local::beforeDestroy modalBeforeDestroy ${modal.type}BeforeDestroy`, modal);
    if (modal.$el) {
      modal.$el.trigger(`modal:beforedestroy ${modal.type.toLowerCase()}:beforedestroy`);
      if (modal.$el.length && modal.$el[0].f7Modal) {
        delete modal.$el[0].f7Modal;
      }
    }
    deleteProps(modal);
    modal.destroyed = true;
  }
};
var modal_class_default = Modal;

// node_modules/framework7/components/modal/custom-modal-class.js
var CustomModal = class extends modal_class_default {
  constructor(app, params) {
    const extendedParams = extend2({
      backdrop: true,
      closeByBackdropClick: true,
      on: {}
    }, params);
    super(app, extendedParams);
    const customModal = this;
    customModal.params = extendedParams;
    let $el;
    if (!customModal.params.el) {
      $el = dom7_default(customModal.params.content);
    } else {
      $el = dom7_default(customModal.params.el);
    }
    if ($el && $el.length > 0 && $el[0].f7Modal) {
      return $el[0].f7Modal;
    }
    if ($el.length === 0) {
      return customModal.destroy();
    }
    let $backdropEl;
    if (customModal.params.backdrop) {
      $backdropEl = app.$el.children(".custom-modal-backdrop");
      if ($backdropEl.length === 0) {
        $backdropEl = dom7_default('<div class="custom-modal-backdrop"></div>');
        app.$el.append($backdropEl);
      }
    }
    function handleClick(e) {
      if (!customModal || customModal.destroyed) return;
      if ($backdropEl && e.target === $backdropEl[0]) {
        customModal.close();
      }
    }
    customModal.on("customModalOpened", () => {
      if (customModal.params.closeByBackdropClick && customModal.params.backdrop) {
        app.on("click", handleClick);
      }
    });
    customModal.on("customModalClose", () => {
      if (customModal.params.closeByBackdropClick && customModal.params.backdrop) {
        app.off("click", handleClick);
      }
    });
    extend2(customModal, {
      app,
      $el,
      el: $el[0],
      $backdropEl,
      backdropEl: $backdropEl && $backdropEl[0],
      type: "customModal"
    });
    $el[0].f7Modal = customModal;
    return customModal;
  }
};
var custom_modal_class_default = CustomModal;

// node_modules/framework7/components/modal/modal.js
var modal_default = {
  name: "modal",
  static: {
    Modal: modal_class_default,
    CustomModal: custom_modal_class_default
  },
  create() {
    const app = this;
    app.customModal = {
      create(params) {
        return new custom_modal_class_default(app, params);
      }
    };
  },
  params: {
    modal: {
      queueDialogs: true
    }
  }
};

export {
  getDocument,
  getWindow,
  dom7_default,
  id,
  mdPreloaderContent,
  iosPreloaderContent,
  deleteProps,
  nextTick,
  nextFrame,
  now,
  getTranslate,
  serializeObject,
  extend2 as extend,
  colorHexToRgb,
  colorRgbToHex,
  colorRgbToHsl,
  colorHslToRgb,
  colorHsbToHsl,
  colorHslToHsb,
  bindMethods,
  utils_exports,
  getSupport,
  getDevice,
  class_default,
  ConstructorMethods,
  ModalMethods,
  jsx_default,
  app_class_default,
  device_default,
  support_default,
  utils_default,
  resize_default,
  touch_default,
  router_default,
  clicks_default,
  history_default2 as history_default,
  service_worker_default,
  create_store_default,
  store_default,
  statusbar_default,
  view_default,
  navbar_default,
  toolbar_default,
  subnavbar_default,
  touch_ripple_default,
  modal_class_default,
  modal_default
};
//# sourceMappingURL=chunk-YVUCKT22.js.map
